'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { LockIcon, MailIcon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Login as LoginType } from '@/graphql/declarations/auth';
import { AUTHSTORE } from '@/lib/auth-storage';
import { generateMFA, verifyMFA } from '@/graphql/declarations/otpValues';
import { useBrowserSessionID } from '@/components/hooks/useSession';
import { Authclient_Mfa_GenerateInputChannel } from '@/graphql/generated/graphql';
import { env } from 'next-runtime-env';

const LoginPage = (props: {
  form: any;
  setForm: any;
  isLoggingIn: boolean;
  handleLogin: () => void;
  error?: string;
}) => {
  const redirectToRegister = () => {
    // Get the current URL's query parameters
    const queryParams = new URLSearchParams(window.location.search);

    // Construct the new URL for the register page
    const registerUrl = new URL(
      env('NEXT_PUBLIC_SIGNUP_CUSTOM_URL') || '/register',
      window.location.origin,
    );

    // Append each query parameter to the new URL
    queryParams.forEach((value, key) => {
      registerUrl.searchParams.append(key, value);
    });

    // Redirect to the new URL
    window.location.href = registerUrl.toString();
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="Logo" />
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <div className="relative">
            <MailIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="email"
              placeholder="<EMAIL>"
              type="email"
              className="pl-10"
              value={props.form.email}
              onChange={(e) => props.setForm({ ...props.form, email: e.target.value })}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <LockIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="password"
              type="password"
              className="pl-10"
              value={props.form.password}
              onChange={(e) => props.setForm({ ...props.form, password: e.target.value })}
            />
          </div>
        </div>
        <Button className="w-full" onClick={props.handleLogin} disabled={props.isLoggingIn}>
          {props.isLoggingIn ? 'Logging in...' : 'Sign In'}
        </Button>
        {props.error && (
          <p className="-pt-2 mx-auto text-center text-xs text-red-600">{props.error}</p>
        )}
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        <Button variant="link" className="text-sm text-muted-foreground">
          Forgot password?
        </Button>
      </CardFooter>
    </>
  );
};

const MFA = (props: {
  onVerify: (code: string) => void;
  onResend: () => void;
  onBack: () => void;
  error?: string;
  submitting?: boolean;
  pendingToResend?: number;
}) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      // Move focus to the next input
      if (value !== '' && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="Logo" />
        </CardTitle>
        <CardDescription className="text-center">
          For your security, we need to verify your identity. Please enter the 6-digit code sent to
          your provided Account SMS Number.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="code-0" className="sr-only">
            2FA Code
          </Label>
          <div className="mx-auto flex max-w-xs justify-between">
            {code.map((digit, index) => (
              <Input
                key={index}
                id={`code-${index}`}
                type="text"
                inputMode="numeric"
                pattern="\d*"
                maxLength={1}
                className="h-12 w-12 text-center text-2xl"
                value={digit}
                onChange={(e) => handleCodeChange(index, e.target.value)}
              />
            ))}
          </div>
        </div>
        <Button
          className="w-full"
          type="submit"
          onClick={() => props.onVerify(code.join(''))}
          disabled={props.submitting}
        >
          {props.submitting ? 'Verifying...' : 'Verify'}
        </Button>
        {props.error && (
          <p className="mx-auto -mt-2 text-center text-xs text-red-600">{props.error}</p>
        )}
      </CardContent>
      <CardFooter className="-mt-4 flex flex-col space-y-2">
        {!props.pendingToResend ? (
          <Button variant="link" className="text-sm text-muted-foreground" onClick={props.onResend}>
            Resend code
          </Button>
        ) : (
          <p className="text-center text-sm text-muted-foreground">
            Resend code in {props.pendingToResend} seconds
          </p>
        )}
        <div className="text-center text-sm text-muted-foreground">
          <Button
            variant="link"
            className="text-primary h-auto p-0 font-normal"
            onClick={props.onBack}
          >
            Go Back
          </Button>
        </div>
      </CardFooter>
    </>
  );
};

export default function Component() {
  const [currentPage, setCurrentPage] = useState('login');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [form, setForm] = useState({
    email: '',
    password: '',
  });

  const [error, setError] = useState('');

  const { id: browserID } = useBrowserSessionID();

  const [noPhoneError, setNoPhoneError] = useState(false);

  const handleLogin = async () => {
    if (!form.email || !form.password) {
      setError('Email and Password are required');
      return;
    }

    setIsSubmitting(true);

    try {
      const resp = await apolloClient.mutate({
        mutation: LoginType,
        variables: {
          email: form?.email,
          password: form?.password,
          browserId: browserID,
        },
      });

      if (
        resp.data?.authclient_login?.__typename === 'ClientItemAuthenticationWithPasswordSuccess'
      ) {
        const token = resp.data.authclient_login.sessionToken;

        AUTHSTORE.set(token);

        // check if there's a redirect query param
        const searchParams = new URLSearchParams(window.location.search);
        const redirect = searchParams.get('redirect');
        if (redirect) {
          window.location.href = redirect;
          return;
        } else {
          // console.log("redirecting to dashboard");
          window.location.href = '/dashboard';
          // router.replace("/dashboard");
          // console.log(window.location.href);
        }
      } else if (
        resp.data?.authclient_login?.__typename === 'ClientItemAuthenticationWithPasswordFailure'
      ) {
        const isMFA = resp.data.authclient_login.message.includes('MFA');
        if (isMFA) {
          setCurrentPage(isMFA ? 'mfa' : 'login');
          generateOTP();
        }
        setIsSubmitting(false);
      } else {
        setError('Failed to login');
        setIsSubmitting(false);
      }

      // console.log(resp);
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    setError('');
    setIsSubmitting(false);
  }, [form]);

  const sidCode = useRef('');
  const [pendingToResend, setPendingToResend] = useState(0);

  useEffect(() => {
    setError('');
  }, [form, currentPage]);

  useEffect(() => {
    setIsSubmitting(false);
  }, [currentPage]);

  useEffect(() => {
    if (pendingToResend) {
      const interval = setInterval(() => {
        setPendingToResend((prev) => (prev > 0 ? prev - 1 : 0));
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [pendingToResend]);

  const generateOTP = async () => {
    if (!form.email) {
      setError('Email is required');
      return false;
    }

    try {
      const response = await apolloClient.mutate({
        mutation: generateMFA,
        variables: {
          input: {
            channel: Authclient_Mfa_GenerateInputChannel.Sms,
            email: form.email,
            browserID: browserID,
          },
        },
      });

      if (response.data?.authclient_mfa_generate?.sid) {
        setPendingToResend(60);
        sidCode.current = response.data.authclient_mfa_generate.sid;
        return true;
      } else {
        setNoPhoneError(true);
        return false;
      }
    } catch (error) {
      setError('An error occurred. Please try again');
      setNoPhoneError(true);
      return;
    }
  };

  const validateOTP = async (code: string) => {
    if (code.length !== 6) {
      setError('Invalid code. Please enter a 6-digit code');
      return;
    }

    if (!sidCode.current) {
      setError('SID Code missing. Please try again');
      return;
    }

    setIsSubmitting(true);

    const response = await apolloClient.mutate({
      mutation: verifyMFA,
      variables: {
        input: {
          sid: sidCode.current,
          code,
        },
      },
    });

    if (response.data?.authclient_mfa_verify?.status) {
      handleLogin();
    } else {
      setIsSubmitting(false);
      setError('Invalid code. Please try again');
    }
  };

  const rerequestOtp = async () => {
    setIsSubmitting(true);
    const otpStatus = await generateOTP();

    if (otpStatus) {
      setIsSubmitting(false);
      setPendingToResend(60);
    } else {
      setError('An error occurred. Please try again');
    }
  };

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        {currentPage === 'login' ? (
          <LoginPage
            form={form}
            setForm={setForm}
            isLoggingIn={isSubmitting}
            handleLogin={handleLogin}
            error={error}
          />
        ) : null}
        {!noPhoneError && currentPage === 'mfa' ? (
          <MFA
            onBack={() => setCurrentPage('login')}
            error={error}
            onResend={() => {
              rerequestOtp();
            }}
            onVerify={(code) => {
              validateOTP(code);
            }}
            submitting={isSubmitting}
            pendingToResend={pendingToResend}
          />
        ) : null}
        {noPhoneError && (
          <CardFooter className="flex flex-col space-y-2">
            <p className="mt-6 p-2 text-center">
              Looks like your account doesn&apos;t have a phone number, please contact support to
              enable MFA for your account.
            </p>
            <Button
              variant="link"
              className="text-sm text-muted-foreground"
              onClick={() => {
                setNoPhoneError(false);
                setCurrentPage('login');
              }}
            >
              Go Back
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}
