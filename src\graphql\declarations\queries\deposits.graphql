query Gateway_deposits($input: Gateway_depositsInput!) {
  gateway_deposits(input: $input) {
    data {
      depositID
      location
      date
      amount
      status
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}

query Gateway_deposit($input: Gateway_depositInput!) {
  gateway_deposit(input: $input) {
    depositID
    date
    last4
    amount
    sales
    fees
    totalDeposit
    batches {
      batchID
      depositID
      total
    }
    transactions {
      transactionID
      date
      method
      name
      last4
      customer
      amount
      brand
      status
    }
  }
}
