'use client';

import { Dispatch, SetStateAction } from 'react';
// import { createAuroraDraft, updateAuroraDraft } from '@/graphql/declarations/createDraft';

const MerchantForm = ({
  currentID,
  setOpen,
}: {
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentID?: string | null;
}) => {
  // const router = useRouter();
  // const { toast } = useToast();

  // const { data: submitData } = useQuery(getGroups, {
  //   variables: {
  //     where: {
  //       id: {
  //         equals: currentID!,
  //       },
  //     },
  //     take: 10,
  //     skip: 0,
  //   },
  //   skip: !currentID,
  // });
  // const formState = useForm<z.infer<typeof merchantSchema>>({
  //   resolver: zodResolver(merchantSchema),
  //   defaultValues: {
  //     legal_business_name: '',
  //     dba_name: '',
  //     ein: '',
  //     business_established_date: undefined,
  //     website_link: '',
  //     business_email: '',
  //     country_number: '',
  //     country_number2: '',
  //     dial_code: '',
  //     dial_code2: '',
  //     phone_number: '',
  //     phone_number2: '',
  //     zip_code: '',
  //     city: '',
  //     state: '',
  //     street: '',
  //     business_catergory: '',
  //     description_of_what_you_sell: '',
  //     use_different_legal_email: false,

  //     // transactions
  //     swipe: '',
  //     keyed: '',
  //     ecommerce: '',
  //     vmd_average_transaction_amount: '',
  //     vmd_highest_transaction_amount: '',
  //     vmd_gross_monthly_sales_volume: '',
  //     amex_average_transaction_amount: '',
  //     amex_highest_transaction_amount: '',
  //     amex_gross_monthly_sales_volume: '',

  //     is_an_individual: false,
  //     ownership: [
  //       {
  //         first_name: '',
  //         last_name: '',
  //         position: '',
  //         ownership: '',
  //         phone_number: '',
  //         home_address: '',
  //         country: '',
  //         state: '',
  //         city: '',
  //         zip_code: '',
  //         ssn: '',
  //         date_of_birth: undefined,
  //         email: '',
  //       },
  //     ],

  //     routing_number: '',

  //     documents: [
  //       {
  //         document_type: 'PhotoIdentification',
  //         file: null,
  //       },
  //     ],
  //   },
  //   reValidateMode: 'onChange',
  //   shouldFocusError: true,
  // });

  // async function loadExisting() {
  //   // let data = fetchData?.groups?.[0];
  //   // if (!data) return;
  //   // let dat = reverseTranscoding(fetchData?.groups?.[0]);
  //   // @ts-ignore
  //   // formState.reset(dat);
  // }

  // async function initializeGroup() {
  //   // this would ideally be called after the first page is submitted
  //   const groupName = values.legal_business_name;
  //   if (!currentID) {
  //     // create a group

  //     const group_response = await apolloClient.mutate({
  //       mutation: createGroup,
  //       variables: {
  //         data: {
  //           actualName: groupName,
  //         },
  //       },
  //     });

  //     const groupId = group_response?.data?.createGroup?.id;

  //     // update current route query
  //     const curLink = window.location.href;
  //     // get params and update modal to group id
  //     const url = new URL(curLink);
  //     url.searchParams.set('modal', groupId || '');
  //     router.push(url.toString());

  //     await saveCache({ id: groupId! });
  //   }
  // }

  // const [selectedTab, setSelectedTab] = useState(MerchantFormState.BusinessInformation);
  // const [isSubmitting, setIsSubmitting] = useState(false);

  // const values = formState.watch();
  // const renderForm = () => {
  //   switch (selectedTab) {
  //     case MerchantFormState.BusinessInformation:
  //       return <BusinessInformation formState={formState} />;
  //     case MerchantFormState.Transactions:
  //       return <Transactions formState={formState} />;
  //     case MerchantFormState.Ownership:
  //       return <Ownership formState={formState} />;
  //     case MerchantFormState.Banking:
  //       return <BankingInformation formState={formState} />;
  //     case MerchantFormState.Documents:
  //       return <Documents formState={formState} />;
  //     case MerchantFormState.Review:
  //       return <Review formState={formState} />;
  //     default:
  //       return <div></div>;
  //   }
  // };

  // const onNext = async (state: MerchantFormState, fieldsToValidate: string[]) => {
  //   const results = await Promise.all(
  //     fieldsToValidate.map((field: string) => formState.trigger(field as any)),
  //   );

  //   console.log(results);

  //   if (results.every(Boolean)) {
  //     setSelectedTab(state);
  //   }
  // };

  // const handlePrev = () => {
  //   switch (selectedTab) {
  //     case MerchantFormState.Transactions:
  //       setSelectedTab(MerchantFormState.BusinessInformation);

  //       break;
  //     case MerchantFormState.Ownership:
  //       setSelectedTab(MerchantFormState.Transactions);
  //       break;
  //     case MerchantFormState.Banking:
  //       setSelectedTab(MerchantFormState.Ownership);
  //       break;
  //     case MerchantFormState.Documents:
  //       setSelectedTab(MerchantFormState.Banking);
  //       break;
  //     case MerchantFormState.Review:
  //       setSelectedTab(MerchantFormState.Documents);
  //       break;
  //     default:
  //       return;
  //   }
  // };

  // async function handleNext() {
  //   if (currentID) {
  //     await saveCache({ id: currentID });
  //   }
  //   switch (selectedTab) {
  //     case MerchantFormState.BusinessInformation:
  //       onNext(MerchantFormState.Transactions, [
  //         'legal_business_name',
  //         'type_of_business',
  //         'dba_name',
  //         'ein',
  //         'business_established_date',
  //         'business_email',
  //         'phone_number',
  //         'phone_number2',
  //         'zip_code',
  //         'city',
  //         'state',
  //         'street',
  //         // "phone_number",
  //         // "phone_number2",
  //         // "dial_code",
  //         // "dial_code2",
  //       ]);
  //       initializeGroup();
  //       break;
  //     case MerchantFormState.Transactions:
  //       onNext(MerchantFormState.Ownership, [
  //         'mccObject',
  //         'description_of_what_you_sell',
  //         'swipe',
  //         'keyed',
  //         'ecommerce',
  //         'vmd_average_transaction_amount',
  //         'vmd_highest_transaction_amount',
  //         'vmd_gross_monthly_sales_volume',
  //         'amex_average_transaction_amount',
  //         'amex_highest_transaction_amount',
  //         'amex_gross_monthly_sales_volume',
  //       ]);

  //       break;
  //     case MerchantFormState.Ownership:
  //       onNext(MerchantFormState.Banking, [
  //         'ownership',
  //         ...values.ownership.flatMap((_, index) => [
  //           `ownership[${index}].first_name`,
  //           `ownership[${index}].last_name`,
  //           `ownership[${index}].position`,
  //           `ownership[${index}].ownership`,
  //           `ownership[${index}].phone_number`,
  //           `ownership[${index}].home_address`,
  //           `ownership[${index}].country`,
  //           `ownership[${index}].state`,
  //           `ownership[${index}].city`,
  //           `ownership[${index}].zip_code`,
  //           `ownership[${index}].ssn`,
  //           `ownership[${index}].date_of_birth`,
  //           `ownership[${index}].email`,
  //         ]),
  //       ]);
  //       createDraft({
  //         dontClose: true,
  //       });

  //       break;
  //     case MerchantFormState.Banking:
  //       onNext(MerchantFormState.Documents, [
  //         'routing_number',
  //         'account_number',
  //         'name_on_account',
  //         'bank_name',
  //       ]);

  //       break;
  //     case MerchantFormState.Documents:
  //       onNext(MerchantFormState.Review, []);
  //       break;

  //     case MerchantFormState.Review:
  //       createDraft();

  //     default:
  //       return;
  //   }
  // }

  // async function saveCache(args: { id: string }) {
  //   const cacheKey = `merchantForm_${args.id}`;
  //   // save
  //   await apolloClient.mutate({
  //     mutation: SaveCache,
  //     variables: {
  //       input: {
  //         keyword: cacheKey,
  //         data: JSON.stringify(values),
  //       },
  //     },
  //   });

  //   await apolloClient.mutate({
  //     mutation: updateGroup,
  //     variables: {
  //       where: {
  //         id: args.id,
  //       },
  //       data: {
  //         actualName: values.legal_business_name,
  //       },
  //     },
  //   });

  //   apolloClient.resetStore();
  // }

  // async function loadCache(args: { id: string }) {
  //   const cacheKey = `merchantForm_${args.id}`;
  //   // load
  //   const c = await apolloClient.query({
  //     query: GetCache,
  //     variables: {
  //       input: {
  //         keyword: cacheKey,
  //       },
  //     },
  //   });

  //   const data = c?.data?.cache_get?.data;
  //   if (data) {
  //     formState.reset(JSON.parse(data));
  //   }
  // }

  // useEffect(() => {
  //   if (currentID) {
  //     loadCache({ id: currentID });
  //   }
  // }, [currentID]);

  // async function createDraft(args?: { dontClose?: boolean }) {
  //   try {
  //     console.log('submitting');
  //     let ownerships = values.ownership.map((obj, index) => {
  //       return {
  //         [`owner${index + 1}`]: {
  //           ownership: Number(obj.ownership),
  //           firstName: obj.first_name,
  //           lastName: obj.last_name,
  //           title: obj.position,
  //           phoneNumber: obj.dial_code + removeSpaces(obj.phone_number),
  //           email: obj.email,
  //           ssn: obj.ssn.replace(/-/g, ''),
  //           dateOfBirth: obj.date_of_birth,
  //           address: {
  //             addressLine1: obj.home_address,
  //             addressLine2: obj.state,
  //             city: obj.city,
  //             regionCode: obj.state,
  //             postalCode: obj.zip_code,
  //             countryCode: 'US',
  //           },
  //         },
  //       };
  //     });
  //     // turn ownerships into object
  //     let ownershipsObj = ownerships.reduce((acc, obj) => {
  //       return { ...acc, ...obj };
  //     }, {});

  //     setIsSubmitting(true);

  //     if (!currentID) throw new Error('Group ID not found');

  //     const variables: any = {
  //       input: {
  //         groupId: currentID,
  //         data: {
  //           businessInformation: {
  //             mccCodeAliasId: values?.mccObject?.id,
  //             detailedExplanationWhatDoYouSell: values.description_of_what_you_sell,
  //             merchantType: 'new',
  //             dbaName: values.dba_name,
  //             legalName: values.legal_business_name,
  //             website: values.website_link,
  //             businessPhone: formatToUS(values.phone_number),
  //             customerServicePhone: formatToUS(values.phone_number2),
  //             businessEmail: values.business_email,
  //             isMerchantOptsOutReceivingFutureCommercialFromAmex: false,
  //             typeOfBusiness: values.type_of_business.mccCode,
  //             businessEstablishedDate: values.business_established_date,
  //             taxId: '*********',
  //             documentationMailingAddress: 'legalAddress',
  //             swiped: Number(values.swipe),
  //             keyed: Number(values.keyed),
  //             ecommerce: Number(values.ecommerce),
  //             desiredLimits: {
  //               monthlyVMcD: Number(values.vmd_gross_monthly_sales_volume),
  //               avgTicketVMcD: Number(values.vmd_average_transaction_amount),
  //               highTicketVMcD: Number(values.vmd_highest_transaction_amount),
  //               monthlyAmex: Number(values.amex_gross_monthly_sales_volume),
  //               avgTicketAmex: Number(values.amex_average_transaction_amount),
  //               highTicketAmex: Number(values.amex_highest_transaction_amount),
  //             },
  //             sellingOutsideOfUs: true,
  //             siteInspectionType: 'aurora',
  //             bankInfo: {
  //               routing: values.routing_number,
  //               bank: values.bank_name,
  //               account: values.account_number,
  //               accountName: values.name_on_account,
  //             },
  //           },
  //           locationAddress: {
  //             typeOfLocation: 'home',
  //             locationIfOther: '',
  //             addressLine1: values.street,
  //             addressLine2: values.state,
  //             city: values.city,
  //             regionCode: values.state,
  //             postalCode: values.zip_code,
  //             countryCode: 'US',
  //           },
  //           isLegalAddressTheSame: values.use_different_legal_email,
  //           legalAddress: values.use_different_legal_email
  //             ? {
  //                 addressLine1: values.diff_street,
  //                 addressLine2: values.state,
  //                 city: values.diff_city,
  //                 regionCode: values.state,
  //                 postalCode: values.zip_code,
  //                 countryCode: 'US',
  //               }
  //             : undefined,
  //           ...ownershipsObj,
  //           authorizedSigner: values.is_an_individual
  //             ? {
  //                 firstName: values?.ownership?.[0]?.first_name,
  //                 lastName: values?.ownership?.[0]?.last_name,
  //                 title: values?.ownership?.[0]?.position,
  //                 phoneNumber: formatToUS(values?.ownership?.[0]?.phone_number),
  //                 email: values?.ownership?.[0]?.email,
  //                 ssn: values?.ownership?.[0]?.ssn?.replace(/-/g, ''),
  //                 dateOfBirth: values?.ownership?.[0]?.date_of_birth,
  //                 address: {
  //                   addressLine1: values?.ownership?.[0]?.home_address,
  //                   addressLine2: values?.ownership?.[0]?.state,
  //                   city: values?.ownership?.[0]?.city,
  //                   regionCode: values?.ownership?.[0]?.state,
  //                   postalCode: values?.ownership?.[0]?.zip_code,
  //                   countryCode: 'US',
  //                 },
  //               }
  //             : undefined,
  //           isPrimaryContactTheSame: true,
  //           // primaryContact: {
  //           //   firstName: 'Brian',
  //           //   lastName: 'Harm',
  //           //   title: 'Founder',
  //           //   phoneNumber: '+17701234567',
  //           //   email: '<EMAIL>',
  //           // },
  //           achProcessor: 'achq',
  //           achProcessorData: {
  //             monthlyVolumeAch: 1,
  //             monthlyVolumeAchPayouts: 1,
  //             highTicketAch: 1,
  //             highTicketAchPayouts: 1,
  //             ticketCountAch: 1,
  //             ticketCountAchPayouts: 1,
  //             customerType: 'individuals',
  //             paymentAuthorizationType: 'signedContractOrAgreement',
  //             billingRouting: values.routing_number,
  //             billingBank: values.bank_name,
  //             billingAccount: values.account_number,
  //             billingAccountName: values.name_on_account,
  //             statementDescriptor: 'URL',
  //             useOfPaymentServiceDescription: 'URL',
  //           },
  //         },
  //       },
  //     };

  //     console.log(variables);
  //     const curGroupData = submitData?.groups?.[0];
  //     if (curGroupData?.mainProcessor === 'aur') {
  //       // update draft
  //       const response = await apolloClient.mutate({
  //         mutation: updateAuroraDraft,
  //         variables,
  //       });

  //       apolloClient.resetStore();

  //       console.log(response);
  //       console.log('Draft updated successfully');

  //       if (!args?.dontClose) {
  //         setOpen(false);
  //         router.push('/dashboard/accounts');
  //       }
  //     } else {
  //       // create draft
  //       const response = await apolloClient.mutate({
  //         mutation: createAuroraDraft,
  //         variables,
  //       });

  //       apolloClient.resetStore();

  //       console.log(response);
  //       console.log('Draft created successfully');

  //       if (!args?.dontClose) {
  //         setOpen(false);
  //         router.push('/dashboard/accounts');
  //       }
  //     }
  //   } catch (err) {
  //     toast({
  //       variant: 'destructive',
  //       title: 'Uh oh! Something went wrong.',
  //       description: 'There was a problem with your request.',
  //       action: <ToastAction altText="Try again">Try again</ToastAction>,
  //     });
  //     console.log(err);
  //   } finally {
  //     setIsSubmitting(false);
  //   }
  // }

  // return (
  //   <div className="relative flex h-fit max-h-[80vh] flex-col overflow-hidden">
  //     <div className="mb-4 mt-1 flex w-full flex-row items-center justify-center px-3"></div>
  //     <div className="flex flex-col px-3">
  //       <DialogTitle className="mb-10 text-xl font-bold text-gray-800 dark:text-white">
  //         {formSteps[selectedTab].label}
  //       </DialogTitle>
  //     </div>
  //     {false && (
  //       <div className="flex h-32 items-center justify-center">
  //         <LoaderSquares />
  //       </div>
  //     )}

  //     <Form {...formState}>
  //       <form
  //         className="scrollbar-medium overflow-y-auto px-4 pb-6"
  //         onSubmit={formState.handleSubmit(handleNext)}
  //       >
  //         <div>{renderForm()}</div>
  //       </form>
  //     </Form>
  //     <div className="bottom-0 left-0 flex w-full items-center justify-between rounded-b-lg px-6 py-4">
  //       <div>
  //         {formSteps[selectedTab].step !== MerchantFormState.BusinessInformation && (
  //           <Button type="submit" variant="primary" className="py-3" onClick={() => handlePrev()}>
  //             Previous
  //           </Button>
  //         )}
  //       </div>

  //       <Button
  //         type="submit"
  //         variant="primary"
  //         className="py-3"
  //         disabled={isSubmitting}
  //         onClick={() => handleNext()}
  //       >
  //         {selectedTab !== MerchantFormState.Review ? 'Next' : 'Save'} {isSubmitting && <Spinner />}
  //       </Button>
  //     </div>
  //   </div>
  // );

  return <></>;
};

export default MerchantForm;
