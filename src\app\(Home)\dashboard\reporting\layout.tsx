'use client';

import { TabLinks } from '@/components/globals';
import { ComingSoon } from '@/components/globals/coming-soon';
import { ReportingPages } from '@/components/globals/Headerv3';
import { env } from 'next-runtime-env';
import React from 'react';

export default function Page({ children }: { children: React.ReactNode }) {
  const isProd = env('NEXT_PUBLIC_COMING_SOON_TRANSACTIONS') === 'true';
  return (
    <>
      <TabLinks items={ReportingPages} />
      {isProd && <ComingSoon />}
      {!isProd && <div className="mx-4">{children}</div>}
    </>
  );
}
