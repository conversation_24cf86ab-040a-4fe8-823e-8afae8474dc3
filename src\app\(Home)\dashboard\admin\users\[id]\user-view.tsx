'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import {
  CreatGroupMemberData,
  DeleteGroupMembers,
  GetFlags,
  GetQuickInviteInfo,
  GetQuickUserInfo,
  GetUserMembershipInfo,
  UpdateGroupMemberData,
} from '@/graphql/declarations/user-members';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { Accordion, TextInput } from 'flowbite-react';
import { HelpCircle } from 'lucide-react';
import { useParams, useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';

export default function UserView() {
  const params = useParams<{ id: string }>();
  const queryParams = useSearchParams();

  let groupID = queryParams?.get('group');
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);

  useEffect(() => {
    if (!groupID) {
      return;
    }
    setSelectedGroup(groupID);
  }, [groupID]);

  const isInvite = params?.id === 'invite';

  const { data: groupList } = useQuery(GET_GROUPS_LIST, {
    variables: {
      where: {},
    },
  });

  const { data: userData } = useQuery(GetQuickUserInfo, {
    variables: {
      where: {
        id: params?.id,
      },
    },
    skip: !params?.id || isInvite,
  });

  let inviteID = queryParams?.get('invite');

  let isNewEntry = isInvite && !inviteID;

  const { data: membershipData } = useQuery(GetUserMembershipInfo, {
    variables: {
      where: {
        group: {
          id: {
            equals: selectedGroup,
          },
        },
        OR: [
          ...(params?.id ? [{ user: { id: { equals: params.id } } }] : []),
          ...(inviteID ? [{ invite: { id: { equals: inviteID } } }] : []),
        ],
      },
      take: 1,
    },
    skip: (!inviteID && !params?.id) || !selectedGroup,
  });

  const { data: inviteData } = useQuery(GetQuickInviteInfo, {
    variables: {
      where: {
        id: inviteID,
      },
    },
    skip: !inviteID || !isInvite,
  });

  const { data: fieldsList } = useQuery(GetFlags, {
    variables: { input: {} },
  });

  const [userDataForm, setUserDataForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    title: '',
  });

  const [userFlags, setUserFlags] = useState<string[]>([]);

  const loadUserInfo = async () => {
    if (userData) {
      setUserDataForm({
        firstName: userData.user?.name || '',
        lastName: userData.user?.lastName || '',
        email: userData.user?.email || '',
        phone: userData.user?.phone || '',
        title: userData.user?.title || '',
      });
    }
  };

  const loadInviteInfo = async () => {
    if (inviteData) {
      setUserDataForm({
        firstName: '',
        lastName: '',
        email: inviteData.groupMemberInvite?.email || '',
        phone: '',
        title: '',
      });
    }
  };

  useEffect(() => {
    if (isInvite) {
      loadInviteInfo();
    } else {
      loadUserInfo();
    }
  }, [userData, inviteData]);

  const [adminInfo, setAdminInfo] = useState(false);

  useEffect(() => {
    setUserFlags(
      membershipData?.groupMembers?.[0]?.flags?.filter((e) => e.flag).map((e) => e.flag!) || [],
    );
    setAdminInfo((membershipData?.groupMembers?.[0]?.access ?? 2) === 3);
  }, [membershipData]);

  function onlyUnique(value, index, array) {
    return array.indexOf(value) === index;
  }

  const categoriesList = useMemo(() => {
    return (
      fieldsList?.group_flags?.flags
        .filter((group) => group)
        .map((group) => group?.category)
        .filter(onlyUnique) ?? []
    );
  }, [fieldsList]);

  const onPermissionCreate = async (e) => {
    e?.preventDefault?.();

    if (!selectedGroup) {
      return;
    }

    if (!userDataForm.email) {
      toast.error('Please enter an email');
      return;
    }

    const newFlags: string[] = userFlags;

    // update the base permission of user
    const res = await apolloClient.mutate({
      mutation: CreatGroupMemberData,
      variables: {
        input: {
          email: userDataForm.email,
          groupID: selectedGroup,
          accessLevel: adminInfo ? 3 : 2,
          flags: newFlags ?? [],
        },
      },
    });

    // change query params invite
    if (res.data?.group_createInvite?.id) {
      // get current root url
      let root = window.location.protocol + '//' + window.location.host;
      const url = new URL(root + '/dashboard/admin/users/invite');
      // set url to /admin/users/invite?id=inviteID
      url.searchParams.set('invite', res.data?.group_createInvite?.id);
      // add group
      url.searchParams.set('group', selectedGroup);
      window.history.pushState({}, '', url.toString());
    }

    await apolloClient.resetStore();

    toast.success('User permissions created successfully');

    // update the flags of user
    // if new flag in on list, create it
    // if flag is deleted on new list, delete it
  };

  const [loading, setLoading] = useState(false);
  const onPermissionUpdate = async (e) => {
    setLoading(true);
    e?.preventDefault?.();

    // get the specific permission definition of user
    const userPermission = membershipData?.groupMembers?.[0];

    if (!userPermission) {
      return;
    }

    // @ts-ignore
    const originalFlags: string[] =
      userPermission?.flags?.map((e) => e.flag).filter((e) => !!e) || [];
    const newFlags: string[] = userFlags;

    const flagsToAdd = newFlags.filter((e) => !originalFlags.includes(e));
    const flagsToRemove = originalFlags.filter((e) => !newFlags.includes(e));

    const flagsToRemoveIDs = userPermission?.flags?.filter(
      (e) => e.flag && flagsToRemove.includes(e.flag),
    );

    // update the base permission of user
    await apolloClient.mutate({
      mutation: UpdateGroupMemberData,
      variables: {
        where: {
          id: userPermission.id,
        },
        updateBaseMemberData: {
          access: adminInfo ? 3 : 2,
        },
        deleteMemberFlags:
          flagsToRemoveIDs?.map((e) => ({
            id: e.id,
          })) ?? [],
        createMemberFlags: flagsToAdd.map((data) => ({
          flag: data,
          groupMember: {
            connect: {
              id: userPermission.id,
            },
          },
        })),
      },
    });

    await apolloClient.resetStore();
    setLoading(false);

    toast.success('User permissions updated successfully');

    // update the flags of user
    // if new flag in on list, create it
    // if flag is deleted on new list, delete it
  };

  const revokeAccess = async (e) => {
    e?.preventDefault?.();
    await apolloClient.mutate({
      mutation: DeleteGroupMembers,
      variables: {
        where: {
          id: membershipData?.groupMembers?.[0]?.id,
        },
      },
    });
    // return back to user list
    window.location.href = '/dashboard/admin/users';
  };

  const ToggleItem = (args: {
    id: string;
    label: string;
    description: string;
    checked: boolean;
    onCheckedChange: (checked: boolean) => void;
  }) => (
    <div className="flex items-center justify-between py-2">
      <div>
        <Label htmlFor={args.id} className="font-medium">
          {args.label}
        </Label>
        <p className="text-sm text-gray-500">{args.description}</p>
      </div>
      <Switch id={args.id} checked={args.checked} onCheckedChange={args.onCheckedChange} />
    </div>
  );

  return (
    <div className="container mx-auto rounded-lg bg-white p-6 shadow">
      <form>
        <section className="mb-8">
          <h2 className="mb-4 text-xl font-semibold">General Information</h2>
          {!isInvite && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <TextInput id="firstName" value={userDataForm.firstName || '--'} disabled />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <TextInput id="lastName" value={userDataForm.lastName || '--'} disabled />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <TextInput id="email" value={userDataForm.email || '--'} disabled />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <TextInput id="phone" value={userDataForm.phone || '--'} disabled />
              </div>
              <div className="col-span-2">
                <Label htmlFor="jobTitle">Job Title</Label>
                <TextInput id="jobTitle" value={userDataForm.title || '--'} disabled />
              </div>
            </div>
          )}
          {isInvite && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <TextInput
                  id="email"
                  value={userDataForm.email}
                  onChange={(e) => setUserDataForm({ ...userDataForm, email: e.target.value })}
                  disabled={isInvite && !isNewEntry}
                />
              </div>
            </div>
          )}
        </section>
        <section className="mb-8">
          <h2 className="mb-4 text-xl font-semibold">Location Permission</h2>
          <p className="mb-2 text-sm text-gray-500">Only one location can be set per submission</p>
          <div className="mb-4 flex items-center gap-4">
            <div className="flex-grow">
              <Label htmlFor="location" className="flex items-center gap-2">
                Location <HelpCircle size={16} className="text-gray-400" />{' '}
                {JSON.stringify(selectedGroup)}
              </Label>
              <div className="relative mt-3">
                <Select
                  disabled={isInvite && !isNewEntry}
                  onValueChange={(value) => {
                    if (!value) {
                      return;
                    }
                    // update query params of group
                    const url = new URL(location.href);
                    url.searchParams.set('group', value);
                    window.history.pushState({}, '', url.toString());
                    setSelectedGroup(value);
                  }}
                  value={selectedGroup ?? ''}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select A Location" />
                  </SelectTrigger>
                  <SelectContent>
                    {groupList?.groups?.map((group) => (
                      <SelectItem key={group.id} value={group.id}>
                        {group.labelName ? `${group.labelName} (${group.name})` : group.name}
                      </SelectItem>
                    )) ?? []}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="mt-5 flex items-center gap-2">
              <Switch
                id="locationAdmin"
                checked={adminInfo}
                onCheckedChange={(checked) => setAdminInfo(checked)}
              />
              <Label htmlFor="locationAdmin">
                <div>Location Admin</div>
                <p className="text-xs text-gray-500">Allow user to manage location permissions</p>
              </Label>
            </div>
          </div>
        </section>

        {!isNewEntry && (
          <Button onClick={revokeAccess} className="my-6 ml-auto" variant={'destructive'}>
            {isInvite ? 'Revoke Invite' : 'Revoke Access'}
          </Button>
        )}

        <Accordion className="mb-8">
          {categoriesList.map((category, i) => (
            <Accordion.Panel key={category || i}>
              <Accordion.Title>{category}</Accordion.Title>
              <Accordion.Content>
                <div className="mb-2 flex justify-end">
                  <Button
                    variant="link"
                    className="text-blue-600"
                    onClick={(e) => {
                      e.preventDefault();
                      const fieldsToToggle =
                        fieldsList?.group_flags?.flags.filter(
                          (group) => group?.category === category,
                        ) ?? [];

                      // If all flags are already toggled, untoggle them

                      if (fieldsToToggle.every((flag) => userFlags.includes(flag?.key || ''))) {
                        let newTags = [...userFlags];
                        fieldsToToggle.forEach((flag) => {
                          newTags = newTags.filter((e) => e !== flag?.key);
                        });
                        setUserFlags(newTags);
                        return;
                      } else {
                        let newTags = [...userFlags];
                        fieldsToToggle.forEach((flag) => {
                          if (!newTags.includes(flag?.key || '')) {
                            newTags.push(flag?.key || '');
                          }
                        });
                        setUserFlags(newTags);
                      }
                    }}
                  >
                    Toggle all
                  </Button>
                </div>
                {fieldsList?.group_flags?.flags
                  .filter((group) => group?.category === category)
                  .map((flag) => (
                    <ToggleItem
                      key={flag?.key}
                      id={flag?.key || ''}
                      label={flag?.name || ''}
                      description={flag?.description || ''}
                      checked={userFlags.includes(flag?.key || '')}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setUserFlags([...userFlags, flag?.key || '']);
                        } else {
                          setUserFlags(userFlags.filter((e) => e !== flag?.key));
                        }
                      }}
                    />
                  ))}
              </Accordion.Content>
            </Accordion.Panel>
          )) ?? []}
        </Accordion>

        <div className="flex justify-start gap-4">
          {(membershipData?.groupMembers?.length ?? 0) === 0 && (
            <Button type="submit" className="bg-blue-600 text-white" onClick={onPermissionCreate}>
              Invite User
            </Button>
          )}
          {(membershipData?.groupMembers?.length ?? 0) > 0 && (
            <Button
              type="submit"
              className="bg-blue-600 text-white"
              disabled={loading}
              onClick={onPermissionUpdate}
            >
              {loading ? 'Updating...' : 'Update Permissions'}
            </Button>
          )}
          <Button type="button" variant="outline" onClick={() => window.history.back()}>
            Close
          </Button>
          {(membershipData?.groupMembers ?? []).length > 0 && (
            <Button className="ml-auto" type="button" variant="destructive">
              Revoke All Access
            </Button>
          )}
        </div>
      </form>
    </div>
  );
}
