import { useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'flowbite-react';
import { CategoryForm } from './category-form';
import { useMutation } from '@apollo/client';
import {
  Gateway_CreateCategoryDocument,
  Gateway_CreateCategoryInputDataStatus,
} from '@/graphql/generated/graphql';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import { CategoryFormData, categoryFormDataDefault } from '../utils';
import { useForm } from 'react-hook-form';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { FaPlus } from 'react-icons/fa';
import { capitalizeFirstLetter } from '@/components/shared/utils/strings';

type CategoryAddProps = {
  refetchListPage: () => void;
  groupID: string;
};

export const CategoryAdd = ({ refetchListPage, groupID }: CategoryAddProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const methods = useForm<CategoryFormData>({
    defaultValues: { ...categoryFormDataDefault },
  });

  const [createCategoyMutation, { loading: createCategoyLoading }] = useMutation(
    Gateway_CreateCategoryDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Category'));
        methods.reset({ ...categoryFormDataDefault });
        setIsModalOpen(false);
        refetchListPage();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Category', error.message));
      },
    },
  );

  const onSubmitForm = async (data: CategoryFormData) => {
    try {
      await createCategoyMutation({
        variables: {
          input: {
            groupID,
            data: {
              name: capitalizeFirstLetter(data.name),
              description: capitalizeFirstLetter(data.description),
              subCategory: data.subCategories,
              status: Gateway_CreateCategoryInputDataStatus.Active,
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  return (
    <>
      <Button className="mt-[20px] h-[38px] p-0" color="blue" onClick={() => setIsModalOpen(true)}>
        <div className="flex items-center gap-x-3">
          <FaPlus className="text-xl" />
          <span>Add Categories</span>
        </div>
      </Button>
      <Modal show={isModalOpen} onClose={() => setIsModalOpen(false)} size="4xl">
        <Modal.Header className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-blue-600">Add Category</h3>
        </Modal.Header>
        <Modal.Body>
          <SpinnerLoading isLoading={createCategoyLoading} />
          <CategoryForm methods={methods} onSubmit={onSubmitForm} />
        </Modal.Body>
      </Modal>
    </>
  );
};
