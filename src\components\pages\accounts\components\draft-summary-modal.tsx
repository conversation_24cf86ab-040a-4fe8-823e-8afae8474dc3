import { Modal } from 'flowbite-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { HiX } from 'react-icons/hi';
import { Calendar, Mail, Phone, Building2, CircleDollarSign, Users, Building } from 'lucide-react';

interface DraftSummaryModalProps {
  isOpen: boolean;
  onClose: () => void;
  draftData: any; // Replace with proper type
  onContinueEditing: () => void;
}

export function DraftSummaryModal({
  isOpen,
  onClose,
  draftData,
  onContinueEditing,
}: DraftSummaryModalProps) {
  if (!draftData) return null;

  const InfoItem = ({ icon: Icon, label, value }) => (
    <div className="flex items-center space-x-2">
      <Icon className="h-4 w-4 text-muted-foreground" />
      <div>
        <div className="text-sm font-medium text-muted-foreground">{label}</div>
        <div className="font-medium">{value || '-'}</div>
      </div>
    </div>
  );

  return (
    <Modal show={isOpen} onClose={onClose} size="2xl">
      <div className="flex items-center justify-between p-4">
        <h2 className="text-2xl font-bold">Draft Account Summary</h2>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <HiX className="h-5 w-5" />
        </Button>
      </div>

      <Modal.Body>
        <div className="space-y-6">
          {/* Business Information */}
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Business Information</h3>
            <div className="grid gap-4 md:grid-cols-2">
              <InfoItem
                icon={Building2}
                label="Legal Business Name"
                value={draftData?.businessInfo?.legalBusinessName}
              />
              <InfoItem
                icon={Building2}
                label="DBA Name"
                value={draftData?.businessInfo?.dbaName}
              />
              <InfoItem
                icon={Mail}
                label="Business Email"
                value={draftData?.businessInfo?.businessEmail}
              />
              <InfoItem
                icon={Phone}
                label="Business Phone"
                value={draftData?.businessInfo?.businessPhone}
              />
              <InfoItem
                icon={Calendar}
                label="Date Established"
                value={draftData?.businessInfo?.dateBusinessEstablished}
              />
            </div>
          </Card>

          {/* Transaction Information */}
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Transaction Information</h3>
            <div className="grid gap-4 md:grid-cols-2">
              <InfoItem
                icon={CircleDollarSign}
                label="Average Transaction"
                value={draftData?.transactionInfo?.avgTransactionAmount}
              />
              <InfoItem
                icon={CircleDollarSign}
                label="Monthly Volume"
                value={draftData?.transactionInfo?.grossMonthlySalesVolume}
              />
            </div>
          </Card>

          {/* Bank Information */}
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Bank Information</h3>
            <div className="grid gap-4 md:grid-cols-2">
              <InfoItem icon={Building} label="Bank Name" value={draftData?.bankInfo?.bankName} />
              <InfoItem
                icon={Building}
                label="Name on Account"
                value={draftData?.bankInfo?.nameOnAccount}
              />
            </div>
          </Card>

          {/* Ownership Information */}
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Ownership Information</h3>
            <div className="space-y-4">
              {draftData?.owners?.map((owner, index) => (
                <div key={index} className="border-t pt-4 first:border-t-0 first:pt-0">
                  <div className="grid gap-4 md:grid-cols-2">
                    <InfoItem
                      icon={Users}
                      label="Owner Name"
                      value={`${owner.firstName} ${owner.lastName}`}
                    />
                    <InfoItem icon={Users} label="Ownership %" value={owner.ownershipPercentage} />
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button variant="primary" onClick={onContinueEditing}>
            Continue Editing
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
}
