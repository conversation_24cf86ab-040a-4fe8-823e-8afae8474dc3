import { <PERSON><PERSON><PERSON>er, TopComponent, Status<PERSON>hip, StatusFilter, Variant } from '@/components/globals';

import { useMemo, useState } from 'react';
import moment from 'moment';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import useDebounce from '@/components/hooks/useDebounce';
import { useSearchParams } from 'next/navigation';
import DisputeDetailsModal from '../components/dispute-details-modal';
import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { moneyFormat } from '@/lib/utils';
import { hashCustomerData } from '@/lib/hashUtils';
import { mockDisputes } from '@/mock/disputes-data';

//NOTE: need to apple this to other parts. TO USE THE ACTUAL ENUM FROM BE
export const getGatewayUniDisputeOutputStatusChip = (
  status: string | null | undefined,
): [Variant, string] => {
  const statusMap: Record<string, Variant> = {
    won: 'success',
    lost: 'danger',
    challenge: 'warning',
  };

  const variant = statusMap[status?.toLowerCase() ?? ''] || 'neutral';
  const label = status ? status.toUpperCase() : '';

  return [variant, label];
};

export const DisputesTab = () => {
  const queryParams = useSearchParams();
  const caseID = queryParams?.get('id');
  const [statusFilter, setStatusFilter] = useState('All');
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);
  const [selectedCaseID, setSelectedCaseID] = useState(caseID ?? null);

  const { locationSelectorElement } = useLocationSelector({
    onlyActive: true,
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const rows = useMemo(() => {
    let filteredData = mockDisputes;

    // Apply status filter if not showing all
    if (statusFilter !== 'All') {
      filteredData = filteredData.filter(
        (row) => row.status?.toLowerCase() === statusFilter.toLowerCase(),
      );
    }

    // Apply search filter if we have a search term
    if (debouncedSearchQuery) {
      const searchLower = debouncedSearchQuery.toLowerCase();
      filteredData = filteredData.filter(
        (row) =>
          row.caseID?.toLowerCase().includes(searchLower) ||
          row.customer?.toLowerCase().includes(searchLower) ||
          row.transactionID?.toLowerCase().includes(searchLower) ||
          row.reason?.toLowerCase().includes(searchLower),
      );
    }

    return filteredData;
  }, [statusFilter, debouncedSearchQuery]);

  const fetchExportData = async () => {
    return mockDisputes;
  };

  const columns: Column[] = [
    {
      key: 'caseID',
      header: 'Case #',
      sortable: true,
      width: '120px',
      onClick: (row) => setSelectedCaseID(row.caseID),
    },
    {
      key: 'customer',
      header: 'Customer',
      sortable: true,
      renderCell: (row) => <>{hashCustomerData.name(row.customer) ?? '--'}</>,
    },
    {
      key: 'date',
      header: 'Date',
      sortable: true,
      width: '80px',
      valueGetter: (row) => moment(row?.date).format('MM/DD/YYYY'),
    },
    {
      key: 'reason',
      header: 'Reason',
      sortable: true,
      width: '120px',
    },
    {
      key: 'amount',
      header: 'Amount',
      sortable: true,
      renderCell: (row) => <span>{moneyFormat(row.amount)}</span>,
    },
    {
      key: 'transactionID',
      header: 'Transaction #',
      sortable: true,
      width: '120px',
    },
    {
      key: 'hasChallenged',
      header: 'Challenged',
      sortable: true,
      width: '120px',
      renderCell: (row) => <p>{row.hasChallenged ? 'Yes' : 'No'}</p>,
    },
    {
      key: 'daysToRepresent',
      header: 'Rem. Days',
      sortable: true,
      width: '120px',
      renderCell: (row) => <p>{row.daysToRepresent}</p>,
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getGatewayUniDisputeOutputStatusChip(row.status);
        return <StatusChip variant={status} label={label} />;
      },
    },
  ];

  return (
    <>
      <StaticInfoBox />
      <PageHeader text="Disputes" />
      <div className="mt-2 flex justify-between border-b border-gray-300">
        <div className="w-1/4">{locationSelectorElement}</div>
        <TopComponent value={searchValue} setValue={setSearchValue}>
          <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="disputes" />
        </TopComponent>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={rows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={false}
          actionComponent={
            <StatusFilter
              value={statusFilter}
              setValue={setStatusFilter}
              statusList={['All', 'won', 'lost', 'challenge']}
            />
          }
          mode="client"
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          totalRecords={rows.length}
          onRowClick={(row) => setSelectedCaseID(row.caseID ?? null)}
        />
        <DisputeDetailsModal
          isOpen={selectedCaseID !== null}
          onClose={() => setSelectedCaseID(null)}
          queryData={{
            caseID: selectedCaseID ?? '',
            groupID: '',
          }}
        />
      </div>
    </>
  );
};
