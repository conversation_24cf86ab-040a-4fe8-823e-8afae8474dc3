'use client';

import { TabLinks } from '@/components/globals';
import { AffiliatePages } from '@/components/globals/Headerv3';
import { Affiliation_get } from '@/graphql/declarations/affiliate';
import { useQuery } from '@apollo/client';
import { usePathname } from 'next/navigation';
import React, { Suspense } from 'react';
import AffiliateSetupPage from './setup';
import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { Me } from '@/graphql/declarations/me';

export default function Page({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  const { data: me, loading: meLoading } = useQuery(Me);

  const { data: affiliateget, loading: affiliateLoading } = useQuery(Affiliation_get, {
    variables: {},
  });

  if (affiliateLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <LoaderSquares />
      </div>
    );
  }

  if (!meLoading && !me?.authenticatedItem?.flag_canAffiliate) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold">You are not authorized to view this page</h1>
          <p className="text-gray-500">Please contact the administrator for more information</p>
        </div>
      </div>
    );
  }

  if (!affiliateget?.affiliation_get?.id) {
    return (
      <Suspense>
        <AffiliateSetupPage />
      </Suspense>
    );
  }

  return (
    <>
      <TabLinks items={AffiliatePages} />
      <div className="mx-4">{children}</div>
    </>
  );
}
