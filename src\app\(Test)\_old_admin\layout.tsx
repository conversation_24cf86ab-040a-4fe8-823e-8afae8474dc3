'use client';

import { SortableTableV2 } from '@/components/globals';
import { SimpleTabs } from '@/components/globals/simple-tab';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Script from 'next/script';
import React from 'react';

let pages = [
  { id: '/dashboard/admin', title: 'Locations' },
  { id: '/dashboard/admin/users', title: 'Users' },
  { id: '/dashboard/admin/payments', title: 'Online Payments' },
  { id: '/dashboard/admin/batching', title: 'Batching' },
  { id: '/dashboard/admin/security', title: 'Security' },
  { id: '/dashboard/admin/receipts', title: 'Receipts' },
];

export default function Page({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  return (
    <>
      <div className="container mx-auto mb-4 mt-8 w-full border-b border-gray-200 dark:border-gray-700">
        <ul
          className="-mb-px flex flex-wrap text-center text-sm font-medium"
          id="default-styled-tab"
          role="tablist"
        >
          {pages.map((page) => (
            <li className="me-2" role="presentation" key={page.id}>
              <Link
                href={`${page.id}`}
                className={cn(
                  'inline-block rounded-t-lg border-b-2 p-4',
                  pathname === page.id
                    ? 'border-primary-600 text-primary-600'
                    : 'border-transparent text-gray-500 dark:text-gray-400',
                )}
                id={`${page.id}-styled-tab`}
                type="button"
                role="tab"
                aria-controls={page.id}
                aria-selected="false"
              >
                {page.title}
              </Link>
            </li>
          ))}
        </ul>
      </div>
      <div className="mx-4">{children}</div>
    </>
  );
}
