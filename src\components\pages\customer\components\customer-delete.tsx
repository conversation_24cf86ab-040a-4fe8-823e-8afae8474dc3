// import { ConfirmDialog } from '@/components/globals';
// import { message } from '@/components/shared/utils';
// import { Gateway_DeleteCustomerDocument } from '@/graphql/generated/graphql';
// import { useMutation } from '@apollo/client';
// import { Button } from 'flowbite-react';
// import { Dispatch, SetStateAction, useEffect, useState } from 'react';
// import { FaTrashAlt } from 'react-icons/fa';
// import { toast } from 'react-toastify';
import { Dispatch, SetStateAction } from 'react';

type CustomerDeleteProps = {
  onClose: () => void;
  queryData: {
    customerID: string;
    groupID: string;
  };
  setParentLoading: Dispatch<SetStateAction<boolean>>;
};

// export const CustomerDelete = ({ queryData, onClose, setParentLoading }: CustomerDeleteProps) => {
//   const { customerID, groupID } = queryData;
//   const [delateConfirmDialog, setDelateConfirmDialog] = useState(false);
//   const [deleteCustomerMutation, { loading: deleteCustomerLoading }] = useMutation(
//     Gateway_DeleteCustomerDocument,
//     {
//       onCompleted: (_) => {
//         toast.success(message.api.successDelete('Customer'));
//         onClose();
//       },
//       onError: (error) => {
//         toast.error(message.api.errorDelete('Customer', error.message));
//       },
//     },
//   );
//   const handleDeleteCustomer = async () => {
//     try {
//       setDelateConfirmDialog(false);
//       await deleteCustomerMutation({
//         variables: {
//           input: {
//             groupID,
//             data: {
//               customerID,
//             },
//           },
//         },
//       });
//     } catch (e) {
//       console.error('Delete Customer Mutation error: ', e);
//     }
//   };

//   useEffect(() => {
//     setParentLoading(deleteCustomerLoading);
//   }, [deleteCustomerLoading]);

//   return (
//     <div>
//       <Button
//         className="bg-red-500 text-white hover:bg-red-700"
//         onClick={() => {
//           setDelateConfirmDialog(true);
//         }}
//       >
//         Delete Customer
//       </Button>
//       <ConfirmDialog
//         header={<FaTrashAlt color="gray" className="w-full pl-[10px] text-center" />}
//         body={
//           <div className="flex w-full items-center justify-center">
//             <h3 className="mb-5 text-center text-lg font-normal text-gray-500 dark:text-gray-400">
//               Deleting this customer will remove all stored payment information, payment plans,
//               scheduled payments, and payment cards from all locations.
//             </h3>
//           </div>
//         }
//         actions={
//           <div className="flex w-full items-center justify-center gap-4">
//             <Button color="gray" onClick={() => setDelateConfirmDialog(false)}>
//               No, cancel
//             </Button>
//             <Button color="failure" onClick={handleDeleteCustomer}>
//               Yes, I'm sure
//             </Button>
//           </div>
//         }
//         open={delateConfirmDialog}
//         setOpen={setDelateConfirmDialog}
//       />
//     </div>
//   );

// Temporarily disabled customer functionality
export const CustomerDelete = () => {
  return null;
};
