query Gateway_batches($input: Gateway_batchesInput!) {
  gateway_batches(input: $input) {
    data {
      batchID
      location
      date
      amount
      status
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}

query Gateway_batch($input: Gateway_batchInput!) {
  gateway_batch(input: $input) {
    batchID
    status
    date
    location
    locationID
    amount
    transactions {
      transactionID
      date
      method
      name
      last4
      customer
      amount
      brand
      status
    }
  }
}
