import { CustomerFormData, UpdateFormData, CardFormData } from './types';

// export const customerFormDefaults: CustomerFormData = {
//   customerID: '',
//   nameOnCard: '',
//   billingAddress: '',
//   email: '',
//   city: '',
//   phoneNumber: '',
//   state: '',
//   zipCode: '',
//   country: '',
//   // cardNumber: '',
//   cardToken: '',
//   cvc: '',
//   expiryDate: '',
//   processType: 'gpecomm',
//   accountNumber: '',
//   accountType: '',
//   holderType: '',
//   routingNumber: '',
// };
// Temporarily disabled customer functionality
export const customerFormDefaults: CustomerFormData = {} as CustomerFormData;

// Remove duplicate declarations and use comprehensive definitions
export const cardFormDefaults: CardFormData = {
  cardNumber: '',
  cvc: '',
  expiryDate: '',
  processType: 'gpecomm',
  accountNumber: '',
  accountType: '',
  holderType: '',
  routingNumber: '',
};

export const updateFormDefaults: UpdateFormData = {
  nameOnCard: '',
  billingAddress: '',
  email: '',
  city: '',
  phone: '',
  state: '',
  zipCode: '',
  country: '',
};
