'use client';

import { Label } from '@/components/ui/label';
import { useState } from 'react';
import { MerchantForm } from '../types/merchart-schema';
import moment from 'moment';
const Review = ({ formState }: { formState: MerchantForm }) => {
  const [checkingZipCode, setCheckingZipCode] = useState(false);

  const { setValue, register, watch, formState: state, setError, trigger } = formState;
  const values = watch();

  const { errors } = state;

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h3 className="mb-2 w-full text-lg font-bold text-[#495057]">Business Information</h3>
        <div className="flex w-full flex-col gap-3">
          <div className="flex flex-1 flex-col gap-2">
            <Label className="text-base font-bold text-zinc-500">Legal Business Name</Label>
            <p className="text-zinc-500">{values.legal_business_name}</p>
          </div>
          <div className="flex flex-1 flex-col gap-2">
            <Label className="text-base font-bold text-zinc-500">Type of Business</Label>
            <p className="text-zinc-500">{values.type_of_business.description}</p>
          </div>
          <div className="flex flex-1 flex-col gap-2">
            <Label className="text-base font-bold text-zinc-500">DBA Name</Label>
            <p className="text-zinc-500">{values.dba_name}</p>
          </div>
        </div>
        <div className="flex w-full flex-col gap-3">
          <div className="flex flex-1 flex-col gap-2">
            <Label className="text-base font-bold text-zinc-500">EIN</Label>
            <p className="text-zinc-500">{values.ein}</p>
          </div>
          <div className="flex flex-1 flex-col gap-2">
            <Label className="text-base font-bold text-zinc-500">Business Established Date</Label>
            <p className="text-zinc-500">
              {moment(values.business_established_date).format('MMMM DD, YYYY')}
            </p>
          </div>
          <div className="flex flex-1 flex-col gap-2">
            <Label className="text-base font-bold text-zinc-500">Website Link</Label>
            <p className="text-zinc-500">{values.website_link}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Review;
