// import { graphql } from '../generated';

// export const GHLDataTransactions = graphql(`
//   query Ghl_data_getGHLTransactionList($input: Ghl_data_getGHLTransactionListInput!) {
//     ghl_data_getGHLTransactionList(input: $input) {
//       data {
//         _id
//         status
//         markAsTest
//         isParent
//         amountRefunded
//         altType
//         altId
//         contactId
//         contactSnapshot {
//           id
//           country
//           city
//           source
//           type
//           locationId
//           validEmailDate
//           lastNameLowerCase
//           emailLowerCase
//           firstName
//           email
//           validEmail
//           address1
//           fullNameLowerCase
//           lastName
//           firstNameLowerCase
//           createdBy {
//             sourceId
//             channel
//             source
//             timestamp
//           }
//           dateAdded
//           deleted
//           postalCode
//           unsubscribeEmail
//           phone
//           state
//           dateUpdated
//         }
//         currency
//         amount
//         liveMode
//         entityType
//         entityId
//         entitySource {
//           type
//           subType
//           id
//           name
//         }
//         subscriptionId
//         paymentProvider {
//           type
//           connectedAccount {
//             accountId
//             name
//             liveMode
//             providerId
//           }
//           customProvider
//         }
//         meta {
//           sessionType
//           message
//         }
//         createdAt
//         updatedAt
//         chargeId
//         receiptId
//       }
//       totalCount
//     }
//   }
// `);

// export const GHLDataGetTransaction = graphql(`
//   query Ghl_data_getGHLTransactionByID($input: Ghl_data_getGHLTransactionByIDInput!) {
//     ghl_data_getGHLTransactionByID(input: $input) {
//       _id
//       status
//       markAsTest
//       isParent
//       amountRefunded
//       altType
//       altId
//       contactId
//       contactSnapshot {
//         id
//         country
//         city
//         source
//         type
//         locationId
//         validEmailDate
//         lastNameLowerCase
//         emailLowerCase
//         firstName
//         email
//         validEmail
//         address1
//         fullNameLowerCase
//         lastName
//         firstNameLowerCase
//         createdBy {
//           sourceId
//           channel
//           source
//           timestamp
//         }
//         dateAdded
//         deleted
//         postalCode
//         unsubscribeEmail
//         phone
//         state
//         dateUpdated
//       }
//       currency
//       amount
//       liveMode
//       entityType
//       entityId
//       entitySource {
//         type
//         subType
//         id
//         name
//       }
//       subscriptionId
//       paymentProvider {
//         type
//         connectedAccount {
//           accountId
//           name
//           liveMode
//           providerId
//         }
//         customProvider
//       }
//       meta {
//         sessionType
//         message
//       }
//       createdAt
//       updatedAt
//       chargeId
//       receiptId
//     }
//   }
// `);

// export const GHLDataSubscriptions = graphql(`
//   query Ghl_data_getGHLSubscriptionList($input: Ghl_data_getGHLSubscriptionListInput!) {
//     ghl_data_getGHLSubscriptionList(input: $input) {
//       data {
//         _id
//         markAsTest
//         altType
//         altId
//         contactId
//         contactSnapshot {
//           id
//           country
//           city
//           source
//           type
//           locationId
//           validEmailDate
//           lastNameLowerCase
//           emailLowerCase
//           firstName
//           email
//           validEmail
//           address1
//           fullNameLowerCase
//           lastName
//           firstNameLowerCase
//           createdBy {
//             sourceId
//             channel
//             source
//             timestamp
//           }
//           dateAdded
//           deleted
//           postalCode
//           unsubscribeEmail
//           phone
//           state
//           dateUpdated
//         }
//         currency
//         amount
//         status
//         liveMode
//         entityType
//         entityId
//         entitySource {
//           type
//           subType
//           id
//           name
//         }
//         subscriptionId
//         recurringProduct {
//           product {
//             _id
//             type
//             parentId
//             archived
//             deleted
//             image
//             isTaxesEnabled
//             isLabelEnabled
//             locationId
//             name
//             productType
//             description
//             availableInStore
//             seo {
//               title
//               description
//             }
//             userId
//             createdAt
//             updatedAt
//           }
//           _id
//           name
//           price {
//             _id
//             deleted
//             trackInventory
//             name
//             type
//             currency
//             amount
//             compareAtPrice
//             locationId
//             userId
//             createdAt
//             updatedAt
//             product
//             recurring {
//               interval
//               intervalCount
//             }
//             setupFee
//             trialPeriod
//             totalCycles
//           }
//           qty
//         }
//         paymentProvider {
//           type
//           connectedAccount {
//             accountId
//             name
//             liveMode
//             providerId
//           }
//         }
//         coupon {
//           _id
//           usageCount
//           hasAffiliateCoupon
//           deleted
//           limitPerCustomer
//           altId
//           altType
//           name
//           code
//           discountType
//           discountValue
//           userId
//           status
//           startDate
//           applyToFuturePayments
//           createdAt
//           updatedAt
//           couponSessionId
//         }
//         createdAt
//         updatedAt
//         traceId
//       }
//       totalCount
//     }
//   }
// `);

// export const GHLDataGetSubscription = graphql(`
//   query Ghl_data_getGHLSubscriptionByID($input: Ghl_data_getGHLSubscriptionByIDInput!) {
//     ghl_data_getGHLSubscriptionByID(input: $input) {
//       _id
//       markAsTest
//       altType
//       altId
//       contactId
//       contactName
//       contactEmail
//       contactPhone
//       contactSnapshot {
//         id
//         country
//         city
//         source
//         type
//         locationId
//         validEmailDate
//         lastNameLowerCase
//         emailLowerCase
//         firstName
//         email
//         validEmail
//         address1
//         fullNameLowerCase
//         lastName
//         firstNameLowerCase
//         createdBy {
//           sourceId
//           channel
//           source
//           timestamp
//         }
//         dateAdded
//         deleted
//         postalCode
//         unsubscribeEmail
//         phone
//         state
//         dateUpdated
//       }
//       currency
//       amount
//       status
//       liveMode
//       entityType
//       entityId
//       entitySource {
//         type
//         subType
//         id
//         name
//       }
//       entitySourceType
//       entitySourceSubType
//       entitySourceName
//       entitySourceId
//       subscriptionId
//       recurringProduct {
//         product {
//           _id
//           type
//           parentId
//           archived
//           deleted
//           image
//           isTaxesEnabled
//           isLabelEnabled
//           locationId
//           name
//           productType
//           description
//           availableInStore
//           seo {
//             title
//             description
//           }
//           userId
//           createdAt
//           updatedAt
//         }
//         _id
//         name
//         price {
//           _id
//           deleted
//           trackInventory
//           name
//           type
//           currency
//           amount
//           compareAtPrice
//           locationId
//           userId
//           createdAt
//           updatedAt
//           product
//           recurring {
//             interval
//             intervalCount
//           }
//           setupFee
//           trialPeriod
//           totalCycles
//         }
//         qty
//       }
//       paymentProvider {
//         type
//         connectedAccount {
//           accountId
//           name
//           liveMode
//           providerId
//         }
//       }
//       paymentProviderType
//       customProvider {
//         _id
//         deleted
//         locationId
//         marketplaceAppId
//         name
//         description
//         imageUrl
//         queryUrl
//         paymentsUrl
//         providerConfig {
//           live {
//             liveMode
//             apiKey
//             publishableKey
//           }
//           test {
//             liveMode
//             apiKey
//             publishableKey
//           }
//         }
//         createdAt
//         updatedAt
//       }
//       paymentProviderConnectedAccount
//       coupon {
//         _id
//         usageCount
//         hasAffiliateCoupon
//         deleted
//         limitPerCustomer
//         altId
//         altType
//         name
//         code
//         discountType
//         discountValue
//         userId
//         status
//         startDate
//         applyToFuturePayments
//         createdAt
//         updatedAt
//         couponSessionId
//       }
//       createdAt
//       updatedAt
//       traceId
//     }
//   }
// `);

// export const GHLDataOrders = graphql(`
//   query Ghl_data_getGHLOrderList($input: Ghl_data_getGHLOrderListInput!) {
//     ghl_data_getGHLOrderList(input: $input) {
//       data {
//         _id
//         status
//         paymentStatus
//         items {
//           product {
//             _id
//             type
//             parentId
//             archived
//             deleted
//             image
//             isTaxesEnabled
//             isLabelEnabled
//             locationId
//             name
//             productType
//             description
//             availableInStore
//             seo {
//               title
//               description
//             }
//             userId
//             createdAt
//             updatedAt
//           }
//           _id
//           name
//           price {
//             _id
//             deleted
//             trackInventory
//             name
//             type
//             currency
//             amount
//             compareAtPrice
//             locationId
//             userId
//             createdAt
//             updatedAt
//             product
//             recurring {
//               interval
//               intervalCount
//             }
//             setupFee
//             trialPeriod
//             totalCycles
//           }
//           qty
//           unitDiscount
//           unitDiscountWithoutSetupFee
//         }
//         markAsTest
//         fulfillmentStatus
//         automaticTaxesCalculated
//         altType
//         altId
//         contactId
//         contactSnapshot {
//           id
//           country
//           city
//           source
//           type
//           locationId
//           validEmailDate
//           lastNameLowerCase
//           emailLowerCase
//           firstName
//           email
//           validEmail
//           address1
//           fullNameLowerCase
//           lastName
//           firstNameLowerCase
//           createdBy {
//             sourceId
//             channel
//             source
//             timestamp
//           }
//           dateAdded
//           deleted
//           postalCode
//           unsubscribeEmail
//           phone
//           state
//           dateUpdated
//         }
//         currency
//         amount
//         liveMode
//         amountSummary {
//           subtotal
//           discount
//           tax
//           shipping
//         }
//         source {
//           type
//           subType
//           id
//           name
//         }
//         coupon {
//           _id
//           usageCount
//           hasAffiliateCoupon
//           deleted
//           limitPerCustomer
//           altId
//           altType
//           name
//           code
//           discountType
//           discountValue
//           userId
//           status
//           startDate
//           applyToFuturePayments
//           createdAt
//           updatedAt
//           couponSessionId
//         }
//         fingerprint
//         trackingId
//         traceId
//         createdAt
//         updatedAt
//       }
//       totalCount
//     }
//   }
// `);

// export const GHLDataGetOrder = graphql(`
//   query Ghl_data_getGHLOrderByID($input: Ghl_data_getGHLOrderByIDInput!) {
//     ghl_data_getGHLOrderByID(input: $input) {
//       _id
//       altId
//       altType
//       contactId
//       contactName
//       contactEmail
//       currency
//       amount
//       subtotal
//       discount
//       status
//       paymentStatus
//       liveMode
//       totalProducts
//       sourceType
//       sourceName
//       sourceId
//       couponCode
//       createdAt
//       updatedAt
//       sourceSubType
//       fulfillmentStatus
//       onetimeProducts
//       items {
//         product {
//           _id
//           type
//           parentId
//           archived
//           deleted
//           image
//           isTaxesEnabled
//           isLabelEnabled
//           locationId
//           name
//           productType
//           description
//           availableInStore
//           seo {
//             title
//             description
//           }
//           userId
//           createdAt
//           updatedAt
//         }
//         _id
//         name
//         price {
//           _id
//           deleted
//           trackInventory
//           name
//           type
//           currency
//           amount
//           compareAtPrice
//           locationId
//           userId
//           createdAt
//           updatedAt
//           product
//           recurring {
//             interval
//             intervalCount
//           }
//           setupFee
//           trialPeriod
//           totalCycles
//         }
//         qty
//         unitDiscount
//         unitDiscountWithoutSetupFee
//       }
//       markAsTest
//       automaticTaxesCalculated
//       contactSnapshot {
//         id
//         country
//         city
//         source
//         type
//         locationId
//         validEmailDate
//         lastNameLowerCase
//         emailLowerCase
//         firstName
//         email
//         validEmail
//         address1
//         fullNameLowerCase
//         lastName
//         firstNameLowerCase
//         createdBy {
//           sourceId
//           channel
//           source
//           timestamp
//         }
//         dateAdded
//         deleted
//         postalCode
//         unsubscribeEmail
//         phone
//         state
//         dateUpdated
//       }
//       amountSummary {
//         subtotal
//         discount
//         tax
//         shipping
//       }
//       source {
//         type
//         subType
//         id
//         name
//       }
//       coupon {
//         _id
//         usageCount
//         hasAffiliateCoupon
//         deleted
//         limitPerCustomer
//         altId
//         altType
//         name
//         code
//         discountType
//         discountValue
//         userId
//         status
//         startDate
//         applyToFuturePayments
//         createdAt
//         updatedAt
//         couponSessionId
//       }
//       fingerprint
//       trackingId
//       traceId
//     }
//   }
// `);
