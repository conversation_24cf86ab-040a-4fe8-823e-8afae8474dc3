'use client';

import { PaymentFunctionArgs, PaymentFunctionReturn } from '@/components/Payment';
import { LoaderPaymentForm } from '@/components/Payment/restloader';
import { SubmitPaymentUNIPageData } from '@/graphql/declarations/payments';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { NextPage } from 'next';
import { useSearchParams } from 'next/navigation';

const Payment: NextPage = () => {
  const searchParams = useSearchParams();
  const paymentData = searchParams?.get('paymentData');
  const token = searchParams?.get('token');
  const groupID = searchParams?.get('groupID');
  const initialValuesString = searchParams?.get('initialValues');
  const publishableKey = searchParams?.get('publishableKey');
  const transactionId = searchParams?.get('transactionId');

  async function postTransactionsFlag(args: { type: string; message?: string }) {
    switch (args.type) {
      case 'success': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_success_response',
            chargeId: args.message || '',
          }),
          '*',
        );
        break;
      }
      case 'error': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_error_response',
            error: {
              description: args.message || '',
            },
          }),
          '*',
        );
        break;
      }
      case 'cancel': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_close_response',
          }),
          '*',
        );
        break;
      }
    }
  }

  const onAfterPayment = async (args: { success: boolean; transactionID: string }) => {
    if (args.success) {
      postTransactionsFlag({
        type: 'success',
        message: args.transactionID,
      });
    }
  };

  const customPaymentFunction = async (
    args: PaymentFunctionArgs,
  ): Promise<PaymentFunctionReturn> => {
    try {
      let month = args.expiryDate?.split('/')?.[0] ?? '';
      let year = args.expiryDate?.split('/')?.[1] ?? '';
      const resp = await apolloClient.mutate({
        mutation: SubmitPaymentUNIPageData,
        variables: {
          input: {
            apiKey: publishableKey || '',
            transactionID: transactionId || '',
            billingAddress: args.street || '',
            billingCity: args.city || '',
            billingState: args.state || '',
            billingZip: args.zipCode || '',
            billingCountry: args.country || '',
            email: args.email || '',
            nameOnCard: args.fullName || '',
            phoneNumber: args.phoneNumber || '',

            // paymentToken: payToken,
            paymentCard:
              args.paymentMethod === 'card'
                ? {
                    // We know this is blank, leave it blank
                    cardCvv: args.cvc,
                    cardExpMonth: month,
                    cardExpYear: year,
                    cardToken: args.cardToken,
                  }
                : undefined,
            ach:
              args.paymentMethod === 'ach'
                ? {
                    accountType: args.accountType!,
                    accountNumber: args.accountNumber!,
                    routingNumber: args.routingNumber!,
                    accountHolderType: args.holderType!,
                  }
                : undefined,
            gpecomm:
              args.paymentMethod === 'gpecomm'
                ? {
                    id: args.gpEcomm!,
                  }
                : undefined,
            ipAddress: args.ip || '',
          },
        },
      });

      let txID = resp.data?.ghl_api_submitPayment_uni?.txID;

      if (!txID) {
        throw new Error('Payment Failed');
      }

      return {
        status: 'CAPTURED',
        transactionID: txID,
      };
    } catch (error) {
      console.error(error);
      // toast.error("Payment Failed");
      // postTransactionsFlag({ type: "error", message: error?.message });

      return {
        status: 'DECLINED',
        transactionID: '',
      };
    }
  };

  return (
    <LoaderPaymentForm
      onAfterPayment={onAfterPayment}
      customPaymentFunction={customPaymentFunction}
      initialValues={initialValuesString ? JSON.parse(initialValuesString) : undefined}
      input={{
        data: {
          dynamicData: {
            discountCodes: [],
            quantityAmounts: [],
          },
          paymentData: paymentData || '',
        },
        token: token || '',
        groupID: groupID || '',
      }}
    />
  );
};

export default Payment;
