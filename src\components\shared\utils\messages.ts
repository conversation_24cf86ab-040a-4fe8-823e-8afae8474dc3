export const message = {
  requiredField: 'Field is required',
  phonePattern: 'Please provide a valid phone number',
  emailPattern: 'Please provide a valid email address',
  oneToHundredPattern: 'Please provide a Vacancy rate',
  errorsInForm: 'There are fields with errors or missing information',
  minLength: (min) => `Must be at least ${min} characters`,
  maxLength: (max) => `Cannot be more than ${max} characters`,
  noFile: 'No file',
  fileToBig: 'File is too big, size limit is 5MB',
  fileInvalid: 'File is too big or invalid (limit is 5MB)',
  discontinuedCompany: 'The company is bankrupt, discontinued, or under forced liquidation.',
  getBrregCompany:
    'Fill in a valid organisation number to get the organisation information from Brønnøysund',
  alreadyRegistered: 'The company is already registered',
  invalidOrgNumber: 'Could not find company in Brønnøysundregisteret',
  companyNotFoundByOrgnumber: 'Company not found',
  invalidCompanyOrgNumber: 'Fill in a valid et organisation number',
  companyAlreadyAdded: 'Company already added',
  unauthorized: 'Unauthorized',
  'url-example': 'http://www.example.no',
  invalidValue: 'Invalid value',
  date: {
    disableFuture: 'Date cannot be in the future',
    disablePast: 'Date cannot be in the past',
    invalidDate: 'Invalid date',
    maxDate: 'Date cannot be after {{date}}',
    minDate: 'Date cannot be before {{date}}',
    shouldDisableDate: 'Date is not available',
    shouldDisableMonth: 'Month is not available',
    shouldDisableYear: 'Year is not available',
  },
  tooltip: {
    state: 'Enter the state you in.',
    zipCode: 'Enter you Zip code',
  },
  api: {
    successCreate: (item) => `${item} added successfully!`,
    errorCreate: (item, errorMessage) => `Error adding ${item}: ${errorMessage}`,
    successUpdate: (item) => `${item} updated successfully!`,
    errorUpdate: (item, errorMessage) => `Error updating ${item}: ${errorMessage}`,
    successDelete: (item) => `${item} deleted successfully!`,
    errorDelete: (item, errorMessage) => `Error deleting ${item}: ${errorMessage}`,
  },
};

export const requiredAmountIsValid = {
  required: {
    value: true,
    message: 'Amount is required',
  },
  min: {
    value: 0,
    message: 'Amount cannot be less than 0',
  },
  validate: {
    nonNegative: (value) => value >= 0 || 'Amount cannot be negative',
  },
};

export const phoneNumberIsValid = {
  required: {
    value: true,
    message: 'Phone is required',
  },
  validate: (phone: number | string) => {
    const phoneString = String(phone).replace(/[^\d+]/g, '');
    const phoneRegex = /^\+?[1-9]\d{1,14}$/; // E.164 format

    if (!phoneRegex.test(phoneString)) {
      return 'Invalid phone number format';
    }

    if (phoneString.length < 9) {
      return 'Phone number is too short to be valid';
    }

    return true;
  },
};
