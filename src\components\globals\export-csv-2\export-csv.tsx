import { Button } from 'flowbite-react';
import { HiDocumentDownload } from 'react-icons/hi';

// Only pass flat data to  be able to export properly
const ExportCSV2 = (props: { fetchFunction: () => Promise<any[]>; fileName: string }) => {
  // Function to convert the object array to CSV format with headers
  const convertToCSV = (objArray) => {
    if (!Array.isArray(objArray) || objArray.length === 0) return '';

    // Get the headers (keys of the first object)
    const keys = Object.keys(objArray[0]);

    // Add the headers as the first row
    let csvString = keys.join(',') + '\r\n';

    objArray.forEach((obj) => {
      const values = keys.map((key) => (obj[key] !== undefined ? `"${obj[key]}"` : '')); // Map values and quote them to avoid CSV format issues

      // Add each row
      csvString += values.join(',') + '\r\n';
    });

    return csvString;
  };

  const exportCSVFile = async () => {
    const data = await props.fetchFunction();
    const csvData = convertToCSV(data);
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${props.fileName}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <Button className="p-0" color="gray" onClick={exportCSVFile}>
      <div className="flex items-center gap-x-3">
        <HiDocumentDownload className="text-xl" />
        <span>Export</span>
      </div>
    </Button>
  );
};

export default ExportCSV2;
