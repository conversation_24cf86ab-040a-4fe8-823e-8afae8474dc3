query Gateway_products($input: Gateway_productsInput!) {
  gateway_products(input: $input) {
    data {
      id
      name
      price
      discount
      taxExempt
      brand
      kitchenItem
      sku
      isRecurring
      recurringInterval
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}

query Gateway_product($input: Gateway_productInput!) {
  gateway_product(input: $input) {
    id
    name
    price
    discount
    productStatus
    taxExempt
    kitchenItem
    sku
    category
    subCategory
    brand
    isRecurring
    recurringMode
    recurringInterval
    recurringFrequency
    recurringTotalCycles
    recurringTrialDays
    recurringSetupFee
    description
    isInStore
    isOnline
    productImages {
      url
      name
    }
  }
}
