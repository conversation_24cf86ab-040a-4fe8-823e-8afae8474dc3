.text {
  position: relative;
  line-height: 125%;
  font-weight: 500;
}
.navItem {
  align-self: stretch;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.lineIcon {
  flex: 1;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  height: 0px;
  object-fit: contain;
}
.navItem1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.navItem2 {
  align-self: stretch;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #3f83f8;
}
.stepperNavigation {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 20px;
  gap: 16px;
  color: #6b7280;
}
.provideTheFollowing {
  margin: 0;
}
.provideTheFollowingContainer {
  align-self: stretch;
  flex: 1;
  position: relative;
  line-height: 150%;
  display: flex;
  align-items: center;
}
.column {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: center;
  font-size: 12px;
  color: #6b7280;
}
.checkbox {
  width: 16.5px;
  border-radius: 4px;
  background-color: #1c64f2;
  border: 0.5px solid #d1d5db;
  box-sizing: border-box;
  height: 16.5px;
}
.label {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.inputField {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.checkboxParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 20px;
  gap: 16px;
}
.label1 {
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.button {
  border-radius: 8px;
  background-color: #e02424;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  padding: 4px;
}
.sm {
  align-self: stretch;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px;
  font-size: 20px;
  color: #fff;
}
.labelParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.inputText {
  flex: 1;
  position: relative;
  line-height: 125%;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.inputFieldParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  font-size: 14px;
}
.frameParent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  font-size: 16px;
}
.unitedStates {
  align-self: stretch;
  flex: 1;
  position: relative;
  line-height: 125%;
  display: flex;
  align-items: center;
}
.chevronDownIcon {
  width: 10px;
  position: relative;
  height: 10px;
  overflow: hidden;
  flex-shrink: 0;
}
.content6 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input6 {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 12px;
  color: #6b7280;
}
.inputFieldGroup {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.inputWidgetLgInner {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.calendarMonthIcon {
  width: 14px;
  position: relative;
  height: 14px;
  overflow: hidden;
  flex-shrink: 0;
}
.content10 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}
.datepicker {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  box-sizing: border-box;
  height: 42px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.selectInput {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.column1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.inputWidgetLg {
  align-self: stretch;
  box-shadow:
    0px 1px 3px rgba(0, 0, 0, 0.1),
    0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 20px;
  gap: 16px;
  font-size: 14px;
}
.button1 {
  border-radius: 8px;
  background-color: #31c48d;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
}
.sm1 {
  align-self: stretch;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px;
  color: #fff;
}
.button2 {
  width: 85px;
  border-radius: 8px;
  background-color: #e5e7eb;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  box-sizing: border-box;
}
.button3 {
  border-radius: 8px;
  background-color: #1a56db;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  color: #fff;
}
.sm2 {
  align-self: stretch;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
}
.ownership {
  width: 100%;
  position: relative;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  gap: 32px;
  text-align: left;
  font-size: 20px;
  color: #111928;
  font-family: Inter;
}
