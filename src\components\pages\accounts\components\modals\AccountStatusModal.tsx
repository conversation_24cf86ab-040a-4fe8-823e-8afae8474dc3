import { Card } from '@/components/ui/card';
import { mockAccountData } from '@/mock/account-status-data';
import { BaseAccountModal } from './BaseAccountModal';
import { InfoItem } from './InfoItem';
import * as Icons from 'lucide-react';

type AccountStatus = 'active' | 'pending' | 'disabled';

interface AccountStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  status: AccountStatus;
}

const statusConfig = {
  active: {
    title: 'Active Account',
    icon: Icons.CheckCircle2,
    color: 'text-green-500',
  },
  pending: {
    title: 'Pending Account',
    icon: Icons.Clock,
    color: 'text-yellow-500',
  },
  disabled: {
    title: 'Disabled Account',
    icon: Icons.AlertTriangle,
    color: 'text-red-500',
  },
};

export const AccountStatusModal = ({ isOpen, onClose, status }: AccountStatusModalProps) => {
  const data =
    status === 'disabled'
      ? mockAccountData.disabled
      : status === 'pending'
        ? mockAccountData.submitted // Using submitted data for pending status
        : mockAccountData.active;
  const config = statusConfig[status];

  return (
    <BaseAccountModal
      isOpen={isOpen}
      onClose={onClose}
      title={config.title}
      accountData={data}
      statusIcon={config.icon}
      statusColor={config.color}
    >
      {/* Account Performance - Only shown for active accounts */}
      {status === 'active' && (
        <Card className="p-4">
          <h3 className="mb-4 text-lg font-semibold">Account Performance</h3>
          <div className="grid gap-4 md:grid-cols-2">
            {'performance' in data && (
              <>
                <InfoItem
                  icon={Icons.CircleDollarSign}
                  label="Monthly Volume"
                  value={data.performance?.monthlyVolume}
                />
                <InfoItem
                  icon={Icons.Calendar}
                  label="Last Transaction"
                  value={data.performance?.lastTransaction}
                />
                <InfoItem
                  icon={Icons.CircleDollarSign}
                  label="Average Ticket"
                  value={data.performance?.averageTicket}
                />
                <InfoItem
                  icon={Icons.CircleDollarSign}
                  label="Month to Date"
                  value={data.performance?.monthToDateVolume}
                />
              </>
            )}
          </div>
        </Card>
      )}

      {/* Transaction Limits - Only shown for active accounts */}
      {status === 'active' && (
        <Card className="p-4">
          <h3 className="mb-4 text-lg font-semibold">Transaction Limits</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <InfoItem
              icon={Icons.CircleDollarSign}
              label="Card Per Transaction"
              value={`$${(5000).toLocaleString()}`}
            />
            <InfoItem
              icon={Icons.CircleDollarSign}
              label="Card Monthly"
              value={`$${(50000).toLocaleString()}`}
            />
            <InfoItem
              icon={Icons.CircleDollarSign}
              label="Bank Transfer Per Transaction"
              value={`$${(10000).toLocaleString()}`}
            />
            <InfoItem
              icon={Icons.CircleDollarSign}
              label="Bank Transfer Monthly"
              value={`$${(100000).toLocaleString()}`}
            />
          </div>
          <div className="mt-4 grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">Card Monthly Usage</div>
              <div className="h-2 w-full rounded-full bg-gray-200">
                <div className="h-full w-[45%] rounded-full bg-green-500" />
              </div>
              <div className="text-sm text-gray-600">45% of limit used</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-muted-foreground">
                Bank Transfer Monthly Usage
              </div>
              <div className="h-2 w-full rounded-full bg-gray-200">
                <div className="h-full w-[25%] rounded-full bg-green-500" />
              </div>
              <div className="text-sm text-gray-600">25% of limit used</div>
            </div>
          </div>
        </Card>
      )}

      {/* Bank Account Information - Only shown for active accounts */}
      {status === 'active' && (
        <Card className="p-4">
          <h3 className="mb-4 text-lg font-semibold">Bank Account Information</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <InfoItem icon={Icons.Building2} label="Bank Name" value="JPMORGAN CHASE BANK, NA" />
            <InfoItem icon={Icons.Building2} label="Account Number" value="**** **** 4321" />
            <InfoItem icon={Icons.Building2} label="Routing Number" value="*********" />
          </div>
        </Card>
      )}

      {/* Pending Account Message */}
      {status === 'pending' && (
        <Card className="border-yellow-200 bg-yellow-50 p-4">
          <div className="mb-4 flex items-center gap-2">
            <Icons.Clock className="h-5 w-5 text-yellow-500" />
            <p className="text-yellow-700">
              Your account is pending approval. This usually takes 1-2 business days.
            </p>
          </div>
          <div className="grid gap-4">
            {' '}
            {'submissionDetails' in data && (
              <>
                <InfoItem
                  icon={Icons.Calendar}
                  label="Submission Date"
                  value={data.submissionDetails?.submissionDate}
                />
                <InfoItem
                  icon={Icons.Clock}
                  label="Estimated Completion"
                  value={data.submissionDetails?.estimatedCompletionDate}
                />
                <InfoItem
                  icon={Icons.CheckCircle2}
                  label="Current Step"
                  value="Review in Progress"
                />
              </>
            )}
          </div>
        </Card>
      )}

      {/* Disabled Account Message */}
      {status === 'disabled' && (
        <Card className="border-red-200 bg-red-50 p-4">
          <div className="mb-4 flex items-center gap-2">
            <Icons.AlertTriangle className="h-5 w-5 text-red-500" />
            <p className="text-red-700">
              This account is currently disabled. Contact support for assistance.
            </p>
          </div>
          {'disabledDate' in data && (
            <div className="grid gap-4">
              <InfoItem icon={Icons.Calendar} label="Disabled Date" value={data.disabledDate} />
              <InfoItem
                icon={Icons.Calendar}
                label="Last Active Date"
                value={data.lastActiveDate}
              />
              <InfoItem icon={Icons.AlertTriangle} label="Reason" value={data.reason} />
            </div>
          )}
        </Card>
      )}
    </BaseAccountModal>
  );
};
