'use client';

import LoadingButton from '@/components/globals/loading-button';
import useLocalStorage from '@/components/hooks/use-local-storage/useLocalStorage';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SAVE_STRIPE_INTEGRATION } from '@/graphql/declarations/integrations';
import { useMutation } from '@apollo/client';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Eye, EyeOff, KeyRound, Save, ShieldCheck } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { z } from 'zod';

interface StripeIntegrationFormProps {
  groupId: string;
}

const formSchema = z.object({
  stripePublicKey: z
    .string()
    .min(1, { message: 'Stripe public key is required' })
    .regex(/^(pk_live_|pk_test_)[A-Za-z0-9]{24,}$/, {
      message: 'Enter a valid Stripe public key (e.g., pk_live_xxx or pk_test_xxx)',
    }),
  stripeSecretKey: z
    .string()
    .min(1, { message: 'Stripe secret key is required' })
    .regex(/^(sk_live_|sk_test_)[A-Za-z0-9]{24,}$/, {
      message: 'Enter a valid Stripe secret key (e.g., sk_live_xxx or sk_test_xxx)',
    }),
});

type FormSchema = z.infer<typeof formSchema>;

export default function StripeIntegrationForm({ groupId }: StripeIntegrationFormProps) {
  const [showPublicKey, setShowPublicKey] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [stripePublicKey, setStripePublicKey] = useLocalStorage<string>('stripePublicKey');
  const [stripeSecretKey, setStripeSecretKey] = useLocalStorage<string>('stripeSecretKey');

  const {
    register,
    formState: { errors, isValid },
    getValues,
  } = useForm<FormSchema>({
    defaultValues: {
      stripePublicKey: stripePublicKey ?? '',
      stripeSecretKey: stripeSecretKey ?? '',
    },
    resolver: zodResolver(formSchema),
  });

  const [saveStripeIntegration, { loading: saving }] = useMutation(SAVE_STRIPE_INTEGRATION);

  const handleSave = async () => {
    const values = getValues();

    const saveResult = await saveStripeIntegration({
      variables: {
        input: {
          publishableKey: values.stripePublicKey,
          secretKey: values.stripeSecretKey,
          groupId,
          type: 'api',
        },
      },
    });

    if (saveResult.errors) {
      console.error(`Failed to save stripe integration`, saveResult.errors);

      toast.error('Failed to save stripe integration');
    }

    setStripePublicKey(values.stripePublicKey);
    setStripeSecretKey(values.stripeSecretKey);

    toast.success('Stripe integration saved successfully');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <ShieldCheck className="text-primary h-6 w-6" />
          Stripe API Keys
        </CardTitle>
        <CardDescription>Configure your Stripe API credentials</CardDescription>
      </CardHeader>
      <CardContent>
        <form className="space-y-6">
          <div className="space-y-4">
            {[
              {
                id: 'public-key',
                label: 'Stripe Public Key',
                placeholder: 'pk_live_...',
                name: 'stripePublicKey' as const,
                error: errors.stripePublicKey?.message,
                show: showPublicKey,
                setShow: setShowPublicKey,
              },
              {
                id: 'secret-key',
                label: 'Stripe Secret Key',
                placeholder: 'sk_live_...',
                name: 'stripeSecretKey' as const,
                error: errors.stripeSecretKey?.message,
                show: showSecretKey,
                setShow: setShowSecretKey,
              },
            ].map(({ error, id, label, placeholder, name, show, setShow }) => (
              <div key={id} className="space-y-2">
                <Label htmlFor={id} className="flex items-center gap-2">
                  <KeyRound className="h-4 w-4" />
                  {label}
                </Label>
                <div className="relative">
                  <Input
                    id={id}
                    type={show ? 'text' : 'password'}
                    placeholder={placeholder}
                    {...register(name)}
                  />
                  <button
                    type="button"
                    onClick={() => setShow(!show)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 transform"
                  >
                    {show ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {error && <p className="text-sm text-red-500">{error}</p>}
              </div>
            ))}
          </div>

          <Alert variant="default" className="bg-[#F4F4F5]">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Enter your Stripe API keys to enable Stripe integration. We do not store or save your
              API keys.
            </AlertDescription>
          </Alert>

          <div className="flex items-center justify-end gap-2">
            <LoadingButton
              variant="primary"
              type="button"
              disabled={saving || !isValid}
              isLoading={saving}
              icon={<Save size={14} />}
              loadingText="Saving"
              className="bg-green-500 hover:bg-green-500/80"
              onClick={handleSave}
            >
              Save
            </LoadingButton>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
