import { useState } from 'react';
import { ConfirmDialog } from '@/components/globals';
import { Button } from 'flowbite-react';
import { HiTrash } from 'react-icons/hi';
import { useMutation } from '@apollo/client';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import { CartDiscountFormData } from '../utils';
import { useLocationSelector } from '@/components/hooks';
import { Gateway_DeleteDiscountDocument } from '@/graphql/generated/graphql';

type CartDiscountDeleteProps = {
  cartDiscount: CartDiscountFormData;
  onDelete?: () => void;
};

export const CartDiscountDelete = ({ cartDiscount, onDelete }: CartDiscountDeleteProps) => {
  const [confirmDialog, setShowUpdateModal] = useState(false);
  const { locationFilter } = useLocationSelector({});

  const [deleteCartDiscountMutation, { loading: deleteCartDiscountLoading, error }] = useMutation(
    Gateway_DeleteDiscountDocument,
    {
      onCompleted: (_) => {
        setShowUpdateModal(false);
        toast.success(message.api.successDelete('Cart Discount'));
        onDelete && onDelete();
      },
      onError: (error) => {
        toast.error(message.api.errorDelete('Cart Discount', error.message));
      },
    },
  );

  const handleDeleteCartDiscount = async () => {
    try {
      await deleteCartDiscountMutation({
        variables: {
          input: {
            groupID: locationFilter?.id ?? '',
            data: {
              id: cartDiscount.id ?? '',
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Cart Discount Mutation error: ', e);
    }
  };
  return (
    <>
      <Button color="failure" onClick={() => setShowUpdateModal(true)}>
        <HiTrash className="mr-2 h-5 w-5" /> Delete
      </Button>
      <ConfirmDialog
        header={<>Delete "{cartDiscount.name}"</>}
        body={<>Are you sure you want to delete this Cart Discount?</>}
        actions={
          <div className="flex justify-center space-x-4">
            <Button color="gray" onClick={() => setShowUpdateModal(false)}>
              No, cancel
            </Button>
            <Button color="failure" onClick={handleDeleteCartDiscount}>
              Yes, I'm sure
            </Button>
          </div>
        }
        open={confirmDialog}
        setOpen={setShowUpdateModal}
      />
    </>
  );
};
