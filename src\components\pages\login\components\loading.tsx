import { AutoCompleteOption } from '@/components/globals';
import { Button } from '@/components/ui/button';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { gql } from '@apollo/client';
import { Progress } from 'flowbite-react';
import { env } from 'next-runtime-env';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Initialize ApolloClient
// const client = new ApolloClient({
//   link: new HttpLink({ uri: 'YOUR_GRAPHQL_ENDPOINT' }),
//   cache: new InMemoryCache(),
// });

// GraphQL mutation
//TODO: need to have a real api for syncing accounts
const LINK_LOCATION_MUTATION = gql`
  mutation LinkLocation($location: ID!) {
    linkLocation(location: $location) {
      success
      progress
    }
  }
`;

type LoadingComponent = {
  location: AutoCompleteOption | null;
  subAccountID?: string;
};

export const LoadingComponent = ({ location, subAccountID }: LoadingComponent) => {
  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const linkLocation = async () => {
      try {
        setLoading(true);
        const { data } = await apolloClient.mutate({
          mutation: LINK_LOCATION_MUTATION,
          variables: { location: location?.id },
        });

        if (data && data.linkLocation) {
          // Update the progress based on the mutation response
          setProgress(data.linkLocation.progress);
        }
      } catch (error) {
        console.error('Error linking location:', error);
      } finally {
        setLoading(false);
      }
    };

    if (location) {
      linkLocation();
    }
  }, [location]);

  const redirectToDashboard = () => {
    // if inside iframe, then just go to dashboard
    if (window.self !== window.top) {
      // router.push('/dashboard');
      window.location.href = '/dashboard';
    } else {
      // if not inside iframe, then redirect to parent window
      window.location.href = `https://app.gohighlevel.com/v2/location/${subAccountID}/custom-page-link/${env('NEXT_PUBLIC_GHL_APPID')}`;
    }
  };

  //FAKE THE PROGRESS BAR
  useEffect(() => {
    const speed = Math.random() * 5;
    const simulateProgress = () => {
      if (progress < 100) {
        setTimeout(() => {
          // Increment progress by a random amount each time (between 5 and 15)
          setProgress((prevProgress) => Math.min(prevProgress + Math.random() * 10, 100));
        }, speed * 100); // Delay for 500ms before updating progress
      }
    };

    simulateProgress();
  }, [progress]);

  return (
    <>
      {progress < 100 && (
        <div className="fle items-center justify-center bg-gray-100">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-md">
            <div className="mb-4 text-center">
              <div className="text-lg text-gray-900">
                We are synchronizing your payments, orders, and subscriptions to {location?.label}
              </div>

              {loading ? (
                <p>Loading...</p>
              ) : (
                <>
                  <div className="mb-4">
                    <h2 className="text-lg font-bold text-gray-900">Linking {location?.label}</h2>
                    <p className="text-sm text-gray-500">{Math.ceil(progress)}%</p>
                  </div>

                  <Progress progress={progress} color="blue" size="lg" />
                </>
              )}
            </div>
          </div>
        </div>
      )}
      {progress >= 100 && (
        <div className="flex flex-col items-center justify-center rounded-lg bg-white p-6 shadow-md">
          {/* Checkmark icon */}
          <div className="mb-4">
            <svg
              className="h-8 w-8 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              ></path>
            </svg>
          </div>

          {/* Payment and Name Section */}
          <div className="mb-4 flex w-full items-center justify-center rounded-md bg-gray-100">
            <div className="mr-4 h-10 w-10">
              <svg
                className="h-full w-full text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 14l9-5-9-5-9 5 9 5zm0 7v-6.588a1 1 0 00-.553-.894l-6.447-3.611L3 10.883v7.177a2 2 0 002 2h14a2 2 0 002-2v-7.177l-2.053 1.138-6.447 3.611A1 1 0 0012 20.412V22z"
                ></path>
              </svg>
            </div>
            <div>
              <p className="text-lg font-semibold text-gray-800">NGnair Payments</p>
              <p className="text-sm text-gray-500">{location?.label}</p>
            </div>
          </div>

          {/* Success message */}
          <p className="mb-4 text-gray-600">This location was successfully linked</p>

          {/* Button to dashboard */}
          <Button variant="primary" onClick={redirectToDashboard}>
            Go to Dashboard
          </Button>
        </div>
      )}
    </>
  );
};
