import React, { useState } from 'react';
import { Modal, Button, TextInput, ToggleSwitch, Card } from 'flowbite-react';
import { StatusChip } from '@/components/globals';
import { FaCircleCheck } from 'react-icons/fa6';

type EquipmentData = {
  terminalName: string;
  terminalStatus: string;
  dateAdded: string;
  terminalSerial: string;
  terminalType: string;
  nameOnCard: string;
};

type EquipmentDetailModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (data: EquipmentData) => void;
  equipmentData: EquipmentData;
};

export const EquipmentDetailModal: React.FC<EquipmentDetailModalProps> = ({
  isOpen,
  onClose,
  onUpdate,
  equipmentData,
}) => {
  const [isEnabled, setIsEnabled] = useState(equipmentData.terminalStatus === 'Enabled');
  const [nameOnCard, setNameOnCard] = useState(equipmentData.nameOnCard);

  const handleUpdate = () => {
    onUpdate({
      ...equipmentData,
      terminalStatus: isEnabled ? 'Enabled' : 'Disabled',
      nameOnCard: nameOnCard,
    });
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="5xl">
      <Modal.Header className="flex items-center justify-between border-b">
        <div className="flex items-center gap-4">
          <h3 className="text text-xl font-semibold text-blue-600">
            {equipmentData.terminalName}{' '}
          </h3>
          <StatusChip variant="success" label={equipmentData.terminalStatus} big />
        </div>
      </Modal.Header>
      <Modal.Body>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <Card>
              <DetailItem label="Date Added" value={equipmentData.dateAdded} />
              <DetailItem label="Serial #" value={equipmentData.terminalSerial} />
              <DetailItem label="Terminal Type" value={equipmentData.terminalType} />
            </Card>
          </div>
          <div className="space-y-4">
            <Card>
              <div className="flex items-center justify-between">
                <span className="font-medium">Terminal Status:</span>
                <ToggleSwitch
                  checked={isEnabled}
                  onChange={setIsEnabled}
                  label={isEnabled ? 'Enable' : 'Disable'}
                />
              </div>
              <div>
                <label
                  htmlFor="nameOnCard"
                  className="mb-2 block text-sm font-medium text-gray-700"
                >
                  Name on Card
                </label>
                <div className="flex items-center">
                  <TextInput
                    id="nameOnCard"
                    type="text"
                    value={nameOnCard}
                    onChange={(e) => setNameOnCard(e.target.value)}
                    className="flex-grow"
                  />

                  <FaCircleCheck color="green" size={20} className="ml-2" />
                  <Button color="blue" size="sm" className="ml-2" onClick={handleUpdate}>
                    Update
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

const DetailItem: React.FC<{ label: string; value: string }> = ({ label, value }) => (
  <div className="flex">
    <span className="font-medium">{label}:</span>
    <div className="text-sm text-gray-500">{value}</div>
  </div>
);
