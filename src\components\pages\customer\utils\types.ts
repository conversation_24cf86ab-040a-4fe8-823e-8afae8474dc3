// export type CustomerFormData = {
//   customerID?: string;
//   nameOnCard: string;
//   billingAddress: string;
//   email: string;
//   city: string;
//   phoneNumber: string;
//   state: string;
//   zipCode: string;
//   country: string;
//   cardToken: string;
//   cvc: string;
//   expiryDate: string;
//   processType: 'card' | 'ach' | 'gpecomm';
//   accountNumber?: string;
//   routingNumber?: string;
//   accountType?: string;
//   holderType?: string;
//   gpEcomm?: string;
// };

// export type UpdateFormData = {
//   nameOnCard: string;
//   billingAddress: string;
//   email: string;
//   city: string;
//   phone: string;
//   state: string;
//   zipCode: string;
//   country: string;
// };

// export type CardFormData = {
//   processType: 'card' | 'ach' | 'gpecomm';

//   accountNumber?: string;
//   routingNumber?: string;
//   accountType?: string;
//   holderType?: string;

//   cardNumber: string;
//   cvc: string;
//   expiryDate: string;

//   gpEcomm?: string;
// };

// Temporarily disabled customer functionality
export type CustomerFormData = any;
export type UpdateFormData = any;
export type CardFormData = any;
