import { <PERSON>Header } from '@/components/globals';
import { Input } from '@/components/ui/input';
import { Affiliation_get, Affiliation_setAffiliateCode } from '@/graphql/declarations/affiliate';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { Card, Label, Button, Modal } from 'flowbite-react';
import { useEffect, useState } from 'react';
import { HiOutlineExclamationCircle } from 'react-icons/hi';
import { toast } from 'react-toastify';

type SettingsTabProps = {
  defaultCode?: string;
};
export const SettingsTab = () => {
  const [affiliateCode, setAffiliateCode] = useState('');
  const [openModal, setOpenModal] = useState(false);
  const [loading, setLoading] = useState(false); // Add loading state

  const { data: affiliateData } = useQuery(Affiliation_get);

  useEffect(() => {
    if (affiliateData) {
      const code = affiliateData?.affiliation_get?.codes?.[0];
      setAffiliateCode(code?.code ?? '');
    }
  }, [affiliateData]);

  const defaultCode = affiliateData?.affiliation_get?.codes?.[0]?.code;

  const updateAffiliateCode = async () => {
    setLoading(true); // Set loading to true
    try {
      await apolloClient.mutate({
        mutation: Affiliation_setAffiliateCode,
        variables: {
          input: {
            code: affiliateCode,
          },
        },
      });

      await apolloClient.refetchQueries({
        include: [Affiliation_get],
      });

      toast.success('Affiliate code updated successfully');
    } catch (error) {
      console.error('Error updating affiliate code:', error);
    } finally {
      setLoading(false); // Set loading to false
    }
  };

  return (
    <div className="mx-auto mt-4 space-y-6 py-0">
      <Card className="space-y-4">
        <PageHeader text="Affiliate Settings" />
        <div className="flex items-center">
          <Label htmlFor="require-cvv" className="text-base font-medium text-gray-500">
            https://ngnair.com/affiliate/{affiliateCode}
          </Label>
        </div>
        <div>
          <div className="flex items-center">
            <Label htmlFor="transaction-limits" className="text-base font-medium">
              Url:
            </Label>

            <div className="ml-8 flex items-center space-x-2">
              <Label htmlFor="require-cvv" className="text-base font-medium">
                https://ngnair.com/affiliate/
              </Label>
              <div className="relative flex-grow">
                <Input
                  type="text"
                  value={affiliateCode}
                  onChange={(e) => setAffiliateCode(e.target.value)}
                  placeholder="Enter you code"
                  className="w-72"
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Button
        className="w-full sm:w-auto"
        color="blue"
        disabled={defaultCode === affiliateCode || loading} // Disable button when loading
        onClick={() => setOpenModal(true)}
      >
        {loading ? 'Saving...' : 'Save changes'}
      </Button>
      <Modal show={openModal} size="md" onClose={() => setOpenModal(false)} popup>
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamationCircle className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to update your affiliate settings?
              <br />
              This action will make the changes permanent.
            </h3>
            <div className="flex justify-center gap-4">
              <Button color="gray" onClick={() => setOpenModal(false)}>
                No, cancel
              </Button>
              <Button
                color="failure"
                onClick={() => {
                  setOpenModal(false);
                  updateAffiliateCode();
                }}
              >
                {"Yes, I'm sure"}
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};
