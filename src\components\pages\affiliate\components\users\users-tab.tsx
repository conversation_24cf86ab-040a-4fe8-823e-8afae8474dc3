'use client';

import {
  Affiliation_get,
  Affiliation_getAffiliatedUsersWithMerchants,
  Affiliation_setBankAccount,
} from '@/graphql/declarations/affiliate';
import { useQuery } from '@apollo/client';
import { AffiliateSummaryCard } from './AffiliateSummaryCard';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import moment from 'moment';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mo<PERSON>, ModalFooter, ModalBody, ModalHeader } from 'flowbite-react';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { toast } from 'react-toastify';

const Pagination = ({ currentPage, totalItems, itemsPerPage, onPageChange }) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  return (
    <div className="flex items-center gap-4">
      <Button onClick={handlePreviousPage} disabled={currentPage === 1}>
        Previous
      </Button>
      <span>
        Page {currentPage} of {totalPages}
      </span>
      <Button onClick={handleNextPage} disabled={currentPage === totalPages}>
        Next
      </Button>
    </div>
  );
};

export const UsersTab = () => {
  const { data: affiliationGet } = useQuery(Affiliation_get, {
    variables: {},
  });

  const { data: affiliateData, loading: affiliateLoading } = useQuery(
    Affiliation_getAffiliatedUsersWithMerchants,
    {
      variables: {
        input: {
          take: 100,
        },
      },
    },
  );

  const [totals, setTotals] = useState({
    Earnings: 0,
    Withdrawn: 0,
    Withdrawable: 0,
    Users: 0,
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState('10');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [routingNumber, setRoutingNumber] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [accountName, setAccountName] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(value);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSavePaymentMethod = async () => {
    setIsSaving(true);
    try {
      await apolloClient.mutate({
        mutation: Affiliation_setBankAccount,
        variables: {
          input: {
            bank_routingNumber: routingNumber,
            bank_accountNumber: accountNumber,
            bank_accountName: accountName,
          },
        },
      });
      handleCloseModal();
    } catch (error) {
      console.error('Error saving payment method:', error);
    } finally {
      toast.success('Payment method saved successfully');
      setIsSaving(false);
    }
  };

  const paginatedAffiliates =
    affiliateData?.affiliation_getAffiliatedUsersWithMerchants?.data.slice(
      (currentPage - 1) * parseInt(itemsPerPage),
      currentPage * parseInt(itemsPerPage),
    );

  useEffect(() => {
    if (affiliateData) {
      setTotals({
        Earnings: affiliateData?.affiliation_getAffiliatedUsersWithMerchants?.totalEarnings ?? 0,
        Withdrawn: affiliateData?.affiliation_getAffiliatedUsersWithMerchants?.totalWithdrawn ?? 0,
        Withdrawable:
          affiliateData?.affiliation_getAffiliatedUsersWithMerchants?.totalWithdrawable ?? 0,
        Users: affiliateData?.affiliation_getAffiliatedUsersWithMerchants?.data.length ?? 0,
      });
    }
  }, [affiliateData]);

  useEffect(() => {
    if (affiliationGet) {
      setRoutingNumber(affiliationGet?.affiliation_get?.bank_routingNumber ?? '');
      setAccountNumber(affiliationGet?.affiliation_get?.bank_accountNumber ?? '');
      setAccountName(affiliationGet?.affiliation_get?.bank_accountName ?? '');
    }
  }, [affiliationGet]);

  if (affiliateLoading) {
    return (
      <div className="mt-8 flex justify-center">
        <LoaderSquares />
      </div>
    );
  }

  return (
    <div className="mt-8">
      <AffiliateSummaryCard totals={totals} handleOpenModal={handleOpenModal} />

      <Modal show={isModalOpen} onClose={handleCloseModal} size="lg" popup className="p-8">
        <ModalHeader>Change Payout Method</ModalHeader>
        <ModalBody className="">
          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              Please enter your payout bank details so we can send your earnings to you.
            </p>
            <label className="text-sm font-semibold">Routing Number</label>
            <Input value={routingNumber} onChange={(e) => setRoutingNumber(e.target.value)} />
            <label className="text-sm font-semibold">Account Number</label>
            <Input value={accountNumber} onChange={(e) => setAccountNumber(e.target.value)} />
            <label className="text-sm font-semibold">Account Name</label>
            <Input value={accountName} onChange={(e) => setAccountName(e.target.value)} />
          </div>
        </ModalBody>
        <ModalFooter>
          <Button onClick={handleCloseModal} disabled={isSaving}>
            Cancel
          </Button>
          <Button variant={'primary'} onClick={handleSavePaymentMethod} disabled={isSaving}>
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </ModalFooter>
      </Modal>
      <div className="mt-4">
        <div className="user-cards grid grid-cols-1">
          {paginatedAffiliates?.map((affiliate) => (
            <Card key={affiliate?.user?.id} className="mb-4 border">
              <CardHeader>
                <h3 className="text-xl font-bold">{affiliate?.user?.name}</h3>
              </CardHeader>
              <CardContent className="space-y-2">
                <p>
                  Last Login:{' '}
                  {moment(parseInt(affiliate?.user?.lastLogin ?? '0')).format('MMMM Do YYYY')}
                </p>
                <hr />
                <h4 className="font-semibold">Merchants:</h4>
                <ul className="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
                  {affiliate?.merchant?.map((merchant) => (
                    <li key={merchant?.createdAt} className="rounded-lg border p-2 shadow-md">
                      <p className="text-xl font-bold">{merchant?.name}</p>
                      <p>
                        {merchant?.city}, {merchant?.state}
                      </p>
                      <p
                        className={`${merchant?.status === 'active' ? 'font-semibold text-green-500' : ''}`}
                      >
                        Status: {merchant?.status}
                      </p>
                      <p>
                        Created At:{' '}
                        {moment(parseInt(merchant?.createdAt ?? '0')).format('MMMM Do YYYY')}
                      </p>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="mt-4 flex items-center justify-between">
        <Pagination
          currentPage={currentPage}
          totalItems={affiliateData?.affiliation_getAffiliatedUsersWithMerchants?.data.length}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
        />
        <label>
          Items per page:
          <Select value={itemsPerPage} onValueChange={handleItemsPerPageChange}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select Document Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
            </SelectContent>
          </Select>
        </label>
      </div>
    </div>
  );
};
