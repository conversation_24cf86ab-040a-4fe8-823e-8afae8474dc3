'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Search, UserPlus } from 'lucide-react';
import Link from 'next/link';

// Mock data for demonstration
const mockMembers = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', type: 'Admin' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', type: 'Member' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', type: 'Contributor' },
  // Add more mock members as needed
];

const memberTypes = ['All Members', 'Admin', 'Member', 'Contributor'];
const memberGroup = ['All Groups', 'Marketing', 'Engineering', 'Sales'];

export default function GroupMembersPage() {
  const [members, setMembers] = useState(mockMembers);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All Members');
  const [selectedGroup, setSelectedGroup] = useState('All Groups');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const filteredMembers = members.filter(
    (member) =>
      (selectedType === 'All Members' || member.type === selectedType) &&
      (member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member.email.toLowerCase().includes(searchTerm.toLowerCase())),
  );

  const handleAddMember = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    const newMember = {
      id: members.length + 1,
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      type: formData.get('type') as string,
    };
    setMembers([...members, newMember]);
    setIsModalOpen(false);
  };

  return (
    <div className="container mx-auto">
      <div className="mb-6 flex flex-col items-center justify-between space-y-4 sm:flex-row sm:space-y-0">
        <div className="flex w-fit space-x-4 sm:w-auto">
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search members..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Member Type" />
            </SelectTrigger>
            <SelectContent>
              {memberTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedGroup} onValueChange={setSelectedGroup}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="Group" />
            </SelectTrigger>
            <SelectContent>
              {memberGroup.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Link href="/dashboard/admin/users/add">
          <Button className="w-full sm:w-auto">
            <UserPlus className="mr-2 h-4 w-4" /> Add Member
          </Button>
        </Link>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Type</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMembers.map((member) => (
              <TableRow key={member.id}>
                <TableCell>
                  <Link className="text-primary-700" href={`/dashboard/admin/users/${member.id}`}>
                    {member.name}
                  </Link>
                </TableCell>
                <TableCell>{member.email}</TableCell>
                <TableCell>{member.type}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
