// import { ConfirmDialog } from '@/components/globals';
// import { useLocationSelector } from '@/components/hooks';
// import { message } from '@/components/shared/utils';
// import {
//   Gateway_DeletePaymentMethodDocument,
//   GatewayUniCustomerOutputPaymentCards,
// } from '@/graphql/generated/graphql';
// import { useMutation } from '@apollo/client';
// import { Button } from 'flowbite-react';
// import { Dispatch, SetStateAction, useEffect, useState } from 'react';
// import { FaTrashAlt } from 'react-icons/fa';
// import { toast } from 'react-toastify';
import { GatewayUniCustomerOutputPaymentCards } from '@/graphql/generated/graphql';
import { Dispatch, SetStateAction } from 'react';

type CustomerCardDeleteProps = {
  card: GatewayUniCustomerOutputPaymentCards;
  setParentLoading: Dispatch<SetStateAction<boolean>>;
  refetch: () => void;
  customerID: string;
};

// export const CustomerCardDelete = ({
//   card,
//   customerID,
//   refetch,
//   setParentLoading,
// }: CustomerCardDeleteProps) => {
//   const { locationFilter } = useLocationSelector({ readonly: true, onlyActive: true });

//   const [delatePaymentConfirmDialog, setDelatePaymentConfirmDialog] = useState(false);

//   const [deletePaymentMethodMutation, { loading: deletePaymentMethodLoading }] = useMutation(
//     Gateway_DeletePaymentMethodDocument,
//     {
//       onCompleted: (_) => {
//         toast.success(message.api.successDelete('Payment Method'));
//         refetch();
//       },
//       onError: (error) => {
//         toast.error(message.api.errorDelete('Payment Method', error.message));
//       },
//     },
//   );

//   const handleDelatePaymentMethod = async () => {
//     try {
//       setDelatePaymentConfirmDialog(false);
//       await deletePaymentMethodMutation({
//         variables: {
//           input: {
//             groupID: locationFilter?.id ?? '',
//             data: {
//               cardID: card.cardID ?? '',
//               customerID,
//             },
//           },
//         },
//       });
//     } catch (e) {
//       console.error('Delete Payment method Mutation error: ', e);
//     }
//   };

//   useEffect(() => {
//     setParentLoading(deletePaymentMethodLoading);
//   }, [deletePaymentMethodLoading]);

//   return (
//     <div>
//       <Button
//         color="failure"
//         onClick={() => {
//           setDelatePaymentConfirmDialog(true);
//         }}
//       >
//         <FaTrashAlt size={20} />
//         {'  '} <span className="ml-2">Delete</span>
//       </Button>
//       <ConfirmDialog
//         header={<FaTrashAlt color="gray" className="w-full pl-[10px] text-center" />}
//         body={
//           <div className="flex w-full items-center justify-center">
//             <h3 className="mb-5 text-center text-lg font-normal text-gray-500 dark:text-gray-400">
//               {card.isDefault
//                 ? `This is your default card used in all your transactions, Change you default card first
//                   before Deleting this one.`
//                 : `Deleting this Payment method will remove all stored payment using this card from
//                   all locations`}
//             </h3>
//           </div>
//         }
//         actions={
//           <div className="flex w-full items-center justify-center gap-4">
//             {!card.isDefault ? (
//               <>
//                 <Button color="gray" onClick={() => setDelatePaymentConfirmDialog(false)}>
//                   No, cancel
//                 </Button>
//                 <Button color="failure" onClick={handleDelatePaymentMethod}>
//                   Yes, I'm sure
//                 </Button>
//               </>
//             ) : (
//               <Button color="failure" onClick={() => setDelatePaymentConfirmDialog(false)}>
//                 OK
//               </Button>
//             )}
//           </div>
//         }
//         open={delatePaymentConfirmDialog}
//         setOpen={setDelatePaymentConfirmDialog}
//       />
//     </div>
//   );
// Temporarily disabled customer functionality
export const CustomerCardDelete = () => {
  return null;
};
