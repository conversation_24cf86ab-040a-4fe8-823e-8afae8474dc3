'use client';

import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function IntegGLBindPage() {
  const params = useSearchParams();

  const redirectToLogin = async () => {
    // copy current url and pass it as redirect query param
    const redirectUrl = window.location.href;
    const redirectParam = encodeURIComponent(redirectUrl);
    window.location.href = `/login?redirect=${redirectParam}&mode=ghl-binding&token=${params?.get('token')}`;
  };

  useEffect(() => {
    if (params?.get('token')) {
      redirectToLogin();
    }
  }, [params]);

  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <LoaderSquares />
    </div>
  );
}
