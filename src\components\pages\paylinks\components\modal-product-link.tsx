import { Modal } from 'flowbite-react';
import { useLocationSelector } from '@/components/hooks';
import { Column, TopComponent, useDataGridView } from '@/components/globals';
import { Gateway_ProductsDocument } from '@/graphql/generated/graphql';
import { useState, useMemo } from 'react';
import useDebounce from '@/components/hooks/useDebounce';
import { moneyFormatString } from '@/lib/utils';
import DataGridView from '@/components/globals/sortable-table/data-grid-view';

export type ModalAddProductLinkProps = {
  isOpen: boolean;
  onClose: () => void;
  onAddProduct: (productId: string, amount: number, name: string, price: number) => void;
};

export const ModalAddProductLink = ({
  isOpen,
  onClose,
  onAddProduct,
}: ModalAddProductLinkProps) => {
  const { locationFilter } = useLocationSelector({ readonly: true });
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    data: productListData,
    loading: productListLoading,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
  } = useDataGridView({
    query: Gateway_ProductsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: { page: 1, pageSize: 10 },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
  });

  const handleSelectProduct = (productId: string, amount: number, name: string, price: number) => {
    onAddProduct(productId, amount, name, price);
    onClose();
  };

  const columns: Column[] = [
    {
      key: 'name',
      header: 'Name',
      onClick: (row) => handleSelectProduct(row.id ?? '', 1, row.name, row.price),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'price',
      header: 'Price',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      valueGetter: (row) => `${moneyFormatString(row.price)}`,
    },
    {
      key: 'isRecurring',
      header: 'Is Recurring',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      valueGetter: (row) =>
        `${row.isRecurring ? 'Yes (per ' + row.recurringInterval + ' Days)' : 'N'}`,
    },
    {
      key: 'brand',
      header: 'Brand',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'sku',
      header: 'SKU',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];

  const rows = useMemo(() => {
    const data = productListData?.gateway_products?.data;
    if (!data) return [];
    return data.filter((item): item is NonNullable<typeof item> => item !== null);
  }, [productListData?.gateway_products?.data]);

  return (
    <Modal show={isOpen} onClose={onClose} size="7xl">
      <Modal.Header className="flex items-center justify-between">
        <div className="text-xl font-semibold">Add Product</div>
      </Modal.Header>
      <Modal.Body>
        <div className="">
          <div className="border-b border-gray-300 pb-2 sm:flex">
            {/* <div className="w-1/4">{locationSelectorElement}</div> */}
            <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
              <TopComponent value={searchValue} setValue={setSearchValue}>
                <div></div>
              </TopComponent>
            </div>
          </div>
          <div>
            <DataGridView
              columns={columns}
              rows={rows}
              pageSize={pageSize}
              currentPage={currentPage}
              isLoading={productListLoading}
              mode="server"
              onPageChange={onPageChange}
              onPageSizeChange={onPageSizeChange}
              totalRecords={productListData?.gateway_products?.page?.total ?? 0}
            />
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
