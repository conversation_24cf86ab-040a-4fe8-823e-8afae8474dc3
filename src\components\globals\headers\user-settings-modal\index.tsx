import styles from './index.module.css';
import { cn } from '@/lib/utils';
import { useQuery } from '@apollo/client';
import {
  DeleteGHLSSOBinding,
  GetGHLSSOBindings,
  Me,
  UpdateMe,
  UpdatePassword,
} from '@/graphql/declarations/me';
import { useState, useEffect } from 'react';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';
import { emailPatternRegex, message } from '@/components/shared/utils';
import { Card } from 'flowbite-react';
import { FormInput } from '../../form-input';
import { FormProvider, useForm } from 'react-hook-form';
import { HiCheckCircle, HiMinusCircle } from 'react-icons/hi';

type ProfilePaymentFormData = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: string;
};

type UpdatePasswordFormData = {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
};
//NOTE: Need to export the form to a separate component. global component should know nothing about business logic.
const UpdateUserSettingsRightDra = () => {
  const { data: userData } = useQuery(Me);

  const methods = useForm<ProfilePaymentFormData>({
    defaultValues: {
      firstName: userData?.authenticatedItem?.name || '',
      lastName: userData?.authenticatedItem?.lastName || '',
      email: userData?.authenticatedItem?.email || '',
      phone: userData?.authenticatedItem?.phone || '',
      role: userData?.authenticatedItem?.title || '',
    },
  });
  const { setValue } = methods;

  const [updating, setUpdating] = useState(false);
  const onSubmitProfile = async (data: ProfilePaymentFormData) => {
    if (!userData?.authenticatedItem?.id) return;
    setUpdating(true);
    const resp = await apolloClient.mutate({
      mutation: UpdateMe,
      variables: {
        where: {
          id: userData?.authenticatedItem?.id,
        },
        data: {
          name: data.firstName,
          lastName: data.lastName,
          title: data.role,
        },
      },
    });

    if (resp.data?.updateUser?.id) {
      toast.success('Profile updated successfully');
      apolloClient.resetStore();
    }

    setUpdating(false);
  };

  useEffect(() => {
    setValue('firstName', userData?.authenticatedItem?.name || '');
    setValue('lastName', userData?.authenticatedItem?.lastName || '');
    setValue('email', userData?.authenticatedItem?.email || '');
    setValue('phone', userData?.authenticatedItem?.phone || '');
    setValue('role', userData?.authenticatedItem?.title || '');
  }, [userData]);

  const passwordMethods = useForm<UpdatePasswordFormData>({
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const [passwordRequirementCheck, setPasswordRequirementCheck] = useState({
    length: false,
    caseCheck: false,
    specialChar: false,
    significantlyDifferent: false,
    passwordConfirmMatch: false,
  });

  const currentPassword = passwordMethods.watch('currentPassword');
  const newPassword = passwordMethods.watch('newPassword');
  const confirmPassword = passwordMethods.watch('confirmPassword');
  useEffect(() => {
    setPasswordRequirementCheck({
      length: newPassword.length >= 10 && newPassword.length <= 100,
      caseCheck: /[a-z]/.test(newPassword) && /[A-Z]/.test(newPassword),
      specialChar: /[!@#?]/.test(newPassword),
      significantlyDifferent: newPassword !== currentPassword,
      passwordConfirmMatch: newPassword === confirmPassword && confirmPassword.length > 0,
    });
  }, [currentPassword, newPassword, confirmPassword]);

  const updatePasswordOnSubmit = async () => {
    if (!userData?.authenticatedItem?.id) return;
    setUpdating(true);

    // if any of requirements are not met, return
    for (const key in passwordRequirementCheck) {
      if (!passwordRequirementCheck[key]) {
        toast.error('Password requirements not met');
        setUpdating(false);
        return;
      }
    }

    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match');
      setUpdating(false);
      return;
    }

    const resp = await apolloClient.mutate({
      mutation: UpdatePassword,
      variables: {
        oldPassword: currentPassword,
        newPassword: newPassword,
      },
    });

    if (resp.data?.authclient_changePassword) {
      toast.success('Password updated successfully');
    }

    setUpdating(false);
  };

  const [updatePassForm, setUpdatePassForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [requirementCheck, setRequirementCheck] = useState({
    length: false,
    caseCheck: false,
    specialChar: false,
    significantlyDifferent: false,
  });

  useEffect(() => {
    setRequirementCheck({
      length: updatePassForm.newPassword.length >= 10 && updatePassForm.newPassword.length <= 100,
      caseCheck:
        /[a-z]/.test(updatePassForm.newPassword) && /[A-Z]/.test(updatePassForm.newPassword),
      specialChar: /[!@#?]/.test(updatePassForm.newPassword),
      significantlyDifferent: updatePassForm.newPassword !== updatePassForm.currentPassword,
    });
  }, [updatePassForm]);

  const updatePassword = async () => {
    if (!userData?.authenticatedItem?.id) return;
    setUpdating(true);

    // if any of requirements are not met, return
    for (const key in requirementCheck) {
      if (!requirementCheck[key]) {
        toast.error('Password requirements not met');
        setUpdating(false);
        return;
      }
    }

    if (updatePassForm.newPassword !== updatePassForm.confirmPassword) {
      toast.error('Passwords do not match');
      setUpdating(false);
      return;
    }

    const resp = await apolloClient.mutate({
      mutation: UpdatePassword,
      variables: {
        oldPassword: updatePassForm.currentPassword,
        newPassword: updatePassForm.newPassword,
      },
    });

    if (resp.data?.authclient_changePassword) {
      toast.success('Password updated successfully');
    }

    setUpdating(false);
  };

  const { data: ssoIntegrationData } = useQuery(GetGHLSSOBindings);

  const removeIntegration = async (id: string) => {
    setUpdating(true);
    const resp = await apolloClient.mutate({
      mutation: DeleteGHLSSOBinding,
      variables: {
        where: {
          id,
        },
      },
    });

    if (resp.data?.deleteGHLSSOBinding?.id) {
      toast.success('Integration removed successfully');
      localStorage.removeItem(`location_${userData?.authenticatedItem?.id}`);
      apolloClient.resetStore();
    }

    setUpdating(false);
  };

  return (
    <div
      className={cn(styles.updateUserSettingsRightDra, 'z-30 mx-auto flex w-full rounded-lg')}
      onClick={(e) => e.stopPropagation()}
    >
      <div className={cn(styles.inputWidgetMdParent, 'h-full max-h-screen overflow-y-auto')}>
        <Card className="w-full">
          <div className={styles.headerSm}>
            <b className={styles.b}>Update User</b>
            <img className={styles.infoIcon} alt="" src="info.svg" />
          </div>
          {/* <div className={styles.avatarButtons}>
            <img className={styles.avatarIcon} alt="" src="Avatar.png" />
            <div className={styles.buttons}>
              <div className={styles.button}>
                <img className={styles.uploadIcon} alt="" src="upload.svg" />
                <div className={styles.text}>{`Upload `}</div>
              </div>
              <div className={styles.button1}>
                <div className={styles.text}>Remove</div>
              </div>
            </div>
          </div> */}
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmitProfile)} className="space-y-4">
              <FormInput
                id="firstName"
                name="firstName"
                label="First Name"
                rules={{
                  required: message.requiredField,
                }}
              />
              <FormInput
                id="lastName"
                name="lastName"
                label="Last Name"
                rules={{
                  required: message.requiredField,
                }}
              />
              <FormInput
                id="email"
                name="email"
                label="email"
                disabled
                rules={{
                  required: message.requiredField,
                  pattern: {
                    value: emailPatternRegex,
                    message: message.emailPattern,
                  },
                }}
              />
              <FormInput
                id="phone"
                name="phone"
                label="Phone"
                disabled
                // rules={{
                //   required: message.requiredField,
                // }}
              />
              <FormInput id="role" name="role" label="Role" />

              <Button type="submit" variant="primary">
                <div className={styles.text}>{updating ? 'Updating...' : 'Update Profile'}</div>
              </Button>
            </form>
          </FormProvider>
        </Card>

        <Card>
          <div className={styles.heading}>
            <b className={styles.b1}>Linked accounts</b>
            <div className={styles.thisWeekVisitors}>
              We use this to let you sign in and take payments through third party software
            </div>
          </div>
          <div className={styles.integrations}>
            <div className={styles.row}>
              {ssoIntegrationData?.gHLSSOBindings?.map((integration) => (
                <div key={integration.id} className={styles.card}>
                  <div className={styles.header}>
                    <b className={styles.link}>GoHighLevel Inetegration</b>
                    <Button
                      variant={'destructive'}
                      className={styles.removeButton}
                      onClick={() => removeIntegration(integration.id)}
                      disabled={updating}
                    >
                      Remove
                    </Button>
                  </div>
                  <div className={styles.description}>
                    You are binded to user {integration.ghlUserID} at {integration.locationID}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        <div className="flex w-full flex-col gap-2 p-2 pb-16">
          <FormProvider {...passwordMethods}>
            <form onSubmit={methods.handleSubmit(updatePasswordOnSubmit)} className="space-y-4">
              <div className={styles.headerSm}>
                <b className={styles.b}>Password</b>
                <img className={styles.infoIcon} alt="" src="info.svg" />
              </div>
              <FormInput
                id="currentPassword"
                name="currentPassword"
                label="Enter your current password"
                type="password"
                rules={{
                  required: message.requiredField,
                }}
              />
              <FormInput
                id="newPassword"
                name="newPassword"
                label="Your new password"
                type="password"
                rules={{
                  required: message.requiredField,
                }}
              />
              <FormInput
                id="confirmPassword"
                name="confirmPassword"
                label="Confirm new password"
                type="password"
                rules={{
                  required: message.requiredField,
                }}
              />

              <div className="border-b pb-4">
                <div className={styles.headingDescription}>
                  <div className={styles.label}>Password requirements:</div>
                  <div className={styles.ensureThatThese}>
                    Ensure that these requirements are met:
                  </div>
                </div>
                <div className={cn(styles.requirements, 'mt-6 gap-4')}>
                  <div className={styles.requirement1}>
                    {passwordRequirementCheck.length ? (
                      <HiCheckCircle color="green" size={25} />
                    ) : (
                      <HiMinusCircle color="red" size={25} />
                    )}
                    <div className={styles.link}>
                      At least 10 characters (and up to 100 characters)
                    </div>
                  </div>
                  <div className={styles.requirement1}>
                    {passwordRequirementCheck.caseCheck ? (
                      <HiCheckCircle color="green" size={25} />
                    ) : (
                      <HiMinusCircle color="red" size={25} />
                    )}
                    <div className={styles.link}>
                      At least one each: lowercase, uppercase and numeric character
                    </div>
                  </div>
                  <div className={styles.requirement3}>
                    {passwordRequirementCheck.specialChar ? (
                      <HiCheckCircle color="green" size={25} />
                    ) : (
                      <HiMinusCircle color="red" size={25} />
                    )}
                    <div className={styles.significantlyDifferentFrom}>
                      At least one special character, e.g., ! @ # ?
                    </div>
                  </div>
                  <div className={styles.requirement3}>
                    {passwordRequirementCheck.significantlyDifferent ? (
                      <HiCheckCircle color="green" size={25} />
                    ) : (
                      <HiMinusCircle color="red" size={25} />
                    )}
                    <div className={styles.significantlyDifferentFrom}>
                      Significantly different from your previous passwords
                    </div>
                  </div>
                  <div className={styles.requirement3}>
                    {passwordRequirementCheck.passwordConfirmMatch ? (
                      <HiCheckCircle color="green" size={25} />
                    ) : (
                      <HiMinusCircle color="red" size={25} />
                    )}
                    <div className={styles.significantlyDifferentFrom}>
                      New password confirms match
                    </div>
                  </div>
                </div>
              </div>
              <Button type="submit" variant="primary">
                {updating ? 'Updating...' : 'Update Password'}
              </Button>
              <br />
              <br />
            </form>
          </FormProvider>
        </div>
      </div>
    </div>
  );
};

export default UpdateUserSettingsRightDra;
