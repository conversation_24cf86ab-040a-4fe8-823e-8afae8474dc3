'use client';

import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

const Filter = ({ onClose }: any) => {
  const { register, handleSubmit, watch, setValue, reset } = useForm({
    defaultValues: [],
  });

  const housekeepingStatusMapping = {};
  const filterOptions = {};

  const handleClear = () => {
    reset();
    onClose();
  };

  return (
    <form className="flex h-[480px] w-full flex-col items-center justify-start gap-6 overflow-y-auto rounded-lg bg-white px-6 py-4">
      <div className="inline-flex items-center justify-between self-stretch">
        <div className="text-lg font-bold text-[#495057]">Filters</div>
        <button type="reset" onClick={handleClear} className="text-sm font-normal text-[#495057]">
          Clear
        </button>
      </div>

      <div className="flex w-full items-end justify-end gap-2">
        <Button onClick={onClose} type="reset" variant="secondary" className="w-[101px] px-4 py-3">
          Cancel
        </Button>
        <Button type="submit" variant="primary" className="w-[101px] px-4 py-3">
          Apply
        </Button>
      </div>
    </form>
  );
};

export default Filter;
