// Helper function to convert File to base64
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
}

// Helper function to convert base64 to Blob
export function base64ToBlob(base64String: string): Blob {
  // Remove data URL prefix if it exists (e.g., "data:application/pdf;base64,")
  const base64WithoutPrefix = base64String.split(';base64,').pop() || base64String;

  // Convert base64 to raw binary data
  const binaryString = Buffer.from(base64WithoutPrefix, 'base64').toString('binary');

  // Convert binary to Uint8Array
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Create Blob from the Uint8Array
  return new Blob([bytes]);
}

// If you need to get the file type from the base64 string
export function getFileTypeFromBase64(base64String: string): string | null {
  const match = base64String.match(/^data:(.+);base64,/);
  return match ? match[1].split('/')[1] : null;
}
