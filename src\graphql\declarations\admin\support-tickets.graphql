query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {
  groupSupportTicketsCount(where: $where)
}

mutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {
  createGroupSupportMessage(data: $data) {
    id
  }
}

query Propay_admin_balance($input: Propay_admin_balanceInput!) {
  propay_admin_balance(input: $input) {
    balance
    itemCount
    positives
    positiveItemCount
    negatives
    negativeItemCount
    positiveUncounted
    positiveUncountedItemCount
    negativeUncounted
    negativeUncountedItemCount
    actualCount
    rangeStart
    rangeEnd
    propayBalance
  }
}
