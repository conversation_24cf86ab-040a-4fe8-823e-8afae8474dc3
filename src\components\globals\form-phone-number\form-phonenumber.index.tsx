import { ChangeEvent } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import PhoneInput, { CountryData } from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
import { cn } from '@/lib/utils';

export type FormPhoneNumberProps = {
  name: string;
  helperText?: string;
  id: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  visible?: boolean;
  readOnly?: boolean;
  className?: string;
  maxLength?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  onCountryChange?: (country: string) => void;
  label?: string;
  disabled?: boolean;
  defaultValue?: string;
  endAdornment?: React.ReactNode;
  onlyCountries?: string[];
  preferredCountries?: string[];
  excludeCountries?: string[];
  country?: string;
  autoFormat?: boolean;
  disableDropdown?: boolean;
  countryCodeEditable?: boolean;
};

export const FormPhoneNumber = ({
  id,
  name,
  label,
  rules,
  disabled,
  helperText = '',
  defaultValue,
  className,
  visible = true,
  readOnly = false,
  maxLength,
  tooltip,
  endAdornment,
  autoFormat = true,
  disableDropdown = false,
  onlyCountries,
  country,
  excludeCountries,
  preferredCountries,
  countryCodeEditable,
  onChangeCallback = (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    e;
  },
  onCountryChange,
  ...props
}: FormPhoneNumberProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || ''}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={cn('relative w-full', className)}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div>
            <div className="relative">
              {/* <p>{console.log(value)}</p> */}
              <PhoneInput
                inputProps={{ ref }}
                value={value}
                inputClass="!py-2.5 !text-sm !w-full bg-gray disabled:opacity-70"
                onChange={(phone, country) => {
                  if (maxLength && phone.length > maxLength) return;
                  // console.log(phone);
                  onChange(phone);
                  onChangeCallback(phone as any);
                  if (onCountryChange) {
                    onCountryChange((country as CountryData).dialCode);
                  }
                }}
                onBlur={(e, country) => {
                  onChange(e);
                  onBlur();
                }}
                disabled={isDisabled()}
                placeholder={`Enter ${label}`}
                country={country}
                onlyCountries={onlyCountries}
                countryCodeEditable={countryCodeEditable}
                disableDropdown={disableDropdown}
                autoFormat={autoFormat}
                excludeCountries={excludeCountries}
                preferredCountries={preferredCountries}
                {...props}
              />
              {endAdornment && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  {endAdornment}
                </div>
              )}
            </div>
            <HelperText color={invalid ? 'failure' : 'default'}>
              {invalid ? error?.message : helperText}{' '}
            </HelperText>
          </div>
        </div>
      )}
    />
  );
};

export default FormPhoneNumber;
