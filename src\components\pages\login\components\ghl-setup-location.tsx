import { AutoCompleteInput, AutoCompleteOption } from '@/components/globals';
import { Button } from '@/components/ui/button';
import { GHLIntegDetails, GHLSetupCommit } from '@/graphql/declarations/gohighlevel-setup';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { GetGroupListQuery } from '@/graphql/generated/graphql';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { Checkbox, HelperText, Label } from 'flowbite-react';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import { LoadingComponent } from '.';

export const GHLSetupLocation = () => {
  const query = useSearchParams();
  const [isChecked, setIsChecked] = useState(false);
  const [syncProducts, setSyncProducts] = useState(false);
  const [isProceeding, setIsProceeding] = useState(false);
  const handleCheckboxChange = () => setIsChecked(!isChecked);
  const handleSyncProductsChange = () => setSyncProducts(!syncProducts);
  const [data, setData] = useState<GetGroupListQuery['groups']>();
  const [selectedLocation, setSelectedLocation] = useState<AutoCompleteOption | null>(null);
  const [, setAppName] = useState<string | false>(false);

  const { data: installData } = useQuery(GHLIntegDetails, {
    variables: {
      input: {
        code: query?.get('code') || '',
      },
    },
    skip: !query?.get('code'),
    onCompleted: (data) => {
      setAppName(data?.ghl_auth_getIntegrationDetails?.locationName || false);
    },
    onError: () => {
      toast.error('Invalid code');
    },
  });

  const getLocationList = async () => {
    const locations = await apolloClient.query({
      query: GET_GROUPS_LIST,
      variables: {
        where: {
          mainGateway: {
            not: {
              // empty
              equals: '',
            },
          },
        },
      },
    });
    setData(locations.data.groups);
  };

  // Memoized options for the AutoCompleteInput
  const locationOption = useMemo<AutoCompleteOption[]>(() => {
    if (!data) return [];
    return data.map((group) => ({
      label: group.name ?? '',
      id: group.id ?? '',
    }));
  }, [data]);

  const handleSubmit = () => {
    installApp({ groupID: selectedLocation?.id ?? '' });
  };

  const installApp = async (args: { groupID: string }) => {
    const code = query?.get('code');

    if (!code) {
      return;
    }

    if (!args.groupID) {
      return;
    }

    const res = await apolloClient.mutate({
      mutation: GHLSetupCommit,
      variables: {
        input: {
          code: code,
          groupID: args.groupID,
          sync: syncProducts,
        },
      },
    });

    if (res.data?.ghl_auth_completeIntegration) {
      const expiryDate = Date.now() + 30 * 24 * 60 * 60 * 1000;
      localStorage.setItem('binding_expiry', expiryDate.toString());
      localStorage.setItem('location', JSON.stringify(selectedLocation));
      setIsProceeding(true);
    } else {
      toast.error('Failed to install app');
    }
  };

  useEffect(() => {
    getLocationList();
  }, []);

  return (
    <>
      {!isProceeding && (
        <div className="flex items-center justify-center bg-gray-100">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-md">
            <div className="mb-4 text-center">
              <Image
                src="/logo.webp"
                alt="NGnair Payments"
                width={200}
                height={50}
                className="mx-auto mb-4 h-12"
              />
              <h1 className="text-2xl font-bold text-gray-900">Select Location to Link</h1>
              <p className="text-sm text-gray-500">
                Linking your payment location will allow you to accept and manage payments from{' '}
                {installData?.ghl_auth_getIntegrationDetails?.locationName} in{' '}
                {selectedLocation?.label}
              </p>
            </div>

            <div className="mb-4">
              <Label
                htmlFor="locationName"
                className="mb-2 block text-left font-medium text-gray-700"
              >
                Location Name
              </Label>
              <AutoCompleteInput
                options={locationOption}
                value={selectedLocation}
                placeholder="Search Location"
                onChange={setSelectedLocation}
                inputValue={selectedLocation?.label ?? ''}
                className="w-full"
              />
            </div>

            <div className="mb-4 flex items-start">
              <Checkbox
                id="confirmation"
                className="mr-2"
                checked={isChecked}
                onChange={handleCheckboxChange}
              />
              <Label htmlFor="confirmation" className="text-sm text-gray-700">
                I confirm that I am linking {selectedLocation?.label} location to{' '}
                {installData?.ghl_auth_getIntegrationDetails?.locationName}.
              </Label>
            </div>

            <div className="mb-4 flex items-start">
              <Checkbox
                id="syncProducts"
                className="mr-2"
                checked={syncProducts}
                onChange={handleSyncProductsChange}
              />
              <Label htmlFor="syncProducts" className="text-sm text-gray-700">
                Sync my GHL shop products to NGNair
              </Label>
            </div>

            <Button
              type="submit"
              variant="primary"
              className="w-full rounded bg-blue-600 px-4 py-2 font-semibold text-white hover:bg-blue-700"
              disabled={!isChecked || !selectedLocation}
              onClick={handleSubmit}
            >
              Proceed
            </Button>
            <div className="mt-4 text-center text-sm">
              <HelperText>
                If you need help, contact{' '}
                <a href="#" className="text-blue-600">
                  NGnair Payments Support
                </a>
                .
              </HelperText>
            </div>
          </div>
        </div>
      )}

      {isProceeding && (
        <LoadingComponent
          location={selectedLocation}
          subAccountID={installData?.ghl_auth_getIntegrationDetails?.locationID}
        />
      )}
    </>
  );
};
