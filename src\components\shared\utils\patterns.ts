export const emailPatternRegex =
  /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
export const phonePatternRegex = /^\d*[1-9]\d*$/;

export const oneToHundredRegex = /\b((100)|[1-9]\d?)\b/;

export const oneToHundredNoDecimalRegex = /^(100|[1-9][0-9]?)$/;

export const onlyPositiveNumbersRegex = /^[0-9]*$/;

export const oneToThreeSixtySixRegex = /^(366|[1-9][0-9]?|[1-2][0-9]{2}|3[0-5][0-9]|36[0-6])$/;

export const urlPatternRegEx =
  /^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/;

export const datePatternRegex = /^(0[1-9]|[12][0-9]|3[01])[- /.](0[1-9]|1[012])[- /.](19|20)\d\d$/;

export const emailUsernamePatternRegex = /^[a-zA-Z0-9!#$%&'*+-/=?^_`|{]+$/;

export const nonAlphanumericRegex = /[^a-zA-Z0-9]/g;

export const cvvValidPattern = /^\d{3,4}$/;

export const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{1,3})?Z?$/;

/** Validates a string value against a regex pattern */
export const validateRegex = (value?: string | null, pattern?: RegExp): boolean => {
  if (!value || !pattern) return false;
  return pattern.test(value);
};
