import styled from '@emotion/styled';
import { Editor, type IAllProps } from '@tinymce/tinymce-react';

//NOTED: Temporary api
const API_KEY = 'zis0qx1ugem679yrdtrqb9goh29advaqtlzon6r9gkiv3ote';

const createFileInput = () => {
  const fileInput = document.createElement('input');
  fileInput.setAttribute('type', 'file');
  fileInput.setAttribute('accept', 'image/png, image/gif, image/jpeg, image/bmp, image/x-icon');
  fileInput.setAttribute('style', 'display: none;');
  return fileInput;
};

const readAsDataURL = (files: FileList, callback: (result: string, file: File) => void): void => {
  [].forEach.call(files, (file: File) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target) {
        callback(event.target.result as string, file);
      }
    };

    const blob = (file as any).getAsFile ? (file as any).getAsFile() : file;
    if (blob instanceof Blob) {
      reader.readAsDataURL(blob);
    }
  });
};

const Container = styled.div`
  .tox-statusbar__right-container {
    svg {
      display: none;
    }
  }
`;

export type HtmlEditorProps = IAllProps;

/** HtmlEditorWithButtonRow should be used instead if we expect big pictures as input.
 *
 * @see HtmlEditorWithButtonRow
 */
export const HtmlEditor = (props: HtmlEditorProps) => {
  const plugins =
    'advlist autolink lists link image charmap preview anchor searchreplace visualblocks fullscreen insertdatetime media table paste help wordcount hr';

  const toolbar = `undo redo | formatselect | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify |
    bullist numlist outdent indent | link image media | table | hr | removeformat | preview visualblocks fullscreen |
    searchreplace | help`;

  return (
    <Container>
      <Editor
        apiKey={API_KEY}
        {...props}
        init={{
          height: 500,
          menubar: false,
          plugins,
          toolbar,
          paste_data_images: true,
          image_title: true,
          automatic_uploads: false,
          file_picker_types: 'image',
          file_picker_callback: function (callback) {
            const fileInput = createFileInput();
            fileInput.addEventListener('change', () => {
              if (fileInput.files && fileInput.files.length > 0) {
                readAsDataURL(fileInput.files, (dataUri: string, file: File) => {
                  callback(dataUri, {
                    title: file.name,
                  });
                });
              }
            });
            fileInput.click();
          },
          ...props.init,
        }}
      />
    </Container>
  );
};

export default HtmlEditor;
