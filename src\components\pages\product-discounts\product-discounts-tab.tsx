import {
  <PERSON>Header,
  TopComponent,
  Status<PERSON>hip,
  StatusFilter,
  useDataGridView,
} from '@/components/globals';
import { Button } from 'flowbite-react';

import { useEffect, useMemo, useState } from 'react';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import { FaPlus } from 'react-icons/fa6';
import { Gateway_DiscountsDocument } from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import { DiscountStatus, getDiscountStatus } from '../cart-discounts/cart-discounts-tab';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export const ProductDiscountsTab = () => {
  const [statusFilter, setStatusFilter] = useState('All');
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({});

  const {
    data: discountListData,
    loading: discountListLoading,
    refetch: refetchdiscountListData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    maxVariables,
  } = useDataGridView({
    query: Gateway_DiscountsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            scope: '',
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refetchdiscountListData();
    }
  }, [locationFilter]);

  const rows = useMemo(() => {
    const data = discountListData?.gateway_discounts?.data;
    if (!data) return [];

    // First filter out nulls
    const nonNullData = data.filter((item): item is NonNullable<typeof item> => item !== null);

    // Then apply status filter if needed
    if (statusFilter !== 'All') {
      return nonNullData.filter((item) => item.status === statusFilter);
    }
    return nonNullData;
  }, [discountListData?.gateway_discounts?.data, statusFilter]);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_DiscountsDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_discounts?.data ?? [];
  };

  const columns: Column[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
    },
    {
      key: 'type',
      header: 'Type',
      sortable: true,
    },
    {
      key: 'discount',
      header: 'Amount',
      sortable: true,
      valueGetter: (row) => parseFloat(row.discount.toFixed(2)),
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getDiscountStatus(row.status);
        return <StatusChip variant={status} label={label} />;
      },
    },
  ];

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Product Discounts" />
        <Button
          className="mt-[20px] h-[38px] p-0"
          color="blue"
          // onClick={() => setIsModalOpen(true)}
        >
          <div className="flex items-center gap-x-3">
            <FaPlus className="text-xl" />
            <span>Add Product discounts</span>
          </div>
        </Button>
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
          <TopComponent value={searchValue} setValue={setSearchValue}>
            <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="discounts" />
          </TopComponent>
        </div>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={rows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={discountListLoading || loadingGroupList}
          actionComponent={
            <StatusFilter
              value={statusFilter}
              setValue={setStatusFilter}
              statusList={Object.values(DiscountStatus)}
            />
          }
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={discountListData?.gateway_discounts?.page?.total ?? 0}
        />
      </div>
    </>
  );
};
