import type { LucideIcon } from 'lucide-react';

interface InfoItemProps {
  icon: LucideIcon;
  label: string;
  value: string | number | undefined;
}

export const InfoItem = ({ icon: Icon, label, value }: InfoItemProps) => (
  <div className="flex items-center space-x-2">
    <Icon className="h-4 w-4 text-muted-foreground" />
    <div>
      <div className="text-sm font-medium text-muted-foreground">{label}</div>
      <div className="font-medium">{value || '-'}</div>
    </div>
  </div>
);
