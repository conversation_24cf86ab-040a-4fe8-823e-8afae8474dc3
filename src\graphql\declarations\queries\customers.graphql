query Gateway_customers($input: Gateway_customersInput!) {
  gateway_customers(input: $input) {
    data {
      id
      isDefault
      last4
      brand
      expires
      name
      city_state
      zip
      email
      phone
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}

query Gateway_customer($input: Gateway_customerInput!) {
  gateway_customer(input: $input) {
    id
    nameOnCard
    email
    phone
    billingAddress
    billingCity
    billingState
    billingZip
    billingCountry
    paymentCards {
      type
      cardID
      isDefault
      last4
      brand
      expires

      accountNumber
      routingNumber
      accountType
      accountHolderType

      gpEcommID
    }
  }
}

query GetCustomerListByLocationForDropdown($input: Gateway_customersInput!) {
  gateway_customers(input: $input) {
    data {
      id
      name
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}
