'use client';

import { PageHeader } from '@/components/globals';
import { useLocationSelector } from '@/components/hooks';
import { PaymentLineItem } from '@/components/Payment';
import { OrderItems } from '@/components/Payment/OrderItems';
import { PriceBreakdown } from '@/components/Payment/PriceBreakdown';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Paylink_delete, Paylink_get } from '@/graphql/declarations/paylinks';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { capitalize } from '@mui/material';
import { useParams } from 'next/navigation';
import { useMemo } from 'react';
import { int2DecToFloat, moneyFormat } from '@/lib/utils';

// Component to display recurring payment information
const RecurringPaymentInfo = ({ lineItems }) => {
  if (!lineItems || lineItems.length === 0) return null;

  const recurringItems = lineItems.filter((item) => item.isRecurring);
  if (recurringItems.length === 0) return null;

  return (
    <div className="mt-4 border-t pt-4">
      <h3 className="mb-3 font-bold">Recurring Payment Information:</h3>
      {recurringItems.map((item, index) => (
        <div key={`${item.id}-${index}`} className="mb-2 rounded border border-gray-200 p-2">
          <p className="font-semibold">{item.name}</p>
          {item.recurringMode === 'MONTHLY' ? (
            <p className="text-sm text-blue-600">
              Monthly Subscription - {moneyFormat(int2DecToFloat(item.price))} per month
            </p>
          ) : item.recurringMode === 'YEARLY' ? (
            <p className="text-sm text-blue-600">
              Yearly Subscription - {moneyFormat(int2DecToFloat(item.price))} per year
            </p>
          ) : item.recurringMode ? (
            <p className="text-sm text-blue-600">
              Recurring: {item.recurringMode}
              {item.recurringFrequency > 0 &&
                ` (Every ${item.recurringFrequency} ${item.recurringInterval === 1 ? 'day' : 'days'})`}
            </p>
          ) : (
            <p className="text-sm text-blue-600">
              Recurring Payment
              {item.recurringFrequency > 0 &&
                ` (Every ${item.recurringFrequency} ${item.recurringInterval === 1 ? 'day' : 'days'})`}
            </p>
          )}
          {item.recurringSetupFee > 0 && (
            <p className="text-sm">
              Setup Fee: {moneyFormat(int2DecToFloat(item.recurringSetupFee))}
            </p>
          )}
          {item.recurringTotalCycles > 0 && (
            <p className="text-sm">Total Cycles: {item.recurringTotalCycles}</p>
          )}
          {item.recurringTrialDays > 0 && (
            <p className="text-sm">Trial Period: {item.recurringTrialDays} days</p>
          )}
        </div>
      ))}
    </div>
  );
};

export default function PaylinkDetailPage() {
  const params = useParams();

  const { locationFilter } = useLocationSelector({});
  const groupID = locationFilter?.id ?? '';
  const id = (params?.id ?? '') as string;
  const { data, loading } = useQuery(Paylink_get, {
    variables: {
      input: {
        groupID,
        data: {
          id,
        },
      },
    },
    skip: locationFilter?.id && id ? false : true,
  });

  const valuesToDisplay = useMemo(() => {
    let paymentCheck = data?.gateway_payLink?.paymentCheck;
    if (!paymentCheck) return {};

    const breakdowns = {};
    let aaa = paymentCheck.breakdown ?? {};
    for (const breakdown in aaa) {
      if (['__typename', 'total'].includes(breakdown)) continue;
      breakdowns[capitalize(breakdown)] = aaa[breakdown];
    }

    const lineItems: PaymentLineItem[] = [];
    for (const lineItem of paymentCheck.lineItems ?? []) {
      const recurringMode = lineItem?.product?.recurringMode ?? '';
      // Handle special recurring modes
      const formattedRecurringMode =
        recurringMode === 'MONTHLY' || recurringMode === 'YEARLY'
          ? recurringMode
          : (lineItem?.product?.recurringMode ?? '');

      lineItems.push({
        name: lineItem?.product?.name ?? '',
        price: lineItem?.product?.price ?? 0,
        amount: lineItem?.amount ?? 0,
        total: lineItem?.total ?? 0,
        description: lineItem?.product?.description ?? '',
        id: lineItem?.productId ?? '',
        isRecurring: lineItem?.product?.isRecurring ?? false,
        recurringMode: formattedRecurringMode,
        recurringFrequency: lineItem?.product?.recurringFrequency ?? 0,
        recurringInterval: lineItem?.product?.recurringInterval ?? 0,
        recurringSetupFee: lineItem?.product?.recurringSetupFee ?? 0,
        recurringTotalCycles: lineItem?.product?.recurringTotalCycles ?? 0,
        recurringTrialDays: lineItem?.product?.recurringTrialDays ?? 0,
      });
    }

    return {
      total: paymentCheck.breakdown?.total,
      subscriptionTotal: paymentCheck.breakdown?.subscriptionTotal,
      calculations: breakdowns,
      lineItems: lineItems,
    };
  }, [data]);

  const metaValues = useMemo(() => {
    const paymentRaw = data?.gateway_payLink?.paymentRaw ?? {};
    const toCheck = ['allowEdit', 'allowExtraDiscount', 'allowTip', 'onSuccessUrl', 'onFailureUrl'];

    const metaValues: {
      [key: string]: any;
    } = {};
    for (const key of toCheck) {
      metaValues[capitalize(key)] = paymentRaw[key];
    }

    return metaValues;
  }, [data]);

  const onURLCopy = () => {
    const url = `${window.location.origin}/pay?groupID=${groupID}&paymentDataID=${id}`;
    navigator.clipboard.writeText(url);
    alert('Payment link copied to clipboard');
  };

  const onURLOpen = () => {
    const url = `${window.location.origin}/pay?groupID=${groupID}&paymentDataID=${id}`;
    window.open(url, '_blank');
  };

  const onDelete = async (e) => {
    e.preventDefault();
    await apolloClient.mutate({
      mutation: Paylink_delete,
      variables: {
        input: {
          groupID,
          data: {
            id,
          },
        },
      },
    });

    window.location.href = '/dashboard/catalog/paylink';
  };

  const onBack = () => {
    window.history.back();
  };

  if (!id) return <div>Invalid ID</div>;

  if (loading) return <div>Loading...</div>;

  return (
    <div className="px-8">
      <PageHeader text={`Payment Link`} />
      <div className="flex flex-col gap-2 md:flex-row">
        <Card className="flex-[2]">
          {/* Price Breakdown */}
          <PriceBreakdown
            calculations={valuesToDisplay.calculations || {}}
            total={valuesToDisplay.total ?? 0}
            subscriptionTotal={valuesToDisplay.subscriptionTotal ?? 0}
          />

          {/* Order Items */}
          <OrderItems lineItems={valuesToDisplay.lineItems || []} currentAmount={{}} />
        </Card>
        <Card className="flex-1 p-4">
          <div className="flex flex-col gap-2">
            <h2>
              Payment Link: <span className="text-blue-500">{data?.gateway_payLink?.id}</span>
            </h2>
            <h3 className="my-3 font-bold">Metadata:</h3>

            {Object.keys(metaValues).map((key) => (
              <div key={key} className="flex justify-between border-b text-sm">
                <span>{key}</span>
                <span>{metaValues[key] ? 'Applied' : '--'}</span>
              </div>
            ))}

            {/* Add Recurring Payment Information */}
            <RecurringPaymentInfo lineItems={valuesToDisplay.lineItems || []} />

            <h3 className="my-3 font-bold">Actions:</h3>

            <Button variant="primary" className="w-full" size="sm" onClick={onURLOpen}>
              Open Pay Link
            </Button>
            <Button
              variant="primary"
              size="sm"
              className="w-full bg-primary-500"
              onClick={onURLCopy}
            >
              Copy Link
            </Button>
            <Button variant="destructive" size="sm" className="w-full" onClick={onDelete}>
              Delete
            </Button>
            <Button variant="outline" size="sm" className="w-full border-gray-600" onClick={onBack}>
              Back
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}
