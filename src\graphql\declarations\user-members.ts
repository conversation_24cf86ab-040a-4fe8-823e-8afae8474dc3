import { graphql } from '../generated';

export const UserMembersList = graphql(`
  query GroupMembers($where: GroupMemberWhereInput!, $take: Int, $skip: Int!) {
    groupMembers(where: $where, take: $take, skip: $skip) {
      id
      access
      group {
        id
        name
      }
      invite {
        id
        email
      }
      user {
        id
        lastLogin
        name
        lastName
        createdAt
        email
      }
    }
    groupMembersCount(where: $where)
  }
`);

export const GetQuickUserInfo = graphql(`
  query GetQuickUserInfo($where: UserWhereUniqueInput!) {
    user(where: $where) {
      id
      name
      lastName
      phone
      email
      title
    }
  }
`);

export const GetQuickInviteInfo = graphql(`
  query GetQuickInviteInfo($where: GroupMemberInviteWhereUniqueInput!) {
    groupMemberInvite(where: $where) {
      id
      email
    }
  }
`);

export const GetUserMembershipInfo = graphql(`
  query GetUserMembershipInfo($take: Int, $where: GroupMemberWhereInput!) {
    groupMembers(take: $take, where: $where) {
      access
      flags {
        id
        flag
      }
      id
      invite {
        id
        email
      }
      user {
        id
        email
      }
      group {
        id
      }
    }
  }
`);

export const GetFlags = graphql(`
  query Flags($input: Group_flagsInput!) {
    group_flags(input: $input) {
      flags {
        name
        key
        category
        description
      }
    }
  }
`);

export const UpdateGroupMemberData = graphql(`
  mutation UpdateGroupMemberData(
    $where: GroupMemberWhereUniqueInput!
    $updateBaseMemberData: GroupMemberUpdateInput!
    $createMemberFlags: [GroupMemberFlagCreateInput!]!
    $deleteMemberFlags: [GroupMemberFlagWhereUniqueInput!]!
  ) {
    updateGroupMember(where: $where, data: $updateBaseMemberData) {
      id
    }
    createGroupMemberFlags(data: $createMemberFlags) {
      id
    }
    deleteGroupMemberFlags(where: $deleteMemberFlags) {
      id
    }
  }
`);

export const CreatGroupMemberData = graphql(`
  mutation CreatGroupMemberData($input: Group_createInviteInput!) {
    group_createInvite(input: $input) {
      id
    }
  }
`);

export const DeleteGroupMembers = graphql(`
  mutation DeleteGroupMembers($where: [GroupMemberWhereUniqueInput!]!) {
    deleteGroupMembers(where: $where) {
      id
    }
  }
`);

export const GET_BANK_ACCOUNT_INFO = graphql(`
  query Processor_account_status($input: Processor_account_statusInput!) {
    processor_account_status(input: $input) {
      bank {
        holder_type
        last4
        bank_code
        bank_name
      }
      status
      status2
      pricing_profile
    }
  }
`);

export const UPDATE_BANK_ACCOUNT_INFO = graphql(`
  mutation Processor_account_payoutUpdate($input: Processor_account_payoutUpdateInput!) {
    processor_account_payoutUpdate(input: $input) {
      submissionID
      groupID
    }
  }
`);
