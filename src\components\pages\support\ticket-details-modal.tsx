'use client';

import { Modal } from 'flowbite-react';
import { mockSupportTickets } from '@/lib/mock/support-mock';
import { HtmlEditorMdx } from '@/components/globals/html-editor/html-editor-mdx';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Paperclip } from 'lucide-react';
import { headingsPlugin } from '@mdxeditor/editor';

interface TicketDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  ticketId: string | null;
}

export function TicketDetailsModal({ isOpen, onClose, ticketId }: TicketDetailsModalProps) {
  if (!ticketId) {
    return null;
  }

  const ticket = mockSupportTickets.find((t) => t.id === ticketId);

  if (!ticket) {
    return (
      <Modal show={isOpen} onClose={onClose} size="4xl">
        <Modal.Header className="border-b">
          <div className="text-lg">No ticket found</div>
        </Modal.Header>
        <Modal.Body>Ticket not found</Modal.Body>
      </Modal>
    );
  }

  return (
    <Modal show={isOpen} onClose={onClose} size="4xl">
      <Modal.Header className="border-b">
        <div className="flex w-full justify-between">
          <div>
            <div className="text-lg">{ticket.title}</div>
            <div className="text-sm text-muted-foreground">Case #{ticket.id}</div>
          </div>
          <div>
            <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
              {ticket.status}
            </span>
          </div>
        </div>
      </Modal.Header>

      <Modal.Body>
        <div className="space-y-6">
          <div>
            <h3 className="mb-2 text-base font-medium">Description</h3>
            <HtmlEditorMdx
              markdown={ticket.description}
              className="prose prose-sm dark:prose-invert"
              plugins={[headingsPlugin()]}
            />
          </div>

          <div>
            <h3 className="mb-2 text-base font-medium">Messages</h3>
            <div className="space-y-4">
              {ticket.messages.map((message) => (
                <div key={message.id} className="border-b pb-4 last:border-0">
                  <div className="flex items-center space-x-2">
                    <Avatar>
                      <AvatarFallback>
                        {message.sender
                          ?.split(' ')
                          ?.map((name) => name[0])
                          .join('') ?? 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="font-medium">{message.sender}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(message.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  <div className="mt-2 rounded-lg bg-gray-50 px-2 py-3">
                    <HtmlEditorMdx
                      markdown={message.message}
                      className="prose prose-sm dark:prose-invert"
                      plugins={[headingsPlugin()]}
                    />

                    {message.files?.length > 0 && (
                      <div className="mt-2 border-t pt-2">
                        {message.files.map((file, index) => (
                          <a
                            key={index}
                            href={file.url}
                            className="flex items-center gap-2 text-sm text-blue-600 hover:underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Paperclip className="h-4 w-4" />
                            {file.filename}
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
}
