import {
  AutoCompleteOption,
  FormAutoComplete,
  FormDatepicker,
  FormInput,
  FormSelect,
} from '@/components/globals';
import { useCustomerSelector, useLocationSelector } from '@/components/hooks';
import { message, requiredAmountIsValid } from '@/components/shared/utils';
import {
  Gateway_CustomerDocument,
  GatewayUniCustomerOutputPaymentCards,
} from '@/graphql/generated/graphql';
import { Card, Button } from 'flowbite-react';
import { useEffect, useMemo, useState } from 'react';
import { FormProvider, UseFormReturn } from 'react-hook-form';
import { PaymentFormData } from '../utils';
import { useQuery } from '@apollo/client';
import moment from 'moment';

type PaymentPlanFormProps = {
  methods: UseFormReturn<PaymentFormData, any, undefined>;
  onSubmitForm: (data: PaymentFormData) => Promise<void>;
  isEdit?: boolean;
  cancelSubscription?: () => void;
};

export const PaymentPlanForm = ({
  methods,
  onSubmitForm,
  isEdit,
  cancelSubscription,
}: PaymentPlanFormProps) => {
  const { locationFilter, locationSelectorElement } = useLocationSelector({
    readonly: true,
    onlyActive: true,
  });

  const { setValue, watch } = methods;

  const watchValue = watch();

  const {
    data: customerData,
    loading: customerDataLoading,
    refetch: refetchCustomerData,
  } = useQuery(Gateway_CustomerDocument, {
    variables: {
      input: {
        data: {
          customerID: watchValue.customerID ?? '',
        },
        groupID: locationFilter?.id ?? '',
      },
    },
    skip: !watchValue.customerID || !locationFilter?.id,
  });

  const [customerSearchText, setCustomerSearchText] = useState<string | undefined>();
  const { customerOptions } = useCustomerSelector({
    groupdId: locationFilter?.id ?? '',
    preselectedID: customerSearchText ?? watchValue.customerID,
    canPay: true,
  });

  const cardOption = useMemo((): AutoCompleteOption<GatewayUniCustomerOutputPaymentCards>[] => {
    if (!customerData) return [] as AutoCompleteOption<GatewayUniCustomerOutputPaymentCards>[];
    return customerData?.gateway_customer?.paymentCards?.map((card) => {
      return {
        id: card?.cardID,
        label: card?.last4 || card?.accountNumber,
        obj: card,
      };
    }) as AutoCompleteOption<GatewayUniCustomerOutputPaymentCards>[];
  }, [customerData?.gateway_customer?.paymentCards]);

  useEffect(() => {
    if (customerOptions) {
      const _selectedCustomer = customerOptions.find((item) => item.id === watchValue.customerID);
      // console.log('customerData', _selectedCustomer);
      if (_selectedCustomer) {
        if (watchValue.customerID !== _selectedCustomer.id) {
          setValue('customer', {
            id: _selectedCustomer.id,
            label: _selectedCustomer.label,
            obj: _selectedCustomer.obj,
          });
        }
      }
    }
  }, [customerOptions, watchValue]);

  // useEffect(() => {
  //   if (cardOption) {
  //     const _selectedCard = cardOption.find((item) => item.id === watchValue.paymentID);
  //     console.log('paymentData', _selectedCard);
  //     if (_selectedCard) {
  //       setValue('card', {
  //         id: _selectedCard.id,
  //         label: _selectedCard.label,
  //         obj: _selectedCard.obj,
  //       });
  //     }
  //   }
  // }, [cardOption, watchValue.paymentID]);

  // useEffect(() => {
  //   const selectedCustomer = customerOptions.find((item) => item.id === watchValue.customer?.id);
  //   if (selectedCustomer) {
  //     setValue('customer', {
  //       id: selectedCustomer.id,
  //       label: selectedCustomer.label,
  //       obj: selectedCustomer.obj,
  //     });
  //   }
  // }, [watchValue]);

  useEffect(() => {
    if (watchValue.customerID) {
      refetchCustomerData();
    }
    // setValue('card', null);
  }, [watchValue.customerID]);

  const presetCreate = () => {
    setValue('startDate', new Date().toISOString());
    setValue('paymentInterval', '30');
    setValue('amount', 1);
    setValue('planName', `New Plan ${moment().format('MM/DD/YYYY')}`);
    setValue('customerID', '');
    setValue('paymentID', '');
  };

  useEffect(() => {
    if (!isEdit) {
      presetCreate();
    }
  }, []);

  return (
    <div>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmitForm)} className="space-y-4">
          <div className="w-full md:w-1/4">{locationSelectorElement}</div>
          <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
            <Card className="col-span-2">
              <div className="flex w-full gap-2">
                <FormAutoComplete
                  id="customerID"
                  name="customerID"
                  label="Customer"
                  options={[...customerOptions]}
                  // defaultValue={customerOptions?.find(
                  //   (item) => item.id === watchValue.customer?.id,
                  // )}
                  rules={{ required: message.requiredField }}
                  onChangeStrategy="id"
                  initialText={
                    customerOptions?.find((item) => item.id === watchValue.customerID)?.label || ''
                  }
                  onTextChange={(text) => setCustomerSearchText(text)}
                />
                {/* <p>
                  Val:{' '}
                  {JSON.stringify(
                    customerOptions?.find((item) => item.id === watchValue.customer?.id),
                  )}
                </p> */}
                <FormAutoComplete
                  id="paymentID"
                  name="paymentID"
                  label="Card"
                  options={[...(cardOption ?? [])]}
                  optionsLoading={customerDataLoading}
                  rules={{ required: message.requiredField }}
                  onChangeStrategy="id"
                  initialText={
                    cardOption?.find((item) => item.id === watchValue.paymentID)?.label || ''
                  }
                />
              </div>
              {/* <p>{JSON.stringify(watchValue)}</p> */}
            </Card>
            <Card className="col-span-3 space-y-4">
              <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                <div className="col-span-2 flex w-full gap-2">
                  <FormInput
                    id="planName"
                    name="planName"
                    label="Plan Name"
                    rules={{ required: message.requiredField }}
                  />
                  <FormInput
                    id="amount"
                    name="amount"
                    label="Amount"
                    type="number"
                    rules={requiredAmountIsValid}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                <FormDatepicker
                  id="startDate"
                  name="startDate"
                  label="Start Date"
                  rules={{ required: message.requiredField }}
                />
                <FormSelect
                  id="paymentInterval"
                  name="paymentInterval"
                  label="Payment Interval"
                  rules={{ required: message.requiredField }}
                  options={[
                    {
                      label: 'Daily',
                      value: '1',
                    },
                    {
                      label: 'Weekly',
                      value: '7',
                    },
                    {
                      label: 'Monthly',
                      value: '30',
                    },
                    {
                      label: 'Yearly',
                      value: '365',
                    },
                    {
                      label: 'Custom',
                      value: '-1',
                    },
                  ]}
                />
              </div>

              <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                <FormDatepicker
                  id="endDate"
                  name="endDate"
                  label="End Date"
                  // rules={{ required: message.requiredField }}
                />
                <FormSelect
                  id="recurringMode"
                  name="recurringMode"
                  label="Recurring Mode"
                  options={[
                    { label: 'By Day (every n Days)', value: '' },
                    { label: 'By Month (nth of Month)', value: 'MONTHLY' },
                    { label: 'By Year (nth of Year)', value: 'YEARLY' },
                  ]}
                />
                <FormInput
                  id="paymentEvery"
                  name="paymentEvery"
                  label="Payment Frequency"
                  type="number"
                  rules={{
                    required: message.requiredField,
                  }}
                  helperText="(Pay every interval (1), or every other interval (2), or so on)"
                />
              </div>

              {/* Add information about monthly/yearly payments */}
              <div className="rounded-lg border border-blue-100 bg-blue-50 p-3 text-sm text-blue-800">
                <p>
                  <strong>Note:</strong> When using Monthly or Yearly mode, the customer will be
                  charged on the same day of the month/year as the initial payment.
                </p>
                <p className="mt-1">
                  For payments starting on the 29th-31st, the payment will adjust to the last day of
                  shorter months to accommodate different month lengths and leap years.
                </p>
                <p className="mt-1">
                  <strong>For yearly payments:</strong> If a payment was started on February 29th
                  (leap year), it will be automatically adjusted to February 28th in non-leap years.
                </p>
              </div>

              <div className="flex flex-col gap-2">
                <h3 className="text-lg font-semibold">Quick Set End Date (based of Start Date)</h3>
                <div className="flex gap-2">
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.startDate).add(1, 'week').add(1, 'day').toISOString(),
                      )
                    }
                  >
                    1 Week
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.startDate).add(2, 'weeks').add(1, 'day').toISOString(),
                      )
                    }
                  >
                    2 Weeks
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.startDate).add(1, 'month').add(1, 'day').toISOString(),
                      )
                    }
                  >
                    1 Month
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.startDate).add(3, 'months').add(1, 'day').toISOString(),
                      )
                    }
                  >
                    3 Months
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.startDate).add(6, 'months').add(1, 'day').toISOString(),
                      )
                    }
                  >
                    6 Months
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.startDate).add(1, 'year').add(1, 'day').toISOString(),
                      )
                    }
                  >
                    1 Year
                  </Button>
                </div>
                <h3 className="text-lg font-semibold">Adjust End Date</h3>
                <div className="flex gap-2">
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.endDate).subtract(1, 'month').toISOString(),
                      )
                    }
                  >
                    -1 Month
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.endDate).subtract(1, 'week').toISOString(),
                      )
                    }
                  >
                    -1 Week
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue(
                        'endDate',
                        moment(watchValue.endDate).subtract(1, 'day').toISOString(),
                      )
                    }
                  >
                    -1 Day
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue('endDate', moment(watchValue.endDate).add(1, 'day').toISOString())
                    }
                  >
                    +1 Day
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue('endDate', moment(watchValue.endDate).add(1, 'week').toISOString())
                    }
                  >
                    +1 Week
                  </Button>
                  <Button
                    color="blue"
                    size="xs"
                    className="h-fit"
                    onClick={() =>
                      setValue('endDate', moment(watchValue.endDate).add(1, 'month').toISOString())
                    }
                  >
                    +1 Month
                  </Button>
                </div>
              </div>
            </Card>
          </div>
          <div className="flex justify-between">
            <Button type="submit" color="blue">
              {isEdit ? 'Update' : 'Add'} Payment Plan
            </Button>
            {isEdit && (
              <Button
                type="reset"
                color="red"
                className="bg-red-600 text-white"
                onClick={cancelSubscription}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </FormProvider>
    </div>
  );
};
