export function SimpleTabs(props: {
  pages: {
    id: string;
    title: string;
    content: React.ReactNode;
  }[];
}) {
  return (
    <>
      <div className="mb-4 w-full border-b border-gray-200 dark:border-gray-700">
        <ul
          className="-mb-px flex flex-wrap text-center text-sm font-medium"
          id="default-styled-tab"
          data-tabs-toggle="#default-styled-tab-content"
          data-tabs-active-classes="text-purple-600 hover:text-purple-600 dark:text-purple-500 dark:hover:text-purple-500 border-purple-600 dark:border-purple-500"
          data-tabs-inactive-classes="dark:border-transparent text-gray-500 hover:text-gray-600 dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300"
          role="tablist"
        >
          {props.pages.map((page) => (
            <li className="me-2" role="presentation" key={page.id}>
              <button
                className="inline-block rounded-t-lg border-b-2 p-4"
                id={`${page.id}-styled-tab`}
                data-tabs-target={`#${page.id}`}
                type="button"
                role="tab"
                aria-controls={page.id}
                aria-selected="false"
              >
                {page.title}
              </button>
            </li>
          ))}
        </ul>
      </div>
      <div id="default-styled-tab-content w-full">
        {props.pages.map((page) => (
          <div
            className="hidden rounded-lg bg-gray-50 p-4 dark:bg-gray-800"
            id={page.id}
            role="tabpanel"
            aria-labelledby={`${page.id}-styled-tab`}
            key={page.id}
          >
            {page.content}
          </div>
        ))}
      </div>
    </>
  );
}
