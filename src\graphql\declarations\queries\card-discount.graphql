query Gateway_discounts($input: Gateway_discountsInput!) {
  gateway_discounts(input: $input) {
    data {
      id
      name
      type
      discount
      status
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}

query Gateway_discount($input: Gateway_discountInput!) {
  gateway_discount(input: $input) {
    id
    name
    type
    discount
    status
  }
}
