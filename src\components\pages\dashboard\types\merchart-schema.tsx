import { z } from 'zod';
import { UseFormReturn, FieldValues, UseFormProps } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

export enum MerchantFormState {
  BusinessInformation,
  Transactions,
  Ownership,
  Banking,
  Documents,
  Review,
}

export const formSteps = [
  {
    count: 1,
    label: 'Business Information',
    step: MerchantFormState.BusinessInformation,
  },
  {
    count: 2,
    label: 'Transactions',
    step: MerchantFormState.Transactions,
  },
  { count: 3, label: 'Ownership', step: MerchantFormState.Ownership },
  { count: 4, label: 'Banking Information', step: MerchantFormState.Banking },
  { count: 5, label: 'Documents', step: MerchantFormState.Documents },
  { count: 6, label: 'Review', step: MerchantFormState.Review },
];

const merchantInfoSchema = z.object({
  // Business Information

  legal_business_name: z.string().min(1, 'Legal business name is required'),
  type_of_business: z.object({
    mccCode: z.string().min(1),
    description: z.string().min(1),
  }),
  mccObject: z.object({
    id: z.string().min(1),
    mccCode: z.string().min(1),
    description: z.string().min(1),
  }),
  dba_name: z.string().min(1, 'DBA name is required'),
  ein: z
    .string()
    .refine((value) => value.length === 10 || value.length === 0, 'EIN must be 9 digits'),
  business_established_date: z.string(),
  website_link: z.string().url('Please enter a valid URL').optional(),
  business_email: z.string().email(),
  country_number: z.string().optional(),
  country_number2: z.string().optional(),
  dial_code: z.string().min(1),
  dial_code2: z.string().min(1),
  phone_number: z.string(),
  phone_number2: z.string(),
  zip_code: z
    .string()
    .min(1)
    .refine((length) => length.length >= 5 && length.length <= 9),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  street: z.string().min(1, 'Street is required'),
  use_different_legal_email: z.boolean(),
  diff_zip_code: z
    .string()
    .min(1)
    .refine((length) => length.length >= 5 && length.length <= 9),
  diff_city: z.string().optional(),
  diff_state: z.string().optional(),
  diff_street: z.string().optional(),
  // transactions

  business_catergory: z.string().min(1, 'Business catergory is required'),
  description_of_what_you_sell: z.string().min(1, 'Description of what you sell'),
  swipe: z.string().min(1, 'Swipe is required'),
  keyed: z.string().min(1, 'Keyed is required'),
  ecommerce: z.string().min(1, 'Ecommerce is required'),
  vmd_average_transaction_amount: z.string().min(1, 'Average transaction amount is required'),
  vmd_highest_transaction_amount: z.string().min(1, 'Highest transaction amount is required'),
  vmd_gross_monthly_sales_volume: z.string().min(1, 'Gross monthly sales volume is required'),
  amex_average_transaction_amount: z.string().min(1, 'Average transaction amount is required'),
  amex_highest_transaction_amount: z.string().min(1, 'Highest transaction amount is required'),
  amex_gross_monthly_sales_volume: z.string().min(1, 'Gross monthly sales volume is required'),

  // onwership
  is_an_individual: z.boolean(),
  ownership: z
    .array(
      z.object({
        first_name: z.string().min(1, 'First name is required'),
        last_name: z.string().min(1, 'Last name is required'),
        position: z.string().min(1, 'Position is required'),
        ownership: z.string().min(1, 'Ownership percentage is required'),
        dial_code: z.string().min(1),
        phone_number: z.string(),
        home_address: z.string().min(1, 'Home address is required'),
        country: z.string().min(1, 'Country is required'),
        state: z.string().min(1, 'State is required'),
        city: z.string().min(1, 'City is required'),
        zip_code: z
          .string()
          .min(1, 'Zip code is required')
          .refine((length) => length.length >= 5 && length.length <= 9),
        ssn: z.string(),
        date_of_birth: z.string().nullable(),
        email: z.string().email('Valid email is required'),
      }),
    )
    .min(1, 'At least one owner is required')
    .max(4, 'No more than 4 owners are allowed'),

  // banking information

  routing_number: z.string().min(1, 'Routing number is required'),
  account_number: z.string().min(1, 'Account number is required'),
  name_on_account: z.string().min(1, 'Name on account is required'),
  bank_name: z.string().min(1, 'Bank name is required'),

  // documents

  documents: z
    .array(
      z.object({
        document_type: z.string().min(1, 'Document type is required'),
        file: z.any(),
      }),
    )
    .optional(),
});

export const merchantSchema = merchantInfoSchema;

export type MerchantFormValues = z.infer<typeof merchantSchema>;

export type MerchantForm<
  TFieldValues extends FieldValues = MerchantFormValues,
  TContext = any,
> = UseFormReturn<TFieldValues, TContext>;

export interface UseMerchantFormProps extends UseFormProps<MerchantFormValues> {}

export const useMerchantForm = (props?: UseMerchantFormProps): MerchantForm => {
  return useForm<MerchantFormValues>({
    resolver: zodResolver(merchantSchema),
    ...props,
  });
};

export default merchantSchema;
