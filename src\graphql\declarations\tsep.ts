import { graphql } from '../generated';

export const TsepManifest = graphql(`
  mutation TsepManifest {
    tsep_manifest {
      deviceID
      manifestKey
      url
    }
  }
`);

export const TsepVerify = graphql(`
  mutation TsepVerify($input: Tsep_verifyInput!) {
    tsep_verify(input: $input) {
      status
      code
      message
      authCode
      cardType
      addressVerificationCode
      maskedCardNumber
      expirationDate
    }
  }
`);

export const GPHFToken = graphql(`
  mutation GPHFToken($input: Gphf_tokenInput!) {
    gphf_token(input: $input) {
      token
      merchantID
    }
  }
`);
