query Dashboard_get_summary($input: Dashboard_get_summaryInput!) {
  dashboard_get_summary(input: $input) {
    dateStart
    dateEnd
    totalPortfolio
    captured {
      total
      percentageChange
      timeStart
      timeEnd
      timeEntries {
        time
        value
      }
    }
    refunds {
      total
      percentageChange
      timeStart
      timeEnd
      timeEntries {
        time
        value
      }
    }
    batched {
      total
      percentageChange
      timeStart
      timeEnd
      timeEntries {
        time
        value
      }
    }
    deposits {
      total
      percentageChange
      timeStart
      timeEnd
      timeEntries {
        time
        value
      }
    }
    disputes {
      total
      percentageChange
      timeStart
      timeEnd
      timeEntries {
        time
        value
      }
    }
    earnings {
      total
      percentageChange
      timeStart
      timeEnd
      timeEntries {
        time
        value
      }
    }
  }
}

query Dashboard_location_summary($input: Dashboard_location_summaryInput!) {
  dashboard_location_summary(input: $input) {
    data {
      locationID
      locationName
      changePercentage
      currentYearTotal
      lastYearTotal
      yearChangePercentage
    }
  }
}

query Gateway_accountFunds($input: Gateway_accountFundsInput!) {
  gateway_accountFunds(input: $input) {
    card_transaction_limit_amount
    card_monthly_limit_amount
    bank_transfer_per_transaction_limit_amount
    bank_transfer_monthly_limit_amount
    bank_transfer_month_to_date_processed_amount
    card_month_to_date_processed_amount
    available_balance_amount
    pending_balance_amount
    reserve_balance_amount
    allowed_negative_balance_amount
    disable_payout_card_limit_percentage
    disable_payout_bank_transfer_limit_percentage
    card_month_to_date_processed_percentage
    bank_transfer_month_to_date_processed_percentage
  }
}
