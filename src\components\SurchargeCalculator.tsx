'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';

export function SurchargeCalculator() {
  const [amount, setAmount] = useState('');
  const [paymentType, setPaymentType] = useState<'card' | 'ach'>('card');
  const [customerPays, setCustomerPays] = useState(false);

  const calculateSurcharge = (baseAmount: number) => {
    if (paymentType === 'card') {
      return baseAmount * 0.03;
    } else {
      if (baseAmount <= 1667) {
        return Math.min(baseAmount * 0.006, 10);
      } else {
        const baseFee = 10;
        const extraAmount = baseAmount - 1667;
        const extraFee = extraAmount * 0.001;
        return baseFee + extraFee;
      }
    }
  };

  const formattedAmount = amount ? parseFloat(amount) : 0;
  const surcharge = calculateSurcharge(formattedAmount);
  const total = customerPays ? formattedAmount + surcharge : formattedAmount;
  const netEarn = customerPays ? formattedAmount : formattedAmount - surcharge;

  return (
    <div className="space-y-4 rounded-lg border p-4">
      <div className="flex items-center justify-between">
        <h4 className="font-semibold">Surcharge Calculator</h4>
        <div className="flex items-center space-x-2">
          <Label htmlFor="customerPays">Customer pays surcharge</Label>
          <Switch id="customerPays" checked={customerPays} onCheckedChange={setCustomerPays} />
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex space-x-4">
          <Button
            variant={paymentType === 'card' ? 'default' : 'outline'}
            onClick={() => setPaymentType('card')}
          >
            Card Payment
          </Button>
          <Button
            variant={paymentType === 'ach' ? 'default' : 'outline'}
            onClick={() => setPaymentType('ach')}
          >
            ACH Payment
          </Button>
        </div>

        <div className="grid w-full items-center gap-1.5">
          <Label htmlFor="amount">Transaction Amount</Label>
          <div className="relative">
            <span className="absolute left-3 top-2.5">$</span>
            <Input
              id="amount"
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              className="pl-6"
              placeholder="0.00"
            />
          </div>
        </div>

        <div className="mt-4 space-y-2">
          <div className="flex justify-between">
            <span>Base Amount:</span>
            <span>${formattedAmount.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span>Surcharge:</span>
            <span>${surcharge.toFixed(2)}</span>
          </div>
          <div className="flex justify-between font-bold">
            <span>Customer Pays:</span>
            <span>${total.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-sm font-bold">
            <span>Your Net Earn:</span>
            <span>${netEarn.toFixed(2)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
