import { cn } from '@/lib/utils';
import { Metadata } from 'next';
import { PublicEnvScript } from 'next-runtime-env';
import { Inter } from 'next/font/google';
import '../styles/globals.scss';
const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'NGnair Payments',
  description: 'NGnair Payments',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <PublicEnvScript />
      </head>
      <body className={cn('noScrollbar m-auto min-h-screen font-sans antialiased', inter)}>
        {children}
      </body>
    </html>
  );
}
