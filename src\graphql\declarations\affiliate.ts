import { graphql } from '../generated';

export const Affiliation_get = graphql(`
  query Affiliation_get {
    affiliation_get {
      id
      affiliatesCount
      groupAffiliatesCount
      codesCount
      bank_routingNumber
      bank_accountNumber
      bank_accountName
      codes {
        code
        description
      }
    }
  }
`);

export const Affiliation_initiate = graphql(`
  mutation Affiliation_initiate {
    affiliation_initiate {
      id
    }
  }
`);

export const Affiliation_setAffiliateCode = graphql(`
  mutation Affiliation_setAffiliateCode($input: Affiliation_setAffiliateCodeInput!) {
    affiliation_setAffiliateCode(input: $input) {
      code
      description
    }
  }
`);

export const Affiliation_setBankAccount = graphql(`
  mutation Affiliation_setBankAccount($input: Affiliation_setBankAccountInput!) {
    affiliation_setBankAccount(input: $input)
  }
`);

export const Affiliation_getAffiliatedUsersWithMerchants = graphql(`
  query Affiliation_getAffiliatedUsersWithMerchants(
    $input: Affiliation_getAffiliatedUsersWithMerchantsInput!
  ) {
    affiliation_getAffiliatedUsersWithMerchants(input: $input) {
      totalEarnings
      totalWithdrawn
      totalWithdrawable
      data {
        user {
          id
          name
          email
          lastLogin
          title
          earnings
        }
        merchant {
          name
          city
          state
          status
          createdAt
        }
      }
    }
  }
`);
