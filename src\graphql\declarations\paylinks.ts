import { graphql } from '../generated';

export const Paylink_create = graphql(`
  mutation Paylink_create($input: Gateway_createPayLinkInput!) {
    gateway_createPayLink(input: $input) {
      payLinkID
      paymentData
    }
  }
`);

export const Paylink_delete = graphql(`
  mutation Paylink_delete($input: Gateway_deletePayLinkInput!) {
    gateway_deletePayLink(input: $input) {
      id
    }
  }
`);

export const Paylink_list = graphql(`
  query Gateway_payLinks($input: Gateway_payLinksInput!) {
    gateway_payLinks(input: $input) {
      data {
        id
        paymentData
        items
        total
        createdAt
      }
      page {
        total
        range {
          from
          to
        }
        page
        pageSize
      }
    }
  }
`);

export const Paylink_get = graphql(`
  query Gateway_payLink($input: Gateway_payLinkInput!) {
    gateway_payLink(input: $input) {
      id
      paymentData
      paymentRaw {
        pricing {
          methodVerifyOrProcess
          lineItems {
            productId
            product {
              sku
              name
              description
              price
              isRecurring
              recurringMode
              recurringFrequency
              recurringInterval
              recurringSetupFee
              recurringTotalCycles
              recurringTrialDays
            }
            amount
            total
            metadata
          }
          amount
          tip
          tipType
          tax
          taxType
          discountCodes
          shipping
        }
        allowEdit
        allowExtraDiscount
        allowTip
        validUntil
        referenceID
        onSuccessURL
        onFailureURL
      }
      paymentCheck {
        breakdown {
          discount
          directDiscount
          actualDiscount
          tax
          shipping
          shippingDiscount
          shippingDirectDiscount
          shippingActualDiscount
          fees
          actualFees
          tip
          subtotal
          subscriptionTotal
          rawTotal
          total
          expectedTotal
        }
        lineItems {
          productId
          product {
            sku
            name
            description
            price
            isRecurring
            recurringMode
            recurringFrequency
            recurringInterval
            recurringSetupFee
            recurringTotalCycles
            recurringTrialDays
          }
          amount
          total
          metadata
        }
        discountBreakdown {
          code
          amount
        }
        allowEdit
        allowExtraDiscount
        paymentInput {
          methodVerifyOrProcess
          lineItems {
            productId
            product {
              sku
              name
              description
              price
              isRecurring
              recurringMode
              recurringFrequency
              recurringInterval
              recurringSetupFee
              recurringTotalCycles
              recurringTrialDays
            }
            amount
            total
            metadata
          }
          amount
          tip
          tipType
          tax
          taxType
          discountCodes
          shipping
        }
      }
    }
  }
`);
