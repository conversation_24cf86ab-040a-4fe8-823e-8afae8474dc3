import { graphql } from '../generated';

export const GetInviteInfo = graphql(`
  query GetInviteInfo($input: Group_getInviteInfoInput!) {
    group_getInviteInfo(input: $input) {
      alreadySigned
      email
      groupName
    }
  }
`);

export const ClaimInvite = graphql(`
  mutation ClaimInvite($input: Group_claimInviteInput!) {
    group_claimInvite(input: $input)
  }
`);

export const DeclineInvite = graphql(`
  mutation DeclineInvite($input: Group_declineInviteInput!) {
    group_declineInvite(input: $input)
  }
`);
