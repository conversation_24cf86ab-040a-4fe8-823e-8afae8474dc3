'use client';

import { useState, useEffect, useMemo } from 'react';
import { useQuery } from '@apollo/client';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { Search } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import useDebounce from '@/components/hooks/useDebounce';
import {
  GroupSupportTicket,
  GroupSupportTicketsCountDocument,
  GroupSupportTicketsDocument,
  QueryMode,
} from '@/graphql/generated/graphql';
import { Column } from '@/components/globals';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import DataGridView from '@/components/globals/sortable-table/data-grid-view';

const SupportTicketsTable = () => {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [isSortAsc, setIsSortAsc] = useState(true);
  const [sortKey, setSortKey] = useState('createdAt');
  const { toast } = useToast();
  const router = useRouter();
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    data: supportData,
    refetch,
    loading,
    error,
  } = useQuery(GroupSupportTicketsDocument, {
    variables: {
      where: {
        OR: [
          {
            title: {
              contains: debouncedSearchQuery,
              mode: QueryMode.Insensitive,
            },
          },
          { description: { contains: debouncedSearchQuery } },
        ],
      },
      orderBy: [{ [sortKey]: isSortAsc ? 'asc' : 'desc' }],
      take: pageSize || 10,
      skip: (currentPage - 1) * pageSize || 0,
    },
  });
  const { data: tiketCountData, refetch: refetchTotal } = useQuery(
    GroupSupportTicketsCountDocument,
    {
      variables: { where: {} },
    },
  );

  useEffect(() => {
    refetch();
  }, [pageSize, currentPage, sortKey, isSortAsc, refetch]);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Failed to load support tickets',
        description: error.message,
        variant: 'destructive',
      });
    }
  }, [error]);

  const supportRows = useMemo(() => {
    if (!supportData?.groupSupportTickets) return [];
    return supportData?.groupSupportTickets ?? [];
  }, [supportData]);

  const columns: Column[] = [
    {
      key: 'id',
      header: 'PLCase #',
      sortable: false,
      onClick: (row) => {
        router.push(`/dashboard/backoffice/support-tickets/${row.id}`);
      },
      renderCell: (row) => {
        return <button>{row.id.slice(-6, -1)}</button>;
      },
    },
    {
      key: 'title',
      header: 'Title',
      sortable: true,
      onServerSort(key) {
        setSortKey(key as keyof GroupSupportTicket);
        setIsSortAsc((v) => !v);
      },
      renderCell: (row) => {
        return <button>{row.title}</button>;
      },
    },
    {
      key: 'group',
      header: 'Group',
      sortable: false,
      renderCell: (row) => {
        return <span>{row.group?.labelName || 'N/A'}</span>;
      },
    },
    {
      key: 'createdBy',
      header: 'Created By',
      sortable: false,
      renderCell: (row) => {
        return <span>{row.createdBy?.displayName || 'N/A'}</span>;
      },
    },
    {
      key: 'category',
      header: 'Category',
      sortable: true,
      valueGetter: (row) => (row.category === 'GENERAL' ? 'General Inquiry' : row.category),
      onServerSort(key) {
        setSortKey(key as keyof GroupSupportTicket);
        setIsSortAsc((v) => !v);
      },
    },
    {
      key: 'createdAt',
      header: 'Creation Date',
      renderCell: (row) => {
        return <span>{moment(row.createdAt).format('DD/MM/YYYY hh:mm A')}</span>;
      },
      sortable: true,
      onServerSort(key) {
        setSortKey(key as keyof GroupSupportTicket);
        setIsSortAsc((v) => !v);
      },
    },
    {
      key: 'lastMessageCreatedAt',
      header: 'Last Message Date',
      renderCell: (row) => {
        return <span>{moment(row.lastMessageCreatedAt).format('DD/MM/YYYY hh:mm A')}</span>;
      },
    },
    {
      key: 'status',
      header: 'Status',
      width: '100px',
      sortable: true,
      onServerSort(key) {
        setSortKey(key as keyof GroupSupportTicket);
        setIsSortAsc((v) => !v);
      },
    },
  ];

  return (
    <div className="mx-5">
      <div className="my-2 flex justify-end">
        <form
          className="flex w-full max-w-sm items-center justify-start space-x-2"
          onSubmit={(e) => e.preventDefault()}
        >
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              placeholder="Search"
              className="pl-8"
            />
          </div>
          <Button type="submit">Search</Button>
        </form>
      </div>
      <DataGridView
        columns={columns}
        rows={supportRows ?? []}
        pageSize={pageSize}
        currentPage={currentPage}
        isLoading={loading}
        mode="server"
        onPageChange={setCurrentPage}
        onPageSizeChange={setPageSize}
        totalRecords={tiketCountData?.groupSupportTicketsCount || 0}
      />
    </div>
  );
};

export default SupportTicketsTable;
