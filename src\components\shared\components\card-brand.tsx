import {
  FaCcVisa,
  FaCcMastercard,
  FaRegCreditCard,
  FaCcAmex,
  FaCcDiscover,
  FaCcDinersClub,
  FaCcJcb,
} from 'react-icons/fa';

type CardBrandProps = {
  brand: string | undefined | null;
};
export const CardBrand = ({ brand }: CardBrandProps) => {
  const cardBrand = brand?.toUpperCase();
  if (cardBrand === 'VISA') return <FaCcVisa className="text-blue-400" size={25} />;
  if (cardBrand === 'MASTERCARD') return <FaCcMastercard className="text-red-400" size={25} />;
  if (cardBrand === 'DISCOVER') return <FaCcDiscover className="text-blue-600" size={25} />;
  if (cardBrand === 'DINERS') return <FaCcDinersClub className="text-blue-600" size={25} />;
  if (cardBrand === 'JCB') return <FaCcJcb className="text-yellow-600" size={25} />;
  if (cardBrand === 'UNKNOWN') return <FaRegCreditCard className="text-gray-600" size={25} />;
  if (cardBrand === 'AMEX') return <FaCcAmex size={25} />;
  return cardBrand;
};
