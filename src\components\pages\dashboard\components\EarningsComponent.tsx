import { Card, CardContent } from '@/components/ui/card';

export type EarningsData = {
  completed: number;
  pending: number;
  completedItemCount?: number;
  pendingItemCount?: number;
  totalItemCount?: number;
  propayBalance?: number;
};

export type EarningsComponentProps = {
  /**
   * The earnings data to display
   */
  data: EarningsData;
  /**
   * Title for the dashboard
   * @default "Earnings Dashboard"
   */
  title?: string;
  /**
   * Color for the completed earnings bar
   * @default "bg-green-500"
   */
  completedColor?: string;
  /**
   * Color for the pending earnings text
   * @default "text-amber-500"
   */
  pendingColor?: string;
  /**
   * Color for the completed earnings text
   * @default "text-green-600"
   */
  completedTextColor?: string;
  /**
   * Class name for the container
   */
  className?: string;
};

export const EarningsComponent = ({
  data,
  title = 'Earnings Dashboard',
  completedColor = 'bg-green-500',
  pendingColor = 'text-amber-500',
  completedTextColor = 'text-green-600',
  className = '',
}: EarningsComponentProps) => {
  // Calculate percentage and total
  const totalEarnings = data.completed + data.pending;
  const pendingPercentage =
    totalEarnings > 0 ? Math.round((data.pending / totalEarnings) * 100) : 0;
  const completedPercentage = 100 - pendingPercentage;

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className={`container p-6 ${className}`}>
      <h1 className="mb-6 text-2xl font-bold">{title}</h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Propay Balance Card */}
        {data.propayBalance !== undefined && (
          <Card>
            <CardContent className="p-6">
              <h2 className="text-sm font-medium text-gray-500">Propay Balance</h2>
              <p className="mt-2 text-3xl font-bold text-blue-600">
                {formatCurrency(data.propayBalance)}
              </p>
            </CardContent>
          </Card>
        )}

        {/* Total Earnings Card */}
        <Card>
          <CardContent className="p-6">
            <h2 className="text-sm font-medium text-gray-500">Total Earnings</h2>
            <p className="mt-2 text-3xl font-bold">{formatCurrency(totalEarnings)}</p>
            {data.totalItemCount !== undefined && (
              <div className="mt-2 text-xs text-gray-500">{data.totalItemCount} transactions</div>
            )}
          </CardContent>
        </Card>

        {/* Completed Earnings Card */}
        <Card>
          <CardContent className="p-6">
            <h2 className="text-sm font-medium text-gray-500">Completed Earnings</h2>
            <p className={`mt-2 text-3xl font-bold ${completedTextColor}`}>
              {formatCurrency(data.completed)}
            </p>
            {data.completedItemCount !== undefined && (
              <div className="mt-2 text-xs text-gray-500">
                {data.completedItemCount} transactions
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pending Earnings Card */}
        <Card>
          <CardContent className="p-6">
            <h2 className="text-sm font-medium text-gray-500">Pending Earnings</h2>
            <p className={`mt-2 text-3xl font-bold ${pendingColor}`}>
              {formatCurrency(data.pending)}
            </p>
            {data.pendingItemCount !== undefined && (
              <div className="mt-2 text-xs text-gray-500">{data.pendingItemCount} transactions</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Earnings Distribution Card */}
      <Card className="mt-6">
        <CardContent className="p-6">
          <h2 className="mb-4 text-lg font-medium">Earnings Distribution</h2>

          <div className="mb-2 flex items-center justify-between">
            <span className="text-sm text-gray-500">Completed ({completedPercentage}%)</span>
            <span className="text-sm text-gray-500">Pending ({pendingPercentage}%)</span>
          </div>

          {/* Progress Bar */}
          <div className="h-4 w-full overflow-hidden rounded-full bg-gray-200">
            <div
              className={`h-full rounded-full ${completedColor}`}
              style={{
                width: `${completedPercentage}%`,
                transition: 'width 0.5s ease-in-out',
              }}
            />
          </div>

          <div className="mt-4 flex justify-between">
            <div>
              <p className="text-sm font-medium">Completed Earnings</p>
              <p className={`text-xl font-bold ${completedTextColor}`}>
                {formatCurrency(data.completed)}
              </p>
              {data.completedItemCount !== undefined && (
                <div className="text-xs text-gray-500">{data.completedItemCount} transactions</div>
              )}
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">Pending Earnings</p>
              <p className={`text-xl font-bold ${pendingColor}`}>{formatCurrency(data.pending)}</p>
              {data.pendingItemCount !== undefined && (
                <div className="text-xs text-gray-500">{data.pendingItemCount} transactions</div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
