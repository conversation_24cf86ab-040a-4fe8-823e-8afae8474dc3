'use client';

import LoadingButton from '@/components/globals/loading-button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DeleteStripeDataApplications,
  StripeDataApplications,
} from '@/graphql/declarations/stripeAuth';
import { useMutation, useQuery } from '@apollo/client';
import { AlertCircle, Unplug } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';

interface StripeDisconnectManagerProps {
  groupId: string;
}

export default function StripeDisconnectManager({ groupId }: StripeDisconnectManagerProps) {
  const [disconnecting, setDisconnecting] = useState(false);

  const {
    data: stripeDataApplications,
    loading: loadingStripeData,
    refetch,
  } = useQuery(StripeDataApplications);
  const [deleteStripeDataApplications] = useMutation(DeleteStripeDataApplications);

  const stripeConnections = stripeDataApplications?.stripeDataApplications || [];
  const hasConnections = stripeConnections.length > 0;

  const handleDisconnect = async () => {
    if (!hasConnections) {
      toast.error('No Stripe connections to disconnect');
      return;
    }

    setDisconnecting(true);
    try {
      const disconnectResult = await deleteStripeDataApplications({
        variables: {
          where: stripeConnections.map((connection) => ({ id: connection.id })),
        },
      });

      if (disconnectResult.errors) {
        console.error('Failed to disconnect Stripe integration', disconnectResult.errors);
        toast.error('Failed to disconnect Stripe integration');
        return;
      }

      await refetch();
      toast.success('Stripe integration disconnected successfully');
    } catch (error) {
      console.error('Error disconnecting Stripe:', error);
      toast.error('An error occurred while disconnecting Stripe');
    } finally {
      setDisconnecting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <Unplug className="text-primary h-6 w-6" />
          Disconnect Stripe
        </CardTitle>
        <CardDescription>Remove all Stripe API connections</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {loadingStripeData ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>Loading Stripe configuration...</AlertDescription>
            </Alert>
          ) : (
            !hasConnections && (
              <Alert variant="default" className="bg-[#F4F4F5]">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No Stripe connections found. Nothing to disconnect.
                </AlertDescription>
              </Alert>
            )
          )}

          {hasConnections && (
            <>
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Warning: This will remove all Stripe API connections. This action cannot be
                  undone.
                </AlertDescription>
              </Alert>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Active Connections: {stripeConnections.length}</h3>
                  <p className="text-sm text-slate-600">
                    Disconnecting will remove all Stripe API keys and OAuth tokens
                  </p>
                </div>
                <LoadingButton
                  type="button"
                  variant="destructive"
                  disabled={!hasConnections || loadingStripeData || disconnecting}
                  isLoading={disconnecting}
                  icon={<Unplug size={14} />}
                  loadingText="Disconnecting"
                  className="bg-red-600 hover:bg-red-700"
                  onClick={handleDisconnect}
                >
                  Disconnect Stripe
                </LoadingButton>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
