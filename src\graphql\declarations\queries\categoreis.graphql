query Gateway_categories($input: Gateway_categoriesInput!) {
  gateway_categories(input: $input) {
    data {
      id
      name
      status
      color
      description
      subCategory
      colors
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}
query Gateway_category($input: Gateway_categoryInput!) {
  gateway_category(input: $input) {
    id
    name
    status
    color
    description
    subCategory
    colors
  }
}
