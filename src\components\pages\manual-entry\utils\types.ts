export enum EntryType {
  verify = 'verify',
  process = 'process',
}

export enum ProcessType {
  // authorize = 'Authorize only',
  card = 'Credit/Debit Card',
  ach = 'ACH',
  gpecomm = 'GPEComm',
  // bank = 'Direct Bank (RTP)',
}

export type ManualEntryFormData = {
  groupID: string;
  amount?: string;
  tip?: string;
  tax?: string;
  enableReceipt?: boolean;
  processType?: string;
  methodVerifyOrProcess: string;
  customerID: string;
  nameOnCard: string;
  email: string;
  phoneNumber: string;

  // card
  expiryDate: string;
  cvc: string;
  cardNumber: string;
  cardToken: string;

  // ach
  accountNumber: string;
  routingNumber: string;
  accountType: string;
  holderType: string;

  // gpecomm
  gpEcomm: string;

  address: string;
  city: string;
  state: string;
  country: string;
  zip: string;

  brand: string;
  savedCardID: string;
};
