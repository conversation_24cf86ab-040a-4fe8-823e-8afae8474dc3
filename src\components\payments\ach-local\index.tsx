'use client';

import { useForm, FormProvider } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useCallback, useEffect, useState, useMemo } from 'react';
import debounce from 'lodash/debounce';
import { Button } from '@/components/ui/button'; // Add this import
import { env } from 'next-runtime-env';
import { encryptECC } from '@/lib/encryptions/ecc';

export type ACHResponse = {
  accountNumber: string;
  routingNumber: string;
  accountType: 'CHECKING' | 'SAVINGS';
  holderType: 'PERSONAL' | 'BUSINESS';
};

interface ACHFieldsProps {
  onEvent?: (eventType: string, event: any) => void;
  onToken?: (response: ACHResponse) => void;
  onError?: (error: any) => void;
  labels?: {
    accountNumber?: string;
    routingNumber?: string;
    accountType?: string;
    holderType?: string;
  };
}

export const ACHFields = ({ onEvent, onToken, onError, labels = {} }: ACHFieldsProps) => {
  const methods = useForm<ACHResponse>({
    mode: 'onBlur',
  });
  const {
    register,
    handleSubmit,
    setValue,
    reset, // Add reset from useForm
    formState: { errors },
  } = methods;
  const [fieldValues, setFieldValues] = useState<Partial<ACHResponse>>({});

  // Debounced field change handler
  const debouncedFieldChange = useCallback(
    debounce((field: string, value: string) => {
      onEvent?.('fieldChange', { field, value });
      setFieldValues((prev) => ({ ...prev, [field]: value }));
    }, 500),
    [onEvent],
  );

  const handleFieldChange = (field: string, value: string) => {
    debouncedFieldChange(field, value);
  };

  const onSubmit = (data: ACHResponse) => {
    try {
      if (data.routingNumber.length !== 9) {
        throw new Error('Invalid routing number');
      }
      if (data.accountNumber.length < 4 || data.accountNumber.length > 17) {
        throw new Error('Invalid account number');
      }
      data.accountNumber = encryptECC(data.accountNumber, env('NEXT_PUBLIC_ACH_KEY') ?? '');
      console.log('ACH Fields submitted:', data);
      onToken?.(data);
    } catch (error) {
      onError?.(error);
    }
  };

  // Check if all fields are filled and valid
  useEffect(() => {
    const isComplete =
      (fieldValues?.accountNumber?.length ?? 0) >= 4 &&
      fieldValues?.routingNumber?.length === 9 &&
      fieldValues?.accountType &&
      fieldValues?.holderType;

    if (isComplete) {
      handleSubmit(onSubmit)();
    }
  }, [fieldValues]);

  const handleClearFields = () => {
    reset(); // Reset form fields
    setFieldValues({}); // Clear field values state
    onEvent?.('clearFields', {}); // Notify parent component
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-4">
        <div className="xmd:xgrid-cols-2 grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="accountNumber">{labels.accountNumber || 'Account Number'}</Label>
            <Input
              id="accountNumber"
              type="number"
              {...register('accountNumber', {
                required: 'Account number is required',
                validate: (value) =>
                  (value.length >= 4 && value.length <= 17) ||
                  'Account number must be between 4 and 17 digits',
              })}
              onChange={(e) => {
                setValue('accountNumber', e.target.value);
                handleFieldChange('accountNumber', e.target.value);
              }}
            />
            {errors.accountNumber && (
              <p className="text-sm text-red-500">{errors.accountNumber.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="routingNumber">{labels.routingNumber || 'Routing Number'}</Label>
            <Input
              id="routingNumber"
              type="number"
              {...register('routingNumber', {
                required: 'Routing number is required',
                validate: (value) =>
                  value.length === 9 || 'Routing number must be exactly 9 digits',
              })}
              onChange={(e) => {
                setValue('routingNumber', e.target.value);
                handleFieldChange('routingNumber', e.target.value);
              }}
            />
            {errors.routingNumber && (
              <p className="text-sm text-red-500">{errors.routingNumber.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="accountType">{labels.accountType || 'Account Type'}</Label>
            <Select
              onValueChange={(value) => {
                setValue('accountType', value as 'CHECKING' | 'SAVINGS', { shouldValidate: true });
                handleFieldChange('accountType', value);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select account type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CHECKING">Checking</SelectItem>
                <SelectItem value="SAVING">Savings</SelectItem>
              </SelectContent>
            </Select>
            {errors.accountType && (
              <p className="text-sm text-red-500">{errors.accountType.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="holderType">{labels.holderType || 'Holder Type'}</Label>
            <Select
              onValueChange={(value) => {
                setValue('holderType', value as 'PERSONAL' | 'BUSINESS', { shouldValidate: true });
                handleFieldChange('holderType', value);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select holder type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="PERSONAL">Personal</SelectItem>
                <SelectItem value="BUSINESS">Business</SelectItem>
              </SelectContent>
            </Select>
            {errors.holderType && (
              <p className="text-sm text-red-500">{errors.holderType.message}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end pt-2">
          <Button
            onClick={handleClearFields}
            size="sm"
            type="button"
            variant="outline"
            className="text-red-500 hover:bg-red-50 hover:text-red-600"
          >
            Clear Fields
          </Button>
        </div>
      </form>
    </FormProvider>
  );
};

interface ACHHostedComponentProps {
  onEvent?: (eventType: string, event: any) => void;
  onToken?: (response: ACHResponse) => void;
  onError?: (error: any) => void;
  labels?: {
    accountNumber?: string;
    routingNumber?: string;
    accountType?: string;
    holderType?: string;
  };
  parentComponentAttributes?: { [key: string]: any };
  iframeComponentAttributes?: { [key: string]: any };
}

export const ACHHostedComponent = (props: ACHHostedComponentProps) => {
  const registerListener = () => {
    window.addEventListener('message', (event) => {
      const eventType = event.data.type;
      const data = event.data.data;

      switch (eventType) {
        case 'event': {
          props.onEvent?.(data.event, data.data);
          break;
        }
        case 'token': {
          props.onToken?.(data);
          break;
        }
        case 'error': {
          props.onError?.(data);
          break;
        }
      }
    });
  };

  useEffect(() => {
    registerListener();
  }, []);

  useState('');

  const settingsString = JSON.stringify({
    labels: props.labels,
  });

  const iframeURL = useMemo(() => {
    const baseURL = env('NEXT_PUBLIC_ACH_HOSTED_URL');
    if (!baseURL) {
      return '';
    }
    const url = new URL(baseURL);
    // url.searchParams.append('hash', hash);
    url.searchParams.append('settings', settingsString);
    return url.toString();
  }, [settingsString]);

  return (
    <div {...(props.parentComponentAttributes ?? {})}>
      <iframe
        src={iframeURL}
        width={props.iframeComponentAttributes?.width ?? '100%'}
        height={props.iframeComponentAttributes?.height ?? '450px'}
        {...(props.iframeComponentAttributes ?? {})}
      />
      {/* <Button
        size="sm"
        type="button"
        variant="outline"
        className="text-red-500 hover:bg-red-50 hover:text-red-600"
        onClick={setNewRandomHash}
      >
        Reset ACH Form
      </Button> */}
    </div>
  );
};
