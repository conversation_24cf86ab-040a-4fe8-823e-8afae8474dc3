const mockSupportTickets = [
  {
    id: '1',
    title: 'Issue with payment processing',
    problem: 'When trying to process a payment, the system shows an error.',
    status: 'OPEN',
    priority: 'HIGH',
    createdAt: '2024-06-01T10:00:00Z',
    supportingFiles: [],
    chatHistory: [
      {
        id: '1',
        sender: '<PERSON>',
        message: 'Having issues with payment processing. Please help.',
        timestamp: '2024-06-01T10:00:00Z',
        files: [],
      },
    ],
  },
  {
    id: '2',
    title: 'Account access problem',
    problem: 'Cannot login to my account',
    status: 'RESOLVED',
    priority: 'MEDIUM',
    createdAt: '2024-06-01T11:00:00Z',
    supportingFiles: [],
    chatHistory: [
      {
        id: '1',
        sender: '<PERSON>',
        message: 'Getting an error when trying to log in.',
        timestamp: '2024-06-01T11:00:00Z',
        files: [],
      },
    ],
  },
];

export default mockSupportTickets;
