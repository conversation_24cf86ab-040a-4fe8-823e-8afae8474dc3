/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
const documents = {
    "mutation Propay_query($input: Propay_queryInput!) {\n  propay_query(input: $input)\n}\n\nmutation Propay_tables {\n  propay_tables {\n    tables\n  }\n}\n\nmutation Propay_status {\n  propay_status {\n    allRunning\n    components {\n      name\n      status\n      operational\n    }\n  }\n}\n\nmutation Propay_links($input: Propay_linksInput!) {\n  propay_links(input: $input) {\n    id\n    account_name\n    status\n    type\n    function\n    time_created\n    time_last_updated\n    url\n    action {\n      id\n      type\n      time_created\n      result_code\n      app_id\n      app_name\n    }\n  }\n}": types.Propay_QueryDocument,
    "query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {\n  groupSupportTicketsCount(where: $where)\n}\n\nmutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {\n  createGroupSupportMessage(data: $data) {\n    id\n  }\n}\n\nquery Propay_admin_balance($input: Propay_admin_balanceInput!) {\n  propay_admin_balance(input: $input) {\n    balance\n    itemCount\n    positives\n    positiveItemCount\n    negatives\n    negativeItemCount\n    positiveUncounted\n    positiveUncountedItemCount\n    negativeUncounted\n    negativeUncountedItemCount\n    actualCount\n    rangeStart\n    rangeEnd\n    propayBalance\n  }\n}": types.GroupSupportTicketsCountDocument,
    "\n  query Affiliation_get {\n    affiliation_get {\n      id\n      affiliatesCount\n      groupAffiliatesCount\n      codesCount\n      bank_routingNumber\n      bank_accountNumber\n      bank_accountName\n      codes {\n        code\n        description\n      }\n    }\n  }\n": types.Affiliation_GetDocument,
    "\n  mutation Affiliation_initiate {\n    affiliation_initiate {\n      id\n    }\n  }\n": types.Affiliation_InitiateDocument,
    "\n  mutation Affiliation_setAffiliateCode($input: Affiliation_setAffiliateCodeInput!) {\n    affiliation_setAffiliateCode(input: $input) {\n      code\n      description\n    }\n  }\n": types.Affiliation_SetAffiliateCodeDocument,
    "\n  mutation Affiliation_setBankAccount($input: Affiliation_setBankAccountInput!) {\n    affiliation_setBankAccount(input: $input)\n  }\n": types.Affiliation_SetBankAccountDocument,
    "\n  query Affiliation_getAffiliatedUsersWithMerchants(\n    $input: Affiliation_getAffiliatedUsersWithMerchantsInput!\n  ) {\n    affiliation_getAffiliatedUsersWithMerchants(input: $input) {\n      totalEarnings\n      totalWithdrawn\n      totalWithdrawable\n      data {\n        user {\n          id\n          name\n          email\n          lastLogin\n          title\n          earnings\n        }\n        merchant {\n          name\n          city\n          state\n          status\n          createdAt\n        }\n      }\n    }\n  }\n": types.Affiliation_GetAffiliatedUsersWithMerchantsDocument,
    "\n  mutation AiMccFromDescription($input: Ai_determineMCCFromSentenceInput!) {\n    ai_determineMCCFromSentence(input: $input)\n  }\n": types.AiMccFromDescriptionDocument,
    "\n  mutation Authclient_login($email: String!, $browserId: String, $password: String!) {\n    authclient_login(email: $email, browserId: $browserId, password: $password) {\n      ... on ClientItemAuthenticationWithPasswordSuccess {\n        sessionToken\n      }\n      ... on ClientItemAuthenticationWithPasswordFailure {\n        message\n      }\n    }\n  }\n": types.Authclient_LoginDocument,
    "\n  mutation Authclient_register(\n    $email: String!\n    $password: String!\n    $phoneNumber: String\n    $otpSid: String\n    $browserId: String\n    $firstName: String\n    $lastName: String\n    $affiliateId: String\n    $title: String\n  ) {\n    authclient_register(\n      email: $email\n      password: $password\n      phoneNumber: $phoneNumber\n      otpSid: $otpSid\n      browserId: $browserId\n      firstName: $firstName\n      lastName: $lastName\n      affiliateId: $affiliateId\n      title: $title\n    )\n  }\n": types.Authclient_RegisterDocument,
    "\n  query GetCache($input: Cache_getInput!) {\n    cache_get(input: $input) {\n      data\n      id\n      keyword\n    }\n  }\n": types.GetCacheDocument,
    "\n  mutation SaveCache($input: Cache_saveInput!) {\n    cache_save(input: $input) {\n      id\n      keyword\n      data\n    }\n  }\n": types.SaveCacheDocument,
    "\n  mutation CreateGroup($data: GroupCreateInput!) {\n    createGroup(data: $data) {\n      id\n      mainGateway\n      mainProcessor\n      processorStatus\n      name\n    }\n  }\n": types.CreateGroupDocument,
    "\n  mutation UpdateGroup($where: GroupWhereUniqueInput!, $data: GroupUpdateInput!) {\n    updateGroup(where: $where, data: $data) {\n      id\n    }\n  }\n": types.UpdateGroupDocument,
    "\n  mutation File_upload($input: File_uploadInput!) {\n    file_upload(input: $input) {\n      files {\n        url\n        filename\n      }\n    }\n  }\n": types.File_UploadDocument,
    "\n  mutation Reportdownload_download($input: Reportdownload_downloadInput!) {\n    reportdownload_download(input: $input) {\n      data\n    }\n  }\n": types.Reportdownload_DownloadDocument,
    "\n  query Processor_aur_zip_search($input: Processor_aur_zip_searchInput!) {\n    processor_aur_zip_search(input: $input) {\n      item {\n        zip\n        latitude\n        longitude\n        city\n        state\n        country\n      }\n    }\n  }\n": types.Processor_Aur_Zip_SearchDocument,
    "\n  query Processor_aur_bank_routing($input: Processor_aur_bank_routingInput!) {\n    processor_aur_bank_routing(input: $input) {\n      items {\n        routing\n        bank\n      }\n      count\n    }\n  }\n": types.Processor_Aur_Bank_RoutingDocument,
    "\n  query Processor_aur_mcc($input: Processor_aur_mccInput!) {\n    processor_aur_mcc(input: $input) {\n      items {\n        id\n        mccCode\n        description\n      }\n      count\n    }\n  }\n": types.Processor_Aur_MccDocument,
    "\n  query Ghl_api_getMerchantGHL($input: Ghl_api_getMerchantGHLInput!) {\n    ghl_api_getMerchantGHL(input: $input) {\n      data {\n        id\n        companyId\n        name\n        domain\n        address\n        city\n        state\n        logoUrl\n        country\n        postalCode\n        website\n        timezone\n        firstName\n        lastName\n        email\n        phone\n      }\n    }\n  }\n": types.Ghl_Api_GetMerchantGhlDocument,
    "\n  query GHLIntegDetails($input: Ghl_auth_getIntegrationDetailsInput!) {\n    ghl_auth_getIntegrationDetails(input: $input) {\n      locationID\n      locationName\n      accountName\n    }\n  }\n": types.GhlIntegDetailsDocument,
    "\n  query GHLSetupGetPending($input: Ghl_auth_checkPendingInstallInput!) {\n    ghl_auth_checkPendingInstall(input: $input) {\n      code\n    }\n  }\n": types.GhlSetupGetPendingDocument,
    "\n  query GHLSetupCheck($input: Ghl_auth_checkGHLAccessInput!) {\n    ghl_auth_checkGHLAccess(input: $input) {\n      hasAccess\n    }\n  }\n": types.GhlSetupCheckDocument,
    "\n  mutation GHLSetupCommit($input: Ghl_auth_completeIntegrationInput!) {\n    ghl_auth_completeIntegration(input: $input)\n  }\n": types.GhlSetupCommitDocument,
    "\n  query GHLSSOCheck($input: Ghl_auth_getSSOInfoInput!) {\n    ghl_auth_getSSOInfo(input: $input) {\n      locationID\n      accountName\n      email\n      groupID\n      groupName\n    }\n  }\n": types.GhlssoCheckDocument,
    "\n  mutation GHLSSOBind($input: Ghl_auth_bindInput!) {\n    ghl_auth_bind(input: $input) {\n      ghlUserID\n      localUserID\n      locationID\n    }\n  }\n": types.GhlssoBindDocument,
    "\n  mutation GHLSSOSignIn($input: Ghl_auth_ssoInput!) {\n    ghl_auth_sso(input: $input) {\n      sessionToken\n      item {\n        id\n        email\n      }\n    }\n  }\n": types.GhlssoSignInDocument,
    "\n  query Groups($where: GroupWhereInput!, $take: Int, $skip: Int!) {\n    groups(where: $where, skip: $skip, take: $take) {\n      id\n      name\n      membersCount\n      processorStatus\n      mainProcessor\n      mainGateway\n      # processorAURCount\n    }\n  }\n": types.GroupsDocument,
    "\n  mutation DeleteGroup($where: GroupWhereUniqueInput!) {\n    deleteGroup(where: $where) {\n      id\n    }\n  }\n": types.DeleteGroupDocument,
    "\n  query GroupList(\n    $where: GroupWhereInput!\n    $orderBy: [GroupOrderByInput!]\n    $take: Int\n    $skip: Int = 0\n  ) {\n    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      name\n      labelName\n      actualName\n      membersCount\n      processorStatus\n      signingURL\n      mainProcessor\n      mainGateway\n      einNumber\n      createdAt\n      updatedAt\n      displayMerchantID\n      pciStatus\n      firstName\n      lastName\n      fullName\n      addressLine1\n      addressLine2\n      city\n      state\n      country\n      zip\n      mccCode\n      phoneNumber\n      email\n      default_includeSurcharge\n      default_globalDisableCC\n      default_globalDisableACH\n      ghlAccessesCount\n      hubspotAccessesCount\n      ghlPayTransactionMapsCount\n      supportTicketsCount\n      serviceAccountsCount\n      flag_disableTokens\n      flag_disableAutoToken\n    }\n    groupsCount(where: $where)\n  }\n": types.GroupListDocument,
    "\n  query GetGroupList(\n    $where: GroupWhereInput!\n    $orderBy: [GroupOrderByInput!]\n    $take: Int\n    $skip: Int = 0\n  ) {\n    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      name\n      labelName\n      actualName\n      membersCount\n      processorStatus\n      signingURL\n      mainProcessor\n      mainGateway\n      einNumber\n      createdAt\n      updatedAt\n      displayMerchantID\n      pciStatus\n      firstName\n      lastName\n      fullName\n      addressLine1\n      addressLine2\n      city\n      state\n      country\n      zip\n      mccCode\n      phoneNumber\n      email\n      default_includeSurcharge\n      default_globalDisableCC\n      default_globalDisableACH\n      ghlAccessesCount\n      hubspotAccessesCount\n      ghlPayTransactionMapsCount\n      supportTicketsCount\n      serviceAccountsCount\n      flag_disableTokens\n      flag_disableAutoToken\n    }\n    groupsCount(where: $where)\n  }\n": types.GetGroupListDocument,
    "\n  mutation Processor_aur_draft_file_delete($input: Processor_aur_draft_file_deleteInput!) {\n    processor_aur_draft_file_delete(input: $input)\n  }\n": types.Processor_Aur_Draft_File_DeleteDocument,
    "\n  mutation SignGroup($input: Group_signGroupInput!) {\n    group_signGroup(input: $input)\n  }\n": types.SignGroupDocument,
    "\n  mutation SyncStripeIntegration($input: Stripe_syncInput!) {\n    stripe_sync(input: $input) {\n      count\n    }\n  }\n": types.SyncStripeIntegrationDocument,
    "\n  mutation SaveStripeIntegration($input: Stripe_setupInput!) {\n    stripe_setup(input: $input)\n  }\n": types.SaveStripeIntegrationDocument,
    "\n  query DownloadStripeTemplateCSV($input: Import_stripe_sampleInput!) {\n    import_stripe_sample(input: $input) {\n      sampleData\n    }\n  }\n": types.DownloadStripeTemplateCsvDocument,
    "\n  query GetInviteInfo($input: Group_getInviteInfoInput!) {\n    group_getInviteInfo(input: $input) {\n      alreadySigned\n      email\n      groupName\n    }\n  }\n": types.GetInviteInfoDocument,
    "\n  mutation ClaimInvite($input: Group_claimInviteInput!) {\n    group_claimInvite(input: $input)\n  }\n": types.ClaimInviteDocument,
    "\n  mutation DeclineInvite($input: Group_declineInviteInput!) {\n    group_declineInvite(input: $input)\n  }\n": types.DeclineInviteDocument,
    "\n  mutation LeadsLogin($input: Leads_loginInput!) {\n    leads_login(input: $input) {\n      sessionToken\n      email\n      password\n    }\n  }\n": types.LeadsLoginDocument,
    "\n  mutation Mutation {\n    leads_initialize\n  }\n": types.MutationDocument,
    "\n  query Me {\n    authenticatedItem {\n      ... on User {\n        id\n        name\n        lastName\n        displayName\n        email\n        phone\n        title\n        role\n        createdAt\n        groupsCount\n        flag_canAffiliate\n      }\n    }\n  }\n": types.MeDocument,
    "\n  mutation UpdateUser($where: UserWhereUniqueInput!, $data: UserUpdateInput!) {\n    updateUser(where: $where, data: $data) {\n      id\n    }\n  }\n": types.UpdateUserDocument,
    "\n  mutation Authclient_changePassword($oldPassword: String!, $newPassword: String!) {\n    authclient_changePassword(oldPassword: $oldPassword, newPassword: $newPassword)\n  }\n": types.Authclient_ChangePasswordDocument,
    "\n  mutation Authclient_requestPasswordReset($email: String!) {\n    authclient_requestPasswordReset(email: $email)\n  }\n": types.Authclient_RequestPasswordResetDocument,
    "\n  mutation Authclient_resetPassword($token: String!, $password: String!) {\n    authclient_resetPassword(token: $token, password: $password)\n  }\n": types.Authclient_ResetPasswordDocument,
    "\n  query GHLSSOBindings {\n    gHLSSOBindings {\n      id\n      locationID\n      ghlUserID\n      createdAt\n    }\n  }\n": types.GhlssoBindingsDocument,
    "\n  mutation DeleteGHLSSOBinding($where: GHLSSOBindingWhereUniqueInput!) {\n    deleteGHLSSOBinding(where: $where) {\n      id\n    }\n  }\n": types.DeleteGhlssoBindingDocument,
    "mutation Gateway_closeBatch($input: Gateway_closeBatchInput!) {\n  gateway_closeBatch(input: $input) {\n    batchID\n    status\n  }\n}": types.Gateway_CloseBatchDocument,
    "mutation Gateway_createDiscount($input: Gateway_createDiscountInput!) {\n  gateway_createDiscount(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_updateDiscount($input: Gateway_updateDiscountInput!) {\n  gateway_updateDiscount(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_deleteDiscount($input: Gateway_deleteDiscountInput!) {\n  gateway_deleteDiscount(input: $input) {\n    id\n  }\n}": types.Gateway_CreateDiscountDocument,
    "mutation Gateway_createCategory($input: Gateway_createCategoryInput!) {\n  gateway_createCategory(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_updateCategory($input: Gateway_updateCategoryInput!) {\n  gateway_updateCategory(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_deleteCategory($input: Gateway_deleteCategoryInput!) {\n  gateway_deleteCategory(input: $input) {\n    id\n  }\n}": types.Gateway_CreateCategoryDocument,
    "mutation Gateway_addCustomer($input: Gateway_addCustomerInput!) {\n  gateway_addCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {\n  gateway_updateCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {\n  gateway_updateCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_deleteCustomer($input: Gateway_deleteCustomerInput!) {\n  gateway_deleteCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_addPaymentMethod($input: Gateway_addPaymentMethodInput!) {\n  gateway_addPaymentMethod(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_deletePaymentMethod($input: Gateway_deletePaymentMethodInput!) {\n  gateway_deletePaymentMethod(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_setDefaultPaymentMethod($input: Gateway_setDefaultPaymentMethodInput!) {\n  gateway_setDefaultPaymentMethod(input: $input) {\n    customerID\n  }\n}": types.Gateway_AddCustomerDocument,
    "mutation Gateway_submitDisputeDocument($input: Gateway_submitDisputeDocumentInput!) {\n  gateway_submitDisputeDocument(input: $input) {\n    disputeID\n  }\n}\n\nmutation Gateway_attendDispute($input: Gateway_attendDisputeInput!) {\n  gateway_attendDispute(input: $input) {\n    disputeID\n  }\n}": types.Gateway_SubmitDisputeDocumentDocument,
    "mutation GatewayInitialize($input: Gateway_flpayset_initializeInput!) {\n  gateway_flpayset_initialize(input: $input) {\n    merchantID\n    processorID\n  }\n}": types.GatewayInitializeDocument,
    "mutation Import_stripe($input: Import_stripeInput!) {\n  import_stripe(input: $input) {\n    processedItems\n    type\n  }\n}": types.Import_StripeDocument,
    "mutation Gateway_createManualEntry($input: Gateway_createManualEntryInput!) {\n  gateway_createManualEntry(input: $input) {\n    transactionID\n  }\n}\n\nmutation Gateway_revertTransaction($input: Gateway_revertTransactionInput!) {\n  gateway_revertTransaction(input: $input) {\n    transactionID\n  }\n}": types.Gateway_CreateManualEntryDocument,
    "mutation Gateway_createPaymentPlan($input: Gateway_createPaymentPlanInput!) {\n  gateway_createPaymentPlan(input: $input) {\n    planID\n  }\n}\n\nmutation Gateway_cancelPaymentPlan($input: Gateway_cancelPaymentPlanInput!) {\n  gateway_cancelPaymentPlan(input: $input) {\n    planID\n  }\n}\n\nmutation Gateway_updatePaymentPlan($input: Gateway_updatePaymentPlanInput!) {\n  gateway_updatePaymentPlan(input: $input) {\n    planID\n  }\n}": types.Gateway_CreatePaymentPlanDocument,
    "mutation Gateway_createProduct($input: Gateway_createProductInput!) {\n  gateway_createProduct(input: $input) {\n    productID\n  }\n}\n\nmutation Gateway_deleteProduct($input: Gateway_deleteProductInput!) {\n  gateway_deleteProduct(input: $input) {\n    productID\n  }\n}\n\nmutation Gateway_updateProduct($input: Gateway_updateProductInput!) {\n  gateway_updateProduct(input: $input) {\n    productID\n  }\n}": types.Gateway_CreateProductDocument,
    "mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {\n  createGroupSupportTicket(data: $data) {\n    id\n  }\n}\n\nmutation UpdateGroupSupportTicket($where: GroupSupportTicketWhereUniqueInput!, $data: GroupSupportTicketUpdateInput!) {\n  updateGroupSupportTicket(where: $where, data: $data) {\n    id\n    title\n    category\n    description\n    status\n    messagesCount\n    createdAt\n    lastMessageCreatedAt\n  }\n}": types.CreateGroupSupportTicketDocument,
    "mutation Gateway_revertTransaction($input: Gateway_revertTransactionInput!) {\n  gateway_revertTransaction(input: $input) {\n    transactionID\n  }\n}": types.Gateway_RevertTransactionDocument,
    "\n  query Notifications(\n    $where: NotificationWhereInput!\n    $orderBy: [NotificationOrderByInput!]!\n    $skip: Int!\n    $take: Int\n  ) {\n    notifications(where: $where, orderBy: $orderBy, skip: $skip, take: $take) {\n      id\n      topics\n      read\n      content\n      createdAt\n    }\n  }\n": types.NotificationsDocument,
    "\n  mutation OauthClientCreate($input: Oauthclient_createInput!) {\n    oauthclient_create(input: $input) {\n      id\n      clientId\n      clientSecret\n    }\n  }\n": types.OauthClientCreateDocument,
    "\n  mutation OauthClientUpdate($input: Oauthclient_updateInput!) {\n    oauthclient_update(input: $input) {\n      id\n      clientId\n      clientSecret\n    }\n  }\n": types.OauthClientUpdateDocument,
    "\n  mutation OauthClientDelete($where: [OAuth_ClientWhereUniqueInput!]!) {\n    deleteOAuthClients(where: $where) {\n      id\n    }\n  }\n": types.OauthClientDeleteDocument,
    "\n  query OAuthClients($where: OAuth_ClientWhereInput!, $take: Int, $skip: Int!) {\n    oAuthClients(where: $where, take: $take, skip: $skip) {\n      id\n      clientId\n      redirectUri\n      grantTypes\n      scope\n      createdAt\n      authCodesCount\n      tokensCount\n      group {\n        id\n        name\n        actualName\n        labelName\n      }\n    }\n  }\n": types.OAuthClientsDocument,
    "\n  mutation OAuthTokenDelete($where: [OAuth_TokenWhereUniqueInput!]!) {\n    deleteOAuthTokens(where: $where) {\n      id\n    }\n  }\n": types.OAuthTokenDeleteDocument,
    "\n  query OAuthTokens($where: OAuth_TokenWhereInput!, $take: Int, $skip: Int!) {\n    oAuthTokens(where: $where, take: $take, skip: $skip) {\n      id\n      accessToken\n      refreshToken\n      expiresAt\n      refreshTokenExpiresAt\n      scope\n      createdAt\n      group {\n        id\n        name\n        actualName\n        labelName\n      }\n      client {\n        id\n        group {\n          actualName\n          name\n          labelName\n          id\n        }\n      }\n    }\n  }\n": types.OAuthTokensDocument,
    "\n  mutation Otp_generate($input: Otp_generateInput!) {\n    otp_generate(input: $input) {\n      sid\n      to\n    }\n  }\n": types.Otp_GenerateDocument,
    "\n  mutation Otp_auth_generate($input: Otp_auth_generateInput!) {\n    otp_auth_generate(input: $input) {\n      sid\n      to\n    }\n  }\n": types.Otp_Auth_GenerateDocument,
    "\n  mutation Otp_verify($input: Otp_verifyInput!) {\n    otp_verify(input: $input) {\n      sid\n      status\n    }\n  }\n": types.Otp_VerifyDocument,
    "\n  mutation generateMFA($input: Authclient_mfa_generateInput!) {\n    authclient_mfa_generate(input: $input) {\n      sid\n      to\n    }\n  }\n": types.GenerateMfaDocument,
    "\n  mutation verifyMFA($input: Authclient_mfa_verifyInput!) {\n    authclient_mfa_verify(input: $input) {\n      sid\n      status\n    }\n  }\n": types.VerifyMfaDocument,
    "\n  mutation Paylink_create($input: Gateway_createPayLinkInput!) {\n    gateway_createPayLink(input: $input) {\n      payLinkID\n      paymentData\n    }\n  }\n": types.Paylink_CreateDocument,
    "\n  mutation Paylink_delete($input: Gateway_deletePayLinkInput!) {\n    gateway_deletePayLink(input: $input) {\n      id\n    }\n  }\n": types.Paylink_DeleteDocument,
    "\n  query Gateway_payLinks($input: Gateway_payLinksInput!) {\n    gateway_payLinks(input: $input) {\n      data {\n        id\n        paymentData\n        items\n        total\n        createdAt\n      }\n      page {\n        total\n        range {\n          from\n          to\n        }\n        page\n        pageSize\n      }\n    }\n  }\n": types.Gateway_PayLinksDocument,
    "\n  query Gateway_payLink($input: Gateway_payLinkInput!) {\n    gateway_payLink(input: $input) {\n      id\n      paymentData\n      paymentRaw {\n        pricing {\n          methodVerifyOrProcess\n          lineItems {\n            productId\n            product {\n              sku\n              name\n              description\n              price\n              isRecurring\n              recurringMode\n              recurringFrequency\n              recurringInterval\n              recurringSetupFee\n              recurringTotalCycles\n              recurringTrialDays\n            }\n            amount\n            total\n            metadata\n          }\n          amount\n          tip\n          tipType\n          tax\n          taxType\n          discountCodes\n          shipping\n        }\n        allowEdit\n        allowExtraDiscount\n        allowTip\n        validUntil\n        referenceID\n        onSuccessURL\n        onFailureURL\n      }\n      paymentCheck {\n        breakdown {\n          discount\n          directDiscount\n          actualDiscount\n          tax\n          shipping\n          shippingDiscount\n          shippingDirectDiscount\n          shippingActualDiscount\n          fees\n          actualFees\n          tip\n          subtotal\n          subscriptionTotal\n          rawTotal\n          total\n          expectedTotal\n        }\n        lineItems {\n          productId\n          product {\n            sku\n            name\n            description\n            price\n            isRecurring\n            recurringMode\n            recurringFrequency\n            recurringInterval\n            recurringSetupFee\n            recurringTotalCycles\n            recurringTrialDays\n          }\n          amount\n          total\n          metadata\n        }\n        discountBreakdown {\n          code\n          amount\n        }\n        allowEdit\n        allowExtraDiscount\n        paymentInput {\n          methodVerifyOrProcess\n          lineItems {\n            productId\n            product {\n              sku\n              name\n              description\n              price\n              isRecurring\n              recurringMode\n              recurringFrequency\n              recurringInterval\n              recurringSetupFee\n              recurringTotalCycles\n              recurringTrialDays\n            }\n            amount\n            total\n            metadata\n          }\n          amount\n          tip\n          tipType\n          tax\n          taxType\n          discountCodes\n          shipping\n        }\n      }\n    }\n  }\n": types.Gateway_PayLinkDocument,
    "\n  mutation Gateway_computeCheckout($input: Gateway_computeCheckoutInput!) {\n    gateway_computeCheckout(input: $input) {\n      breakdown {\n        discount\n        directDiscount\n        actualDiscount\n        tax\n        shipping\n        shippingDiscount\n        shippingDirectDiscount\n        shippingActualDiscount\n        fees\n        actualFees\n        tip\n        subtotal\n        subscriptionTotal\n        rawTotal\n        total\n        expectedTotal\n      }\n      lineItems {\n        productId\n        product {\n          sku\n          name\n          description\n          price\n          isRecurring\n          recurringMode\n          recurringInterval\n          recurringFrequency\n          recurringTotalCycles\n          recurringTrialDays\n          recurringSetupFee\n          recurringRefundable\n        }\n        amount\n        total\n        metadata\n      }\n      discountBreakdown {\n        code\n        amount\n      }\n      allowEdit\n      allowExtraDiscount\n      disableACH\n      disableCard\n      paymentInput {\n        methodVerifyOrProcess\n        lineItems {\n          productId\n          product {\n            sku\n            name\n            description\n            price\n            isRecurring\n            recurringMode\n            recurringInterval\n            recurringFrequency\n            recurringTotalCycles\n            recurringTrialDays\n            recurringSetupFee\n            recurringRefundable\n          }\n          amount\n          total\n          metadata\n        }\n        amount\n        tip\n        tipType\n        tax\n        taxType\n        discountCodes\n        shipping\n      }\n      surcharge\n      meta {\n        includeSurcharge\n        encryptedCheckoutToken\n        encrptedCheckoutTokenSecret\n        dynamic {\n          discountCodes\n          paymentType\n          tip {\n            amount\n            type\n          }\n          quantityAmounts {\n            id\n            quantity\n          }\n        }\n        reference {\n          id\n          source\n        }\n      }\n      referenceID\n      onSuccessUrl\n      onFailureUrl\n      customerID\n      customerData {\n        id\n        nameOnCard\n        email\n        phone\n        billingAddress\n        billingCity\n        billingState\n        billingZip\n        billingCountry\n        paymentCards {\n          cardID\n          type\n          isDefault\n          last4\n          brand\n          expires\n          cvc\n          accountNumber\n          routingNumber\n          accountType\n          accountHolderType\n          gpEcommID\n        }\n      }\n      allowTip\n      customerPaymentID\n      prefilledAddress {\n        email\n        phone\n        country\n        state\n        city\n        zip\n        address\n        nameOnCard\n      }\n      transactionHistory {\n        transactionID\n        date\n        amount\n        status\n        note\n      }\n      disableCustomAddress\n      disableCustomPayment\n      disablePreselectCard\n      overideLineItemsAmount\n    }\n  }\n": types.Gateway_ComputeCheckoutDocument,
    "\n  query Ghl_api_getPaymentPageDataUNI($input: Ghl_api_getPaymentPageDataUNIInput!) {\n    ghl_api_getPaymentPageDataUNI(input: $input) {\n      data {\n        locationID\n        groupID\n        liveMode\n        amount\n        apiKey\n        createdAt\n        amountSummary {\n          subtotal\n          discount\n          tax\n          shipping\n        }\n        subscriptionId\n        lineItems {\n          id\n          name\n          description\n          image\n          quantity\n          price\n          currency\n          total\n          discount\n        }\n        subscriptionItems {\n          id\n          name\n          description\n          image\n          price\n          currency\n          setupFee\n          recurring {\n            interval\n            intervalCount\n            delay\n            cycles\n          }\n          discount\n          discountNoSetupFee\n        }\n        coupon {\n          id\n          name\n          sessionID\n          code\n          discountType\n          discountValue\n          hasAffiliateCoupon\n          userLimit\n          status\n          usageCount\n        }\n        customerData {\n          name\n          email\n          contact\n          first_name\n          last_name\n          company\n          address_line_1\n          address_line_2\n          city\n          state\n          postal_code\n          country\n          phone\n          fax\n        }\n      }\n      paymentLink {\n        paymentData\n        payLinkID\n        token\n        groupID\n      }\n    }\n  }\n": types.Ghl_Api_GetPaymentPageDataUniDocument,
    "\n  query GetPaymentPageData($input: Ghl_api_getPaymentPageDataInput!) {\n    ghl_api_getPaymentPageData(input: $input) {\n      data {\n        locationID\n        groupID\n        liveMode\n        amount\n        apiKey\n        createdAt\n        amountSummary {\n          subtotal\n          discount\n          tax\n          shipping\n        }\n        subscriptionId\n        lineItems {\n          id\n          name\n          description\n          image\n          quantity\n          price\n          currency\n          total\n          discount\n        }\n        subscriptionItems {\n          id\n          name\n          description\n          image\n          price\n          currency\n          setupFee\n          recurring {\n            interval\n            intervalCount\n            delay\n            cycles\n          }\n          discount\n          discountNoSetupFee\n        }\n        coupon {\n          id\n          name\n          sessionID\n          code\n          discountType\n          discountValue\n          hasAffiliateCoupon\n          userLimit\n          status\n          usageCount\n        }\n        customerData {\n          name\n          email\n          contact\n          first_name\n          last_name\n          company\n          address_line_1\n          address_line_2\n          city\n          state\n          postal_code\n          country\n          phone\n          fax\n        }\n      }\n    }\n  }\n": types.GetPaymentPageDataDocument,
    "\n  mutation SubmitPaymentUNIPageData($input: Ghl_api_submitPayment_uniInput!) {\n    ghl_api_submitPayment_uni(input: $input) {\n      txID\n      order {\n        status\n        msg\n        data {\n          id\n          idempotency_time\n          type\n          amount\n          base_amount\n          amount_authorized\n          amount_captured\n          amount_settled\n          amount_refunded\n          payment_adjustment\n          tip_amount\n          settlement_batch_id\n          payment_type\n          tax_amount\n          tax_exempt\n          shipping_amount\n          surcharge\n          discount_amount\n          service_fee\n          currency\n          description\n          order_id\n          po_number\n          ip_address\n          transaction_source\n          email_receipt\n          email_address\n          customer_id\n          customer_payment_type\n          customer_payment_id\n          subscription_id\n          referenced_transaction_id\n          response_body {\n            card {\n              id\n              card_type\n              first_six\n              last_four\n              masked_card\n              expiration_date\n              response\n              response_code\n              auth_code\n              bin_type\n              type\n              avs_response_code\n              cvv_response_code\n              processor_specific\n              created_at\n              updated_at\n            }\n          }\n          status\n          response\n          response_code\n          billing_address {\n            first_name\n            last_name\n            company\n            address_line_1\n            address_line_2\n            city\n            state\n            postal_code\n            country\n            phone\n            fax\n            email\n          }\n          shipping_address {\n            first_name\n            last_name\n            company\n            address_line_1\n            address_line_2\n            city\n            state\n            postal_code\n            country\n            phone\n            fax\n            email\n          }\n          created_at\n          updated_at\n          captured_at\n          settled_at\n        }\n      }\n      subscription {\n        status\n        msg\n        data {\n          id\n          plan_id\n          status\n          description\n          customer {\n            id\n          }\n          amount\n          total_adds\n          total_discounts\n          billing_cycle_interval\n          billing_frequency\n          billing_days\n          duration\n          next_bill_date\n          add_ons {\n            id\n            name\n            description\n            amount\n            percentage\n            duration\n            created_at\n            updated_at\n          }\n          discounts {\n            id\n            name\n            description\n            amount\n            percentage\n            duration\n            created_at\n            updated_at\n          }\n          created_at\n          updated_at\n        }\n      }\n    }\n  }\n": types.SubmitPaymentUniPageDataDocument,
    "\n  mutation processorTestDraftCreate($input: Processor_tst_draft_createInput!) {\n    processor_tst_draft_create(input: $input) {\n      testID\n      groupID\n    }\n  }\n": types.ProcessorTestDraftCreateDocument,
    "\n  mutation processorTestDraftUpdate($input: Processor_tst_draft_updateInput!) {\n    processor_tst_draft_update(input: $input) {\n      testID\n      groupID\n    }\n  }\n": types.ProcessorTestDraftUpdateDocument,
    "\n  mutation processorTestDraftSubmit($input: Processor_tst_draft_submitInput!) {\n    processor_tst_draft_submit(input: $input) {\n      urlForSigning\n      applicationId\n      applicationNumber\n    }\n  }\n": types.ProcessorTestDraftSubmitDocument,
    "\n  query Group($where: GroupWhereUniqueInput!) {\n    group(where: $where) {\n      id\n      processorTST {\n        applicationID\n        applicationNumber\n        id\n        processor_tst_signingUrl\n        processor_tst_applicationStatus {\n          businessInfo {\n            legalBusinessName\n            typeOfBusiness\n            dbaName\n            ein\n            dateBusinessEstablished\n            businessEmail\n            businessPhone\n            website\n            customerServicePhone\n            street\n            zipCode\n            city\n            state\n            country\n            differentLegalAddress\n            legalMailingStreet\n            legalMailingZipCode\n            legalMailingCity\n            legalMailingState\n            legalMailingCountry\n          }\n          transactionInfo {\n            businessCategory\n            description\n            swipe\n            keyed\n            ecommerce\n            avgTransactionAmount\n            highestTransactionAmount\n            grossMonthlySalesVolume\n            amexAvgTransactionAmount\n            amexHighestTransactionAmount\n            amexGrossMonthlySalesVolume\n          }\n          owners {\n            isControlOwner\n            firstName\n            lastName\n            title\n            ownershipPercentage\n            phoneNumber\n            homeAddress\n            country\n            state\n            city\n            zipCode\n            dateOfBirth\n            ssn\n            email\n          }\n          files {\n            name\n            category\n            mimetype\n            b64\n          }\n          bankInfo {\n            routingNumber\n            accountNumber\n            bankName\n            nameOnAccount\n          }\n        }\n      }\n    }\n  }\n": types.GroupDocument,
    "\n  query processorTestDraftZipSearch($input: Processor_tst_zip_searchInput!) {\n    processor_tst_zip_search(input: $input) {\n      item {\n        zip\n        latitude\n        longitude\n        city\n        state\n        country\n      }\n    }\n  }\n": types.ProcessorTestDraftZipSearchDocument,
    "\n  query processorTestDraftMCC($input: Processor_tst_mccInput!) {\n    processor_tst_mcc(input: $input) {\n      items {\n        id\n        description\n      }\n      count\n    }\n  }\n": types.ProcessorTestDraftMccDocument,
    "\n  query processorTestDraftBankRouting($input: Processor_tst_bank_routingInput!) {\n    processor_tst_bank_routing(input: $input) {\n      data {\n        rn\n        name\n      }\n    }\n  }\n": types.ProcessorTestDraftBankRoutingDocument,
    "\n  mutation Processor_draft_create($input: Processor_draft_createInput!) {\n    processor_draft_create(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n": types.Processor_Draft_CreateDocument,
    "\n  mutation Processor_draft_update($input: Processor_draft_updateInput!) {\n    processor_draft_update(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n": types.Processor_Draft_UpdateDocument,
    "\n  mutation Processor_draft_submit($input: Processor_draft_submitInput!) {\n    processor_draft_submit(input: $input) {\n      urlForSigning\n      applicationId\n      applicationNumber\n    }\n  }\n": types.Processor_Draft_SubmitDocument,
    "\n  mutation Processor_draft_documentUpload($input: Processor_draft_documentUploadInput!) {\n    processor_draft_documentUpload(input: $input) {\n      documentID {\n        name\n        id\n      }\n    }\n  }\n": types.Processor_Draft_DocumentUploadDocument,
    "\n  mutation Processor_draft_documentForward($input: Processor_draft_documentForwardInput!) {\n    processor_draft_documentForward(input: $input) {\n      success\n    }\n  }\n": types.Processor_Draft_DocumentForwardDocument,
    "\n  query Processor_draft_status($input: Processor_draft_statusInput!) {\n    processor_draft_status(input: $input) {\n      attestations {\n        name\n        ip_address\n        time_of_attestation\n        url\n        signature\n      }\n      businessInfo {\n        legalBusinessName\n        typeOfBusiness\n        dbaName\n        ein\n        dateBusinessEstablished\n        businessEmail\n        businessPhone\n        businessPhoneCountryCode\n        website\n        customerServicePhone\n        customerServicePhoneCountryCode\n        street\n        zipCode\n        city\n        state\n        country\n        differentLegalAddress\n        legalMailingStreet\n        legalMailingZipCode\n        legalMailingCity\n        legalMailingState\n        legalMailingCountry\n      }\n      transactionInfo {\n        businessCategory\n        description\n        swipe\n        keyed\n        ecommerce\n        avgTransactionAmount\n        highestTransactionAmount\n        grossMonthlySalesVolume\n        amexAvgTransactionAmount\n        amexHighestTransactionAmount\n        amexGrossMonthlySalesVolume\n      }\n      owners {\n        isControlOwner\n        firstName\n        lastName\n        title\n        ownershipPercentage\n        phoneNumber\n        phoneNumberCountryCode\n        homeAddress\n        country\n        state\n        city\n        zipCode\n        dateOfBirth\n        ssn\n        email\n      }\n      files {\n        id\n        name\n        category\n        purpose\n        mimetype\n        b64\n        submittedOn\n      }\n      bankInfo {\n        routingNumber\n        accountNumber\n        bankName\n        nameOnAccount\n      }\n      status {\n        message\n      }\n    }\n  }\n": types.Processor_Draft_StatusDocument,
    "query Gateway_batches($input: Gateway_batchesInput!) {\n  gateway_batches(input: $input) {\n    data {\n      batchID\n      location\n      date\n      amount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_batch($input: Gateway_batchInput!) {\n  gateway_batch(input: $input) {\n    batchID\n    status\n    date\n    location\n    locationID\n    amount\n    transactions {\n      transactionID\n      date\n      method\n      name\n      last4\n      customer\n      amount\n      brand\n      status\n    }\n  }\n}": types.Gateway_BatchesDocument,
    "query Gateway_discounts($input: Gateway_discountsInput!) {\n  gateway_discounts(input: $input) {\n    data {\n      id\n      name\n      type\n      discount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_discount($input: Gateway_discountInput!) {\n  gateway_discount(input: $input) {\n    id\n    name\n    type\n    discount\n    status\n  }\n}": types.Gateway_DiscountsDocument,
    "query Gateway_categories($input: Gateway_categoriesInput!) {\n  gateway_categories(input: $input) {\n    data {\n      id\n      name\n      status\n      color\n      description\n      subCategory\n      colors\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_category($input: Gateway_categoryInput!) {\n  gateway_category(input: $input) {\n    id\n    name\n    status\n    color\n    description\n    subCategory\n    colors\n  }\n}": types.Gateway_CategoriesDocument,
    "query Gateway_customers($input: Gateway_customersInput!) {\n  gateway_customers(input: $input) {\n    data {\n      id\n      isDefault\n      last4\n      brand\n      expires\n      name\n      city_state\n      zip\n      email\n      phone\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_customer($input: Gateway_customerInput!) {\n  gateway_customer(input: $input) {\n    id\n    nameOnCard\n    email\n    phone\n    billingAddress\n    billingCity\n    billingState\n    billingZip\n    billingCountry\n    paymentCards {\n      type\n      cardID\n      isDefault\n      last4\n      brand\n      expires\n      accountNumber\n      routingNumber\n      accountType\n      accountHolderType\n      gpEcommID\n    }\n  }\n}\n\nquery GetCustomerListByLocationForDropdown($input: Gateway_customersInput!) {\n  gateway_customers(input: $input) {\n    data {\n      id\n      name\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}": types.Gateway_CustomersDocument,
    "query Dashboard_get_summary($input: Dashboard_get_summaryInput!) {\n  dashboard_get_summary(input: $input) {\n    dateStart\n    dateEnd\n    totalPortfolio\n    captured {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    refunds {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    batched {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    deposits {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n  }\n}\n\nquery Dashboard_location_summary($input: Dashboard_location_summaryInput!) {\n  dashboard_location_summary(input: $input) {\n    data {\n      locationID\n      locationName\n      changePercentage\n      currentYearTotal\n      lastYearTotal\n      yearChangePercentage\n    }\n  }\n}\n\nquery Gateway_accountFunds($input: Gateway_accountFundsInput!) {\n  gateway_accountFunds(input: $input) {\n    card_transaction_limit_amount\n    card_monthly_limit_amount\n    bank_transfer_per_transaction_limit_amount\n    bank_transfer_monthly_limit_amount\n    bank_transfer_month_to_date_processed_amount\n    card_month_to_date_processed_amount\n    available_balance_amount\n    pending_balance_amount\n    reserve_balance_amount\n    allowed_negative_balance_amount\n    disable_payout_card_limit_percentage\n    disable_payout_bank_transfer_limit_percentage\n    card_month_to_date_processed_percentage\n    bank_transfer_month_to_date_processed_percentage\n  }\n}": types.Dashboard_Get_SummaryDocument,
    "query Gateway_deposits($input: Gateway_depositsInput!) {\n  gateway_deposits(input: $input) {\n    data {\n      depositID\n      location\n      date\n      amount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_deposit($input: Gateway_depositInput!) {\n  gateway_deposit(input: $input) {\n    depositID\n    date\n    last4\n    amount\n    sales\n    fees\n    totalDeposit\n    batches {\n      batchID\n      depositID\n      total\n    }\n    transactions {\n      transactionID\n      date\n      method\n      name\n      last4\n      customer\n      amount\n      brand\n      status\n    }\n  }\n}": types.Gateway_DepositsDocument,
    "query Gateway_disputes($input: Gateway_disputesInput!) {\n  gateway_disputes(input: $input) {\n    data {\n      caseID\n      location\n      date\n      reason\n      amount\n      brand\n      transactionID\n      hasChallenged\n      daysToRepresent\n      customer\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_dispute($input: Gateway_disputeInput!) {\n  gateway_dispute(input: $input) {\n    disputeID\n    caseID\n    location\n    date\n    reason\n    amount\n    brand\n    transactionID\n    paymentType\n    createdBy\n    method\n    batchID\n    paymentPlan\n    source\n    authCode\n    entryMethod\n    daysToRepresent\n    tokenSource\n    gsa\n    emv\n    last4\n    customerID\n    name\n    email\n    phone\n    country\n    billingAddress\n    products {\n      productName\n      productID\n      quantity\n      price\n      discount\n      description\n    }\n    history {\n      action\n      body\n      actor\n      createdAt\n    }\n    files {\n      id\n      fileUrl\n      purpose\n      fileFormat\n      createdAt\n      submittedAt\n    }\n    status\n  }\n}\n\nmutation Gateway_uploadDisputeDocument($input: Gateway_uploadDisputeDocumentInput!) {\n  gateway_uploadDisputeDocument(input: $input) {\n    itemID\n  }\n}\n\nmutation Gateway_submitDisputeDocument($input: Gateway_submitDisputeDocumentInput!) {\n  gateway_submitDisputeDocument(input: $input) {\n    disputeID\n  }\n}": types.Gateway_DisputesDocument,
    "query Gateway_paymentPlans($input: Gateway_paymentPlansInput!) {\n  gateway_paymentPlans(input: $input) {\n    data {\n      planID\n      planName\n      startDate\n      customerName\n      customerID\n      amount\n      last4\n      expires\n      duration\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_paymentPlan($input: Gateway_paymentPlanInput!) {\n  gateway_paymentPlan(input: $input) {\n    planID\n    planName\n    createdAt\n    amount\n    startDate\n    endDate\n    locationName\n    locationID\n    status\n    last4\n    expires\n    creator\n    history {\n      transactionID\n      date\n      amount\n      status\n    }\n    customerName\n    customerID\n    customerEmail\n    customerPhone\n    customerCountry\n    customerBillingAddress\n    paymentBrand\n    paymentEvery\n    paymentInterval\n    paymentExpires\n    mode\n    paymentID\n    nextPaymentDate\n    cancelReason\n    recurringRefundable\n    subscriptionCredit\n  }\n}": types.Gateway_PaymentPlansDocument,
    "query Gateway_products($input: Gateway_productsInput!) {\n  gateway_products(input: $input) {\n    data {\n      id\n      name\n      price\n      discount\n      taxExempt\n      brand\n      kitchenItem\n      sku\n      isRecurring\n      recurringInterval\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_product($input: Gateway_productInput!) {\n  gateway_product(input: $input) {\n    id\n    name\n    price\n    discount\n    productStatus\n    taxExempt\n    kitchenItem\n    sku\n    category\n    subCategory\n    brand\n    isRecurring\n    recurringMode\n    recurringInterval\n    recurringFrequency\n    recurringTotalCycles\n    recurringTrialDays\n    recurringSetupFee\n    description\n    isInStore\n    isOnline\n    productImages {\n      url\n      name\n    }\n  }\n}": types.Gateway_ProductsDocument,
    "query Gateway_scheduled($input: Gateway_scheduledInput!) {\n  gateway_scheduled(input: $input) {\n    data {\n      planID\n      planName\n      customerID\n      customerName\n      nextPayment\n      amount\n      last4\n      expires\n      datePaid\n      amountPaid\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}": types.Gateway_ScheduledDocument,
    "query Gateway_transactions($input: Gateway_transactionsInput!) {\n  gateway_transactions(input: $input) {\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n    data {\n      transactionID\n      location\n      date\n      method\n      customer\n      brand\n      status\n      last4\n      amount\n    }\n  }\n}\n\nquery Gateway_transaction($input: Gateway_transactionInput!) {\n  gateway_transaction(input: $input) {\n    transactionID\n    status\n    date\n    createdBy\n    paymentType\n    method\n    batchID\n    paymentPlan\n    source\n    authCode\n    amount\n    entryMethod\n    tokenSource\n    gsa\n    emv\n    last4\n    customerName\n    customerID\n    customerEmail\n    customerPhone\n    customerCountry\n    customerBillingAddress\n    commercialLevel\n    result\n    message\n    brandReference\n    breakdown {\n      discount\n      directDiscount\n      actualDiscount\n      tax\n      shipping\n      shippingDiscount\n      shippingDirectDiscount\n      shippingActualDiscount\n      fees\n      actualFees\n      tip\n      subtotal\n      subscriptionTotal\n      rawTotal\n      total\n      expectedTotal\n    }\n    transactionHistory {\n      date\n      status\n      response\n      avs\n      cvv\n    }\n    purchaseDetails {\n      productName\n      productID\n      quantity\n      price\n      discount\n    }\n    relatedTransactions {\n      transactionID\n      amount\n      status\n      date\n      paymentType\n    }\n  }\n}": types.Gateway_TransactionsDocument,
    "\n  mutation Serviceacct_create($input: Serviceacct_createInput!) {\n    serviceacct_create(input: $input) {\n      id\n      token\n      secret\n    }\n  }\n": types.Serviceacct_CreateDocument,
    "\n  mutation ServiceAcct_delete($where: [ServiceAPIAccountWhereUniqueInput!]!) {\n    deleteServiceAPIAccounts(where: $where) {\n      id\n    }\n  }\n": types.ServiceAcct_DeleteDocument,
    "\n  query ServiceAcct_list($where: ServiceAPIAccountWhereInput!) {\n    serviceAPIAccounts(where: $where) {\n      active\n      allowedMethods\n      createdAt\n      id\n      ipWhitelist\n      secretView\n      token\n      expiresAt\n    }\n  }\n": types.ServiceAcct_ListDocument,
    "\n  query ServiceAcct_get($where: ServiceAPIAccountWhereUniqueInput!) {\n    serviceAPIAccount(where: $where) {\n      id\n      token\n      secretView\n      active\n      ipWhitelist\n      allowedMethods\n      createdAt\n      updatedAt\n    }\n  }\n": types.ServiceAcct_GetDocument,
    "\n  mutation ServiceAcct_update($data: [ServiceAPIAccountUpdateArgs!]!) {\n    updateServiceAPIAccounts(data: $data) {\n      id\n    }\n  }\n": types.ServiceAcct_UpdateDocument,
    "\n  query StripeDataApplications {\n    stripeDataApplications {\n      id\n      keyType\n      publishableKey\n      secretKey\n      accessKey\n      refreshKey\n      userId\n      stripeUserId\n      accountId\n      tokenType\n      scope\n      liveMode\n    }\n  }\n": types.StripeDataApplicationsDocument,
    "\n  mutation DeleteStripeDataApplications($where: [StripeDataApplicationWhereUniqueInput!]!) {\n    deleteStripeDataApplications(where: $where) {\n      id\n    }\n  }\n": types.DeleteStripeDataApplicationsDocument,
    "\n  query GroupSupportTickets(\n    $where: GroupSupportTicketWhereInput!\n    $orderBy: [GroupSupportTicketOrderByInput!]!\n    $take: Int\n    $skip: Int!\n  ) {\n    groupSupportTickets(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      title\n      category\n      description\n      status\n      messagesCount\n      createdAt\n      lastMessageCreatedAt\n      group {\n        actualName\n        labelName\n        name\n        id\n      }\n      createdBy {\n        displayName\n        id\n      }\n    }\n    groupSupportTicketsCount(where: $where)\n  }\n": types.GroupSupportTicketsDocument,
    "\n  query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {\n    groupSupportTicketsCount(where: $where)\n  }\n": types.GroupSupportTicketsCountDocument,
    "\n  query GroupSupportTicket($where: GroupSupportTicketWhereUniqueInput!) {\n    groupSupportTicket(where: $where) {\n      id\n      title\n      status\n      category\n      description\n      messagesCount\n      createdAt\n      lastMessageCreatedAt\n      group {\n        actualName\n        labelName\n        name\n        id\n      }\n      createdBy {\n        displayName\n        id\n      }\n      messages {\n        createdAt\n        actualName\n        name\n        message\n        files\n        id\n      }\n    }\n  }\n": types.GroupSupportTicketDocument,
    "\n  mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {\n    createGroupSupportTicket(data: $data) {\n      id\n    }\n  }\n": types.CreateGroupSupportTicketDocument,
    "\n  mutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {\n    createGroupSupportMessage(data: $data) {\n      id\n    }\n  }\n": types.CreateGroupSupportMessageDocument,
    "\n  mutation TsepManifest {\n    tsep_manifest {\n      deviceID\n      manifestKey\n      url\n    }\n  }\n": types.TsepManifestDocument,
    "\n  mutation TsepVerify($input: Tsep_verifyInput!) {\n    tsep_verify(input: $input) {\n      status\n      code\n      message\n      authCode\n      cardType\n      addressVerificationCode\n      maskedCardNumber\n      expirationDate\n    }\n  }\n": types.TsepVerifyDocument,
    "\n  mutation GPHFToken($input: Gphf_tokenInput!) {\n    gphf_token(input: $input) {\n      token\n      merchantID\n    }\n  }\n": types.GphfTokenDocument,
    "\n  query GroupMembers($where: GroupMemberWhereInput!, $take: Int, $skip: Int!) {\n    groupMembers(where: $where, take: $take, skip: $skip) {\n      id\n      access\n      group {\n        id\n        name\n      }\n      invite {\n        id\n        email\n      }\n      user {\n        id\n        lastLogin\n        name\n        lastName\n        createdAt\n        email\n      }\n    }\n    groupMembersCount(where: $where)\n  }\n": types.GroupMembersDocument,
    "\n  query GetQuickUserInfo($where: UserWhereUniqueInput!) {\n    user(where: $where) {\n      id\n      name\n      lastName\n      phone\n      email\n      title\n    }\n  }\n": types.GetQuickUserInfoDocument,
    "\n  query GetQuickInviteInfo($where: GroupMemberInviteWhereUniqueInput!) {\n    groupMemberInvite(where: $where) {\n      id\n      email\n    }\n  }\n": types.GetQuickInviteInfoDocument,
    "\n  query GetUserMembershipInfo($take: Int, $where: GroupMemberWhereInput!) {\n    groupMembers(take: $take, where: $where) {\n      access\n      flags {\n        id\n        flag\n      }\n      id\n      invite {\n        id\n        email\n      }\n      user {\n        id\n        email\n      }\n      group {\n        id\n      }\n    }\n  }\n": types.GetUserMembershipInfoDocument,
    "\n  query Flags($input: Group_flagsInput!) {\n    group_flags(input: $input) {\n      flags {\n        name\n        key\n        category\n        description\n      }\n    }\n  }\n": types.FlagsDocument,
    "\n  mutation UpdateGroupMemberData(\n    $where: GroupMemberWhereUniqueInput!\n    $updateBaseMemberData: GroupMemberUpdateInput!\n    $createMemberFlags: [GroupMemberFlagCreateInput!]!\n    $deleteMemberFlags: [GroupMemberFlagWhereUniqueInput!]!\n  ) {\n    updateGroupMember(where: $where, data: $updateBaseMemberData) {\n      id\n    }\n    createGroupMemberFlags(data: $createMemberFlags) {\n      id\n    }\n    deleteGroupMemberFlags(where: $deleteMemberFlags) {\n      id\n    }\n  }\n": types.UpdateGroupMemberDataDocument,
    "\n  mutation CreatGroupMemberData($input: Group_createInviteInput!) {\n    group_createInvite(input: $input) {\n      id\n    }\n  }\n": types.CreatGroupMemberDataDocument,
    "\n  mutation DeleteGroupMembers($where: [GroupMemberWhereUniqueInput!]!) {\n    deleteGroupMembers(where: $where) {\n      id\n    }\n  }\n": types.DeleteGroupMembersDocument,
    "\n  query Processor_account_status($input: Processor_account_statusInput!) {\n    processor_account_status(input: $input) {\n      bank {\n        holder_type\n        last4\n        bank_code\n        bank_name\n      }\n      status\n      status2\n      pricing_profile\n    }\n  }\n": types.Processor_Account_StatusDocument,
    "\n  mutation Processor_account_payoutUpdate($input: Processor_account_payoutUpdateInput!) {\n    processor_account_payoutUpdate(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n": types.Processor_Account_PayoutUpdateDocument,
    "\n  mutation WebhookCreate($input: Group_createWebhookInput!) {\n    group_createWebhook(input: $input) {\n      id\n      url\n      secret\n    }\n  }\n": types.WebhookCreateDocument,
    "\n  query GroupWebhooks(\n    $where: GroupWebhookWhereInput!\n    $orderBy: [GroupWebhookOrderByInput!]!\n    $take: Int\n    $skip: Int!\n  ) {\n    groupWebhooks(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      webhookURL\n      scope\n      group {\n        name\n        id\n      }\n    }\n  }\n": types.GroupWebhooksDocument,
    "\n  mutation WebhookDelete($where: [GroupWebhookWhereUniqueInput!]!) {\n    deleteGroupWebhooks(where: $where) {\n      id\n    }\n  }\n": types.WebhookDeleteDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Propay_query($input: Propay_queryInput!) {\n  propay_query(input: $input)\n}\n\nmutation Propay_tables {\n  propay_tables {\n    tables\n  }\n}\n\nmutation Propay_status {\n  propay_status {\n    allRunning\n    components {\n      name\n      status\n      operational\n    }\n  }\n}\n\nmutation Propay_links($input: Propay_linksInput!) {\n  propay_links(input: $input) {\n    id\n    account_name\n    status\n    type\n    function\n    time_created\n    time_last_updated\n    url\n    action {\n      id\n      type\n      time_created\n      result_code\n      app_id\n      app_name\n    }\n  }\n}"): (typeof documents)["mutation Propay_query($input: Propay_queryInput!) {\n  propay_query(input: $input)\n}\n\nmutation Propay_tables {\n  propay_tables {\n    tables\n  }\n}\n\nmutation Propay_status {\n  propay_status {\n    allRunning\n    components {\n      name\n      status\n      operational\n    }\n  }\n}\n\nmutation Propay_links($input: Propay_linksInput!) {\n  propay_links(input: $input) {\n    id\n    account_name\n    status\n    type\n    function\n    time_created\n    time_last_updated\n    url\n    action {\n      id\n      type\n      time_created\n      result_code\n      app_id\n      app_name\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {\n  groupSupportTicketsCount(where: $where)\n}\n\nmutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {\n  createGroupSupportMessage(data: $data) {\n    id\n  }\n}\n\nquery Propay_admin_balance($input: Propay_admin_balanceInput!) {\n  propay_admin_balance(input: $input) {\n    balance\n    itemCount\n    positives\n    positiveItemCount\n    negatives\n    negativeItemCount\n    positiveUncounted\n    positiveUncountedItemCount\n    negativeUncounted\n    negativeUncountedItemCount\n    actualCount\n    rangeStart\n    rangeEnd\n    propayBalance\n  }\n}"): (typeof documents)["query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {\n  groupSupportTicketsCount(where: $where)\n}\n\nmutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {\n  createGroupSupportMessage(data: $data) {\n    id\n  }\n}\n\nquery Propay_admin_balance($input: Propay_admin_balanceInput!) {\n  propay_admin_balance(input: $input) {\n    balance\n    itemCount\n    positives\n    positiveItemCount\n    negatives\n    negativeItemCount\n    positiveUncounted\n    positiveUncountedItemCount\n    negativeUncounted\n    negativeUncountedItemCount\n    actualCount\n    rangeStart\n    rangeEnd\n    propayBalance\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Affiliation_get {\n    affiliation_get {\n      id\n      affiliatesCount\n      groupAffiliatesCount\n      codesCount\n      bank_routingNumber\n      bank_accountNumber\n      bank_accountName\n      codes {\n        code\n        description\n      }\n    }\n  }\n"): (typeof documents)["\n  query Affiliation_get {\n    affiliation_get {\n      id\n      affiliatesCount\n      groupAffiliatesCount\n      codesCount\n      bank_routingNumber\n      bank_accountNumber\n      bank_accountName\n      codes {\n        code\n        description\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Affiliation_initiate {\n    affiliation_initiate {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation Affiliation_initiate {\n    affiliation_initiate {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Affiliation_setAffiliateCode($input: Affiliation_setAffiliateCodeInput!) {\n    affiliation_setAffiliateCode(input: $input) {\n      code\n      description\n    }\n  }\n"): (typeof documents)["\n  mutation Affiliation_setAffiliateCode($input: Affiliation_setAffiliateCodeInput!) {\n    affiliation_setAffiliateCode(input: $input) {\n      code\n      description\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Affiliation_setBankAccount($input: Affiliation_setBankAccountInput!) {\n    affiliation_setBankAccount(input: $input)\n  }\n"): (typeof documents)["\n  mutation Affiliation_setBankAccount($input: Affiliation_setBankAccountInput!) {\n    affiliation_setBankAccount(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Affiliation_getAffiliatedUsersWithMerchants(\n    $input: Affiliation_getAffiliatedUsersWithMerchantsInput!\n  ) {\n    affiliation_getAffiliatedUsersWithMerchants(input: $input) {\n      totalEarnings\n      totalWithdrawn\n      totalWithdrawable\n      data {\n        user {\n          id\n          name\n          email\n          lastLogin\n          title\n          earnings\n        }\n        merchant {\n          name\n          city\n          state\n          status\n          createdAt\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query Affiliation_getAffiliatedUsersWithMerchants(\n    $input: Affiliation_getAffiliatedUsersWithMerchantsInput!\n  ) {\n    affiliation_getAffiliatedUsersWithMerchants(input: $input) {\n      totalEarnings\n      totalWithdrawn\n      totalWithdrawable\n      data {\n        user {\n          id\n          name\n          email\n          lastLogin\n          title\n          earnings\n        }\n        merchant {\n          name\n          city\n          state\n          status\n          createdAt\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AiMccFromDescription($input: Ai_determineMCCFromSentenceInput!) {\n    ai_determineMCCFromSentence(input: $input)\n  }\n"): (typeof documents)["\n  mutation AiMccFromDescription($input: Ai_determineMCCFromSentenceInput!) {\n    ai_determineMCCFromSentence(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Authclient_login($email: String!, $browserId: String, $password: String!) {\n    authclient_login(email: $email, browserId: $browserId, password: $password) {\n      ... on ClientItemAuthenticationWithPasswordSuccess {\n        sessionToken\n      }\n      ... on ClientItemAuthenticationWithPasswordFailure {\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation Authclient_login($email: String!, $browserId: String, $password: String!) {\n    authclient_login(email: $email, browserId: $browserId, password: $password) {\n      ... on ClientItemAuthenticationWithPasswordSuccess {\n        sessionToken\n      }\n      ... on ClientItemAuthenticationWithPasswordFailure {\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Authclient_register(\n    $email: String!\n    $password: String!\n    $phoneNumber: String\n    $otpSid: String\n    $browserId: String\n    $firstName: String\n    $lastName: String\n    $affiliateId: String\n    $title: String\n  ) {\n    authclient_register(\n      email: $email\n      password: $password\n      phoneNumber: $phoneNumber\n      otpSid: $otpSid\n      browserId: $browserId\n      firstName: $firstName\n      lastName: $lastName\n      affiliateId: $affiliateId\n      title: $title\n    )\n  }\n"): (typeof documents)["\n  mutation Authclient_register(\n    $email: String!\n    $password: String!\n    $phoneNumber: String\n    $otpSid: String\n    $browserId: String\n    $firstName: String\n    $lastName: String\n    $affiliateId: String\n    $title: String\n  ) {\n    authclient_register(\n      email: $email\n      password: $password\n      phoneNumber: $phoneNumber\n      otpSid: $otpSid\n      browserId: $browserId\n      firstName: $firstName\n      lastName: $lastName\n      affiliateId: $affiliateId\n      title: $title\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCache($input: Cache_getInput!) {\n    cache_get(input: $input) {\n      data\n      id\n      keyword\n    }\n  }\n"): (typeof documents)["\n  query GetCache($input: Cache_getInput!) {\n    cache_get(input: $input) {\n      data\n      id\n      keyword\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SaveCache($input: Cache_saveInput!) {\n    cache_save(input: $input) {\n      id\n      keyword\n      data\n    }\n  }\n"): (typeof documents)["\n  mutation SaveCache($input: Cache_saveInput!) {\n    cache_save(input: $input) {\n      id\n      keyword\n      data\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateGroup($data: GroupCreateInput!) {\n    createGroup(data: $data) {\n      id\n      mainGateway\n      mainProcessor\n      processorStatus\n      name\n    }\n  }\n"): (typeof documents)["\n  mutation CreateGroup($data: GroupCreateInput!) {\n    createGroup(data: $data) {\n      id\n      mainGateway\n      mainProcessor\n      processorStatus\n      name\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateGroup($where: GroupWhereUniqueInput!, $data: GroupUpdateInput!) {\n    updateGroup(where: $where, data: $data) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateGroup($where: GroupWhereUniqueInput!, $data: GroupUpdateInput!) {\n    updateGroup(where: $where, data: $data) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation File_upload($input: File_uploadInput!) {\n    file_upload(input: $input) {\n      files {\n        url\n        filename\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation File_upload($input: File_uploadInput!) {\n    file_upload(input: $input) {\n      files {\n        url\n        filename\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Reportdownload_download($input: Reportdownload_downloadInput!) {\n    reportdownload_download(input: $input) {\n      data\n    }\n  }\n"): (typeof documents)["\n  mutation Reportdownload_download($input: Reportdownload_downloadInput!) {\n    reportdownload_download(input: $input) {\n      data\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Processor_aur_zip_search($input: Processor_aur_zip_searchInput!) {\n    processor_aur_zip_search(input: $input) {\n      item {\n        zip\n        latitude\n        longitude\n        city\n        state\n        country\n      }\n    }\n  }\n"): (typeof documents)["\n  query Processor_aur_zip_search($input: Processor_aur_zip_searchInput!) {\n    processor_aur_zip_search(input: $input) {\n      item {\n        zip\n        latitude\n        longitude\n        city\n        state\n        country\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Processor_aur_bank_routing($input: Processor_aur_bank_routingInput!) {\n    processor_aur_bank_routing(input: $input) {\n      items {\n        routing\n        bank\n      }\n      count\n    }\n  }\n"): (typeof documents)["\n  query Processor_aur_bank_routing($input: Processor_aur_bank_routingInput!) {\n    processor_aur_bank_routing(input: $input) {\n      items {\n        routing\n        bank\n      }\n      count\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Processor_aur_mcc($input: Processor_aur_mccInput!) {\n    processor_aur_mcc(input: $input) {\n      items {\n        id\n        mccCode\n        description\n      }\n      count\n    }\n  }\n"): (typeof documents)["\n  query Processor_aur_mcc($input: Processor_aur_mccInput!) {\n    processor_aur_mcc(input: $input) {\n      items {\n        id\n        mccCode\n        description\n      }\n      count\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Ghl_api_getMerchantGHL($input: Ghl_api_getMerchantGHLInput!) {\n    ghl_api_getMerchantGHL(input: $input) {\n      data {\n        id\n        companyId\n        name\n        domain\n        address\n        city\n        state\n        logoUrl\n        country\n        postalCode\n        website\n        timezone\n        firstName\n        lastName\n        email\n        phone\n      }\n    }\n  }\n"): (typeof documents)["\n  query Ghl_api_getMerchantGHL($input: Ghl_api_getMerchantGHLInput!) {\n    ghl_api_getMerchantGHL(input: $input) {\n      data {\n        id\n        companyId\n        name\n        domain\n        address\n        city\n        state\n        logoUrl\n        country\n        postalCode\n        website\n        timezone\n        firstName\n        lastName\n        email\n        phone\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GHLIntegDetails($input: Ghl_auth_getIntegrationDetailsInput!) {\n    ghl_auth_getIntegrationDetails(input: $input) {\n      locationID\n      locationName\n      accountName\n    }\n  }\n"): (typeof documents)["\n  query GHLIntegDetails($input: Ghl_auth_getIntegrationDetailsInput!) {\n    ghl_auth_getIntegrationDetails(input: $input) {\n      locationID\n      locationName\n      accountName\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GHLSetupGetPending($input: Ghl_auth_checkPendingInstallInput!) {\n    ghl_auth_checkPendingInstall(input: $input) {\n      code\n    }\n  }\n"): (typeof documents)["\n  query GHLSetupGetPending($input: Ghl_auth_checkPendingInstallInput!) {\n    ghl_auth_checkPendingInstall(input: $input) {\n      code\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GHLSetupCheck($input: Ghl_auth_checkGHLAccessInput!) {\n    ghl_auth_checkGHLAccess(input: $input) {\n      hasAccess\n    }\n  }\n"): (typeof documents)["\n  query GHLSetupCheck($input: Ghl_auth_checkGHLAccessInput!) {\n    ghl_auth_checkGHLAccess(input: $input) {\n      hasAccess\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GHLSetupCommit($input: Ghl_auth_completeIntegrationInput!) {\n    ghl_auth_completeIntegration(input: $input)\n  }\n"): (typeof documents)["\n  mutation GHLSetupCommit($input: Ghl_auth_completeIntegrationInput!) {\n    ghl_auth_completeIntegration(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GHLSSOCheck($input: Ghl_auth_getSSOInfoInput!) {\n    ghl_auth_getSSOInfo(input: $input) {\n      locationID\n      accountName\n      email\n      groupID\n      groupName\n    }\n  }\n"): (typeof documents)["\n  query GHLSSOCheck($input: Ghl_auth_getSSOInfoInput!) {\n    ghl_auth_getSSOInfo(input: $input) {\n      locationID\n      accountName\n      email\n      groupID\n      groupName\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GHLSSOBind($input: Ghl_auth_bindInput!) {\n    ghl_auth_bind(input: $input) {\n      ghlUserID\n      localUserID\n      locationID\n    }\n  }\n"): (typeof documents)["\n  mutation GHLSSOBind($input: Ghl_auth_bindInput!) {\n    ghl_auth_bind(input: $input) {\n      ghlUserID\n      localUserID\n      locationID\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GHLSSOSignIn($input: Ghl_auth_ssoInput!) {\n    ghl_auth_sso(input: $input) {\n      sessionToken\n      item {\n        id\n        email\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation GHLSSOSignIn($input: Ghl_auth_ssoInput!) {\n    ghl_auth_sso(input: $input) {\n      sessionToken\n      item {\n        id\n        email\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Groups($where: GroupWhereInput!, $take: Int, $skip: Int!) {\n    groups(where: $where, skip: $skip, take: $take) {\n      id\n      name\n      membersCount\n      processorStatus\n      mainProcessor\n      mainGateway\n      # processorAURCount\n    }\n  }\n"): (typeof documents)["\n  query Groups($where: GroupWhereInput!, $take: Int, $skip: Int!) {\n    groups(where: $where, skip: $skip, take: $take) {\n      id\n      name\n      membersCount\n      processorStatus\n      mainProcessor\n      mainGateway\n      # processorAURCount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteGroup($where: GroupWhereUniqueInput!) {\n    deleteGroup(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteGroup($where: GroupWhereUniqueInput!) {\n    deleteGroup(where: $where) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GroupList(\n    $where: GroupWhereInput!\n    $orderBy: [GroupOrderByInput!]\n    $take: Int\n    $skip: Int = 0\n  ) {\n    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      name\n      labelName\n      actualName\n      membersCount\n      processorStatus\n      signingURL\n      mainProcessor\n      mainGateway\n      einNumber\n      createdAt\n      updatedAt\n      displayMerchantID\n      pciStatus\n      firstName\n      lastName\n      fullName\n      addressLine1\n      addressLine2\n      city\n      state\n      country\n      zip\n      mccCode\n      phoneNumber\n      email\n      default_includeSurcharge\n      default_globalDisableCC\n      default_globalDisableACH\n      ghlAccessesCount\n      hubspotAccessesCount\n      ghlPayTransactionMapsCount\n      supportTicketsCount\n      serviceAccountsCount\n      flag_disableTokens\n      flag_disableAutoToken\n    }\n    groupsCount(where: $where)\n  }\n"): (typeof documents)["\n  query GroupList(\n    $where: GroupWhereInput!\n    $orderBy: [GroupOrderByInput!]\n    $take: Int\n    $skip: Int = 0\n  ) {\n    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      name\n      labelName\n      actualName\n      membersCount\n      processorStatus\n      signingURL\n      mainProcessor\n      mainGateway\n      einNumber\n      createdAt\n      updatedAt\n      displayMerchantID\n      pciStatus\n      firstName\n      lastName\n      fullName\n      addressLine1\n      addressLine2\n      city\n      state\n      country\n      zip\n      mccCode\n      phoneNumber\n      email\n      default_includeSurcharge\n      default_globalDisableCC\n      default_globalDisableACH\n      ghlAccessesCount\n      hubspotAccessesCount\n      ghlPayTransactionMapsCount\n      supportTicketsCount\n      serviceAccountsCount\n      flag_disableTokens\n      flag_disableAutoToken\n    }\n    groupsCount(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetGroupList(\n    $where: GroupWhereInput!\n    $orderBy: [GroupOrderByInput!]\n    $take: Int\n    $skip: Int = 0\n  ) {\n    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      name\n      labelName\n      actualName\n      membersCount\n      processorStatus\n      signingURL\n      mainProcessor\n      mainGateway\n      einNumber\n      createdAt\n      updatedAt\n      displayMerchantID\n      pciStatus\n      firstName\n      lastName\n      fullName\n      addressLine1\n      addressLine2\n      city\n      state\n      country\n      zip\n      mccCode\n      phoneNumber\n      email\n      default_includeSurcharge\n      default_globalDisableCC\n      default_globalDisableACH\n      ghlAccessesCount\n      hubspotAccessesCount\n      ghlPayTransactionMapsCount\n      supportTicketsCount\n      serviceAccountsCount\n      flag_disableTokens\n      flag_disableAutoToken\n    }\n    groupsCount(where: $where)\n  }\n"): (typeof documents)["\n  query GetGroupList(\n    $where: GroupWhereInput!\n    $orderBy: [GroupOrderByInput!]\n    $take: Int\n    $skip: Int = 0\n  ) {\n    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      name\n      labelName\n      actualName\n      membersCount\n      processorStatus\n      signingURL\n      mainProcessor\n      mainGateway\n      einNumber\n      createdAt\n      updatedAt\n      displayMerchantID\n      pciStatus\n      firstName\n      lastName\n      fullName\n      addressLine1\n      addressLine2\n      city\n      state\n      country\n      zip\n      mccCode\n      phoneNumber\n      email\n      default_includeSurcharge\n      default_globalDisableCC\n      default_globalDisableACH\n      ghlAccessesCount\n      hubspotAccessesCount\n      ghlPayTransactionMapsCount\n      supportTicketsCount\n      serviceAccountsCount\n      flag_disableTokens\n      flag_disableAutoToken\n    }\n    groupsCount(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Processor_aur_draft_file_delete($input: Processor_aur_draft_file_deleteInput!) {\n    processor_aur_draft_file_delete(input: $input)\n  }\n"): (typeof documents)["\n  mutation Processor_aur_draft_file_delete($input: Processor_aur_draft_file_deleteInput!) {\n    processor_aur_draft_file_delete(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SignGroup($input: Group_signGroupInput!) {\n    group_signGroup(input: $input)\n  }\n"): (typeof documents)["\n  mutation SignGroup($input: Group_signGroupInput!) {\n    group_signGroup(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SyncStripeIntegration($input: Stripe_syncInput!) {\n    stripe_sync(input: $input) {\n      count\n    }\n  }\n"): (typeof documents)["\n  mutation SyncStripeIntegration($input: Stripe_syncInput!) {\n    stripe_sync(input: $input) {\n      count\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SaveStripeIntegration($input: Stripe_setupInput!) {\n    stripe_setup(input: $input)\n  }\n"): (typeof documents)["\n  mutation SaveStripeIntegration($input: Stripe_setupInput!) {\n    stripe_setup(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query DownloadStripeTemplateCSV($input: Import_stripe_sampleInput!) {\n    import_stripe_sample(input: $input) {\n      sampleData\n    }\n  }\n"): (typeof documents)["\n  query DownloadStripeTemplateCSV($input: Import_stripe_sampleInput!) {\n    import_stripe_sample(input: $input) {\n      sampleData\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetInviteInfo($input: Group_getInviteInfoInput!) {\n    group_getInviteInfo(input: $input) {\n      alreadySigned\n      email\n      groupName\n    }\n  }\n"): (typeof documents)["\n  query GetInviteInfo($input: Group_getInviteInfoInput!) {\n    group_getInviteInfo(input: $input) {\n      alreadySigned\n      email\n      groupName\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation ClaimInvite($input: Group_claimInviteInput!) {\n    group_claimInvite(input: $input)\n  }\n"): (typeof documents)["\n  mutation ClaimInvite($input: Group_claimInviteInput!) {\n    group_claimInvite(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeclineInvite($input: Group_declineInviteInput!) {\n    group_declineInvite(input: $input)\n  }\n"): (typeof documents)["\n  mutation DeclineInvite($input: Group_declineInviteInput!) {\n    group_declineInvite(input: $input)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation LeadsLogin($input: Leads_loginInput!) {\n    leads_login(input: $input) {\n      sessionToken\n      email\n      password\n    }\n  }\n"): (typeof documents)["\n  mutation LeadsLogin($input: Leads_loginInput!) {\n    leads_login(input: $input) {\n      sessionToken\n      email\n      password\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Mutation {\n    leads_initialize\n  }\n"): (typeof documents)["\n  mutation Mutation {\n    leads_initialize\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Me {\n    authenticatedItem {\n      ... on User {\n        id\n        name\n        lastName\n        displayName\n        email\n        phone\n        title\n        role\n        createdAt\n        groupsCount\n        flag_canAffiliate\n      }\n    }\n  }\n"): (typeof documents)["\n  query Me {\n    authenticatedItem {\n      ... on User {\n        id\n        name\n        lastName\n        displayName\n        email\n        phone\n        title\n        role\n        createdAt\n        groupsCount\n        flag_canAffiliate\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateUser($where: UserWhereUniqueInput!, $data: UserUpdateInput!) {\n    updateUser(where: $where, data: $data) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateUser($where: UserWhereUniqueInput!, $data: UserUpdateInput!) {\n    updateUser(where: $where, data: $data) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Authclient_changePassword($oldPassword: String!, $newPassword: String!) {\n    authclient_changePassword(oldPassword: $oldPassword, newPassword: $newPassword)\n  }\n"): (typeof documents)["\n  mutation Authclient_changePassword($oldPassword: String!, $newPassword: String!) {\n    authclient_changePassword(oldPassword: $oldPassword, newPassword: $newPassword)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Authclient_requestPasswordReset($email: String!) {\n    authclient_requestPasswordReset(email: $email)\n  }\n"): (typeof documents)["\n  mutation Authclient_requestPasswordReset($email: String!) {\n    authclient_requestPasswordReset(email: $email)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Authclient_resetPassword($token: String!, $password: String!) {\n    authclient_resetPassword(token: $token, password: $password)\n  }\n"): (typeof documents)["\n  mutation Authclient_resetPassword($token: String!, $password: String!) {\n    authclient_resetPassword(token: $token, password: $password)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GHLSSOBindings {\n    gHLSSOBindings {\n      id\n      locationID\n      ghlUserID\n      createdAt\n    }\n  }\n"): (typeof documents)["\n  query GHLSSOBindings {\n    gHLSSOBindings {\n      id\n      locationID\n      ghlUserID\n      createdAt\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteGHLSSOBinding($where: GHLSSOBindingWhereUniqueInput!) {\n    deleteGHLSSOBinding(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteGHLSSOBinding($where: GHLSSOBindingWhereUniqueInput!) {\n    deleteGHLSSOBinding(where: $where) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_closeBatch($input: Gateway_closeBatchInput!) {\n  gateway_closeBatch(input: $input) {\n    batchID\n    status\n  }\n}"): (typeof documents)["mutation Gateway_closeBatch($input: Gateway_closeBatchInput!) {\n  gateway_closeBatch(input: $input) {\n    batchID\n    status\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_createDiscount($input: Gateway_createDiscountInput!) {\n  gateway_createDiscount(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_updateDiscount($input: Gateway_updateDiscountInput!) {\n  gateway_updateDiscount(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_deleteDiscount($input: Gateway_deleteDiscountInput!) {\n  gateway_deleteDiscount(input: $input) {\n    id\n  }\n}"): (typeof documents)["mutation Gateway_createDiscount($input: Gateway_createDiscountInput!) {\n  gateway_createDiscount(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_updateDiscount($input: Gateway_updateDiscountInput!) {\n  gateway_updateDiscount(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_deleteDiscount($input: Gateway_deleteDiscountInput!) {\n  gateway_deleteDiscount(input: $input) {\n    id\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_createCategory($input: Gateway_createCategoryInput!) {\n  gateway_createCategory(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_updateCategory($input: Gateway_updateCategoryInput!) {\n  gateway_updateCategory(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_deleteCategory($input: Gateway_deleteCategoryInput!) {\n  gateway_deleteCategory(input: $input) {\n    id\n  }\n}"): (typeof documents)["mutation Gateway_createCategory($input: Gateway_createCategoryInput!) {\n  gateway_createCategory(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_updateCategory($input: Gateway_updateCategoryInput!) {\n  gateway_updateCategory(input: $input) {\n    id\n  }\n}\n\nmutation Gateway_deleteCategory($input: Gateway_deleteCategoryInput!) {\n  gateway_deleteCategory(input: $input) {\n    id\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_addCustomer($input: Gateway_addCustomerInput!) {\n  gateway_addCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {\n  gateway_updateCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {\n  gateway_updateCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_deleteCustomer($input: Gateway_deleteCustomerInput!) {\n  gateway_deleteCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_addPaymentMethod($input: Gateway_addPaymentMethodInput!) {\n  gateway_addPaymentMethod(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_deletePaymentMethod($input: Gateway_deletePaymentMethodInput!) {\n  gateway_deletePaymentMethod(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_setDefaultPaymentMethod($input: Gateway_setDefaultPaymentMethodInput!) {\n  gateway_setDefaultPaymentMethod(input: $input) {\n    customerID\n  }\n}"): (typeof documents)["mutation Gateway_addCustomer($input: Gateway_addCustomerInput!) {\n  gateway_addCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {\n  gateway_updateCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {\n  gateway_updateCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_deleteCustomer($input: Gateway_deleteCustomerInput!) {\n  gateway_deleteCustomer(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_addPaymentMethod($input: Gateway_addPaymentMethodInput!) {\n  gateway_addPaymentMethod(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_deletePaymentMethod($input: Gateway_deletePaymentMethodInput!) {\n  gateway_deletePaymentMethod(input: $input) {\n    customerID\n  }\n}\n\nmutation Gateway_setDefaultPaymentMethod($input: Gateway_setDefaultPaymentMethodInput!) {\n  gateway_setDefaultPaymentMethod(input: $input) {\n    customerID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_submitDisputeDocument($input: Gateway_submitDisputeDocumentInput!) {\n  gateway_submitDisputeDocument(input: $input) {\n    disputeID\n  }\n}\n\nmutation Gateway_attendDispute($input: Gateway_attendDisputeInput!) {\n  gateway_attendDispute(input: $input) {\n    disputeID\n  }\n}"): (typeof documents)["mutation Gateway_submitDisputeDocument($input: Gateway_submitDisputeDocumentInput!) {\n  gateway_submitDisputeDocument(input: $input) {\n    disputeID\n  }\n}\n\nmutation Gateway_attendDispute($input: Gateway_attendDisputeInput!) {\n  gateway_attendDispute(input: $input) {\n    disputeID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation GatewayInitialize($input: Gateway_flpayset_initializeInput!) {\n  gateway_flpayset_initialize(input: $input) {\n    merchantID\n    processorID\n  }\n}"): (typeof documents)["mutation GatewayInitialize($input: Gateway_flpayset_initializeInput!) {\n  gateway_flpayset_initialize(input: $input) {\n    merchantID\n    processorID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Import_stripe($input: Import_stripeInput!) {\n  import_stripe(input: $input) {\n    processedItems\n    type\n  }\n}"): (typeof documents)["mutation Import_stripe($input: Import_stripeInput!) {\n  import_stripe(input: $input) {\n    processedItems\n    type\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_createManualEntry($input: Gateway_createManualEntryInput!) {\n  gateway_createManualEntry(input: $input) {\n    transactionID\n  }\n}\n\nmutation Gateway_revertTransaction($input: Gateway_revertTransactionInput!) {\n  gateway_revertTransaction(input: $input) {\n    transactionID\n  }\n}"): (typeof documents)["mutation Gateway_createManualEntry($input: Gateway_createManualEntryInput!) {\n  gateway_createManualEntry(input: $input) {\n    transactionID\n  }\n}\n\nmutation Gateway_revertTransaction($input: Gateway_revertTransactionInput!) {\n  gateway_revertTransaction(input: $input) {\n    transactionID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_createPaymentPlan($input: Gateway_createPaymentPlanInput!) {\n  gateway_createPaymentPlan(input: $input) {\n    planID\n  }\n}\n\nmutation Gateway_cancelPaymentPlan($input: Gateway_cancelPaymentPlanInput!) {\n  gateway_cancelPaymentPlan(input: $input) {\n    planID\n  }\n}\n\nmutation Gateway_updatePaymentPlan($input: Gateway_updatePaymentPlanInput!) {\n  gateway_updatePaymentPlan(input: $input) {\n    planID\n  }\n}"): (typeof documents)["mutation Gateway_createPaymentPlan($input: Gateway_createPaymentPlanInput!) {\n  gateway_createPaymentPlan(input: $input) {\n    planID\n  }\n}\n\nmutation Gateway_cancelPaymentPlan($input: Gateway_cancelPaymentPlanInput!) {\n  gateway_cancelPaymentPlan(input: $input) {\n    planID\n  }\n}\n\nmutation Gateway_updatePaymentPlan($input: Gateway_updatePaymentPlanInput!) {\n  gateway_updatePaymentPlan(input: $input) {\n    planID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_createProduct($input: Gateway_createProductInput!) {\n  gateway_createProduct(input: $input) {\n    productID\n  }\n}\n\nmutation Gateway_deleteProduct($input: Gateway_deleteProductInput!) {\n  gateway_deleteProduct(input: $input) {\n    productID\n  }\n}\n\nmutation Gateway_updateProduct($input: Gateway_updateProductInput!) {\n  gateway_updateProduct(input: $input) {\n    productID\n  }\n}"): (typeof documents)["mutation Gateway_createProduct($input: Gateway_createProductInput!) {\n  gateway_createProduct(input: $input) {\n    productID\n  }\n}\n\nmutation Gateway_deleteProduct($input: Gateway_deleteProductInput!) {\n  gateway_deleteProduct(input: $input) {\n    productID\n  }\n}\n\nmutation Gateway_updateProduct($input: Gateway_updateProductInput!) {\n  gateway_updateProduct(input: $input) {\n    productID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {\n  createGroupSupportTicket(data: $data) {\n    id\n  }\n}\n\nmutation UpdateGroupSupportTicket($where: GroupSupportTicketWhereUniqueInput!, $data: GroupSupportTicketUpdateInput!) {\n  updateGroupSupportTicket(where: $where, data: $data) {\n    id\n    title\n    category\n    description\n    status\n    messagesCount\n    createdAt\n    lastMessageCreatedAt\n  }\n}"): (typeof documents)["mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {\n  createGroupSupportTicket(data: $data) {\n    id\n  }\n}\n\nmutation UpdateGroupSupportTicket($where: GroupSupportTicketWhereUniqueInput!, $data: GroupSupportTicketUpdateInput!) {\n  updateGroupSupportTicket(where: $where, data: $data) {\n    id\n    title\n    category\n    description\n    status\n    messagesCount\n    createdAt\n    lastMessageCreatedAt\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation Gateway_revertTransaction($input: Gateway_revertTransactionInput!) {\n  gateway_revertTransaction(input: $input) {\n    transactionID\n  }\n}"): (typeof documents)["mutation Gateway_revertTransaction($input: Gateway_revertTransactionInput!) {\n  gateway_revertTransaction(input: $input) {\n    transactionID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Notifications(\n    $where: NotificationWhereInput!\n    $orderBy: [NotificationOrderByInput!]!\n    $skip: Int!\n    $take: Int\n  ) {\n    notifications(where: $where, orderBy: $orderBy, skip: $skip, take: $take) {\n      id\n      topics\n      read\n      content\n      createdAt\n    }\n  }\n"): (typeof documents)["\n  query Notifications(\n    $where: NotificationWhereInput!\n    $orderBy: [NotificationOrderByInput!]!\n    $skip: Int!\n    $take: Int\n  ) {\n    notifications(where: $where, orderBy: $orderBy, skip: $skip, take: $take) {\n      id\n      topics\n      read\n      content\n      createdAt\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation OauthClientCreate($input: Oauthclient_createInput!) {\n    oauthclient_create(input: $input) {\n      id\n      clientId\n      clientSecret\n    }\n  }\n"): (typeof documents)["\n  mutation OauthClientCreate($input: Oauthclient_createInput!) {\n    oauthclient_create(input: $input) {\n      id\n      clientId\n      clientSecret\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation OauthClientUpdate($input: Oauthclient_updateInput!) {\n    oauthclient_update(input: $input) {\n      id\n      clientId\n      clientSecret\n    }\n  }\n"): (typeof documents)["\n  mutation OauthClientUpdate($input: Oauthclient_updateInput!) {\n    oauthclient_update(input: $input) {\n      id\n      clientId\n      clientSecret\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation OauthClientDelete($where: [OAuth_ClientWhereUniqueInput!]!) {\n    deleteOAuthClients(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation OauthClientDelete($where: [OAuth_ClientWhereUniqueInput!]!) {\n    deleteOAuthClients(where: $where) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query OAuthClients($where: OAuth_ClientWhereInput!, $take: Int, $skip: Int!) {\n    oAuthClients(where: $where, take: $take, skip: $skip) {\n      id\n      clientId\n      redirectUri\n      grantTypes\n      scope\n      createdAt\n      authCodesCount\n      tokensCount\n      group {\n        id\n        name\n        actualName\n        labelName\n      }\n    }\n  }\n"): (typeof documents)["\n  query OAuthClients($where: OAuth_ClientWhereInput!, $take: Int, $skip: Int!) {\n    oAuthClients(where: $where, take: $take, skip: $skip) {\n      id\n      clientId\n      redirectUri\n      grantTypes\n      scope\n      createdAt\n      authCodesCount\n      tokensCount\n      group {\n        id\n        name\n        actualName\n        labelName\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation OAuthTokenDelete($where: [OAuth_TokenWhereUniqueInput!]!) {\n    deleteOAuthTokens(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation OAuthTokenDelete($where: [OAuth_TokenWhereUniqueInput!]!) {\n    deleteOAuthTokens(where: $where) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query OAuthTokens($where: OAuth_TokenWhereInput!, $take: Int, $skip: Int!) {\n    oAuthTokens(where: $where, take: $take, skip: $skip) {\n      id\n      accessToken\n      refreshToken\n      expiresAt\n      refreshTokenExpiresAt\n      scope\n      createdAt\n      group {\n        id\n        name\n        actualName\n        labelName\n      }\n      client {\n        id\n        group {\n          actualName\n          name\n          labelName\n          id\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query OAuthTokens($where: OAuth_TokenWhereInput!, $take: Int, $skip: Int!) {\n    oAuthTokens(where: $where, take: $take, skip: $skip) {\n      id\n      accessToken\n      refreshToken\n      expiresAt\n      refreshTokenExpiresAt\n      scope\n      createdAt\n      group {\n        id\n        name\n        actualName\n        labelName\n      }\n      client {\n        id\n        group {\n          actualName\n          name\n          labelName\n          id\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Otp_generate($input: Otp_generateInput!) {\n    otp_generate(input: $input) {\n      sid\n      to\n    }\n  }\n"): (typeof documents)["\n  mutation Otp_generate($input: Otp_generateInput!) {\n    otp_generate(input: $input) {\n      sid\n      to\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Otp_auth_generate($input: Otp_auth_generateInput!) {\n    otp_auth_generate(input: $input) {\n      sid\n      to\n    }\n  }\n"): (typeof documents)["\n  mutation Otp_auth_generate($input: Otp_auth_generateInput!) {\n    otp_auth_generate(input: $input) {\n      sid\n      to\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Otp_verify($input: Otp_verifyInput!) {\n    otp_verify(input: $input) {\n      sid\n      status\n    }\n  }\n"): (typeof documents)["\n  mutation Otp_verify($input: Otp_verifyInput!) {\n    otp_verify(input: $input) {\n      sid\n      status\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation generateMFA($input: Authclient_mfa_generateInput!) {\n    authclient_mfa_generate(input: $input) {\n      sid\n      to\n    }\n  }\n"): (typeof documents)["\n  mutation generateMFA($input: Authclient_mfa_generateInput!) {\n    authclient_mfa_generate(input: $input) {\n      sid\n      to\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation verifyMFA($input: Authclient_mfa_verifyInput!) {\n    authclient_mfa_verify(input: $input) {\n      sid\n      status\n    }\n  }\n"): (typeof documents)["\n  mutation verifyMFA($input: Authclient_mfa_verifyInput!) {\n    authclient_mfa_verify(input: $input) {\n      sid\n      status\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Paylink_create($input: Gateway_createPayLinkInput!) {\n    gateway_createPayLink(input: $input) {\n      payLinkID\n      paymentData\n    }\n  }\n"): (typeof documents)["\n  mutation Paylink_create($input: Gateway_createPayLinkInput!) {\n    gateway_createPayLink(input: $input) {\n      payLinkID\n      paymentData\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Paylink_delete($input: Gateway_deletePayLinkInput!) {\n    gateway_deletePayLink(input: $input) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation Paylink_delete($input: Gateway_deletePayLinkInput!) {\n    gateway_deletePayLink(input: $input) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Gateway_payLinks($input: Gateway_payLinksInput!) {\n    gateway_payLinks(input: $input) {\n      data {\n        id\n        paymentData\n        items\n        total\n        createdAt\n      }\n      page {\n        total\n        range {\n          from\n          to\n        }\n        page\n        pageSize\n      }\n    }\n  }\n"): (typeof documents)["\n  query Gateway_payLinks($input: Gateway_payLinksInput!) {\n    gateway_payLinks(input: $input) {\n      data {\n        id\n        paymentData\n        items\n        total\n        createdAt\n      }\n      page {\n        total\n        range {\n          from\n          to\n        }\n        page\n        pageSize\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Gateway_payLink($input: Gateway_payLinkInput!) {\n    gateway_payLink(input: $input) {\n      id\n      paymentData\n      paymentRaw {\n        pricing {\n          methodVerifyOrProcess\n          lineItems {\n            productId\n            product {\n              sku\n              name\n              description\n              price\n              isRecurring\n              recurringMode\n              recurringFrequency\n              recurringInterval\n              recurringSetupFee\n              recurringTotalCycles\n              recurringTrialDays\n            }\n            amount\n            total\n            metadata\n          }\n          amount\n          tip\n          tipType\n          tax\n          taxType\n          discountCodes\n          shipping\n        }\n        allowEdit\n        allowExtraDiscount\n        allowTip\n        validUntil\n        referenceID\n        onSuccessURL\n        onFailureURL\n      }\n      paymentCheck {\n        breakdown {\n          discount\n          directDiscount\n          actualDiscount\n          tax\n          shipping\n          shippingDiscount\n          shippingDirectDiscount\n          shippingActualDiscount\n          fees\n          actualFees\n          tip\n          subtotal\n          subscriptionTotal\n          rawTotal\n          total\n          expectedTotal\n        }\n        lineItems {\n          productId\n          product {\n            sku\n            name\n            description\n            price\n            isRecurring\n            recurringMode\n            recurringFrequency\n            recurringInterval\n            recurringSetupFee\n            recurringTotalCycles\n            recurringTrialDays\n          }\n          amount\n          total\n          metadata\n        }\n        discountBreakdown {\n          code\n          amount\n        }\n        allowEdit\n        allowExtraDiscount\n        paymentInput {\n          methodVerifyOrProcess\n          lineItems {\n            productId\n            product {\n              sku\n              name\n              description\n              price\n              isRecurring\n              recurringMode\n              recurringFrequency\n              recurringInterval\n              recurringSetupFee\n              recurringTotalCycles\n              recurringTrialDays\n            }\n            amount\n            total\n            metadata\n          }\n          amount\n          tip\n          tipType\n          tax\n          taxType\n          discountCodes\n          shipping\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query Gateway_payLink($input: Gateway_payLinkInput!) {\n    gateway_payLink(input: $input) {\n      id\n      paymentData\n      paymentRaw {\n        pricing {\n          methodVerifyOrProcess\n          lineItems {\n            productId\n            product {\n              sku\n              name\n              description\n              price\n              isRecurring\n              recurringMode\n              recurringFrequency\n              recurringInterval\n              recurringSetupFee\n              recurringTotalCycles\n              recurringTrialDays\n            }\n            amount\n            total\n            metadata\n          }\n          amount\n          tip\n          tipType\n          tax\n          taxType\n          discountCodes\n          shipping\n        }\n        allowEdit\n        allowExtraDiscount\n        allowTip\n        validUntil\n        referenceID\n        onSuccessURL\n        onFailureURL\n      }\n      paymentCheck {\n        breakdown {\n          discount\n          directDiscount\n          actualDiscount\n          tax\n          shipping\n          shippingDiscount\n          shippingDirectDiscount\n          shippingActualDiscount\n          fees\n          actualFees\n          tip\n          subtotal\n          subscriptionTotal\n          rawTotal\n          total\n          expectedTotal\n        }\n        lineItems {\n          productId\n          product {\n            sku\n            name\n            description\n            price\n            isRecurring\n            recurringMode\n            recurringFrequency\n            recurringInterval\n            recurringSetupFee\n            recurringTotalCycles\n            recurringTrialDays\n          }\n          amount\n          total\n          metadata\n        }\n        discountBreakdown {\n          code\n          amount\n        }\n        allowEdit\n        allowExtraDiscount\n        paymentInput {\n          methodVerifyOrProcess\n          lineItems {\n            productId\n            product {\n              sku\n              name\n              description\n              price\n              isRecurring\n              recurringMode\n              recurringFrequency\n              recurringInterval\n              recurringSetupFee\n              recurringTotalCycles\n              recurringTrialDays\n            }\n            amount\n            total\n            metadata\n          }\n          amount\n          tip\n          tipType\n          tax\n          taxType\n          discountCodes\n          shipping\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Gateway_computeCheckout($input: Gateway_computeCheckoutInput!) {\n    gateway_computeCheckout(input: $input) {\n      breakdown {\n        discount\n        directDiscount\n        actualDiscount\n        tax\n        shipping\n        shippingDiscount\n        shippingDirectDiscount\n        shippingActualDiscount\n        fees\n        actualFees\n        tip\n        subtotal\n        subscriptionTotal\n        rawTotal\n        total\n        expectedTotal\n      }\n      lineItems {\n        productId\n        product {\n          sku\n          name\n          description\n          price\n          isRecurring\n          recurringMode\n          recurringInterval\n          recurringFrequency\n          recurringTotalCycles\n          recurringTrialDays\n          recurringSetupFee\n          recurringRefundable\n        }\n        amount\n        total\n        metadata\n      }\n      discountBreakdown {\n        code\n        amount\n      }\n      allowEdit\n      allowExtraDiscount\n      disableACH\n      disableCard\n      paymentInput {\n        methodVerifyOrProcess\n        lineItems {\n          productId\n          product {\n            sku\n            name\n            description\n            price\n            isRecurring\n            recurringMode\n            recurringInterval\n            recurringFrequency\n            recurringTotalCycles\n            recurringTrialDays\n            recurringSetupFee\n            recurringRefundable\n          }\n          amount\n          total\n          metadata\n        }\n        amount\n        tip\n        tipType\n        tax\n        taxType\n        discountCodes\n        shipping\n      }\n      surcharge\n      meta {\n        includeSurcharge\n        encryptedCheckoutToken\n        encrptedCheckoutTokenSecret\n        dynamic {\n          discountCodes\n          paymentType\n          tip {\n            amount\n            type\n          }\n          quantityAmounts {\n            id\n            quantity\n          }\n        }\n        reference {\n          id\n          source\n        }\n      }\n      referenceID\n      onSuccessUrl\n      onFailureUrl\n      customerID\n      customerData {\n        id\n        nameOnCard\n        email\n        phone\n        billingAddress\n        billingCity\n        billingState\n        billingZip\n        billingCountry\n        paymentCards {\n          cardID\n          type\n          isDefault\n          last4\n          brand\n          expires\n          cvc\n          accountNumber\n          routingNumber\n          accountType\n          accountHolderType\n          gpEcommID\n        }\n      }\n      allowTip\n      customerPaymentID\n      prefilledAddress {\n        email\n        phone\n        country\n        state\n        city\n        zip\n        address\n        nameOnCard\n      }\n      transactionHistory {\n        transactionID\n        date\n        amount\n        status\n        note\n      }\n      disableCustomAddress\n      disableCustomPayment\n      disablePreselectCard\n      overideLineItemsAmount\n    }\n  }\n"): (typeof documents)["\n  mutation Gateway_computeCheckout($input: Gateway_computeCheckoutInput!) {\n    gateway_computeCheckout(input: $input) {\n      breakdown {\n        discount\n        directDiscount\n        actualDiscount\n        tax\n        shipping\n        shippingDiscount\n        shippingDirectDiscount\n        shippingActualDiscount\n        fees\n        actualFees\n        tip\n        subtotal\n        subscriptionTotal\n        rawTotal\n        total\n        expectedTotal\n      }\n      lineItems {\n        productId\n        product {\n          sku\n          name\n          description\n          price\n          isRecurring\n          recurringMode\n          recurringInterval\n          recurringFrequency\n          recurringTotalCycles\n          recurringTrialDays\n          recurringSetupFee\n          recurringRefundable\n        }\n        amount\n        total\n        metadata\n      }\n      discountBreakdown {\n        code\n        amount\n      }\n      allowEdit\n      allowExtraDiscount\n      disableACH\n      disableCard\n      paymentInput {\n        methodVerifyOrProcess\n        lineItems {\n          productId\n          product {\n            sku\n            name\n            description\n            price\n            isRecurring\n            recurringMode\n            recurringInterval\n            recurringFrequency\n            recurringTotalCycles\n            recurringTrialDays\n            recurringSetupFee\n            recurringRefundable\n          }\n          amount\n          total\n          metadata\n        }\n        amount\n        tip\n        tipType\n        tax\n        taxType\n        discountCodes\n        shipping\n      }\n      surcharge\n      meta {\n        includeSurcharge\n        encryptedCheckoutToken\n        encrptedCheckoutTokenSecret\n        dynamic {\n          discountCodes\n          paymentType\n          tip {\n            amount\n            type\n          }\n          quantityAmounts {\n            id\n            quantity\n          }\n        }\n        reference {\n          id\n          source\n        }\n      }\n      referenceID\n      onSuccessUrl\n      onFailureUrl\n      customerID\n      customerData {\n        id\n        nameOnCard\n        email\n        phone\n        billingAddress\n        billingCity\n        billingState\n        billingZip\n        billingCountry\n        paymentCards {\n          cardID\n          type\n          isDefault\n          last4\n          brand\n          expires\n          cvc\n          accountNumber\n          routingNumber\n          accountType\n          accountHolderType\n          gpEcommID\n        }\n      }\n      allowTip\n      customerPaymentID\n      prefilledAddress {\n        email\n        phone\n        country\n        state\n        city\n        zip\n        address\n        nameOnCard\n      }\n      transactionHistory {\n        transactionID\n        date\n        amount\n        status\n        note\n      }\n      disableCustomAddress\n      disableCustomPayment\n      disablePreselectCard\n      overideLineItemsAmount\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Ghl_api_getPaymentPageDataUNI($input: Ghl_api_getPaymentPageDataUNIInput!) {\n    ghl_api_getPaymentPageDataUNI(input: $input) {\n      data {\n        locationID\n        groupID\n        liveMode\n        amount\n        apiKey\n        createdAt\n        amountSummary {\n          subtotal\n          discount\n          tax\n          shipping\n        }\n        subscriptionId\n        lineItems {\n          id\n          name\n          description\n          image\n          quantity\n          price\n          currency\n          total\n          discount\n        }\n        subscriptionItems {\n          id\n          name\n          description\n          image\n          price\n          currency\n          setupFee\n          recurring {\n            interval\n            intervalCount\n            delay\n            cycles\n          }\n          discount\n          discountNoSetupFee\n        }\n        coupon {\n          id\n          name\n          sessionID\n          code\n          discountType\n          discountValue\n          hasAffiliateCoupon\n          userLimit\n          status\n          usageCount\n        }\n        customerData {\n          name\n          email\n          contact\n          first_name\n          last_name\n          company\n          address_line_1\n          address_line_2\n          city\n          state\n          postal_code\n          country\n          phone\n          fax\n        }\n      }\n      paymentLink {\n        paymentData\n        payLinkID\n        token\n        groupID\n      }\n    }\n  }\n"): (typeof documents)["\n  query Ghl_api_getPaymentPageDataUNI($input: Ghl_api_getPaymentPageDataUNIInput!) {\n    ghl_api_getPaymentPageDataUNI(input: $input) {\n      data {\n        locationID\n        groupID\n        liveMode\n        amount\n        apiKey\n        createdAt\n        amountSummary {\n          subtotal\n          discount\n          tax\n          shipping\n        }\n        subscriptionId\n        lineItems {\n          id\n          name\n          description\n          image\n          quantity\n          price\n          currency\n          total\n          discount\n        }\n        subscriptionItems {\n          id\n          name\n          description\n          image\n          price\n          currency\n          setupFee\n          recurring {\n            interval\n            intervalCount\n            delay\n            cycles\n          }\n          discount\n          discountNoSetupFee\n        }\n        coupon {\n          id\n          name\n          sessionID\n          code\n          discountType\n          discountValue\n          hasAffiliateCoupon\n          userLimit\n          status\n          usageCount\n        }\n        customerData {\n          name\n          email\n          contact\n          first_name\n          last_name\n          company\n          address_line_1\n          address_line_2\n          city\n          state\n          postal_code\n          country\n          phone\n          fax\n        }\n      }\n      paymentLink {\n        paymentData\n        payLinkID\n        token\n        groupID\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPaymentPageData($input: Ghl_api_getPaymentPageDataInput!) {\n    ghl_api_getPaymentPageData(input: $input) {\n      data {\n        locationID\n        groupID\n        liveMode\n        amount\n        apiKey\n        createdAt\n        amountSummary {\n          subtotal\n          discount\n          tax\n          shipping\n        }\n        subscriptionId\n        lineItems {\n          id\n          name\n          description\n          image\n          quantity\n          price\n          currency\n          total\n          discount\n        }\n        subscriptionItems {\n          id\n          name\n          description\n          image\n          price\n          currency\n          setupFee\n          recurring {\n            interval\n            intervalCount\n            delay\n            cycles\n          }\n          discount\n          discountNoSetupFee\n        }\n        coupon {\n          id\n          name\n          sessionID\n          code\n          discountType\n          discountValue\n          hasAffiliateCoupon\n          userLimit\n          status\n          usageCount\n        }\n        customerData {\n          name\n          email\n          contact\n          first_name\n          last_name\n          company\n          address_line_1\n          address_line_2\n          city\n          state\n          postal_code\n          country\n          phone\n          fax\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetPaymentPageData($input: Ghl_api_getPaymentPageDataInput!) {\n    ghl_api_getPaymentPageData(input: $input) {\n      data {\n        locationID\n        groupID\n        liveMode\n        amount\n        apiKey\n        createdAt\n        amountSummary {\n          subtotal\n          discount\n          tax\n          shipping\n        }\n        subscriptionId\n        lineItems {\n          id\n          name\n          description\n          image\n          quantity\n          price\n          currency\n          total\n          discount\n        }\n        subscriptionItems {\n          id\n          name\n          description\n          image\n          price\n          currency\n          setupFee\n          recurring {\n            interval\n            intervalCount\n            delay\n            cycles\n          }\n          discount\n          discountNoSetupFee\n        }\n        coupon {\n          id\n          name\n          sessionID\n          code\n          discountType\n          discountValue\n          hasAffiliateCoupon\n          userLimit\n          status\n          usageCount\n        }\n        customerData {\n          name\n          email\n          contact\n          first_name\n          last_name\n          company\n          address_line_1\n          address_line_2\n          city\n          state\n          postal_code\n          country\n          phone\n          fax\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation SubmitPaymentUNIPageData($input: Ghl_api_submitPayment_uniInput!) {\n    ghl_api_submitPayment_uni(input: $input) {\n      txID\n      order {\n        status\n        msg\n        data {\n          id\n          idempotency_time\n          type\n          amount\n          base_amount\n          amount_authorized\n          amount_captured\n          amount_settled\n          amount_refunded\n          payment_adjustment\n          tip_amount\n          settlement_batch_id\n          payment_type\n          tax_amount\n          tax_exempt\n          shipping_amount\n          surcharge\n          discount_amount\n          service_fee\n          currency\n          description\n          order_id\n          po_number\n          ip_address\n          transaction_source\n          email_receipt\n          email_address\n          customer_id\n          customer_payment_type\n          customer_payment_id\n          subscription_id\n          referenced_transaction_id\n          response_body {\n            card {\n              id\n              card_type\n              first_six\n              last_four\n              masked_card\n              expiration_date\n              response\n              response_code\n              auth_code\n              bin_type\n              type\n              avs_response_code\n              cvv_response_code\n              processor_specific\n              created_at\n              updated_at\n            }\n          }\n          status\n          response\n          response_code\n          billing_address {\n            first_name\n            last_name\n            company\n            address_line_1\n            address_line_2\n            city\n            state\n            postal_code\n            country\n            phone\n            fax\n            email\n          }\n          shipping_address {\n            first_name\n            last_name\n            company\n            address_line_1\n            address_line_2\n            city\n            state\n            postal_code\n            country\n            phone\n            fax\n            email\n          }\n          created_at\n          updated_at\n          captured_at\n          settled_at\n        }\n      }\n      subscription {\n        status\n        msg\n        data {\n          id\n          plan_id\n          status\n          description\n          customer {\n            id\n          }\n          amount\n          total_adds\n          total_discounts\n          billing_cycle_interval\n          billing_frequency\n          billing_days\n          duration\n          next_bill_date\n          add_ons {\n            id\n            name\n            description\n            amount\n            percentage\n            duration\n            created_at\n            updated_at\n          }\n          discounts {\n            id\n            name\n            description\n            amount\n            percentage\n            duration\n            created_at\n            updated_at\n          }\n          created_at\n          updated_at\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation SubmitPaymentUNIPageData($input: Ghl_api_submitPayment_uniInput!) {\n    ghl_api_submitPayment_uni(input: $input) {\n      txID\n      order {\n        status\n        msg\n        data {\n          id\n          idempotency_time\n          type\n          amount\n          base_amount\n          amount_authorized\n          amount_captured\n          amount_settled\n          amount_refunded\n          payment_adjustment\n          tip_amount\n          settlement_batch_id\n          payment_type\n          tax_amount\n          tax_exempt\n          shipping_amount\n          surcharge\n          discount_amount\n          service_fee\n          currency\n          description\n          order_id\n          po_number\n          ip_address\n          transaction_source\n          email_receipt\n          email_address\n          customer_id\n          customer_payment_type\n          customer_payment_id\n          subscription_id\n          referenced_transaction_id\n          response_body {\n            card {\n              id\n              card_type\n              first_six\n              last_four\n              masked_card\n              expiration_date\n              response\n              response_code\n              auth_code\n              bin_type\n              type\n              avs_response_code\n              cvv_response_code\n              processor_specific\n              created_at\n              updated_at\n            }\n          }\n          status\n          response\n          response_code\n          billing_address {\n            first_name\n            last_name\n            company\n            address_line_1\n            address_line_2\n            city\n            state\n            postal_code\n            country\n            phone\n            fax\n            email\n          }\n          shipping_address {\n            first_name\n            last_name\n            company\n            address_line_1\n            address_line_2\n            city\n            state\n            postal_code\n            country\n            phone\n            fax\n            email\n          }\n          created_at\n          updated_at\n          captured_at\n          settled_at\n        }\n      }\n      subscription {\n        status\n        msg\n        data {\n          id\n          plan_id\n          status\n          description\n          customer {\n            id\n          }\n          amount\n          total_adds\n          total_discounts\n          billing_cycle_interval\n          billing_frequency\n          billing_days\n          duration\n          next_bill_date\n          add_ons {\n            id\n            name\n            description\n            amount\n            percentage\n            duration\n            created_at\n            updated_at\n          }\n          discounts {\n            id\n            name\n            description\n            amount\n            percentage\n            duration\n            created_at\n            updated_at\n          }\n          created_at\n          updated_at\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation processorTestDraftCreate($input: Processor_tst_draft_createInput!) {\n    processor_tst_draft_create(input: $input) {\n      testID\n      groupID\n    }\n  }\n"): (typeof documents)["\n  mutation processorTestDraftCreate($input: Processor_tst_draft_createInput!) {\n    processor_tst_draft_create(input: $input) {\n      testID\n      groupID\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation processorTestDraftUpdate($input: Processor_tst_draft_updateInput!) {\n    processor_tst_draft_update(input: $input) {\n      testID\n      groupID\n    }\n  }\n"): (typeof documents)["\n  mutation processorTestDraftUpdate($input: Processor_tst_draft_updateInput!) {\n    processor_tst_draft_update(input: $input) {\n      testID\n      groupID\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation processorTestDraftSubmit($input: Processor_tst_draft_submitInput!) {\n    processor_tst_draft_submit(input: $input) {\n      urlForSigning\n      applicationId\n      applicationNumber\n    }\n  }\n"): (typeof documents)["\n  mutation processorTestDraftSubmit($input: Processor_tst_draft_submitInput!) {\n    processor_tst_draft_submit(input: $input) {\n      urlForSigning\n      applicationId\n      applicationNumber\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Group($where: GroupWhereUniqueInput!) {\n    group(where: $where) {\n      id\n      processorTST {\n        applicationID\n        applicationNumber\n        id\n        processor_tst_signingUrl\n        processor_tst_applicationStatus {\n          businessInfo {\n            legalBusinessName\n            typeOfBusiness\n            dbaName\n            ein\n            dateBusinessEstablished\n            businessEmail\n            businessPhone\n            website\n            customerServicePhone\n            street\n            zipCode\n            city\n            state\n            country\n            differentLegalAddress\n            legalMailingStreet\n            legalMailingZipCode\n            legalMailingCity\n            legalMailingState\n            legalMailingCountry\n          }\n          transactionInfo {\n            businessCategory\n            description\n            swipe\n            keyed\n            ecommerce\n            avgTransactionAmount\n            highestTransactionAmount\n            grossMonthlySalesVolume\n            amexAvgTransactionAmount\n            amexHighestTransactionAmount\n            amexGrossMonthlySalesVolume\n          }\n          owners {\n            isControlOwner\n            firstName\n            lastName\n            title\n            ownershipPercentage\n            phoneNumber\n            homeAddress\n            country\n            state\n            city\n            zipCode\n            dateOfBirth\n            ssn\n            email\n          }\n          files {\n            name\n            category\n            mimetype\n            b64\n          }\n          bankInfo {\n            routingNumber\n            accountNumber\n            bankName\n            nameOnAccount\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query Group($where: GroupWhereUniqueInput!) {\n    group(where: $where) {\n      id\n      processorTST {\n        applicationID\n        applicationNumber\n        id\n        processor_tst_signingUrl\n        processor_tst_applicationStatus {\n          businessInfo {\n            legalBusinessName\n            typeOfBusiness\n            dbaName\n            ein\n            dateBusinessEstablished\n            businessEmail\n            businessPhone\n            website\n            customerServicePhone\n            street\n            zipCode\n            city\n            state\n            country\n            differentLegalAddress\n            legalMailingStreet\n            legalMailingZipCode\n            legalMailingCity\n            legalMailingState\n            legalMailingCountry\n          }\n          transactionInfo {\n            businessCategory\n            description\n            swipe\n            keyed\n            ecommerce\n            avgTransactionAmount\n            highestTransactionAmount\n            grossMonthlySalesVolume\n            amexAvgTransactionAmount\n            amexHighestTransactionAmount\n            amexGrossMonthlySalesVolume\n          }\n          owners {\n            isControlOwner\n            firstName\n            lastName\n            title\n            ownershipPercentage\n            phoneNumber\n            homeAddress\n            country\n            state\n            city\n            zipCode\n            dateOfBirth\n            ssn\n            email\n          }\n          files {\n            name\n            category\n            mimetype\n            b64\n          }\n          bankInfo {\n            routingNumber\n            accountNumber\n            bankName\n            nameOnAccount\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query processorTestDraftZipSearch($input: Processor_tst_zip_searchInput!) {\n    processor_tst_zip_search(input: $input) {\n      item {\n        zip\n        latitude\n        longitude\n        city\n        state\n        country\n      }\n    }\n  }\n"): (typeof documents)["\n  query processorTestDraftZipSearch($input: Processor_tst_zip_searchInput!) {\n    processor_tst_zip_search(input: $input) {\n      item {\n        zip\n        latitude\n        longitude\n        city\n        state\n        country\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query processorTestDraftMCC($input: Processor_tst_mccInput!) {\n    processor_tst_mcc(input: $input) {\n      items {\n        id\n        description\n      }\n      count\n    }\n  }\n"): (typeof documents)["\n  query processorTestDraftMCC($input: Processor_tst_mccInput!) {\n    processor_tst_mcc(input: $input) {\n      items {\n        id\n        description\n      }\n      count\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query processorTestDraftBankRouting($input: Processor_tst_bank_routingInput!) {\n    processor_tst_bank_routing(input: $input) {\n      data {\n        rn\n        name\n      }\n    }\n  }\n"): (typeof documents)["\n  query processorTestDraftBankRouting($input: Processor_tst_bank_routingInput!) {\n    processor_tst_bank_routing(input: $input) {\n      data {\n        rn\n        name\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Processor_draft_create($input: Processor_draft_createInput!) {\n    processor_draft_create(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n"): (typeof documents)["\n  mutation Processor_draft_create($input: Processor_draft_createInput!) {\n    processor_draft_create(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Processor_draft_update($input: Processor_draft_updateInput!) {\n    processor_draft_update(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n"): (typeof documents)["\n  mutation Processor_draft_update($input: Processor_draft_updateInput!) {\n    processor_draft_update(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Processor_draft_submit($input: Processor_draft_submitInput!) {\n    processor_draft_submit(input: $input) {\n      urlForSigning\n      applicationId\n      applicationNumber\n    }\n  }\n"): (typeof documents)["\n  mutation Processor_draft_submit($input: Processor_draft_submitInput!) {\n    processor_draft_submit(input: $input) {\n      urlForSigning\n      applicationId\n      applicationNumber\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Processor_draft_documentUpload($input: Processor_draft_documentUploadInput!) {\n    processor_draft_documentUpload(input: $input) {\n      documentID {\n        name\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation Processor_draft_documentUpload($input: Processor_draft_documentUploadInput!) {\n    processor_draft_documentUpload(input: $input) {\n      documentID {\n        name\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Processor_draft_documentForward($input: Processor_draft_documentForwardInput!) {\n    processor_draft_documentForward(input: $input) {\n      success\n    }\n  }\n"): (typeof documents)["\n  mutation Processor_draft_documentForward($input: Processor_draft_documentForwardInput!) {\n    processor_draft_documentForward(input: $input) {\n      success\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Processor_draft_status($input: Processor_draft_statusInput!) {\n    processor_draft_status(input: $input) {\n      attestations {\n        name\n        ip_address\n        time_of_attestation\n        url\n        signature\n      }\n      businessInfo {\n        legalBusinessName\n        typeOfBusiness\n        dbaName\n        ein\n        dateBusinessEstablished\n        businessEmail\n        businessPhone\n        businessPhoneCountryCode\n        website\n        customerServicePhone\n        customerServicePhoneCountryCode\n        street\n        zipCode\n        city\n        state\n        country\n        differentLegalAddress\n        legalMailingStreet\n        legalMailingZipCode\n        legalMailingCity\n        legalMailingState\n        legalMailingCountry\n      }\n      transactionInfo {\n        businessCategory\n        description\n        swipe\n        keyed\n        ecommerce\n        avgTransactionAmount\n        highestTransactionAmount\n        grossMonthlySalesVolume\n        amexAvgTransactionAmount\n        amexHighestTransactionAmount\n        amexGrossMonthlySalesVolume\n      }\n      owners {\n        isControlOwner\n        firstName\n        lastName\n        title\n        ownershipPercentage\n        phoneNumber\n        phoneNumberCountryCode\n        homeAddress\n        country\n        state\n        city\n        zipCode\n        dateOfBirth\n        ssn\n        email\n      }\n      files {\n        id\n        name\n        category\n        purpose\n        mimetype\n        b64\n        submittedOn\n      }\n      bankInfo {\n        routingNumber\n        accountNumber\n        bankName\n        nameOnAccount\n      }\n      status {\n        message\n      }\n    }\n  }\n"): (typeof documents)["\n  query Processor_draft_status($input: Processor_draft_statusInput!) {\n    processor_draft_status(input: $input) {\n      attestations {\n        name\n        ip_address\n        time_of_attestation\n        url\n        signature\n      }\n      businessInfo {\n        legalBusinessName\n        typeOfBusiness\n        dbaName\n        ein\n        dateBusinessEstablished\n        businessEmail\n        businessPhone\n        businessPhoneCountryCode\n        website\n        customerServicePhone\n        customerServicePhoneCountryCode\n        street\n        zipCode\n        city\n        state\n        country\n        differentLegalAddress\n        legalMailingStreet\n        legalMailingZipCode\n        legalMailingCity\n        legalMailingState\n        legalMailingCountry\n      }\n      transactionInfo {\n        businessCategory\n        description\n        swipe\n        keyed\n        ecommerce\n        avgTransactionAmount\n        highestTransactionAmount\n        grossMonthlySalesVolume\n        amexAvgTransactionAmount\n        amexHighestTransactionAmount\n        amexGrossMonthlySalesVolume\n      }\n      owners {\n        isControlOwner\n        firstName\n        lastName\n        title\n        ownershipPercentage\n        phoneNumber\n        phoneNumberCountryCode\n        homeAddress\n        country\n        state\n        city\n        zipCode\n        dateOfBirth\n        ssn\n        email\n      }\n      files {\n        id\n        name\n        category\n        purpose\n        mimetype\n        b64\n        submittedOn\n      }\n      bankInfo {\n        routingNumber\n        accountNumber\n        bankName\n        nameOnAccount\n      }\n      status {\n        message\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_batches($input: Gateway_batchesInput!) {\n  gateway_batches(input: $input) {\n    data {\n      batchID\n      location\n      date\n      amount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_batch($input: Gateway_batchInput!) {\n  gateway_batch(input: $input) {\n    batchID\n    status\n    date\n    location\n    locationID\n    amount\n    transactions {\n      transactionID\n      date\n      method\n      name\n      last4\n      customer\n      amount\n      brand\n      status\n    }\n  }\n}"): (typeof documents)["query Gateway_batches($input: Gateway_batchesInput!) {\n  gateway_batches(input: $input) {\n    data {\n      batchID\n      location\n      date\n      amount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_batch($input: Gateway_batchInput!) {\n  gateway_batch(input: $input) {\n    batchID\n    status\n    date\n    location\n    locationID\n    amount\n    transactions {\n      transactionID\n      date\n      method\n      name\n      last4\n      customer\n      amount\n      brand\n      status\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_discounts($input: Gateway_discountsInput!) {\n  gateway_discounts(input: $input) {\n    data {\n      id\n      name\n      type\n      discount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_discount($input: Gateway_discountInput!) {\n  gateway_discount(input: $input) {\n    id\n    name\n    type\n    discount\n    status\n  }\n}"): (typeof documents)["query Gateway_discounts($input: Gateway_discountsInput!) {\n  gateway_discounts(input: $input) {\n    data {\n      id\n      name\n      type\n      discount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_discount($input: Gateway_discountInput!) {\n  gateway_discount(input: $input) {\n    id\n    name\n    type\n    discount\n    status\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_categories($input: Gateway_categoriesInput!) {\n  gateway_categories(input: $input) {\n    data {\n      id\n      name\n      status\n      color\n      description\n      subCategory\n      colors\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_category($input: Gateway_categoryInput!) {\n  gateway_category(input: $input) {\n    id\n    name\n    status\n    color\n    description\n    subCategory\n    colors\n  }\n}"): (typeof documents)["query Gateway_categories($input: Gateway_categoriesInput!) {\n  gateway_categories(input: $input) {\n    data {\n      id\n      name\n      status\n      color\n      description\n      subCategory\n      colors\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_category($input: Gateway_categoryInput!) {\n  gateway_category(input: $input) {\n    id\n    name\n    status\n    color\n    description\n    subCategory\n    colors\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_customers($input: Gateway_customersInput!) {\n  gateway_customers(input: $input) {\n    data {\n      id\n      isDefault\n      last4\n      brand\n      expires\n      name\n      city_state\n      zip\n      email\n      phone\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_customer($input: Gateway_customerInput!) {\n  gateway_customer(input: $input) {\n    id\n    nameOnCard\n    email\n    phone\n    billingAddress\n    billingCity\n    billingState\n    billingZip\n    billingCountry\n    paymentCards {\n      type\n      cardID\n      isDefault\n      last4\n      brand\n      expires\n      accountNumber\n      routingNumber\n      accountType\n      accountHolderType\n      gpEcommID\n    }\n  }\n}\n\nquery GetCustomerListByLocationForDropdown($input: Gateway_customersInput!) {\n  gateway_customers(input: $input) {\n    data {\n      id\n      name\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}"): (typeof documents)["query Gateway_customers($input: Gateway_customersInput!) {\n  gateway_customers(input: $input) {\n    data {\n      id\n      isDefault\n      last4\n      brand\n      expires\n      name\n      city_state\n      zip\n      email\n      phone\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_customer($input: Gateway_customerInput!) {\n  gateway_customer(input: $input) {\n    id\n    nameOnCard\n    email\n    phone\n    billingAddress\n    billingCity\n    billingState\n    billingZip\n    billingCountry\n    paymentCards {\n      type\n      cardID\n      isDefault\n      last4\n      brand\n      expires\n      accountNumber\n      routingNumber\n      accountType\n      accountHolderType\n      gpEcommID\n    }\n  }\n}\n\nquery GetCustomerListByLocationForDropdown($input: Gateway_customersInput!) {\n  gateway_customers(input: $input) {\n    data {\n      id\n      name\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Dashboard_get_summary($input: Dashboard_get_summaryInput!) {\n  dashboard_get_summary(input: $input) {\n    dateStart\n    dateEnd\n    totalPortfolio\n    captured {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    refunds {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    batched {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    deposits {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n  }\n}\n\nquery Dashboard_location_summary($input: Dashboard_location_summaryInput!) {\n  dashboard_location_summary(input: $input) {\n    data {\n      locationID\n      locationName\n      changePercentage\n      currentYearTotal\n      lastYearTotal\n      yearChangePercentage\n    }\n  }\n}\n\nquery Gateway_accountFunds($input: Gateway_accountFundsInput!) {\n  gateway_accountFunds(input: $input) {\n    card_transaction_limit_amount\n    card_monthly_limit_amount\n    bank_transfer_per_transaction_limit_amount\n    bank_transfer_monthly_limit_amount\n    bank_transfer_month_to_date_processed_amount\n    card_month_to_date_processed_amount\n    available_balance_amount\n    pending_balance_amount\n    reserve_balance_amount\n    allowed_negative_balance_amount\n    disable_payout_card_limit_percentage\n    disable_payout_bank_transfer_limit_percentage\n    card_month_to_date_processed_percentage\n    bank_transfer_month_to_date_processed_percentage\n  }\n}"): (typeof documents)["query Dashboard_get_summary($input: Dashboard_get_summaryInput!) {\n  dashboard_get_summary(input: $input) {\n    dateStart\n    dateEnd\n    totalPortfolio\n    captured {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    refunds {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    batched {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n    deposits {\n      total\n      percentageChange\n      timeStart\n      timeEnd\n      timeEntries {\n        time\n        value\n      }\n    }\n  }\n}\n\nquery Dashboard_location_summary($input: Dashboard_location_summaryInput!) {\n  dashboard_location_summary(input: $input) {\n    data {\n      locationID\n      locationName\n      changePercentage\n      currentYearTotal\n      lastYearTotal\n      yearChangePercentage\n    }\n  }\n}\n\nquery Gateway_accountFunds($input: Gateway_accountFundsInput!) {\n  gateway_accountFunds(input: $input) {\n    card_transaction_limit_amount\n    card_monthly_limit_amount\n    bank_transfer_per_transaction_limit_amount\n    bank_transfer_monthly_limit_amount\n    bank_transfer_month_to_date_processed_amount\n    card_month_to_date_processed_amount\n    available_balance_amount\n    pending_balance_amount\n    reserve_balance_amount\n    allowed_negative_balance_amount\n    disable_payout_card_limit_percentage\n    disable_payout_bank_transfer_limit_percentage\n    card_month_to_date_processed_percentage\n    bank_transfer_month_to_date_processed_percentage\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_deposits($input: Gateway_depositsInput!) {\n  gateway_deposits(input: $input) {\n    data {\n      depositID\n      location\n      date\n      amount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_deposit($input: Gateway_depositInput!) {\n  gateway_deposit(input: $input) {\n    depositID\n    date\n    last4\n    amount\n    sales\n    fees\n    totalDeposit\n    batches {\n      batchID\n      depositID\n      total\n    }\n    transactions {\n      transactionID\n      date\n      method\n      name\n      last4\n      customer\n      amount\n      brand\n      status\n    }\n  }\n}"): (typeof documents)["query Gateway_deposits($input: Gateway_depositsInput!) {\n  gateway_deposits(input: $input) {\n    data {\n      depositID\n      location\n      date\n      amount\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_deposit($input: Gateway_depositInput!) {\n  gateway_deposit(input: $input) {\n    depositID\n    date\n    last4\n    amount\n    sales\n    fees\n    totalDeposit\n    batches {\n      batchID\n      depositID\n      total\n    }\n    transactions {\n      transactionID\n      date\n      method\n      name\n      last4\n      customer\n      amount\n      brand\n      status\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_disputes($input: Gateway_disputesInput!) {\n  gateway_disputes(input: $input) {\n    data {\n      caseID\n      location\n      date\n      reason\n      amount\n      brand\n      transactionID\n      hasChallenged\n      daysToRepresent\n      customer\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_dispute($input: Gateway_disputeInput!) {\n  gateway_dispute(input: $input) {\n    disputeID\n    caseID\n    location\n    date\n    reason\n    amount\n    brand\n    transactionID\n    paymentType\n    createdBy\n    method\n    batchID\n    paymentPlan\n    source\n    authCode\n    entryMethod\n    daysToRepresent\n    tokenSource\n    gsa\n    emv\n    last4\n    customerID\n    name\n    email\n    phone\n    country\n    billingAddress\n    products {\n      productName\n      productID\n      quantity\n      price\n      discount\n      description\n    }\n    history {\n      action\n      body\n      actor\n      createdAt\n    }\n    files {\n      id\n      fileUrl\n      purpose\n      fileFormat\n      createdAt\n      submittedAt\n    }\n    status\n  }\n}\n\nmutation Gateway_uploadDisputeDocument($input: Gateway_uploadDisputeDocumentInput!) {\n  gateway_uploadDisputeDocument(input: $input) {\n    itemID\n  }\n}\n\nmutation Gateway_submitDisputeDocument($input: Gateway_submitDisputeDocumentInput!) {\n  gateway_submitDisputeDocument(input: $input) {\n    disputeID\n  }\n}"): (typeof documents)["query Gateway_disputes($input: Gateway_disputesInput!) {\n  gateway_disputes(input: $input) {\n    data {\n      caseID\n      location\n      date\n      reason\n      amount\n      brand\n      transactionID\n      hasChallenged\n      daysToRepresent\n      customer\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_dispute($input: Gateway_disputeInput!) {\n  gateway_dispute(input: $input) {\n    disputeID\n    caseID\n    location\n    date\n    reason\n    amount\n    brand\n    transactionID\n    paymentType\n    createdBy\n    method\n    batchID\n    paymentPlan\n    source\n    authCode\n    entryMethod\n    daysToRepresent\n    tokenSource\n    gsa\n    emv\n    last4\n    customerID\n    name\n    email\n    phone\n    country\n    billingAddress\n    products {\n      productName\n      productID\n      quantity\n      price\n      discount\n      description\n    }\n    history {\n      action\n      body\n      actor\n      createdAt\n    }\n    files {\n      id\n      fileUrl\n      purpose\n      fileFormat\n      createdAt\n      submittedAt\n    }\n    status\n  }\n}\n\nmutation Gateway_uploadDisputeDocument($input: Gateway_uploadDisputeDocumentInput!) {\n  gateway_uploadDisputeDocument(input: $input) {\n    itemID\n  }\n}\n\nmutation Gateway_submitDisputeDocument($input: Gateway_submitDisputeDocumentInput!) {\n  gateway_submitDisputeDocument(input: $input) {\n    disputeID\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_paymentPlans($input: Gateway_paymentPlansInput!) {\n  gateway_paymentPlans(input: $input) {\n    data {\n      planID\n      planName\n      startDate\n      customerName\n      customerID\n      amount\n      last4\n      expires\n      duration\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_paymentPlan($input: Gateway_paymentPlanInput!) {\n  gateway_paymentPlan(input: $input) {\n    planID\n    planName\n    createdAt\n    amount\n    startDate\n    endDate\n    locationName\n    locationID\n    status\n    last4\n    expires\n    creator\n    history {\n      transactionID\n      date\n      amount\n      status\n    }\n    customerName\n    customerID\n    customerEmail\n    customerPhone\n    customerCountry\n    customerBillingAddress\n    paymentBrand\n    paymentEvery\n    paymentInterval\n    paymentExpires\n    mode\n    paymentID\n    nextPaymentDate\n    cancelReason\n    recurringRefundable\n    subscriptionCredit\n  }\n}"): (typeof documents)["query Gateway_paymentPlans($input: Gateway_paymentPlansInput!) {\n  gateway_paymentPlans(input: $input) {\n    data {\n      planID\n      planName\n      startDate\n      customerName\n      customerID\n      amount\n      last4\n      expires\n      duration\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_paymentPlan($input: Gateway_paymentPlanInput!) {\n  gateway_paymentPlan(input: $input) {\n    planID\n    planName\n    createdAt\n    amount\n    startDate\n    endDate\n    locationName\n    locationID\n    status\n    last4\n    expires\n    creator\n    history {\n      transactionID\n      date\n      amount\n      status\n    }\n    customerName\n    customerID\n    customerEmail\n    customerPhone\n    customerCountry\n    customerBillingAddress\n    paymentBrand\n    paymentEvery\n    paymentInterval\n    paymentExpires\n    mode\n    paymentID\n    nextPaymentDate\n    cancelReason\n    recurringRefundable\n    subscriptionCredit\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_products($input: Gateway_productsInput!) {\n  gateway_products(input: $input) {\n    data {\n      id\n      name\n      price\n      discount\n      taxExempt\n      brand\n      kitchenItem\n      sku\n      isRecurring\n      recurringInterval\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_product($input: Gateway_productInput!) {\n  gateway_product(input: $input) {\n    id\n    name\n    price\n    discount\n    productStatus\n    taxExempt\n    kitchenItem\n    sku\n    category\n    subCategory\n    brand\n    isRecurring\n    recurringMode\n    recurringInterval\n    recurringFrequency\n    recurringTotalCycles\n    recurringTrialDays\n    recurringSetupFee\n    description\n    isInStore\n    isOnline\n    productImages {\n      url\n      name\n    }\n  }\n}"): (typeof documents)["query Gateway_products($input: Gateway_productsInput!) {\n  gateway_products(input: $input) {\n    data {\n      id\n      name\n      price\n      discount\n      taxExempt\n      brand\n      kitchenItem\n      sku\n      isRecurring\n      recurringInterval\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}\n\nquery Gateway_product($input: Gateway_productInput!) {\n  gateway_product(input: $input) {\n    id\n    name\n    price\n    discount\n    productStatus\n    taxExempt\n    kitchenItem\n    sku\n    category\n    subCategory\n    brand\n    isRecurring\n    recurringMode\n    recurringInterval\n    recurringFrequency\n    recurringTotalCycles\n    recurringTrialDays\n    recurringSetupFee\n    description\n    isInStore\n    isOnline\n    productImages {\n      url\n      name\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_scheduled($input: Gateway_scheduledInput!) {\n  gateway_scheduled(input: $input) {\n    data {\n      planID\n      planName\n      customerID\n      customerName\n      nextPayment\n      amount\n      last4\n      expires\n      datePaid\n      amountPaid\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}"): (typeof documents)["query Gateway_scheduled($input: Gateway_scheduledInput!) {\n  gateway_scheduled(input: $input) {\n    data {\n      planID\n      planName\n      customerID\n      customerName\n      nextPayment\n      amount\n      last4\n      expires\n      datePaid\n      amountPaid\n      status\n    }\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Gateway_transactions($input: Gateway_transactionsInput!) {\n  gateway_transactions(input: $input) {\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n    data {\n      transactionID\n      location\n      date\n      method\n      customer\n      brand\n      status\n      last4\n      amount\n    }\n  }\n}\n\nquery Gateway_transaction($input: Gateway_transactionInput!) {\n  gateway_transaction(input: $input) {\n    transactionID\n    status\n    date\n    createdBy\n    paymentType\n    method\n    batchID\n    paymentPlan\n    source\n    authCode\n    amount\n    entryMethod\n    tokenSource\n    gsa\n    emv\n    last4\n    customerName\n    customerID\n    customerEmail\n    customerPhone\n    customerCountry\n    customerBillingAddress\n    commercialLevel\n    result\n    message\n    brandReference\n    breakdown {\n      discount\n      directDiscount\n      actualDiscount\n      tax\n      shipping\n      shippingDiscount\n      shippingDirectDiscount\n      shippingActualDiscount\n      fees\n      actualFees\n      tip\n      subtotal\n      subscriptionTotal\n      rawTotal\n      total\n      expectedTotal\n    }\n    transactionHistory {\n      date\n      status\n      response\n      avs\n      cvv\n    }\n    purchaseDetails {\n      productName\n      productID\n      quantity\n      price\n      discount\n    }\n    relatedTransactions {\n      transactionID\n      amount\n      status\n      date\n      paymentType\n    }\n  }\n}"): (typeof documents)["query Gateway_transactions($input: Gateway_transactionsInput!) {\n  gateway_transactions(input: $input) {\n    page {\n      total\n      range {\n        from\n        to\n      }\n      page\n      pageSize\n    }\n    data {\n      transactionID\n      location\n      date\n      method\n      customer\n      brand\n      status\n      last4\n      amount\n    }\n  }\n}\n\nquery Gateway_transaction($input: Gateway_transactionInput!) {\n  gateway_transaction(input: $input) {\n    transactionID\n    status\n    date\n    createdBy\n    paymentType\n    method\n    batchID\n    paymentPlan\n    source\n    authCode\n    amount\n    entryMethod\n    tokenSource\n    gsa\n    emv\n    last4\n    customerName\n    customerID\n    customerEmail\n    customerPhone\n    customerCountry\n    customerBillingAddress\n    commercialLevel\n    result\n    message\n    brandReference\n    breakdown {\n      discount\n      directDiscount\n      actualDiscount\n      tax\n      shipping\n      shippingDiscount\n      shippingDirectDiscount\n      shippingActualDiscount\n      fees\n      actualFees\n      tip\n      subtotal\n      subscriptionTotal\n      rawTotal\n      total\n      expectedTotal\n    }\n    transactionHistory {\n      date\n      status\n      response\n      avs\n      cvv\n    }\n    purchaseDetails {\n      productName\n      productID\n      quantity\n      price\n      discount\n    }\n    relatedTransactions {\n      transactionID\n      amount\n      status\n      date\n      paymentType\n    }\n  }\n}"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Serviceacct_create($input: Serviceacct_createInput!) {\n    serviceacct_create(input: $input) {\n      id\n      token\n      secret\n    }\n  }\n"): (typeof documents)["\n  mutation Serviceacct_create($input: Serviceacct_createInput!) {\n    serviceacct_create(input: $input) {\n      id\n      token\n      secret\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation ServiceAcct_delete($where: [ServiceAPIAccountWhereUniqueInput!]!) {\n    deleteServiceAPIAccounts(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation ServiceAcct_delete($where: [ServiceAPIAccountWhereUniqueInput!]!) {\n    deleteServiceAPIAccounts(where: $where) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ServiceAcct_list($where: ServiceAPIAccountWhereInput!) {\n    serviceAPIAccounts(where: $where) {\n      active\n      allowedMethods\n      createdAt\n      id\n      ipWhitelist\n      secretView\n      token\n      expiresAt\n    }\n  }\n"): (typeof documents)["\n  query ServiceAcct_list($where: ServiceAPIAccountWhereInput!) {\n    serviceAPIAccounts(where: $where) {\n      active\n      allowedMethods\n      createdAt\n      id\n      ipWhitelist\n      secretView\n      token\n      expiresAt\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query ServiceAcct_get($where: ServiceAPIAccountWhereUniqueInput!) {\n    serviceAPIAccount(where: $where) {\n      id\n      token\n      secretView\n      active\n      ipWhitelist\n      allowedMethods\n      createdAt\n      updatedAt\n    }\n  }\n"): (typeof documents)["\n  query ServiceAcct_get($where: ServiceAPIAccountWhereUniqueInput!) {\n    serviceAPIAccount(where: $where) {\n      id\n      token\n      secretView\n      active\n      ipWhitelist\n      allowedMethods\n      createdAt\n      updatedAt\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation ServiceAcct_update($data: [ServiceAPIAccountUpdateArgs!]!) {\n    updateServiceAPIAccounts(data: $data) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation ServiceAcct_update($data: [ServiceAPIAccountUpdateArgs!]!) {\n    updateServiceAPIAccounts(data: $data) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query StripeDataApplications {\n    stripeDataApplications {\n      id\n      keyType\n      publishableKey\n      secretKey\n      accessKey\n      refreshKey\n      userId\n      stripeUserId\n      accountId\n      tokenType\n      scope\n      liveMode\n    }\n  }\n"): (typeof documents)["\n  query StripeDataApplications {\n    stripeDataApplications {\n      id\n      keyType\n      publishableKey\n      secretKey\n      accessKey\n      refreshKey\n      userId\n      stripeUserId\n      accountId\n      tokenType\n      scope\n      liveMode\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteStripeDataApplications($where: [StripeDataApplicationWhereUniqueInput!]!) {\n    deleteStripeDataApplications(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteStripeDataApplications($where: [StripeDataApplicationWhereUniqueInput!]!) {\n    deleteStripeDataApplications(where: $where) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GroupSupportTickets(\n    $where: GroupSupportTicketWhereInput!\n    $orderBy: [GroupSupportTicketOrderByInput!]!\n    $take: Int\n    $skip: Int!\n  ) {\n    groupSupportTickets(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      title\n      category\n      description\n      status\n      messagesCount\n      createdAt\n      lastMessageCreatedAt\n      group {\n        actualName\n        labelName\n        name\n        id\n      }\n      createdBy {\n        displayName\n        id\n      }\n    }\n    groupSupportTicketsCount(where: $where)\n  }\n"): (typeof documents)["\n  query GroupSupportTickets(\n    $where: GroupSupportTicketWhereInput!\n    $orderBy: [GroupSupportTicketOrderByInput!]!\n    $take: Int\n    $skip: Int!\n  ) {\n    groupSupportTickets(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      title\n      category\n      description\n      status\n      messagesCount\n      createdAt\n      lastMessageCreatedAt\n      group {\n        actualName\n        labelName\n        name\n        id\n      }\n      createdBy {\n        displayName\n        id\n      }\n    }\n    groupSupportTicketsCount(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {\n    groupSupportTicketsCount(where: $where)\n  }\n"): (typeof documents)["\n  query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {\n    groupSupportTicketsCount(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GroupSupportTicket($where: GroupSupportTicketWhereUniqueInput!) {\n    groupSupportTicket(where: $where) {\n      id\n      title\n      status\n      category\n      description\n      messagesCount\n      createdAt\n      lastMessageCreatedAt\n      group {\n        actualName\n        labelName\n        name\n        id\n      }\n      createdBy {\n        displayName\n        id\n      }\n      messages {\n        createdAt\n        actualName\n        name\n        message\n        files\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  query GroupSupportTicket($where: GroupSupportTicketWhereUniqueInput!) {\n    groupSupportTicket(where: $where) {\n      id\n      title\n      status\n      category\n      description\n      messagesCount\n      createdAt\n      lastMessageCreatedAt\n      group {\n        actualName\n        labelName\n        name\n        id\n      }\n      createdBy {\n        displayName\n        id\n      }\n      messages {\n        createdAt\n        actualName\n        name\n        message\n        files\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {\n    createGroupSupportTicket(data: $data) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {\n    createGroupSupportTicket(data: $data) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {\n    createGroupSupportMessage(data: $data) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {\n    createGroupSupportMessage(data: $data) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation TsepManifest {\n    tsep_manifest {\n      deviceID\n      manifestKey\n      url\n    }\n  }\n"): (typeof documents)["\n  mutation TsepManifest {\n    tsep_manifest {\n      deviceID\n      manifestKey\n      url\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation TsepVerify($input: Tsep_verifyInput!) {\n    tsep_verify(input: $input) {\n      status\n      code\n      message\n      authCode\n      cardType\n      addressVerificationCode\n      maskedCardNumber\n      expirationDate\n    }\n  }\n"): (typeof documents)["\n  mutation TsepVerify($input: Tsep_verifyInput!) {\n    tsep_verify(input: $input) {\n      status\n      code\n      message\n      authCode\n      cardType\n      addressVerificationCode\n      maskedCardNumber\n      expirationDate\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GPHFToken($input: Gphf_tokenInput!) {\n    gphf_token(input: $input) {\n      token\n      merchantID\n    }\n  }\n"): (typeof documents)["\n  mutation GPHFToken($input: Gphf_tokenInput!) {\n    gphf_token(input: $input) {\n      token\n      merchantID\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GroupMembers($where: GroupMemberWhereInput!, $take: Int, $skip: Int!) {\n    groupMembers(where: $where, take: $take, skip: $skip) {\n      id\n      access\n      group {\n        id\n        name\n      }\n      invite {\n        id\n        email\n      }\n      user {\n        id\n        lastLogin\n        name\n        lastName\n        createdAt\n        email\n      }\n    }\n    groupMembersCount(where: $where)\n  }\n"): (typeof documents)["\n  query GroupMembers($where: GroupMemberWhereInput!, $take: Int, $skip: Int!) {\n    groupMembers(where: $where, take: $take, skip: $skip) {\n      id\n      access\n      group {\n        id\n        name\n      }\n      invite {\n        id\n        email\n      }\n      user {\n        id\n        lastLogin\n        name\n        lastName\n        createdAt\n        email\n      }\n    }\n    groupMembersCount(where: $where)\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetQuickUserInfo($where: UserWhereUniqueInput!) {\n    user(where: $where) {\n      id\n      name\n      lastName\n      phone\n      email\n      title\n    }\n  }\n"): (typeof documents)["\n  query GetQuickUserInfo($where: UserWhereUniqueInput!) {\n    user(where: $where) {\n      id\n      name\n      lastName\n      phone\n      email\n      title\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetQuickInviteInfo($where: GroupMemberInviteWhereUniqueInput!) {\n    groupMemberInvite(where: $where) {\n      id\n      email\n    }\n  }\n"): (typeof documents)["\n  query GetQuickInviteInfo($where: GroupMemberInviteWhereUniqueInput!) {\n    groupMemberInvite(where: $where) {\n      id\n      email\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetUserMembershipInfo($take: Int, $where: GroupMemberWhereInput!) {\n    groupMembers(take: $take, where: $where) {\n      access\n      flags {\n        id\n        flag\n      }\n      id\n      invite {\n        id\n        email\n      }\n      user {\n        id\n        email\n      }\n      group {\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetUserMembershipInfo($take: Int, $where: GroupMemberWhereInput!) {\n    groupMembers(take: $take, where: $where) {\n      access\n      flags {\n        id\n        flag\n      }\n      id\n      invite {\n        id\n        email\n      }\n      user {\n        id\n        email\n      }\n      group {\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Flags($input: Group_flagsInput!) {\n    group_flags(input: $input) {\n      flags {\n        name\n        key\n        category\n        description\n      }\n    }\n  }\n"): (typeof documents)["\n  query Flags($input: Group_flagsInput!) {\n    group_flags(input: $input) {\n      flags {\n        name\n        key\n        category\n        description\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateGroupMemberData(\n    $where: GroupMemberWhereUniqueInput!\n    $updateBaseMemberData: GroupMemberUpdateInput!\n    $createMemberFlags: [GroupMemberFlagCreateInput!]!\n    $deleteMemberFlags: [GroupMemberFlagWhereUniqueInput!]!\n  ) {\n    updateGroupMember(where: $where, data: $updateBaseMemberData) {\n      id\n    }\n    createGroupMemberFlags(data: $createMemberFlags) {\n      id\n    }\n    deleteGroupMemberFlags(where: $deleteMemberFlags) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation UpdateGroupMemberData(\n    $where: GroupMemberWhereUniqueInput!\n    $updateBaseMemberData: GroupMemberUpdateInput!\n    $createMemberFlags: [GroupMemberFlagCreateInput!]!\n    $deleteMemberFlags: [GroupMemberFlagWhereUniqueInput!]!\n  ) {\n    updateGroupMember(where: $where, data: $updateBaseMemberData) {\n      id\n    }\n    createGroupMemberFlags(data: $createMemberFlags) {\n      id\n    }\n    deleteGroupMemberFlags(where: $deleteMemberFlags) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation CreatGroupMemberData($input: Group_createInviteInput!) {\n    group_createInvite(input: $input) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation CreatGroupMemberData($input: Group_createInviteInput!) {\n    group_createInvite(input: $input) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteGroupMembers($where: [GroupMemberWhereUniqueInput!]!) {\n    deleteGroupMembers(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteGroupMembers($where: [GroupMemberWhereUniqueInput!]!) {\n    deleteGroupMembers(where: $where) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query Processor_account_status($input: Processor_account_statusInput!) {\n    processor_account_status(input: $input) {\n      bank {\n        holder_type\n        last4\n        bank_code\n        bank_name\n      }\n      status\n      status2\n      pricing_profile\n    }\n  }\n"): (typeof documents)["\n  query Processor_account_status($input: Processor_account_statusInput!) {\n    processor_account_status(input: $input) {\n      bank {\n        holder_type\n        last4\n        bank_code\n        bank_name\n      }\n      status\n      status2\n      pricing_profile\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Processor_account_payoutUpdate($input: Processor_account_payoutUpdateInput!) {\n    processor_account_payoutUpdate(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n"): (typeof documents)["\n  mutation Processor_account_payoutUpdate($input: Processor_account_payoutUpdateInput!) {\n    processor_account_payoutUpdate(input: $input) {\n      submissionID\n      groupID\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation WebhookCreate($input: Group_createWebhookInput!) {\n    group_createWebhook(input: $input) {\n      id\n      url\n      secret\n    }\n  }\n"): (typeof documents)["\n  mutation WebhookCreate($input: Group_createWebhookInput!) {\n    group_createWebhook(input: $input) {\n      id\n      url\n      secret\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GroupWebhooks(\n    $where: GroupWebhookWhereInput!\n    $orderBy: [GroupWebhookOrderByInput!]!\n    $take: Int\n    $skip: Int!\n  ) {\n    groupWebhooks(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      webhookURL\n      scope\n      group {\n        name\n        id\n      }\n    }\n  }\n"): (typeof documents)["\n  query GroupWebhooks(\n    $where: GroupWebhookWhereInput!\n    $orderBy: [GroupWebhookOrderByInput!]!\n    $take: Int\n    $skip: Int!\n  ) {\n    groupWebhooks(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {\n      id\n      webhookURL\n      scope\n      group {\n        name\n        id\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation WebhookDelete($where: [GroupWebhookWhereUniqueInput!]!) {\n    deleteGroupWebhooks(where: $where) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation WebhookDelete($where: [GroupWebhookWhereUniqueInput!]!) {\n    deleteGroupWebhooks(where: $where) {\n      id\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;