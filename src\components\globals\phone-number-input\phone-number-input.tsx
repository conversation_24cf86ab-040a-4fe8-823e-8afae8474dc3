import React from 'react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

const PhoneNumberInput = ({ value, onChange, error }) => {
  return (
    <div className="relative">
      <PhoneInput
        international
        countryCallingCodeEditable={false}
        defaultCountry="US"
        value={value}
        onChange={onChange}
        className="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
      />
      {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      <style jsx global>{`
        .PhoneInputInput {
          border: none;
          border-radius: 0 0.375rem 0.375rem 0;
          padding-left: 0.5rem;
          font-size: 0.875rem;
          line-height: 1.25rem;
        }
        .PhoneInputCountrySelect {
          border: none;
          background-color: transparent;
          font-size: 0.875rem;
          line-height: 1.25rem;
        }
        .PhoneInputCountry {
          border-right: 1px solid #e5e7eb;
          padding-right: 0.5rem;
        }
      `}</style>
    </div>
  );
};

export default PhoneNumberInput;
