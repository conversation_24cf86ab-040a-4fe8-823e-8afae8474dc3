'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Processor, ProcessorConfig } from '@/types/processors';
import { useState } from 'react';

interface ProcessorConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  processor: Processor;
  onSave: (config: ProcessorConfig) => void;
  initialConfig?: ProcessorConfig;
}

export function ProcessorConfigModal({
  isOpen,
  onClose,
  processor,
  onSave,
  initialConfig,
}: ProcessorConfigModalProps) {
  const [config, setConfig] = useState<ProcessorConfig>(initialConfig || {});

  const handleSave = () => {
    onSave(config);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Configure {processor.name}</DialogTitle>
          <DialogDescription>
            Enter the required credentials for this processor. Make sure to keep these secure.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {processor.configFields.clientId && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="clientId" className="text-right">
                Client ID
              </Label>
              <Input
                id="clientId"
                value={config.clientId || ''}
                onChange={(e) => setConfig({ ...config, clientId: e.target.value })}
                className="col-span-3"
              />
            </div>
          )}
          {processor.configFields.clientSecret && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="clientSecret" className="text-right">
                Client Secret
              </Label>
              <Input
                id="clientSecret"
                type="password"
                value={config.clientSecret || ''}
                onChange={(e) => setConfig({ ...config, clientSecret: e.target.value })}
                className="col-span-3"
              />
            </div>
          )}
          {processor.configFields.apiKey && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="apiKey" className="text-right">
                API Key
              </Label>
              <Input
                id="apiKey"
                value={config.apiKey || ''}
                onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
                className="col-span-3"
              />
            </div>
          )}
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleSave}>
            Save changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
