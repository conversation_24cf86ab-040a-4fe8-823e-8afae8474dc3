import { MdHelp } from 'react-icons/md';
import { Tooltip } from 'flowbite-react';

export type FormToolTipProps = {
  /** Text displayed when hovering the tooltip icon */
  tooltip: string;
  /** Affects the styling dependent on which form field type the tooltip is rendered in */
  mode: 'input' | 'select';
};

export const FormToolTip = ({ tooltip, mode }: FormToolTipProps): JSX.Element => {
  const getModePadding = () => {
    switch (mode) {
      case 'input':
        return 'pl-2'; // padding-left for input
      case 'select':
        return 'pr-6'; // padding-right for select
      default:
        return '';
    }
  };

  return (
    <div className={`flex items-center ${getModePadding()}`}>
      <Tooltip content={tooltip} placement="right">
        <MdHelp className="cursor-pointer text-blue-500" />
      </Tooltip>
    </div>
  );
};

export default FormToolTip;
