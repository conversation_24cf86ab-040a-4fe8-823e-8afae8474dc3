import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { moneyFormat } from '@/lib/utils';
export type Product = {
  productName: string;
  description: string;
  quantity: string;
  price: string;
};

export type ProductListSectionProps = {
  products: Product[];
};
export const ProductListSection = ({ products }: ProductListSectionProps) => {
  const columns: Column[] = [
    {
      key: 'productName',
      header: 'PRODUCT',
      sortable: true,
      renderCell: (row) => {
        return (
          <>
            <div className="font-medium">{row.productName}</div>
            <div className="text-sm text-gray-500">{row.description}</div>
          </>
        );
      },
    },
    {
      key: 'quantity',
      header: 'QTY',
      sortable: true,
      renderCell: (row) => {
        return <>x {row.quantity}</>;
      },
    },
    {
      key: 'price',
      header: 'PRICE',
      sortable: true,
      renderCell: (row) => {
        return <>{moneyFormat(row.price)}</>;
      },
    },
    {
      key: 'price',
      header: 'TOTAL PRICE',
      sortable: true,
      renderCell: (row) => {
        return <>{moneyFormat(row.price * row.quantity)}</>;
      },
    },
  ];
  return <DataGridView columns={columns} rows={products} disablePagination />;
};
