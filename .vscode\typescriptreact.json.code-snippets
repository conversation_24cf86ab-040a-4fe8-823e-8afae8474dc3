{
  // Place your frontend workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }
  "React Function Component": {
    "prefix": "rfc", // The shortcut you will type
    "body": [
      "import React from 'react';",
      "",
      "export const ${1:ComponentName} = () => {",
      "  return (",
      "    <div>",
      "      $0", // Cursor position after snippet expansion
      "    </div>",
      "  );",
      "};",
    ],
    "description": "Create a React function component",
  },
  "GraphQL Mutation Snippet": {
    "prefix": "qlMutation", // The shortcut you will type
    "body": [
      "const [${1:mutationName}, { loading: ${2:loadingName} }] = useMutation(",
      "  ${3:Document},",
      "  {",
      "    onCompleted: () => {",
      "      toast.success('${4:Success message}');",
      "      ${5:refetchFunction}();",
      "      ${6:onCloseFunction}();",
      "    },",
      "    onError: (error) => {",
      "      toast.error(message.api.errorCreate('${7:Error message}', error.message));",
      "    },",
      "  },",
      ");",
    ],
    "description": "Insert a GraphQL mutation with success and error handling",
  },
}
