import React, { useEffect, useState } from 'react';
import { Mo<PERSON>, Button } from 'flowbite-react';
import { useForm, FormProvider } from 'react-hook-form';
import { message, parseFilesToBase64 } from '@/components/shared/utils';
import {
  Gateway_UpdateProductDocument,
  Gateway_ProductDocument,
} from '@/graphql/generated/graphql';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import { useLocationSelector } from '@/components/hooks';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { ProductForm } from './product-form';
import { ProductFormData, TaxableItem } from '../utils/types';
import { floatToInt2Dec } from '@/lib/utils';
import { apolloClient } from '@/lib/graphql/ApolloClient';

type ProductUpdateModalProps = {
  isOpen: boolean;
  onClose: () => void;
  queryData: {
    productID: string;
    groupID: string;
  };
};

export const ProductUpdateModal = ({ isOpen, onClose, queryData }: ProductUpdateModalProps) => {
  const { productID, groupID } = queryData;
  const [images, setImages] = useState<File[]>([]);
  const { locationFilter } = useLocationSelector({ readonly: true });

  const {
    data: productData,
    loading: productDataLaoding,
    error: productDataError,
  } = useQuery(Gateway_ProductDocument, {
    variables: {
      input: {
        data: {
          productID,
        },
        groupID,
      },
    },
    skip: !productID || !groupID,
  });

  const methods = useForm<ProductFormData>();

  const { reset, setValue } = methods;

  useEffect(() => {
    if (productDataError) {
      toast.error(productDataError.message);
    }
  }, [productDataError]);

  useEffect(() => {
    if (productData?.gateway_product) {
      const product = productData?.gateway_product;
      const taxableItem = () => {
        if (product.isInStore && product.isOnline) return TaxableItem.both;
        else if (product.isInStore) return TaxableItem.inStore;
        else return TaxableItem.online;
      };
      setValue('productName', product?.name ?? '');
      setValue('category', product?.category ?? '');
      setValue('subCategory', product?.subCategory ?? '');
      setValue('sku', product?.sku ?? '');
      setValue('brand', product?.brand ?? '');
      setValue('price', (product?.price ?? 0)?.toString() ?? '');
      setValue('description', product?.description ?? '');
      setValue('taxableItem', taxableItem());
      // setValue('itemWeight', product?.itemWeight?.toString() ?? '');
      // setValue('length', product?.length?.toString() ?? '');
      // setValue('breadth', product?.breadth?.toString() ?? '');
      // setValue('width', product?.width?.toString() ?? '');
      setValue('isRecurring', product?.isRecurring ?? false);
      setValue('recurringMode', product?.recurringMode ?? '');
      setValue('recurringInterval', product?.recurringInterval?.toString() ?? '');
      setValue('recurringFrequency', product?.recurringFrequency?.toString() ?? '');
      setValue('recurringTotalCycles', product?.recurringTotalCycles?.toString() ?? '');
      setValue('recurringTrialDays', product?.recurringTrialDays?.toString() ?? '');
      setValue('recurringSetupFee', product?.recurringSetupFee?.toString() ?? '');
    }
  }, [productData]);

  const [updateProductMutation, { loading: updateProductLoading }] = useMutation(
    Gateway_UpdateProductDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Product'));
        reset();
        apolloClient.resetStore();
        onClose();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Product', error.message));
      },
    },
  );

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setImages([...images, ...Array.from(event.target.files)]);
    }
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const onSubmitForm = async (data: ProductFormData) => {
    try {
      const base64Images = await parseFilesToBase64(images);
      await updateProductMutation({
        variables: {
          input: {
            groupID: locationFilter?.id ?? '',
            data: {
              productID: productID,
              form: {
                name: data.productName,
                category: data.category,
                subCategory: data.subCategory,
                brand: data.brand,
                price: floatToInt2Dec(Number(data.price)),
                // itemWeight: Number(data.itemWeight),
                // length: Number(data.length),
                // breadth: Number(data.breadth),
                // width: Number(data.width),
                description: data.description,
                isInStore: data.taxableItem === 'inStore' || data.taxableItem === 'both',
                isOnline: data.taxableItem === 'online' || data.taxableItem === 'both',
                productImages:
                  images.length > 0
                    ? base64Images.map((image) => ({
                        b64: image.b64,
                        type: '',
                      }))
                    : undefined,
                sku: data.sku,
                isRecurring: data.isRecurring,

                recurringMode: data.recurringMode ?? '',
                recurringInterval: data.recurringInterval
                  ? parseInt(data.recurringInterval)
                  : undefined,
                recurringFrequency: data.recurringFrequency
                  ? parseInt(data.recurringFrequency)
                  : undefined,
                recurringTotalCycles: data.recurringTotalCycles
                  ? parseInt(data.recurringTotalCycles)
                  : undefined,
                recurringTrialDays: data.recurringTrialDays
                  ? parseInt(data.recurringTrialDays)
                  : undefined,
                recurringSetupFee: data.recurringSetupFee
                  ? floatToInt2Dec(Number(data.recurringSetupFee))
                  : undefined,
              },
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="2xl">
      <Modal.Header className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">Update Product</h3>
      </Modal.Header>
      <Modal.Body>
        <SpinnerLoading isLoading={updateProductLoading || productDataLaoding} />
        {productData && (
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmitForm)} className="space-y-4">
              <ProductForm
                handleImageUpload={handleImageUpload}
                images={images}
                removeImage={removeImage}
              />
              <div className="flex gap-5">
                <Button type="submit" color="blue">
                  Update product
                </Button>
              </div>
            </form>
          </FormProvider>
        )}
      </Modal.Body>
    </Modal>
  );
};
