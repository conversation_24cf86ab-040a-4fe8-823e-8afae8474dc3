import { graphql } from '../generated';

export const GHLIntegDetails = graphql(`
  query GHLIntegDetails($input: Ghl_auth_getIntegrationDetailsInput!) {
    ghl_auth_getIntegrationDetails(input: $input) {
      locationID
      locationName
      accountName
    }
  }
`);

export const GHLSetupGetPending = graphql(`
  query GHLSetupGetPending($input: Ghl_auth_checkPendingInstallInput!) {
    ghl_auth_checkPendingInstall(input: $input) {
      code
    }
  }
`);

export const GHLSetupCheck = graphql(`
  query GHLSetupCheck($input: Ghl_auth_checkGHLAccessInput!) {
    ghl_auth_checkGHLAccess(input: $input) {
      hasAccess
    }
  }
`);

export const GHLSetupCommit = graphql(`
  mutation GHLSetupCommit($input: Ghl_auth_completeIntegrationInput!) {
    ghl_auth_completeIntegration(input: $input)
  }
`);
