'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Me } from '@/graphql/declarations/me';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { getOAuthClientGroup, requestOAuthAuthorization } from '@/lib/functions/oauth';
import { useQuery } from '@apollo/client';
import {
  AlertCircle,
  ArrowRight,
  CheckCircle2,
  Info,
  LoaderCircle,
  Lock,
  Shield,
  ShieldCheck,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AUTHSTORE } from '@/lib/auth-storage';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

const SharedLayout = ({ children }: { children: React.ReactNode }) => (
  <div className="flex h-screen w-screen items-center justify-center bg-gray-50">
    <Card className="w-fit max-w-md space-y-6 border-2 p-6 shadow-lg">
      <div className="flex justify-center space-x-4">
        <img src="/logo.png" alt="ngnair-logo" className="h-12" />
      </div>
      {children}
    </Card>
  </div>
);

export default function OAuthPage() {
  const router = useRouter();
  const params = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedGroup, setSelectedGroup] = useState('');
  const [step, setStep] = useState('loading');
  const [groups, setGroups] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [clientGroupInfo, setClientGroupInfo] = useState<{
    groupId: string;
    groupName: string;
    scope?: string;
  } | null>(null);

  // OAuth parameters
  const oauthClientKey = params?.get('client_id') || '';
  const redirectURI = params?.get('redirect_uri') || '';
  const state = params?.get('state') || '';

  // Check authentication status
  const { data: meData, loading: loadingAuth } = useQuery(Me, {});

  // Get the user's groups using GET_GROUPS_LIST
  const { data: groupsData, loading: loadingGroups } = useQuery(GET_GROUPS_LIST, {
    skip: !meData?.authenticatedItem?.id,
    fetchPolicy: 'network-only',
    variables: {
      where: {},
      take: 100,
      skip: 0,
    },
  });

  // Fetch client group info if available
  useEffect(() => {
    const fetchClientGroupInfo = async () => {
      if (oauthClientKey) {
        try {
          const groupInfo = await getOAuthClientGroup(oauthClientKey);
          if (!groupInfo || !groupInfo.groupId) {
            setError('Invalid OAuth client: unable to retrieve client information');
            setStep('error');
            return;
          }
          setClientGroupInfo(groupInfo);
          // Pre-select this group if it exists
          if (groupInfo.groupId) {
            setSelectedGroup(groupInfo.groupId);
          }
        } catch (err) {
          console.error('Failed to fetch client group info:', err);
          setError('Failed to retrieve OAuth client information');
          setStep('error');
        }
      }
    };

    fetchClientGroupInfo();
  }, [oauthClientKey]);

  useEffect(() => {
    if (loadingAuth) return;

    // Step 1: Check login status
    if (!meData?.authenticatedItem?.id) {
      redirectToLogin();
      return;
    }

    // Verify required OAuth parameters
    if (!oauthClientKey || !redirectURI || !state) {
      setError('Missing required OAuth parameters');
      setStep('error');
      setLoading(false);
      return;
    }

    // If we have the groups data, update state
    if (!loadingGroups && groupsData?.groups) {
      setGroups(groupsData.groups);
      setLoading(false);
      setStep('select-group');
    }
  }, [loadingAuth, loadingGroups, meData, groupsData, oauthClientKey, redirectURI, state]);

  const redirectToLogin = () => {
    const currentPath = window.location.pathname;
    const currentSearch = window.location.search;
    const encodedRedirect = encodeURIComponent(`${currentPath}${currentSearch}`);
    window.location.href = `/login?redirect=${encodedRedirect}`;
  };

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroup(groupId);
  };

  const handleConsent = async () => {
    setIsProcessing(true);
    setStep('processing');

    try {
      if (!selectedGroup) {
        throw new Error('No group selected');
      }

      const token = AUTHSTORE.get() || undefined;

      const response = await requestOAuthAuthorization({
        oauthClientKey,
        redirectURI,
        state,
        token,
        groupId: selectedGroup,
      });

      // Redirect to the provided redirect URI
      if (response.redirectURI) {
        window.location.href = response.redirectURI;
      } else {
        throw new Error('No redirect URI provided in the response');
      }
    } catch (err) {
      console.error('OAuth authorization failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to complete OAuth authorization');
      setStep('error');
      setIsProcessing(false);
    }
  };

  const handleCancel = () => {
    // Redirect back to the client with error
    const errorRedirect = `${redirectURI}?error=access_denied&state=${state}`;
    window.location.href = errorRedirect;
  };

  // Loading state
  if (loading || loadingAuth || loadingGroups) {
    return (
      <SharedLayout>
        <div className="flex flex-col items-center justify-center space-y-4">
          <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
          <p>Loading...</p>
        </div>
      </SharedLayout>
    );
  }

  // Error state
  if (step === 'error') {
    return (
      <SharedLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error || 'An unexpected error occurred'}</AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Link href="/dashboard">
            <Button variant="outline">Back to Dashboard</Button>
          </Link>
        </div>
      </SharedLayout>
    );
  }

  // Select group state
  if (step === 'select-group') {
    return (
      <SharedLayout>
        <div className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-center text-xl font-semibold">
              {clientGroupInfo ? `Connect to ${clientGroupInfo.groupName}` : 'Select Organization'}
            </h2>
            <p className="text-center text-sm text-gray-600">
              {clientGroupInfo
                ? `Choose which organization you'd like to use with ${clientGroupInfo.groupName}`
                : 'Choose which organization you want to use with this application'}
            </p>
          </div>

          {clientGroupInfo && (
            <div className="rounded-md bg-blue-50 p-3">
              <div className="flex">
                <div className="flex-shrink-0">
                  <Info className="h-5 w-5 text-blue-400" aria-hidden="true" />
                </div>
                <div className="ml-3 flex-1 md:flex md:justify-between">
                  <p className="text-sm text-blue-700">
                    This application is from {clientGroupInfo.groupName}
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-4 py-2">
            <label className="text-sm font-medium text-gray-700">Your organization</label>
            <Select onValueChange={handleGroupSelect} value={selectedGroup}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select an organization" />
              </SelectTrigger>
              <SelectContent>
                {groups.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    {group.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500">
              This determines which data the application will have access to.
            </p>
          </div>

          <div className="pt-2">
            <div className="flex items-center justify-between">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                className="bg-blue-500 text-white hover:bg-blue-600"
                onClick={() => setStep('consent')}
                disabled={!selectedGroup}
              >
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </SharedLayout>
    );
  }

  // Consent screen
  if (step === 'consent') {
    const selectedGroupName =
      groups.find((g) => g.id === selectedGroup)?.name ||
      clientGroupInfo?.groupName ||
      'Selected Organization';

    // Parse scope if available
    const scopes = clientGroupInfo?.scope ? clientGroupInfo.scope.split(' ') : [];

    // Get permission descriptions based on scopes
    const getPermissionDescriptions = () => {
      const descriptions = [
        {
          icon: <Shield className="h-4 w-4 text-blue-500" />,
          text: 'View your profile information',
        },
        {
          icon: <CheckCircle2 className="h-4 w-4 text-blue-500" />,
          text: `Access data from ${selectedGroupName}`,
        },
      ];

      if (scopes.includes('write')) {
        descriptions.push({
          icon: <Shield className="h-4 w-4 text-blue-500" />,
          text: 'Modify data within your organization',
        });
      }

      if (scopes.includes('admin')) {
        descriptions.push({
          icon: <ShieldCheck className="h-4 w-4 text-blue-500" />,
          text: 'Perform administrative actions',
        });
      }

      return descriptions;
    };

    return (
      <SharedLayout>
        <div className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-center text-xl font-semibold">
              {clientGroupInfo ? `Connect to ${clientGroupInfo.groupName}` : 'Authorize Access'}
            </h2>

            <div className="flex items-center justify-center space-x-2">
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <Lock className="h-6 w-6 text-blue-500" />
              </div>
              <ArrowRight className="h-4 w-4 text-gray-400" />
              <div className="flex flex-col items-center">
                <div className="h-12 w-12 overflow-hidden rounded-lg border border-gray-200">
                  <div className="flex h-full w-full items-center justify-center bg-gray-100">
                    {selectedGroupName.substring(0, 1).toUpperCase()}
                  </div>
                </div>
                <span className="mt-1 text-xs font-medium">{selectedGroupName}</span>
              </div>
            </div>

            <p className="mt-4 text-center text-sm">
              {clientGroupInfo ? (
                `You are connecting to ${clientGroupInfo.groupName}`
              ) : (
                <>
                  <span className="font-semibold text-blue-600">{oauthClientKey}</span> is
                  requesting access to your account
                </>
              )}
            </p>
          </div>

          {clientGroupInfo && scopes.length > 0 && (
            <div className="rounded-md bg-gray-50 p-3">
              <div className="mb-2 text-center text-sm text-gray-700">Requested permissions:</div>
              <div className="flex flex-wrap justify-center gap-2">
                {scopes.map((scope) => (
                  <Badge key={scope} variant="secondary" className="text-xs">
                    {scope}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center text-sm text-gray-700">
              <Lock className="mr-2 h-4 w-4 text-blue-500" />
              <span className="font-medium">This connection will allow:</span>
            </div>
            <ul className="space-y-3">
              {getPermissionDescriptions().map((item, index) => (
                <li key={index} className="flex items-start">
                  <div className="mr-2 mt-0.5">{item.icon}</div>
                  <span className="text-sm text-gray-700">{item.text}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="rounded-md bg-blue-50 p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <Info className="h-5 w-5 text-blue-400" aria-hidden="true" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  {clientGroupInfo
                    ? `By connecting, you are granting ${clientGroupInfo.groupName} access to your data in ${selectedGroupName}`
                    : `By authorizing this application, you are granting access to your data in ${selectedGroupName}`}
                </p>
              </div>
            </div>
          </div>

          <div className="pt-2">
            <div className="flex items-center justify-between">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button className="bg-blue-500 text-white hover:bg-blue-600" onClick={handleConsent}>
                {clientGroupInfo ? 'Connect' : 'Authorize'}
              </Button>
            </div>
          </div>
        </div>
      </SharedLayout>
    );
  }

  // Processing state
  if (step === 'processing') {
    return (
      <SharedLayout>
        <div className="flex flex-col items-center justify-center space-y-4">
          <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
          <p>Processing authorization...</p>
        </div>
      </SharedLayout>
    );
  }

  // Fallback
  return null;
}
