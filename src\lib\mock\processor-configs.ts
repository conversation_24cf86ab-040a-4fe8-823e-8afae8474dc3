// Mock processor configurations and stats
// Only processors with configurations here will show as "Connected" when enabled

export interface ProcessorStats {
  totalRevenue: number;
  transactions: number;
  successRate: number;
  conversionRate: number;
}

export interface ProcessorMockConfig {
  clientId?: string;
  clientSecret?: string;
  apiKey?: string;
  stats: ProcessorStats;
}

// Mock configurations for select processors
export const MOCK_PROCESSOR_CONFIGS: Record<string, ProcessorMockConfig> = {
  // Payment Processors
  globalpay_propay: {
    clientId: 'gp_test_client_123456',
    clientSecret: 'gp_secret_abcdef123456',
    apiKey: 'gp_api_key_789xyz',
    stats: {
      totalRevenue: 89456.78,
      transactions: 8234,
      successRate: 97.8,
      conversionRate: 96.2,
    },
  },
  elavon: {
    clientId: 'elv_client_987654',
    clientSecret: 'elv_secret_fedcba654321',
    stats: {
      totalRevenue: 67234.56,
      transactions: 5678,
      successRate: 98.9,
      conversionRate: 97.5,
    },
  },
  worldpay: {
    clientId: 'wp_client_456789',
    clientSecret: 'wp_secret_123abc789',
    apiKey: 'wp_api_xyz456',
    stats: {
      totalRevenue: 45678.9,
      transactions: 3456,
      successRate: 99.1,
      conversionRate: 98.7,
    },
  },

  // CRM Processors
  gohighlevel: {
    clientId: 'ghl_client_abc123',
    clientSecret: 'ghl_secret_def456',
    stats: {
      totalRevenue: 23456.78,
      transactions: 1234,
      successRate: 95.6,
      conversionRate: 94.3,
    },
  },
  hubspot: {
    apiKey: 'hs_api_key_789def',
    stats: {
      totalRevenue: 34567.89,
      transactions: 2345,
      successRate: 96.8,
      conversionRate: 95.4,
    },
  },

  // POS Processors
  square: {
    clientId: 'sq_client_xyz789',
    clientSecret: 'sq_secret_abc123',
    stats: {
      totalRevenue: 45678.9,
      transactions: 3456,
      successRate: 98.2,
      conversionRate: 97.1,
    },
  },
  clover: {
    apiKey: 'clv_api_key_456ghi',
    stats: {
      totalRevenue: 12345.67,
      transactions: 987,
      successRate: 97.5,
      conversionRate: 96.8,
    },
  },
  toast: {
    clientId: 'toast_client_def789',
    clientSecret: 'toast_secret_ghi012',
    stats: {
      totalRevenue: 28456.32,
      transactions: 1567,
      successRate: 96.3,
      conversionRate: 95.1,
    },
  },
  lightspeed: {
    clientId: 'ls_client_jkl345',
    clientSecret: 'ls_secret_mno678',
    stats: {
      totalRevenue: 15789.45,
      transactions: 892,
      successRate: 98.7,
      conversionRate: 97.2,
    },
  },
};

// Check if a processor has mock configuration
export function hasProcessorConfig(processorId: string): boolean {
  return processorId in MOCK_PROCESSOR_CONFIGS;
}

// Get mock configuration for a processor
export function getProcessorConfig(processorId: string): ProcessorMockConfig | null {
  return MOCK_PROCESSOR_CONFIGS[processorId] || null;
}

// Get mock stats for a processor
export function getProcessorStats(processorId: string): ProcessorStats | null {
  const config = MOCK_PROCESSOR_CONFIGS[processorId];
  return config ? config.stats : null;
}

// Get processor config without stats (for modal)
export function getProcessorConfigForModal(
  processorId: string,
): { clientId?: string; clientSecret?: string; apiKey?: string } | null {
  const config = MOCK_PROCESSOR_CONFIGS[processorId];
  if (!config) return null;

  const { stats, ...configOnly } = config;
  return configOnly;
}
