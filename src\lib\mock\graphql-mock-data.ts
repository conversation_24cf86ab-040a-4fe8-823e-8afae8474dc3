// Comprehensive mock data for all GraphQL operations
import { mockDisputes } from '@/mock/disputes-data';
import { mockSupportTickets } from '@/lib/mock/support-mock';
import { mockAccounts } from '@/lib/mock/accounts-data';
import { allMockBatches } from '@/mock/batches-data';
import { mockDeposits } from '@/mock/deposits-data';

// Mock user data
export const mockUser = {
  id: 'user-123',
  name: 'Test',
  lastName: 'User',
  displayName: 'Test User',
  email: '<EMAIL>',
  phone: '+****************',
  title: 'admin',
  role: 'admin',
  createdAt: '2024-01-01T00:00:00Z',
  groupsCount: 5,
  flag_canAffiliate: true,
};

// Mock groups data
export const mockGroups = [
  {
    id: 'group-1',
    name: 'Main Business Group',
    labelName: 'Main Business',
    actualName: 'Main Business LLC',
    membersCount: 3,
    processorStatus: 'active',
    signingURL: 'https://example.com/sign',
    mainProcessor: 'STRIPE',
    mainGateway: 'STRIPE_GATEWAY',
    einNumber: '12-3456789',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    displayMerchantID: 'MERCH-001',
    pciStatus: 'COMPLIANT',
    firstName: 'John',
    lastName: 'Doe',
    fullName: 'John Doe',
    addressLine1: '123 Main St',
    addressLine2: 'Suite 100',
    city: 'New York',
    state: 'NY',
    country: 'US',
    zip: '10001',
    mccCode: '5999',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    default_includeSurcharge: true,
    default_globalDisableCC: false,
    default_globalDisableACH: false,
    ghlAccessesCount: 2,
    hubspotAccessesCount: 1,
    ghlPayTransactionMapsCount: 150,
    supportTicketsCount: 3,
    serviceAccountsCount: 2,
    flag_disableTokens: false,
    flag_disableAutoToken: false,
  },
  {
    id: 'group-2',
    name: 'Secondary Business',
    labelName: 'Secondary',
    actualName: 'Secondary Business Inc',
    membersCount: 2,
    processorStatus: 'active',
    signingURL: 'https://example.com/sign2',
    mainProcessor: 'SQUARE',
    mainGateway: 'SQUARE_GATEWAY',
    einNumber: '98-7654321',
    createdAt: '2024-02-01T00:00:00Z',
    updatedAt: '2024-02-15T00:00:00Z',
    displayMerchantID: 'MERCH-002',
    pciStatus: 'PENDING',
    firstName: 'Jane',
    lastName: 'Smith',
    fullName: 'Jane Smith',
    addressLine1: '456 Oak Ave',
    addressLine2: '',
    city: 'Los Angeles',
    state: 'CA',
    country: 'US',
    zip: '90210',
    mccCode: '7299',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    default_includeSurcharge: false,
    default_globalDisableCC: false,
    default_globalDisableACH: true,
    ghlAccessesCount: 1,
    hubspotAccessesCount: 0,
    ghlPayTransactionMapsCount: 75,
    supportTicketsCount: 1,
    serviceAccountsCount: 1,
    flag_disableTokens: false,
    flag_disableAutoToken: true,
  },
];

// Mock transactions data
export const mockTransactions = [
  {
    transactionID: 'P-840-2025-********',
    status: 'CAPTURED',
    date: '2025-01-15T10:30:00Z',
    location: 'NYC Store',
    brand: 'VISA',
    customer: 'Virtual Terminal', // For transaction list display
    createdBy: 'John Doe',
    paymentType: 'CREDIT_CARD',
    method: 'VISA',
    batchID: 'BATCH-8001',
    paymentPlan: null,
    source: 'Virtual Terminal',
    authCode: '123456', // Random 6 digit auth code
    amount: 450, // $450.00 (less than 1000)
    entryMethod: 'KEYED',
    tokenSource: null, // No token source for EMV
    gsa: 'GSA001',
    emv: 'N/A',
    last4: '4242',
    customerName: null,
    customerID: null,
    customerEmail: null,
    customerPhone: null,
    customerCountry: null,
    customerBillingAddress: null,
    commercialLevel: 'LEVEL_1',
    result: '', // Result is blank
    message: 'SUCCESS',
    brandReference: 'BRAND-REF-001',
    productName: 'Premium Service Package',
    processorInfo: {
      name: 'Stripe Payment Processor',
      type: 'PAYMENT',
      id: 'stripe-001',
    },
    breakdown: {
      discount: 0,
      directDiscount: 0,
      actualDiscount: 0,
      tax: 0, // Set to 0
      shipping: 0, // Set to 0
      shippingDiscount: 0,
      shippingDirectDiscount: 0,
      shippingActualDiscount: 0,
      fees: 0, // Set to 0 (extra fees)
      actualFees: 0,
      tip: 0, // Set to 0
      subtotal: 450,
      subscriptionTotal: 0,
      rawTotal: 450,
      total: 450,
      expectedTotal: 450,
    },
    purchaseDetails: [
      {
        id: 'ITEM-001',
        name: 'Professional Service',
        quantity: 1,
        price: 450,
        total: 450,
      },
    ],
    transactionHistory: [
      {
        date: '2025-01-15T10:30:00Z',
        status: 'CAPTURED',
        response: 'SUCCESS', // Response changed to success
        avs: 'MATCH', // AVS response match
        cvv: 'MATCH', // CVV match
      },
    ],
  },
  {
    transactionID: 'P-840-2025-00000438',
    status: 'REFUND',
    date: '2025-01-14T14:20:00Z',
    location: 'LA Store',
    brand: 'MASTERCARD',
    customer: 'Virtual Terminal', // For transaction list display
    createdBy: 'Jane Smith',
    paymentType: 'CREDIT_CARD',
    method: 'MASTERCARD',
    batchID: 'BATCH-8002',
    paymentPlan: null,
    source: 'Virtual Terminal',
    authCode: '789012', // Random 6 digit auth code
    amount: 320, // $320.00 (less than 1000)
    entryMethod: 'KEYED',
    tokenSource: null, // No token source for EMV
    gsa: 'GSA002',
    emv: 'N/A',
    last4: '5555',
    customerName: null,
    customerID: null,
    customerEmail: null,
    customerPhone: null,
    customerCountry: null,
    customerBillingAddress: null,
    commercialLevel: 'LEVEL_2',
    result: '', // Result is blank
    message: 'SUCCESS',
    brandReference: 'BRAND-REF-002',
    productName: 'Basic Service',
    processorInfo: {
      name: 'PayPal Payment Gateway',
      type: 'PAYMENT',
      id: 'paypal-001',
    },
    breakdown: {
      discount: 0,
      directDiscount: 0,
      actualDiscount: 0,
      tax: 0, // Set to 0
      shipping: 0, // Set to 0
      shippingDiscount: 0,
      shippingDirectDiscount: 0,
      shippingActualDiscount: 0,
      fees: 0, // Set to 0 (extra fees)
      actualFees: 0,
      tip: 0, // Set to 0
      subtotal: 320,
      subscriptionTotal: 0,
      rawTotal: 320,
      total: 320,
      expectedTotal: 320,
    },
    purchaseDetails: [
      {
        id: 'ITEM-002',
        name: 'Consultation Service',
        quantity: 1,
        price: 320,
        total: 320,
      },
    ],
    transactionHistory: [
      {
        date: '2025-01-14T14:20:00Z',
        status: 'REFUND',
        response: 'SUCCESS', // Response changed to success
        avs: 'UNMATCH', // AVS response unmatch
        cvv: 'MATCH', // CVV match
      },
    ],
  },
  {
    transactionID: 'P-840-2025-00000439',
    status: 'DECLINE',
    date: '2025-01-13T09:15:00Z',
    location: 'Chicago Store',
    brand: 'AMEX',
    customer: 'Virtual Terminal', // For transaction list display
    createdBy: 'Mike Johnson',
    paymentType: 'CREDIT_CARD',
    method: 'AMEX',
    batchID: 'BATCH-8001',
    paymentPlan: null,
    source: 'Virtual Terminal',
    authCode: '345678', // Random 6 digit auth code
    amount: 185, // $185.00 (less than 1000)
    entryMethod: 'KEYED',
    tokenSource: null, // No token source for EMV
    gsa: 'GSA003',
    emv: 'N/A',
    last4: '1234',
    customerName: null,
    customerID: null,
    customerEmail: null,
    customerPhone: null,
    customerCountry: null,
    customerBillingAddress: null,
    commercialLevel: 'LEVEL_1',
    result: '', // Result is blank
    message: 'SUCCESS',
    brandReference: 'BRAND-REF-003',
    productName: 'Mobile Payment Service',
    processorInfo: {
      name: 'Square Payment System',
      type: 'PAYMENT',
      id: 'square-001',
    },
    breakdown: {
      discount: 0,
      directDiscount: 0,
      actualDiscount: 0,
      tax: 0, // Set to 0
      shipping: 0, // Set to 0
      shippingDiscount: 0,
      shippingDirectDiscount: 0,
      shippingActualDiscount: 0,
      fees: 0, // Set to 0 (extra fees)
      actualFees: 0,
      tip: 0, // Set to 0
      subtotal: 185,
      subscriptionTotal: 0,
      rawTotal: 185,
      total: 185,
      expectedTotal: 185,
    },
    purchaseDetails: [
      {
        id: 'ITEM-003',
        name: 'Basic Package',
        quantity: 1,
        price: 185,
        total: 185,
      },
    ],
    transactionHistory: [
      {
        date: '2025-01-13T09:15:00Z',
        status: 'DECLINE',
        response: 'SUCCESS', // Response changed to success
        avs: 'MATCH', // AVS response match
        cvv: 'UNMATCH', // CVV unmatch
      },
    ],
  },
];

// Mock products data
export const mockProducts = [
  {
    id: 'PROD-001',
    name: 'Premium Service Package',
    price: 9999, // $99.99
    discount: 0,
    productStatus: 'ACTIVE',
    taxExempt: false,
    kitchenItem: false,
    sku: 'PSP-001',
    category: 'Services',
    subCategory: 'Premium',
    brand: 'NGnair',
    isRecurring: true,
    recurringMode: 'SUBSCRIPTION',
    recurringInterval: 'MONTHLY',
    recurringFrequency: 1,
    recurringTotalCycles: 0, // Unlimited
    recurringTrialDays: 7,
    recurringSetupFee: 2500, // $25.00
    description: 'Premium service package with all features included',
    isInStore: true,
    isOnline: true,
    productImages: [
      {
        url: '/images/products/premium-package.jpg',
        name: 'Premium Package Image',
      },
    ],
  },
  {
    id: 'PROD-002',
    name: 'Basic Service',
    price: 2999, // $29.99
    discount: 500, // $5.00 discount
    productStatus: 'ACTIVE',
    taxExempt: false,
    kitchenItem: false,
    sku: 'BS-001',
    category: 'Services',
    subCategory: 'Basic',
    brand: 'NGnair',
    isRecurring: false,
    recurringMode: null,
    recurringInterval: null,
    recurringFrequency: null,
    recurringTotalCycles: null,
    recurringTrialDays: null,
    recurringSetupFee: null,
    description: 'Basic one-time service',
    isInStore: true,
    isOnline: true,
    productImages: [
      {
        url: '/images/products/basic-service.jpg',
        name: 'Basic Service Image',
      },
    ],
  },
];

// Mock categories data
export const mockCategories = [
  {
    id: 'CAT-001',
    name: 'Services',
    status: 'ACTIVE',
    color: '#3B82F6',
    description: 'Service-based products',
    subCategory: ['Premium', 'Basic', 'Enterprise'],
    colors: ['#3B82F6', '#10B981', '#F59E0B'],
  },
  {
    id: 'CAT-002',
    name: 'Products',
    status: 'ACTIVE',
    color: '#10B981',
    description: 'Physical products',
    subCategory: ['Electronics', 'Accessories', 'Software'],
    colors: ['#10B981', '#8B5CF6', '#EF4444'],
  },
];

// Mock dashboard summary data - matches GraphQL schema - ACTUAL CALCULATED VALUES
export const mockDashboardSummary = {
  dateStart: '2024-01-01',
  dateEnd: '2024-01-31',
  totalPortfolio: 1265438, // ACTUAL: Based on earnings ($126,543.89) * 10 for business valuation
  captured: {
    total: 28500, // REALISTIC: $28,500 - proportional to earnings level (in cents)
    percentageChange: '15',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 285000 },
      { time: '2024-01-07T00:00:00Z', value: 295000 },
      { time: '2024-01-14T00:00:00Z', value: 275000 },
      { time: '2024-01-21T00:00:00Z', value: 290000 },
      { time: '2024-01-28T00:00:00Z', value: 280000 },
    ],
  },
  refunds: {
    total: 1425, // REALISTIC: $1,425 - about 5% of captured amount (in cents)
    percentageChange: '5',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 14250 },
      { time: '2024-01-07T00:00:00Z', value: 13800 },
      { time: '2024-01-14T00:00:00Z', value: 15200 },
      { time: '2024-01-21T00:00:00Z', value: 14100 },
      { time: '2024-01-28T00:00:00Z', value: 14650 },
    ],
  },
  batched: {
    total: 27075, // REALISTIC: $27,075 - net captured minus refunds (in cents)
    percentageChange: '10',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 270750 },
      { time: '2024-01-07T00:00:00Z', value: 281200 },
      { time: '2024-01-14T00:00:00Z', value: 259800 },
      { time: '2024-01-21T00:00:00Z', value: 275900 },
      { time: '2024-01-28T00:00:00Z', value: 265350 },
    ],
  },
  deposits: {
    total: 25725, // REALISTIC: $25,725 - batched minus fees (in cents)
    percentageChange: '12',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 257250 },
      { time: '2024-01-07T00:00:00Z', value: 268140 },
      { time: '2024-01-14T00:00:00Z', value: 246810 },
      { time: '2024-01-21T00:00:00Z', value: 262155 },
      { time: '2024-01-28T00:00:00Z', value: 252083 },
    ],
  },
  disputes: {
    total: 855, // REALISTIC: $855 - about 3% of captured amount (in cents)
    percentageChange: '8',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 8550 },
      { time: '2024-01-07T00:00:00Z', value: 8850 },
      { time: '2024-01-14T00:00:00Z', value: 8250 },
      { time: '2024-01-21T00:00:00Z', value: 8700 },
      { time: '2024-01-28T00:00Z', value: 8400 },
    ],
  },
  earnings: {
    total: 12654389, // $126,543.89 from earnings data (in cents)
    percentageChange: '18',
    timeStart: '2024-01-01T00:00:00Z',
    timeEnd: '2024-01-31T23:59:59Z',
    timeEntries: [
      { time: '2024-01-01T00:00:00Z', value: 1265438 },
      { time: '2024-01-07T00:00:00Z', value: 1298765 },
      { time: '2024-01-14T00:00:00Z', value: 1234567 },
      { time: '2024-01-21T00:00:00Z', value: 1287654 },
      { time: '2024-01-28T00:00:00Z', value: 1276543 },
    ],
  },
};

// Mock location summary data - matches GraphQL schema
export const mockLocationSummary = {
  data: [
    {
      locationID: 'LOC-001',
      locationName: 'Main Store',
      changePercentage: '15.5',
      currentYearTotal: '75000.25',
      lastYearTotal: '65000.00',
      yearChangePercentage: '15.4',
    },
    {
      locationID: 'LOC-002',
      locationName: 'Online Store',
      changePercentage: '12.3',
      currentYearTotal: '50000.25',
      lastYearTotal: '44500.00',
      yearChangePercentage: '12.4',
    },
    {
      locationID: 'LOC-003',
      locationName: 'Mobile Sales',
      changePercentage: '8.7',
      currentYearTotal: '25000.00',
      lastYearTotal: '23000.00',
      yearChangePercentage: '8.7',
    },
  ],
};

// Mock affiliation data
export const mockAffiliation = {
  id: 'AFF-001',
  affiliatesCount: 25,
  groupAffiliatesCount: 5,
  codesCount: 10,
  bank_routingNumber: '*********',
  bank_accountNumber: '****1234',
  bank_accountName: 'NGnair Business Account',
  codes: [
    { code: 'PARTNER10', description: '10% partner discount' },
    { code: 'NEWUSER20', description: '20% new user discount' },
    { code: 'BULK50', description: '50% bulk order discount' },
  ],
};

// Mock payment methods data
export const mockPaymentMethods = [
  {
    id: 'PM-001',
    type: 'CREDIT_CARD',
    brand: 'VISA',
    last4: '4242',
    expiryMonth: 12,
    expiryYear: 2025,
    isDefault: true,
    customerID: 'CUST-001',
  },
  {
    id: 'PM-002',
    type: 'BANK_ACCOUNT',
    bankName: 'Chase Bank',
    accountLast4: '1234',
    routingNumber: '*********',
    isDefault: false,
    customerID: 'CUST-001',
  },
];

// Mock customers data
export const mockCustomers = [
  {
    id: 'CUST-001',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Pine St, Seattle, WA 98101',
    country: 'US',
    totalSpent: 450, // $450.00
    totalTransactions: 15,
    lastTransactionDate: '2024-01-15T10:30:00Z',
    status: 'ACTIVE',
    paymentMethods: mockPaymentMethods,
  },
  {
    id: 'CUST-002',
    name: 'Bob Smith',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Oak Ave, Portland, OR 97201',
    country: 'US',
    totalSpent: 28500, // $285.00
    totalTransactions: 8,
    lastTransactionDate: '2024-01-10T14:20:00Z',
    status: 'ACTIVE',
    paymentMethods: [],
  },
];

// Mock subscription data
export const mockSubscriptions = [
  {
    id: 'SUB-001',
    planId: 'PLAN-PREMIUM',
    status: 'ACTIVE',
    description: 'Premium Monthly Plan',
    customerId: 'CUST-001',
    amount: 9999, // $99.99
    billingCycleInterval: 'MONTHLY',
    billingFrequency: 1,
    nextBillDate: '2024-02-15T00:00:00Z',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
];

// Mock webhook data
export const mockWebhooks = [
  {
    id: 'WH-001',
    url: 'https://example.com/webhook',
    events: ['transaction.completed', 'transaction.failed'],
    status: 'ACTIVE',
    secret: 'whsec_mock_secret',
    createdAt: '2024-01-01T00:00:00Z',
  },
];

// Mock API keys data
export const mockApiKeys = [
  {
    id: 'KEY-001',
    name: 'Production API Key',
    keyPrefix: 'pk_live_',
    permissions: ['read', 'write'],
    status: 'ACTIVE',
    lastUsed: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 'KEY-002',
    name: 'Test API Key',
    keyPrefix: 'pk_test_',
    permissions: ['read'],
    status: 'ACTIVE',
    lastUsed: '2024-01-14T16:20:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
];

// Mock notifications data
export const mockNotifications = [
  {
    id: 'NOTIF-001',
    topics: ['TRANSACTION', 'PAYMENT'],
    read: false,
    content: 'New transaction completed for $150.00',
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 'NOTIF-002',
    topics: ['SYSTEM', 'UPDATE'],
    read: false,
    content: 'System maintenance scheduled for tonight',
    createdAt: '2024-01-15T08:00:00Z',
  },
  {
    id: 'NOTIF-003',
    topics: ['DISPUTE', 'ALERT'],
    read: true,
    content: 'Dispute case #DSP-001 has been resolved',
    createdAt: '2024-01-14T16:45:00Z',
  },
  {
    id: 'NOTIF-004',
    topics: ['ACCOUNT', 'SECURITY'],
    read: true,
    content: 'New login detected from Chrome browser',
    createdAt: '2024-01-14T14:20:00Z',
  },
  {
    id: 'NOTIF-005',
    topics: ['PAYMENT', 'SUCCESS'],
    read: true,
    content: 'Monthly subscription payment processed successfully',
    createdAt: '2024-01-13T09:15:00Z',
  },
];

// Export all mock data
export const mockGraphQLData = {
  user: mockUser,
  groups: mockGroups,
  transactions: mockTransactions,
  products: mockProducts,
  categories: mockCategories,
  disputes: mockDisputes,
  batches: allMockBatches,
  deposits: mockDeposits,
  supportTickets: mockSupportTickets,
  accounts: mockAccounts,
  dashboardSummary: mockDashboardSummary,
  locationSummary: mockLocationSummary,
  affiliation: mockAffiliation,
  paymentMethods: mockPaymentMethods,
  customers: mockCustomers,
  subscriptions: mockSubscriptions,
  webhooks: mockWebhooks,
  apiKeys: mockApiKeys,
  notifications: mockNotifications,
};
