import { Column } from '@/components/globals/sortable-table/data-grid-view';
import { Button } from 'flowbite-react';
import { useEffect, useState } from 'react';
import { HiDocumentDownload } from 'react-icons/hi';

type Props = {
  dataFetcher: () => Promise<any>;
  reportName: string;
  columns: Column<any>[];
};

const ExportCSV4 = ({ dataFetcher, reportName, columns }: Props) => {
  const [triggerExport, setTriggerExport] = useState(false);

  const jsonToCSV = (data: any[], columns: Column<any>[]) => {
    const headers = columns.map((col) => col.header);
    const csvRows = data.map((row) =>
      columns
        .map((col) => {
          const value = col.valueGetter ? col.valueGetter(row) : row[col.key];
          return JSON.stringify(value ?? '');
        })
        .join(','),
    );
    return [headers.join(','), ...csvRows].join('\n');
  };

  const exportCSVFile = async () => {
    try {
      const dataToExport = await dataFetcher();
      const csvData = jsonToCSV(dataToExport, columns);
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${reportName}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('Error exporting CSV:', error);
    }
  };

  useEffect(() => {
    if (triggerExport) {
      exportCSVFile();
      setTriggerExport(false);
    }
  }, [triggerExport]);

  return (
    <Button className="h-10 p-0" color="gray" onClick={() => setTriggerExport(true)}>
      <div className="flex items-center gap-x-3">
        <HiDocumentDownload className="text-xl" />
        <span>Export</span>
      </div>
    </Button>
  );
};

export default ExportCSV4;
