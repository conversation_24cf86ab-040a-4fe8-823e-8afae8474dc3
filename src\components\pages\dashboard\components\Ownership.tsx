'use client';

import { useState } from 'react';
import { MerchantForm } from '../types/merchart-schema';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { cn, formatPhoneNumber, formatSSN } from '@/lib/utils';
import React from 'react';
import { Button } from '@/components/ui/button';
import { CalendarIcon, ChevronsUpDown, Trash2 } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import moment from 'moment';
import { Calendar } from '@/components/ui/calendar';
import { countries } from '@/consts/phoneCountries';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';

const Ownership = ({ formState }: { formState: MerchantForm }) => {
  const [checkingZipCode, setCheckingZipCode] = useState(false);

  const {
    setValue,
    register,
    watch,
    formState: state,
    setError,
    trigger,
    reset,
    clearErrors,
  } = formState;
  const values = watch();

  const { errors } = state;

  const addAnotherOwner = () => {
    if (values.ownership.length >= 4) return;
    const template = {
      first_name: '',
      last_name: '',
      position: '',
      ownership: '',
      phone_number: '',
      home_address: '',
      country: '',
      dial_code: '+1',
      state: '',
      city: '',
      zip_code: '',
      ssn: '',
      date_of_birth: null,
      email: '',
    };

    setValue('ownership', [...values.ownership, template]);
  };

  const removeOwner = (index: number) => {
    if (values.ownership.length <= 1) return;
    const newOwnership = values.ownership.filter((_, i) => i !== index);
    reset({ ...values, ownership: newOwnership }, { keepDefaultValues: true });
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="mb-4 space-y-4 text-justify text-sm text-zinc-500">
        <p>
          Provide the following information for each individual who owns, directly or indirectly, 25
          % or more of the equity interest of your business. If no single owner owns more than 25%,
          an individual with significant responsibility can be added as principal 1*.
        </p>
        <p>
          *Individual with significant responsibility includes an executive officer or owner with
          authority to control, manage, and direct the legal entity &#40;e.g. a Chief Executive
          Officer, Chief Financial Officer, Managing Member, General Partner, President, Vice
          President, or Treasurer&#41; or any individual with authority to perform such functions.
        </p>
      </div>

      <FormField
        control={formState.control}
        name="is_an_individual"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center gap-2">
            <FormControl>
              <Checkbox
                checked={field.value}
                onChange={(e) => {
                  const checked = e.target.checked;
                  checked
                    ? reset(
                        {
                          ...values,
                          ownership: [values.ownership?.[0]],
                          is_an_individual: checked,
                        },
                        { keepDefaultValues: true },
                      )
                    : setValue('is_an_individual', checked);

                  if (checked) {
                    setValue('ownership.[0].ownership' as any, '0');
                    setValue('ownership.[0].dial_code' as any, '+1');
                    setValue('ownership.[0].country' as any, 'United States');
                  } else {
                    setValue('ownership.[0].dial_code' as any, '+1');
                    setValue('ownership.[0].country' as any, '');
                  }
                }}
                className="h-[18px] w-[18px] bg-white"
              />
            </FormControl>
            <span className="!mt-0 font-bold text-zinc-600">
              {!values.is_an_individual ? (
                'Is an individual with significant responsibility'
              ) : (
                <div className="flex flex-row items-center gap-x-2">
                  Control Prong
                  <span className="text-xs font-normal">&#40;Must reside in the US&#41;</span>
                </div>
              )}
            </span>
          </FormItem>
        )}
      />

      {values.ownership?.map((owner, index) => (
        <React.Fragment key={index}>
          <div className="mt-5 flex flex-row justify-between">
            <div className="mb-1 text-lg font-bold text-[#495057]">{`Owner ${index + 1}`}</div>
            {values.ownership.length > 1 && (
              <Button
                className="flex flex-row items-center justify-center gap-2"
                onClick={() => removeOwner(index)}
                variant="destructive"
              >
                <Trash2 className="h-4 w-4 stroke-white" />
              </Button>
            )}
          </div>
          <div className="flex w-full flex-row gap-4">
            <FormField
              control={formState.control}
              name={`ownership[${index}].first_name` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    First Name<span className="text-red-400">*</span>
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      {...field}
                      onChange={(e) =>
                        setValue(`ownership[${index}].first_name` as any, e.target.value, {
                          shouldValidate: true,
                        })
                      }
                      placeholder="First Name"
                      className={cn(
                        errors?.ownership?.[index]?.first_name &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={formState.control}
              name={`ownership[${index}].last_name` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Last Name<span className="text-red-400">*</span>
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      {...field}
                      onChange={(e) =>
                        setValue(`ownership[${index}].last_name` as any, e.target.value, {
                          shouldValidate: true,
                        })
                      }
                      placeholder="Last Name"
                      className={cn(
                        errors?.ownership?.[index]?.last_name &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-full flex-row gap-4">
            <FormField
              control={formState.control}
              name={`ownership[${index}].position` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Position<span className="text-red-400">*</span>
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      {...field}
                      placeholder="Position"
                      onChange={(e) =>
                        setValue(`ownership[${index}].position` as any, e.target.value, {
                          shouldValidate: true,
                        })
                      }
                      className={cn(
                        errors?.ownership?.[index]?.position &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div className="flex flex-1 flex-col gap-2">
              <Label>
                Home / Mobile Phone Number
                <span className="text-red-400">*</span>
              </Label>
              <div className="flex flex-row justify-between gap-2">
                <Popover>
                  <PopoverTrigger>
                    <Button
                      type="button"
                      variant="select-outline"
                      role="combobox"
                      disabled={values.is_an_individual}
                      className={cn(
                        !values?.ownership?.[index]?.dial_code &&
                          'border-red-400 text-red-400 hover:bg-red-50 hover:text-red-400',
                        values.is_an_individual && '!cursor-default',
                      )}
                    >
                      {values?.ownership?.[index]?.dial_code ?? 'Select'}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                    <PopoverContent
                      className={cn('h-80', values.is_an_individual && 'hidden')}
                      side="bottom"
                      align="start"
                    >
                      <Command>
                        <CommandInput placeholder="Search country..." />
                        <CommandEmpty>No country found.</CommandEmpty>
                        <CommandGroup>
                          {countries.map((country) => (
                            <CommandItem
                              value={country?.country_name}
                              key={country?.country_name}
                              onSelect={() => {
                                setValue(
                                  `ownership.[${index}].country` as any,
                                  country.country_name,
                                );
                                setValue(
                                  `ownership.[${index}].dial_code` as any,
                                  country.dial_code,
                                );
                              }}
                            >
                              {country?.country_name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </PopoverTrigger>
                </Popover>

                <FormField
                  control={formState.control}
                  name={`ownership[${index}].phone_number` as any}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Phone Number"
                          value={formatPhoneNumber(values.ownership?.[index]?.phone_number)}
                          onChange={(e) =>
                            setValue(`ownership[${index}].phone_number` as any, e.target.value, {
                              shouldValidate: true,
                            })
                          }
                          className={cn(
                            errors?.ownership?.[index]?.phone_number &&
                              'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                          )}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {!values.is_an_individual && (
              <FormField
                control={formState.control}
                name={`ownership[${index}].ownership` as any}
                render={({ field }) => (
                  <FormItem className="flex flex-1 flex-col">
                    <Label>
                      Ownership in %<span className="text-red-400">*</span>
                    </Label>
                    <FormControl className="mt-2">
                      <Input
                        {...field}
                        onChange={(e) =>
                          setValue(`ownership[${index}].ownership` as any, e.target.value, {
                            shouldValidate: true,
                          })
                        }
                        disabled={values.is_an_individual}
                        type="number"
                        placeholder="%"
                        className={cn(
                          errors?.ownership?.[index]?.ownership &&
                            'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                        )}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}
          </div>
          <div className="flex w-full flex-row gap-4">
            <FormField
              control={formState.control}
              name={`ownership[${index}].home_address` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Home Address<span className="text-red-400">*</span>
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      {...field}
                      placeholder="Home Address"
                      onChange={(e) =>
                        setValue(`ownership[${index}].home_address` as any, e.target.value, {
                          shouldValidate: true,
                        })
                      }
                      className={cn(
                        errors?.ownership?.[index]?.home_address &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={formState.control}
              name={`ownership[${index}].country` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Country<span className="text-red-400">*</span>
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      {...field}
                      disabled={values.is_an_individual}
                      value={values.ownership?.[index]?.country}
                      onChange={(e) =>
                        setValue(`ownership[${index}].country` as any, e.target.value, {
                          shouldValidate: true,
                        })
                      }
                      placeholder="Enter Country"
                      className={cn(
                        errors?.ownership?.[index]?.country &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={formState.control}
              name={`ownership[${index}].state` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    State<span className="text-red-400">*</span>
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      {...field}
                      onChange={(e) =>
                        setValue(`ownership[${index}].state` as any, e.target.value, {
                          shouldValidate: true,
                        })
                      }
                      placeholder="Enter State"
                      className={cn(
                        errors?.ownership?.[index]?.state &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-full flex-row gap-4">
            <FormField
              control={formState.control}
              name={`ownership[${index}].city` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    City<span className="text-red-400">*</span>
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      {...field}
                      onChange={(e) =>
                        setValue(`ownership[${index}].city` as any, e.target.value, {
                          shouldValidate: true,
                        })
                      }
                      placeholder="Enter City"
                      className={cn(
                        errors?.ownership?.[index]?.city &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={formState.control}
              name={`ownership[${index}].zip_code` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Zip Code<span className="text-red-400">*</span>
                  </Label>

                  <Input
                    {...field}
                    onChange={(e) =>
                      setValue(`ownership[${index}].zip_code` as any, e.target.value, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="Enter Zip Code"
                    className={cn(
                      errors?.ownership?.[index]?.zip_code &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormItem>
              )}
            />
            <FormField
              control={formState.control}
              name={`ownership[${index}].ssn` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Social Security Number &#40;SSN&#41; <span className="text-red-400">*</span>
                  </Label>

                  <Input
                    {...field}
                    value={formatSSN(values?.ownership?.[index]?.ssn)}
                    onChange={(e) =>
                      setValue(`ownership[${index}].ssn` as any, e.target.value, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="Enter SSN"
                    className={cn(
                      errors?.ownership?.[index]?.ssn &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormItem>
              )}
            />
          </div>
          <div className="flex w-full flex-row gap-4">
            <div className="flex flex-1 flex-col gap-2">
              <Label>
                Date of Birth
                <span className="text-red-400">*</span>
              </Label>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="picker"
                    className={cn(
                      errors?.ownership?.[index]?.date_of_birth &&
                        'border-red-400 text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  >
                    <CalendarIcon className={cn('mr-2 h-4 w-4')} />
                    {values?.ownership?.[index]?.date_of_birth ? (
                      moment(values?.ownership?.[index]?.date_of_birth).format('MMMM DD, YYYY')
                    ) : (
                      <span
                        className={cn(
                          !values?.ownership?.[index]?.date_of_birth && 'text-gray-500',
                        )}
                      >
                        Pick a date
                      </span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="start" className="w-auto p-0">
                  <Calendar
                    mode="single"
                    captionLayout="dropdown-buttons"
                    selected={new Date(moment(values?.ownership?.[index]?.date_of_birth).date())}
                    onSelect={(date) => {
                      date &&
                        setValue(`ownership[${index}].date_of_birth` as any, date.toISOString(), {
                          shouldValidate: true,
                        });
                    }}
                    fromYear={1960}
                    toYear={2030}
                  />
                </PopoverContent>
              </Popover>
            </div>
            <FormField
              control={formState.control}
              name={`ownership[${index}].email` as any}
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Email Address
                    <span className="text-red-400">*</span>
                  </Label>

                  <Input
                    {...field}
                    onChange={(e) =>
                      setValue(`ownership[${index}].email` as any, e.target.value, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="Email"
                    className={cn(
                      errors?.ownership?.[index]?.email &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormItem>
              )}
            />
          </div>
        </React.Fragment>
      ))}
      {values.ownership.length < 4 && !values.is_an_individual && (
        <Button variant="primary" className="w-fit" onClick={() => addAnotherOwner()}>
          Add New Owner
        </Button>
      )}
    </div>
  );
};

export default Ownership;
