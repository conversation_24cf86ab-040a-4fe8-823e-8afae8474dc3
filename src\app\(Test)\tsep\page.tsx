'use client';

import { TSEPHostedTokenizerComponent } from '@/components/payments/tsep/tokenizer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TsepVerify } from '@/graphql/declarations/tsep';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useState } from 'react';

export default function Page() {
  const [data, setData] = useState({
    cardToken: '',
    cvc: '',
    expiryDate: '',
    nameOnCard: '',
    address: '',
    zip: '',
    brand: '',
  });
  const [res, setRes] = useState('');

  const verify = async () => {
    const resp = await apolloClient.mutate({
      mutation: TsepVerify,
      variables: {
        input: {
          card: {
            token: data.cardToken,
            cvv: data.cvc.trim(),
            expiry: data.expiryDate,
            type: data.brand,
          },
          avs: {
            firstName: data.nameOnCard,
            lastName: data.nameOnCard,
            addressLine1: data.address,
            zipCode: data.zip,
          },
        },
      },
    });

    console.log(resp);
    setRes(JSON.stringify(resp, null, 2));
  };

  return (
    <div>
      <Card className="mx-auto max-w-screen-sm p-4">
        <CardHeader>
          <CardTitle>TSEP</CardTitle>
        </CardHeader>
        <CardContent>
          <TSEPHostedTokenizerComponent
            labels={{
              cardNumber: 'Card Number',
              expiryDate: 'Expiry Date',
              cvv: 'CVV',
              cardHolderName: 'Card Holder Name',
              zipCode: 'Zip Code',
            }}
            allowOptionals={{
              // cardHolderName: true,
              // zipCode: true,
              cvv: true,
            }}
            onEvent={(event, data) => {
              console.log(event, data);
            }}
            iframeComponentAttributes={{
              height: '230px',
            }}
            onToken={(token) => {
              // methods.setValue('nameOnCard', token.cardHolderName);
              // methods.setValue('cardToken', token.tsepToken);
              // methods.setValue('cvc', token.cvv2);
              // methods.setValue('expiryDate', token.expirationDate);
              // methods.setValue('zip', token.zipCode);
              // methods.setValue('brand', token.cardType);
              setData({
                cardToken: token.tsepToken,
                cvc: token.cvv2,
                expiryDate: token.expirationDate,
                nameOnCard: token.cardHolderName,
                address: '1045',
                zip: token.zipCode,
                brand: token.cardType,
              });
              // console.log('token', token);
            }}
          />
          <button onClick={verify} className="rounded-md bg-primary-600 p-4 py-2 text-primary-50">
            Verify
          </button>
          <div className="mt-4 whitespace-pre-wrap break-all">{res}</div>
        </CardContent>
      </Card>
    </div>
  );
}
