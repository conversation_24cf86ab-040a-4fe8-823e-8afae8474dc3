import { graphql } from '../generated';

export const SYNC_STRIPE_INTEGRATION = graphql(`
  mutation SyncStripeIntegration($input: Stripe_syncInput!) {
    stripe_sync(input: $input) {
      count
    }
  }
`);

export const SAVE_STRIPE_INTEGRATION = graphql(`
  mutation SaveStripeIntegration($input: Stripe_setupInput!) {
    stripe_setup(input: $input)
  }
`);
export const DOWNLOAD_STRIPE_TEMPLATE_CSV = graphql(`
  query DownloadStripeTemplateCSV($input: Import_stripe_sampleInput!) {
    import_stripe_sample(input: $input) {
      sampleData
    }
  }
`);
