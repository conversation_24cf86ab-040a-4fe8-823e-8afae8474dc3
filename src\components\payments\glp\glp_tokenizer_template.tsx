'use client';

import { MutableRefObject, useEffect, useState } from 'react';
import style from './glp_tokenizer.module.css';
import { GLPayTokenizerComponent } from './glp_tokenizer';
import { cn } from '@/lib/utils';
import { Spinner } from '@/components/ui/spinner';

export interface TokenizerSuccessToken {
  details: Details;
  paymentReference: string;
}

export interface Details {
  accountId: string;
  accountName: string;
  cardBin: string;
  cardLast4: string;
  cardNumber: string;
  cardSecurityCode: boolean;
  cardType: string;
  expiryMonth: string;
  expiryYear: string;
  merchantId: string;
  merchantName: string;
  reference: string;
}

export default function GLPayTokenizerTemplate(args: {
  apiKey: string;
  merchantID?: string;
  liveMode: boolean;
  onSubmission: (resp: TokenizerSuccessToken) => void;
  onLogs?: (log: any) => void;
  onError?: (error: any) => void;
  tokenizerRef: MutableRefObject<any>;
  labels?: {
    cardNumber?: string;
    cvv?: string;
    expiration?: string;
    submitText?: string;
  };
  styles?: {
    input?: {
      borderRadius?: string;
      borderColor?: string;
      focusBorderColor?: string;
      padding?: string;
      marginBottom?: string;
    };
    button?: {
      borderRadius?: string;
      borderColor?: string;
      textColor?: string;
      hoverBgColor?: string;
      hoverTextColor?: string;
      padding?: string;
      margin?: string;
      fontSize?: string;
    };
    label?: {
      fontSize?: string;
      fontWeight?: string;
      color?: string;
      marginBottom?: string;
    };
  };
  config: {
    ids: {
      cardNumber: string;
      cardExpiration: string;
      cardCvv: string;
      cardHolderName?: string;
      submitButton: string;
      paymentForm: string;
    };
    text?: {
      submitText?: string;
    };
  };
}) {
  const [isReady, setIsReady] = useState<{
    [key: string]: boolean;
  }>({});

  const [card, setCard] = useState<{
    cardNumber: string;
    cardExpiration: string;
    cardCvv: string;
    brand: string;
  } | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const onTokenReceived = (token: TokenizerSuccessToken) => {
    setCard({
      cardNumber: token.details.cardNumber,
      brand: token.details.cardType,
      cardCvv: token.details.cardSecurityCode ? '***' : '',
      cardExpiration: `${token.details.expiryMonth}/${token.details.expiryYear}`,
    });
    args.onSubmission(token);
  };

  const [readyNow, setReadyNow] = useState(false);

  useEffect(
    function watchIsReady() {
      if (isReady['cardNumber'] && isReady['cardExpiration'] && isReady['cardCvv']) {
        setReadyNow(true);
      } else {
        setReadyNow(false);
      }
    },
    [isReady],
  );

  return (
    <>
      <form
        className={style['payment-form']}
        id="payment-form"
        method="post"
        ref={args.tokenizerRef}
        onSubmit={(e) => {
          e.preventDefault();
          return false;
        }}
        style={{ display: card ? 'none' : 'block' }}
      >
        <div>
          <div className="flex flex-col gap-2">
            <div className="flex flex-1 flex-col gap-2">
              <label
                htmlFor="card-number"
                className="text-sm font-medium text-gray-700"
                style={{
                  fontSize: args.styles?.label?.fontSize,
                  fontWeight: args.styles?.label?.fontWeight,
                  color: args.styles?.label?.color,
                  marginBottom: args.styles?.label?.marginBottom,
                }}
              >
                {args.labels?.cardNumber || 'Card Number'}
              </label>
              <div id="card-number"></div>
            </div>
            <div className="flex flex-1 gap-2">
              <div className="flex flex-col gap-2">
                <label
                  htmlFor="card-cvv"
                  className="text-sm font-medium text-gray-700"
                  style={{
                    fontSize: args.styles?.label?.fontSize,
                    fontWeight: args.styles?.label?.fontWeight,
                    color: args.styles?.label?.color,
                    marginBottom: args.styles?.label?.marginBottom,
                  }}
                >
                  {args.labels?.cvv || 'CVV'}
                </label>
                <div id="card-cvv"></div>
              </div>
              <div className="flex flex-col gap-2">
                <label
                  htmlFor="card-expiration"
                  className="text-sm font-medium text-gray-700"
                  style={{
                    fontSize: args.styles?.label?.fontSize,
                    fontWeight: args.styles?.label?.fontWeight,
                    color: args.styles?.label?.color,
                    marginBottom: args.styles?.label?.marginBottom,
                  }}
                >
                  {args.labels?.expiration || 'Expiry Date'}
                </label>
                <div id="card-expiration"></div>
              </div>
            </div>
          </div>
        </div>
        <div
          className={cn(
            readyNow ? '' : 'cursor-not-allowed opacity-25',
            !isSubmitting ? 'block' : 'hidden',
          )}
          style={{ width: '100%', marginTop: '12px' }}
          id="submit-button"
        ></div>
        {/* show a loading spinner here */}
        {isSubmitting && (
          <div className="flex h-full w-full items-center justify-center overflow-hidden py-4">
            <Spinner className="h-10 w-10" />
          </div>
        )}
        {readyNow && (
          <div className="pt-2 text-xs text-gray-600">
            Click the button above to confirm card details and use at this transaction
          </div>
        )}
      </form>

      <div className="flex w-full flex-col gap-4 p-2" style={{ display: card ? 'block' : 'none' }}>
        <div className="flex flex-col gap-2">
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-700">Card Number</span>
            <span className="text-sm text-gray-600">{card?.cardNumber}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-700">Card Type</span>
            <span className="text-sm text-gray-600">{card?.brand}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-700">Expiry Date</span>
            <span className="text-sm text-gray-600">{card?.cardExpiration}</span>
          </div>
        </div>
        <button
          className={cn(
            'mt-4 w-full rounded bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-300',
          )}
          onClick={() => {
            setCard(null);
            args.onError?.('Card Removed');
          }}
        >
          Use Different Card
        </button>
      </div>
      <GLPayTokenizerComponent
        merchantID={args.merchantID}
        apiKey={args.apiKey}
        liveMode={args.liveMode}
        onSubmission={onTokenReceived}
        isReady={isReady}
        setIsReady={setIsReady}
        onLogs={args.onLogs}
        isSubmitting={isSubmitting}
        setIsSubmitting={setIsSubmitting}
        config={{
          ids: args.config.ids,
          text: args.config.text,
          styles: args.styles,
        }}
      />
    </>
  );
}
