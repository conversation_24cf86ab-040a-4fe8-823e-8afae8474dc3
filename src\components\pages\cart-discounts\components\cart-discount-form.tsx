import { Button } from 'flowbite-react';
import { FormFormattedInput, FormInput, FormSelect } from '@/components/globals';
import { FormProvider, UseFormReturn, useWatch } from 'react-hook-form';
import { message } from '@/components/shared/utils';
import { CartDiscountFormData } from '../utils';
import { CartDiscountDelete } from './cart-discount-delete';
import { GatewayUniDiscountOutputType } from '@/graphql/generated/graphql';
import { FormToggle } from '@/components/globals/form-toggle';

interface CartDiscountFormProps {
  methods: UseFormReturn<CartDiscountFormData>;
  onSubmit: (data: CartDiscountFormData) => void;
  isEdit?: boolean;
  onDelete?: () => void;
}

export const CartDiscountForm = ({
  methods,
  onSubmit,
  isEdit,
  onDelete,
}: CartDiscountFormProps) => {
  const { getValues, control } = methods;

  const typeWatcher = useWatch({
    control,
    name: 'type',
  });
  const ststusWatcher = useWatch({
    control,
    name: 'status',
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormInput id="name" name="name" label="Name" rules={{ required: message.requiredField }} />
        <div className="w-1/2">
          <FormSelect
            id="type"
            name="type"
            label="Type"
            rules={{ required: message.requiredField }}
            options={[
              { value: '', label: 'Select discount type' },
              {
                value: GatewayUniDiscountOutputType.Fixed,
                label: GatewayUniDiscountOutputType.Fixed,
              },
              {
                value: GatewayUniDiscountOutputType.Percentage,
                label: GatewayUniDiscountOutputType.Percentage,
              },
            ]}
          />
        </div>
        <FormToggle id="status" name="status" label={ststusWatcher ? 'Enable' : 'Disable'} />
        <FormFormattedInput
          id="amount"
          name="amount"
          label={typeWatcher === GatewayUniDiscountOutputType.Percentage ? 'Percentage' : 'Amount'}
          mask={typeWatcher === GatewayUniDiscountOutputType.Percentage ? '99%' : '$9999'}
          rules={{
            required: message.requiredField,
            validate: (value) => {
              const isValid =
                typeWatcher === GatewayUniDiscountOutputType.Percentage && value > 100;
              return !isValid || 'Amount in percentage should not exceed 100%';
            },
          }}
        />
        <div className="flex gap-2">
          <Button color="blue" type="submit">
            {isEdit ? 'Update' : 'Create'}
          </Button>

          {isEdit && <CartDiscountDelete cartDiscount={getValues()} onDelete={onDelete} />}
        </div>
      </form>
    </FormProvider>
  );
};
