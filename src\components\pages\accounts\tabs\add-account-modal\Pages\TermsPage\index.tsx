import { Promisable } from '@/types/types';
import { useSearchParams } from 'next/navigation';
import { MutableRefObject, useEffect, useState, useRef } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { updateData } from '../updateData';
import { TermsContent } from './TermsContent';
import { Button } from '@/components/ui/button';
import moment from 'moment';

const TermsPage = (args: {
  triggerSubmit?: MutableRefObject<() => Promisable<boolean>>;
  initialData?: any;
}) => {
  const methods = useForm({
    defaultValues: {
      attestations: args.initialData ?? [],
    },
  });

  const [ipAdd, setIpAdd] = useState('');
  const [signing, setSigning] = useState(false);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(false);
  const contentRef = useRef<HTMLDivElement | null>(null);

  const searchParams = useSearchParams();
  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: 'attestations',
  });

  // Fetch IP address
  const getIP = async () => {
    const resp = await fetch('https://api.ipify.org?format=json');
    const data = await resp.json();
    return data.ip;
  };

  // Set IP address on component mount
  useEffect(() => {
    getIP().then(setIpAdd);
  }, []);

  // Reset form when initialData changes
  useEffect(() => {
    if (args.initialData) {
      methods.reset({ attestations: args.initialData });
    }
  }, [args.initialData]);

  // Sign document and set attestations
  const signDocument = async () => {
    setSigning(true);
    const ip = await getIP();
    methods.setValue('attestations', [
      {
        ip_address: ip,
        name: 'propay_sub_merchant_terms_and_conditions',
        time_of_attestation: new Date().toISOString(),
        signature: '',
        url: '',
      },
      {
        ip_address: ip,
        name: 'beneficial_owner',
        time_of_attestation: new Date().toISOString(),
        signature: '',
        url: '',
      },
    ]);
    setSigning(false);
  };

  // Watch for attestations
  const hasSigned = methods.watch('attestations') ?? [];

  // Handle scroll event to check if scrolled to bottom
  const handleScroll = () => {
    if (contentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      setIsScrolledToBottom(scrollTop + clientHeight >= scrollHeight * 0.9);
    }
  };

  // Attach scroll event listener
  useEffect(() => {
    const currentRef = contentRef.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (currentRef) {
        currentRef.removeEventListener('scroll', handleScroll);
      }
    };
  }, [contentRef]);

  // Trigger submit function
  useEffect(() => {
    if (args.triggerSubmit) {
      args.triggerSubmit.current = async () => {
        if (hasSigned.length === 0) {
          alert('Please sign the document first if you want to proceed');
          return false;
        }
        const isValid = await methods.trigger();
        if (isValid) {
          const val = methods.getValues();
          try {
            // Remove null values from attestations
            val.attestations = val.attestations.map((attestation: any) => ({
              ip_address: attestation.ip_address,
              name: attestation.name,
              time_of_attestation: attestation.time_of_attestation,
            }));
            return await updateData({
              submitType: 'attestations',
              data: val.attestations,
              groupID: searchParams?.get('groupID') ?? undefined,
              form: methods,
            });
          } catch (error) {
            console.error(error);
            return false;
          }
        }
        return false;
      };
    }
  }, [args.triggerSubmit, hasSigned]);

  return (
    <div className="w-full px-4">
      <div ref={contentRef} className="mb-4 max-h-[50vh] overflow-y-auto border shadow-lg">
        <TermsContent />
      </div>
      {hasSigned.length > 0 ? (
        <div className="mb-2">
          <p className="text-sm text-gray-500">You have signed the document</p>
          <p>
            Time of Attestation:{' '}
            <strong>
              {moment(hasSigned[0]?.time_of_attestation).format('MMMM Do YYYY, h:mm:ss a')}
            </strong>{' '}
            on IP: <strong>{hasSigned[0]?.ip_address}</strong>
          </p>
        </div>
      ) : (
        <Button
          variant={isScrolledToBottom ? 'primary' : 'ghost'}
          className="ml-auto"
          disabled={signing || !isScrolledToBottom} // Disable if not scrolled to bottom
          onClick={signDocument}
        >
          {signing
            ? 'Signing...'
            : isScrolledToBottom
              ? 'Sign Document'
              : 'Scroll the Document to Bottom to Sign'}
        </Button>
      )}
    </div>
  );
};

export default TermsPage;
