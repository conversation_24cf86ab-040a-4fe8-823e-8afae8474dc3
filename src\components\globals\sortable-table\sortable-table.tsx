// import { Table } from '@/libs/ui/table';
import { Radio, Table } from 'flowbite-react';
import { Fragment, useCallback, useMemo, useState } from 'react';

type DataType = {
  transaction: string;
  location: string;
  customer: string;
  date: string;
  method: string;
  brand: string;
  last4: string;
  amount: string;
  status: string;
};

const data: DataType[] = [
  {
    transaction: '12345',
    location: 'Store A',
    customer: '<PERSON>',
    date: '2024-09-01',
    method: 'Credit Card',
    brand: 'Visa',
    last4: '1234',
    amount: '$100.00',
    status: 'Completed',
  },
  {
    transaction: '67890',
    location: 'Store B',
    customer: '<PERSON>',
    date: '2024-09-02',
    method: 'Cash',
    brand: 'MasterCard',
    last4: '5678',
    amount: '$50.00',
    status: 'Pending',
  },
  {
    transaction: '11223',
    location: 'Store C',
    customer: 'Alice Johnson',
    date: '2024-09-03',
    method: 'Debit Card',
    brand: 'Amex',
    last4: '9101',
    amount: '$75.00',
    status: 'Completed',
  },
  {
    transaction: '44556',
    location: 'Store D',
    customer: '<PERSON>',
    date: '2024-09-04',
    method: 'Credit Card',
    brand: 'Discover',
    last4: '1122',
    amount: '$120.00',
    status: 'Failed',
  },
  {
    transaction: '77889',
    location: 'Store E',
    customer: 'Carol White',
    date: '2024-09-05',
    method: 'Cash',
    brand: 'Visa',
    last4: '3344',
    amount: '$30.00',
    status: 'Completed',
  },
  {
    transaction: '99001',
    location: 'Store F',
    customer: 'Dave Black',
    date: '2024-09-06',
    method: 'Debit Card',
    brand: 'MasterCard',
    last4: '5566',
    amount: '$85.00',
    status: 'Pending',
  },
  {
    transaction: '22334',
    location: 'Store G',
    customer: 'Eve Green',
    date: '2024-09-07',
    method: 'Credit Card',
    brand: 'Amex',
    last4: '7788',
    amount: '$95.00',
    status: 'Completed',
  },
  {
    transaction: '55667',
    location: 'Store H',
    customer: 'Frank Blue',
    date: '2024-09-08',
    method: 'Cash',
    brand: 'Discover',
    last4: '9900',
    amount: '$40.00',
    status: 'Failed',
  },
  {
    transaction: '88990',
    location: 'Store I',
    customer: 'Grace Gray',
    date: '2024-09-09',
    method: 'Debit Card',
    brand: 'Visa',
    last4: '1234',
    amount: '$110.00',
    status: 'Completed',
  },
  {
    transaction: '33445',
    location: 'Store J',
    customer: 'Hank Red',
    date: '2024-09-10',
    method: 'Credit Card',
    brand: 'MasterCard',
    last4: '5678',
    amount: '$65.00',
    status: 'Pending',
  },
];

enum Direction {
  dec = 'dec',
  asc = 'asc',
}

const sortData = (data: DataType[], column: keyof DataType, direction: Direction) => {
  return [...data].sort((a, b) => {
    if (a[column] < b[column]) return direction === 'asc' ? -1 : 1;
    if (a[column] > b[column]) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

type SortableTableProps = {};

export const SortableTable = () => {
  const [sortConfig, setSortConfig] = useState<{ key: keyof DataType; direction: Direction }>({
    key: 'transaction',
    direction: Direction.asc,
  });

  const [statusFilter, setStatusFilter] = useState('All');

  //   const filteredData =
  //     statusFilter === "All" ? data : data.filter((row) => row.status === statusFilter);

  const handleSort = (column: keyof DataType) => {
    const direction =
      sortConfig.key === column && sortConfig.direction === Direction.dec
        ? Direction.asc
        : Direction.dec;
    setSortConfig({ key: column, direction });
  };

  //   const sortedData = sortData(data, sortConfig.key, sortConfig.direction);

  const getSortIcon = useCallback(
    (value: string) => {
      if (sortConfig.key === value) return sortConfig.direction === Direction.asc ? ' 🔼' : ' 🔽';
      return '  ';
    },
    [sortConfig],
  );

  const getStatusColor = (status: string) => {
    if (status === 'Pending') return 'gray';
    if (status === 'Failed') return 'red';
    return 'green';
  };

  const rowData = useMemo(() => {
    const sortedData = sortData(data, sortConfig.key, sortConfig.direction);
    if (statusFilter !== 'All') {
      return sortedData.filter((row) => row.status === statusFilter);
    }
    return sortedData;
  }, [sortConfig, statusFilter]);

  return (
    <Fragment>
      <div className="mb-4 flex justify-start space-x-4">
        <label className="flex items-center space-x-2">
          <span>Show only: </span>
        </label>
        <label className="flex items-center space-x-2">
          <Radio
            id="all"
            name="status"
            value="All"
            checked={statusFilter === 'All'}
            onChange={() => setStatusFilter('All')}
          />
          <span>All</span>
        </label>

        <label className="flex items-center space-x-2">
          <Radio
            id="completed"
            name="status"
            value="Completed"
            checked={statusFilter === 'Completed'}
            onChange={() => setStatusFilter('Completed')}
          />
          <span>Completed</span>
        </label>

        <label className="flex items-center space-x-2">
          <Radio
            id="pending"
            name="status"
            value="Pending"
            checked={statusFilter === 'Pending'}
            onChange={() => setStatusFilter('Pending')}
          />
          <span className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100">Pending</span>
        </label>

        <label className="flex items-center space-x-2">
          <Radio
            id="failed"
            name="status"
            value="Failed"
            checked={statusFilter === 'Failed'}
            onChange={() => setStatusFilter('Failed')}
          />
          <span>Failed</span>
        </label>
      </div>

      <Table className="w-full text-left text-sm text-gray-500 dark:text-gray-400">
        <Table.Head className="bg-gray-50 text-xs uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400">
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('transaction')}>
            Transaction #{getSortIcon('transaction')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('location')}>
            Location
            {getSortIcon('location')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('customer')}>
            Customer
            {getSortIcon('customer')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('date')}>
            Date
            {getSortIcon('date')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('method')}>
            Method
            {getSortIcon('method')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('brand')}>
            Brand
            {getSortIcon('brand')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('last4')}>
            Last 4{getSortIcon('last4')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('amount')}>
            Amount
            {getSortIcon('amount')}
          </Table.HeadCell>
          <Table.HeadCell className="px-4 py-3" onClick={() => handleSort('status')}>
            Status
            {getSortIcon('status')}
          </Table.HeadCell>
        </Table.Head>
        <Table.Body>
          {rowData.map((item, index) => (
            <Table.Row
              key={index}
              className="border-b hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
            >
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.transaction}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.location}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.customer}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.date}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.method}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.brand}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.last4}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
                {item.amount}
              </Table.Cell>
              <Table.Cell className="whitespace-nowrap px-4 py-3">
                <span
                  className={`bg-${getStatusColor(item.status)}-100 text-${getStatusColor(item.status)}-800 mr-2 rounded px-2.5 py-0.5 text-xs font-medium dark:bg-${getStatusColor(item.status)}-900 dark:text-${getStatusColor(item.status)}-300`}
                >
                  {item.status}
                </span>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </Fragment>
  );
};
