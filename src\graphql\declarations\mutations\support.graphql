mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {
  createGroupSupportTicket(data: $data) {
    id
  }
}

mutation UpdateGroupSupportTicket(
  $where: GroupSupportTicketWhereUniqueInput!
  $data: GroupSupportTicketUpdateInput!
) {
  updateGroupSupportTicket(where: $where, data: $data) {
    id
    title
    category
    description
    status
    messagesCount
    createdAt
    lastMessageCreatedAt
  }
}
