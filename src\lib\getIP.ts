import { env } from 'next-runtime-env';

// Check if we should use mock mode
const shouldUseMockMode = () => {
  return env('NEXT_PUBLIC_MOCK_MODE') === 'true' || env('NEXT_PUBLIC_DEMO') === 'true';
};

export const getIP = async () => {
  if (shouldUseMockMode()) {
    // Return a mock IP address
    return '*************';
  }

  try {
    const resp = await fetch('https://api.ipify.org?format=json');
    const data = await resp.json();
    return data.ip;
  } catch (error) {
    console.warn('Failed to fetch IP address, using mock IP:', error);
    return '*************'; // Fallback to mock IP
  }
};
