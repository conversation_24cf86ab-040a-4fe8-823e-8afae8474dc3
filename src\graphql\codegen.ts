import { CodegenConfig } from '@graphql-codegen/cli';
import { config as dotenv } from 'dotenv';
import { env } from 'next-runtime-env';
dotenv({ path: '.env.local' }); // Updated to check .env.local
dotenv(); // Load default .env

// Disable GraphQL codegen when in mock mode
const shouldUseMockMode = () => {
  return env('NEXT_PUBLIC_MOCK_MODE') === 'true' || env('NEXT_PUBLIC_DEMO') === 'true';
};

const config: CodegenConfig = {
  // Use a dummy schema when in mock mode to prevent errors
  schema: shouldUseMockMode()
    ? 'type Query { dummy: String }'
    : (env('NEXT_PUBLIC_SERVER_URL') ?? '') + env('NEXT_PUBLIC_GRAPHQL_URL'),
  documents: ['./src/graphql/declarations/**/*.graphql', './src/graphql/declarations/**/*.ts'],
  ignoreNoDocuments: true, // for better experience with the watcher
  generates: shouldUseMockMode()
    ? {} // Don't generate anything in mock mode
    : {
        './src/graphql/generated/': {
          preset: 'client',
          presetConfig: {
            fragmentMasking: false, // HERE
          },
        },
      },
  verbose: !shouldUseMockMode(), // Reduce verbosity in mock mode
};

export default config;
