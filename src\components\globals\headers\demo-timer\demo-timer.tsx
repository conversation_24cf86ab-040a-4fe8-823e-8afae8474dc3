import { AUTHSTORE } from '@/lib/auth-storage';
import { Button, Modal } from 'flowbite-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { HiOutlineExclamationCircle } from 'react-icons/hi';

type DemoTimerProps = {
  userId: string | null;
};

export const DemoTimer = ({ userId }: DemoTimerProps) => {
  const TIMER_DURATION = 60 * 60 * 1000; // 60 minutes in milliseconds
  const router = useRouter();
  const [timeLeft, setTimeLeft] = useState(TIMER_DURATION);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [showEndDemoModal, setShowEndDemoModal] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    // Check if user is logged in (has auth token)
    const authToken = AUTHSTORE.get();
    if (authToken) {
      setIsLoggedIn(true);
      return; // Don't show timer if user is logged in
    }

    // Check if the timer is already running
    const startTime = localStorage.getItem(`startTimer-${userId}`);
    if (startTime) {
      const elapsedTime = Date.now() - parseInt(startTime, 10);
      if (elapsedTime < TIMER_DURATION) {
        setTimeLeft(TIMER_DURATION - elapsedTime);
        setIsTimerActive(true);
      } else {
        // Timer has expired
        setShowEndDemoModal(true);
        setIsTimerActive(false);
        // You can redirect or restrict access here
      }
    }

    // Set up the interval to update the timer
    const interval = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1000) {
          clearInterval(interval);
          // localStorage.removeItem(`startTimer-${userId}`); // Clear the timer
          setShowEndDemoModal(true);
          return 0;
        }
        return prevTime - 1000; // Decrease by 1 second
      });
    }, 1000);

    return () => clearInterval(interval); // Cleanup on unmount
  }, [userId]);

  const startTimer = () => {
    const startTime = Date.now();
    localStorage.setItem(`startTimer-${userId}`, startTime.toString());
    setIsTimerActive(true);
  };

  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };
  const handleEndDemoAction = () => {
    router.push('https://app.ngnair.com/register');
    AUTHSTORE.clear();
  };

  return (
    <>
      {userId && !isLoggedIn && (
        <>
          <div className="mr-4 flex flex-col items-center justify-center rounded-lg border border-red-500 bg-white px-10 shadow-md">
            <h1 className="text-center">Demo will end</h1>
            {isTimerActive && (
              <h2 className="text-center font-bold text-red-500">{formatTime(timeLeft)}</h2>
            )}
          </div>
          {!isTimerActive && (
            <Modal show={!isTimerActive} size="md" popup>
              {/* <Modal.Header /> */}
              <Modal.Body>
                <div className="text-center">
                  <HiOutlineExclamationCircle
                    color="blue"
                    className="mx-auto mb-4 mt-4 h-14 w-14 text-gray-400 dark:text-gray-200"
                  />
                  <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                    Welcome to your demo account. You have 60 minutes to experience NGnair Payments.
                  </h3>
                  <div className="flex justify-center">
                    <Button color="blue" onClick={startTimer}>
                      Start Demo
                    </Button>
                  </div>
                </div>
              </Modal.Body>
            </Modal>
          )}
          <Modal show={showEndDemoModal} size="md" popup>
            {/* <Modal.Header /> */}
            <Modal.Body>
              <div className="text-center">
                <HiOutlineExclamationCircle
                  color="blue"
                  className="mx-auto mb-4 mt-4 h-14 w-14 text-gray-400 dark:text-gray-200"
                />
                <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                  Your timer has expired. Access is restricted.
                </h3>
                <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                  Sign up and create real account
                </h3>
                <div className="flex justify-center">
                  <Button color="blue" onClick={handleEndDemoAction}>
                    Sign up.
                  </Button>
                </div>
              </div>
            </Modal.Body>
          </Modal>
        </>
      )}
    </>
  );
};
