'use client';

import { useState } from 'react';

import { getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/search-input';
import { Filter, Plus, Search } from '@/assets/svg/navigation';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { useDashboardColumn } from './components/dashboardColumns';
import MerchantForm from './components/merchantForm';
import { useQuery } from '@apollo/client';
import { getGroups } from '@/graphql/declarations/group';
import DefaultTable from '@/components/globals/Tables';
import Loading from '../groups/components/loading';
import { X } from 'lucide-react';

const DashboardPage = () => {
  const { data, loading } = useQuery(getGroups, {
    variables: {
      where: {
        mainProcessor: {
          equals: 'aur',
        },
      },
      skip: 0,
      take: 10,
    },
    notifyOnNetworkStatusChange: true,
  });

  console.log(data?.groups);
  const [searchQuery, setSearchQuery] = useState('');
  const pageIndex = 0;
  const pageSize = 10;
  const [selectedRow, setSelectedRow] = useState(null);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [formOpen, setFormOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [actionData, setActionData] = useState(null);
  const [formType, setFormType] = useState('');
  const [playOpen, setPlayOpen] = useState(false);
  const [pauseOpen, setPauseOpen] = useState(false);

  const togglePlay = () => {
    setPlayOpen(!playOpen);
  };

  const handleRowClick = (rowData: any) => {
    setSelectedRowData(rowData);
    setSelectedRow(rowData);
    setIsDialogOpen(true);
  };

  const columns = useDashboardColumn(togglePlay, setPauseOpen, setActionData);

  const table = useReactTable({
    data: data?.groups ?? [],
    columns,
    state: {
      pagination: {
        pageIndex,
        pageSize,
      },
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const onSearchChange = () => {};
  return (
    <div className="w-auto min-w-[1400px]">
      <div className="flex h-[90px] items-center justify-between gap-2 p-2 md:gap-4 md:py-6">
        <div className="flex w-full items-center justify-start gap-3 md:gap-6">
          <Input
            icon={Search}
            placeholder="Search for merchant name, id, status etc."
            className="flex h-9 w-3/4 items-center justify-start gap-1 rounded-lg border border-gray-400 bg-white px-2 py-1.5 text-xs text-neutral-500 2xl:h-auto 2xl:w-4/6 2xl:px-4 2xl:py-2 2xl:text-sm"
            onChange={onSearchChange}
          />

          <Popover>
            <PopoverTrigger className="text-primary border-primary flex h-9 items-center justify-center gap-2 rounded-lg border bg-white px-2 py-1 text-xs font-bold sm:text-sm 2xl:px-4 2xl:py-2">
              <Filter />
              Filter
            </PopoverTrigger>
            <PopoverContent
              side="right"
              align="start"
              className="flex h-[480px] w-96 flex-col items-center justify-start overflow-y-auto rounded-lg bg-white p-0"
            >
              {/* <FilterSearch /> */}
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex w-full items-center justify-end gap-6">
          <Button
            onClick={() => setIsPopoverOpen(true)}
            variant="primary"
            className="bg-primary border-primary flex h-9 max-h-10 items-center justify-center gap-2 rounded-lg border px-2 py-1 2xl:px-4 2xl:py-2"
          >
            <Plus />
            Create
          </Button>
          <Dialog open={isPopoverOpen}>
            <DialogTrigger asChild></DialogTrigger>
            <DialogContent className="h-fit w-full">
              <DialogPrimitive.Close
                onClick={() => setIsPopoverOpen(false)}
                className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 data-[state=open]:text-gray-500 dark:ring-offset-gray-950 dark:focus:ring-gray-300 dark:data-[state=open]:bg-gray-800 dark:data-[state=open]:text-gray-400"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </DialogPrimitive.Close>
              <MerchantForm setOpen={setIsPopoverOpen} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {loading ? (
        <Loading />
      ) : (
        <DefaultTable body={table} head={columns} clickRow={handleRowClick} />
      )}
    </div>
  );
};

export default DashboardPage;
