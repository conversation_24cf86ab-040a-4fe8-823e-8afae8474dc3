// 'use client';

// import { useEffect, useRef, useState } from 'react';
// // @ts-ignore
// import SanitizedHTML from 'react-sanitized-html';

// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Label } from '@/components/ui/label';
// import { Separator } from '@/components/ui/separator';
// import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
// import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';
// import { apolloClient } from '@/lib/graphql/ApolloClient';
// import { GetPaymentPageData, SubmitPaymentPageData } from '@/graphql/declarations/payments';
// import FLPayTokenizerComponent, {
//   FLPayTokenizerComponentConfigSample,
// } from '@/components/payments/flp/tokenizer';
// import { toast } from 'react-toastify';

// const samplePaymentData = {
//   type: 'payment_initiate_props',
//   publishableKey: '6dcf66ee-b9aa-4733-a1e6-97e6578a793e',
//   amount: 50,
//   currency: 'usd',
//   mode: 'payment',
//   name: '',
//   description: '',
//   image: '',
//   contact: {
//     id: 'COvc9jkx5eYgle5QQNyp',
//     name: 'asdas dasdsa',
//     email: '<EMAIL>',
//     contact: '+63 945 135 4875',
//   },
//   orderId: '',
//   transactionId: '66c6e520b1e8e1de1c682ac5',
//   locationId: 'y3Jx2H0D0STHrcQYjlKQ',
// } as any;

// interface PaymentInitiateProps {
//   type: 'payment_initiate_props';
//   publishableKey: string; // Publishable key sent while connecting integration API
//   amount: number; // Amount in decimal currency with max 2 decimal places
//   currency: string; // Standard 3 letter notation for currencies ex. USD, INR
//   mode: string; // Payment mode: subscription/payment
//   productDetails: { productId: string; priceId: string }; // productId and priceId for recurring products. More details can be fetched using the public api for Products/Prices
//   contact?: {
//     // Customer details for customer placing the order
//     id: string; // Customer id in GHL
//     name: string; // Full name of the customer
//     email: string;
//     contact: string; // Contact number of customer with country code
//   };
//   orderId: string; // GHL internal orderId for given order
//   transactionId: string; // GHL internal transactionId for the given transaction
//   subscriptionId: string; // GHL internal subscriptionId passed in case of a recurring product
//   locationId: string; // Sub-account id for which the given order is created.
// }

// export default function Component() {
//   const [isOrderSummaryOpen, setIsOrderSummaryOpen] = useState(true);
//   const [isPaymentDetailsOpen, setIsPaymentDetailsOpen] = useState(true);

//   const [paymentData, setPaymentData] = useState<PaymentInitiateProps | null>(null);

//   const [calculations, setCalculations] = useState({
//     subtotal: 0,
//     discount: 0,
//     total: 0,
//   });

//   const [lineItems, setLineItems] = useState<
//     {
//       name: string;
//       price: number;
//       quantity: number;
//       description: string;
//       total: number;
//     }[]
//   >([]);

//   const [userInfo, setUserInfo] = useState<{
//     name: string;
//     email: string;
//     city: string;
//     state: string;
//     postal_code: string;
//     country: string;
//     phone: string;
//   } | null>(null);

//   const [payAPIKey, setPayAPIKey] = useState('');
//   const [liveMode, setLiveMode] = useState(false);

//   async function loadInvoiceData() {
//     if (!paymentData) return;

//     const d = await apolloClient.query({
//       query: GetPaymentPageData,
//       variables: {
//         input: {
//           apiKey: paymentData?.publishableKey,
//           transactionID: paymentData?.transactionId,
//         },
//       },
//     });

//     let data = d.data.ghl_api_getPaymentPageData?.data;
//     if (!data) return;

//     setCalculations({
//       total: data.amount / 100,
//       discount: (data.amountSummary?.discount ?? 0) / 100,
//       subtotal: (data.amountSummary?.subtotal ?? 0) / 100,
//     });

//     setLineItems(
//       data.lineItems?.map((item: any, i: number) => ({
//         name: item?.name ?? `Item ${i + 1}`,
//         price: (item?.price ?? 0) / 100,
//         quantity: item?.quantity ?? 1,
//         total: (item?.total ?? 0) / 100,
//         description: item?.description ?? '',
//       })) ?? [],
//     );

//     setUserInfo({
//       city: data.customerData?.city ?? '',
//       country: data.customerData?.country ?? '',
//       email: data.customerData?.email ?? '',
//       name: data.customerData?.name ?? '',
//       phone: data.customerData?.phone ?? '',
//       postal_code: data.customerData?.postal_code ?? '',
//       state: data.customerData?.state ?? '',
//     });

//     setPayAPIKey(data.apiKey);
//     setLiveMode(data.liveMode);
//   }
//   const tokenizerRef = useRef<any>(null);

//   useEffect(() => {
//     loadInvoiceData();
//   }, [paymentData]);

//   const isReady = paymentData !== null;

//   const [payToken, setPayToken] = useState<string | false>(false);

//   async function postTransactionsFlag(args: { type: string; message?: string }) {
//     switch (args.type) {
//       case 'success': {
//         window.parent.postMessage(
//           JSON.stringify({
//             type: 'custom_element_success_response',
//             chargeId: args.message || '',
//           }),
//           '*',
//         );
//         break;
//       }
//       case 'error': {
//         window.parent.postMessage(
//           JSON.stringify({
//             type: 'custom_element_error_response',
//             error: {
//               description: args.message || '',
//             },
//           }),
//           '*',
//         );
//         break;
//       }
//       case 'cancel': {
//         window.parent.postMessage(
//           JSON.stringify({
//             type: 'custom_element_close_response',
//           }),
//           '*',
//         );
//         break;
//       }
//     }
//   }

//   async function providerReadyFlag() {
//     const key = await new Promise((resolve) => {
//       window.parent.postMessage(
//         JSON.stringify({
//           type: 'custom_provider_ready',
//           loaded: true,
//         }),
//         '*',
//       );
//     });
//   }

//   function processEvents(rawdata: any) {
//     try {
//       const data = JSON.parse(rawdata);
//       const dType = data.type;
//       if (!dType) {
//         console.log('No type found in data');
//         return;
//       }

//       switch (dType) {
//         case 'payment_initiate_props': {
//           let d = data as PaymentInitiateProps;
//           setPaymentData(d);
//           break;
//         }
//       }
//     } catch (e) {
//       console.error('Error in parsing data', e);
//     }
//   }

//   useEffect(() => {
//     providerReadyFlag();
//     window.addEventListener('message', ({ data }) => {
//       processEvents(data);
//     });
//   }, []);

//   const getIP = async () => {
//     const resp = await fetch('https://api.ipify.org?format=json');
//     const data = await resp.json();
//     return data.ip;
//   };

//   const submitPayment = async () => {
//     if (!paymentData || !payToken) return;
//     let ip = await getIP();
//     try {
//       const resp = await apolloClient.mutate({
//         mutation: SubmitPaymentPageData,
//         variables: {
//           input: {
//             apiKey: paymentData?.publishableKey,
//             transactionID: paymentData?.transactionId,
//             paymentToken: payToken,
//             ipAddress: ip,
//           },
//         },
//       });
//       toast.success('Payment Successful');

//       let txID = resp.data?.ghl_api_submitPayment?.txID;

//       if (!txID) {
//         throw new Error('Payment Failed');
//       }

//       postTransactionsFlag({
//         type: 'success',
//         message: txID,
//       });
//     } catch (error) {
//       console.error(error);
//       // toast.error("Payment Failed");
//       // postTransactionsFlag({ type: "error", message: error?.message });
//     }
//   };

//   return (
//     <div className="container mx-auto max-w-screen-lg p-4 md:p-6">
//       <img src="/logo.webp" className="mb-4 mr-3 h-9" alt="Flowbite React Logo" />
//       {!isReady && <div>Loading</div>}
//       {isReady && (
//         <div className="grid gap-6">
//           <Card>
//             <CardHeader>
//               <CardTitle>Payment Information</CardTitle>
//             </CardHeader>
//             <CardContent>
//               {!payToken && (
//                 <div>
//                   <div id="fpayContainer" className="p-4"></div>
//                   {payAPIKey && payAPIKey.length > 1 && (
//                     <FLPayTokenizerComponent
//                       apiKey={payAPIKey}
//                       liveMode={liveMode}
//                       containerSelector="#fpayContainer"
//                       tokenizerConfig={FLPayTokenizerComponentConfigSample}
//                       tokenizerRef={tokenizerRef}
//                       onSubmission={(resp) => {
//                         if (resp.status === 'success') {
//                           setPayToken(resp.token);
//                         }
//                       }}
//                     />
//                   )}
//                 </div>
//               )}

//               {payToken && (
//                 <div className="flex flex-col">
//                   <Button className="w-full" variant="primary" onClick={submitPayment}>
//                     Pay ${calculations.total.toFixed(2)}
//                   </Button>
//                   <Button className="w-full" variant="secondary">
//                     Re-input Payment Details
//                   </Button>
//                 </div>
//               )}
//               {!payToken && (
//                 <Button
//                   className="w-full"
//                   variant="primary"
//                   onClick={() => {
//                     tokenizerRef.current?.submit();
//                   }}
//                 >
//                   Proceed to Payment
//                 </Button>
//               )}
//             </CardContent>
//           </Card>

//           <div className="grid gap-6 md:grid-cols-2">
//             <Collapsible
//               open={isOrderSummaryOpen}
//               onOpenChange={setIsOrderSummaryOpen}
//               className="space-y-2"
//             >
//               <Card>
//                 <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                   <CardTitle>Order Summary</CardTitle>
//                   <CollapsibleTrigger asChild>
//                     <Button variant="ghost" size="sm" className="w-9 p-0">
//                       {isOrderSummaryOpen ? (
//                         <ChevronUpIcon className="h-4 w-4" />
//                       ) : (
//                         <ChevronDownIcon className="h-4 w-4" />
//                       )}
//                       <span className="sr-only">Toggle Order Summary</span>
//                     </Button>
//                   </CollapsibleTrigger>
//                 </CardHeader>
//                 <CollapsibleContent>
//                   <CardContent className="space-y-4">
//                     {lineItems.map((item, index) => (
//                       <div key={index} className="flex justify-between">
//                         <div className="text-sm">
//                           <p>
//                             <span className="font-semibold">{item.name}</span>{' '}
//                             <span className="text-xs">x {item.quantity}</span>
//                           </p>
//                           <SanitizedHTML
//                             allowedAttributes={{ a: ['href'] }}
//                             allowedTags={['a']}
//                             html={item.description}
//                           />
//                         </div>
//                         <span>${item.total.toFixed(2)}</span>
//                       </div>
//                     ))}
//                     <Separator />
//                     <div className="flex justify-between">
//                       <span>Subtotal</span>
//                       <span>${calculations.subtotal.toFixed(2)}</span>
//                     </div>
//                     {calculations.discount > 0 && (
//                       <div className="flex justify-between text-green-600">
//                         <span>Discount</span>
//                         <span>-${calculations.discount.toFixed(2)}</span>
//                       </div>
//                     )}
//                     <Separator />
//                     <div className="flex justify-between font-bold">
//                       <span>Total</span>
//                       <span>${calculations.total.toFixed(2)}</span>
//                     </div>
//                   </CardContent>
//                 </CollapsibleContent>
//               </Card>
//             </Collapsible>

//             <Collapsible
//               open={isPaymentDetailsOpen}
//               onOpenChange={setIsPaymentDetailsOpen}
//               className="space-y-2"
//             >
//               <Card>
//                 <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
//                   <CardTitle>Payment Details</CardTitle>
//                   <CollapsibleTrigger asChild>
//                     <Button variant="ghost" size="sm" className="w-9 p-0">
//                       {isPaymentDetailsOpen ? (
//                         <ChevronUpIcon className="h-4 w-4" />
//                       ) : (
//                         <ChevronDownIcon className="h-4 w-4" />
//                       )}
//                       <span className="sr-only">Toggle Payment Details</span>
//                     </Button>
//                   </CollapsibleTrigger>
//                 </CardHeader>
//                 <CollapsibleContent>
//                   <CardContent className="space-y-4">
//                     <div>
//                       <Label htmlFor="name">Name</Label>
//                       <p>{userInfo?.name}</p>
//                     </div>
//                     <div>
//                       <Label htmlFor="email">Email</Label>
//                       <p>{userInfo?.email}</p>
//                     </div>
//                     <div>
//                       <Label htmlFor="city">City</Label>
//                       <p>{userInfo?.city}</p>
//                     </div>
//                     <div>
//                       <Label htmlFor="state">State</Label>
//                       <p>{userInfo?.state}</p>
//                     </div>
//                     <div>
//                       <Label htmlFor="postal_code">Postal Code</Label>
//                       <p>{userInfo?.postal_code}</p>
//                     </div>
//                     <div>
//                       <Label htmlFor="country">Country</Label>
//                       <p>{userInfo?.country}</p>
//                     </div>
//                     <div>
//                       <Label htmlFor="phone">Phone</Label>
//                       <p>{userInfo?.phone}</p>
//                     </div>
//                   </CardContent>
//                 </CollapsibleContent>
//               </Card>
//             </Collapsible>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }

export default function Page() {
  return <div></div>;
}
