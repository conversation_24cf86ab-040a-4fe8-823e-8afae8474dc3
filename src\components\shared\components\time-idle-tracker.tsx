import { AUTHSTORE } from '@/lib/auth-storage';
import { Button, Modal } from 'flowbite-react';
import { useRouter } from 'next/navigation';
import { useState, useEffect, useRef } from 'react';
import { HiOutlineExclamationCircle } from 'react-icons/hi';

export const TimeIdleTracker = () => {
  const [openModal, setOpenModal] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const idleTimeRef = useRef(0);
  const modalRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  //TODO:  need to update below to 15 and 14. used 1 and .5 min for quick testing
  const idleLimit = 15 * 60; // 15 minutes
  const warningTime = 14 * 60; // 14 minutes

  const resetIdleTime = () => {
    idleTimeRef.current = 0; //
    if (openModal) setOpenModal(false);
  };
  const logoutUser = () => {
    router.replace('/login?idleSession=true');
    AUTHSTORE.clear();
  };

  const handleMouseClick = (event: MouseEvent) => {
    if (!openModal || (modalRef.current && !modalRef.current.contains(event.target as Node))) {
      resetIdleTime(); // Only reset if the click is outside the modal
    }
  };
  const handleKeyDown = () => {
    resetIdleTime();
  };

  useEffect(() => {
    const checkIdleTime = setInterval(() => {
      idleTimeRef.current++;

      if (idleTimeRef.current >= warningTime && idleTimeRef.current < idleLimit) {
        setOpenModal(true);
      } else if (idleTimeRef.current >= idleLimit) {
        logoutUser();
        resetIdleTime();
      }
      setCountdown(idleLimit - idleTimeRef.current);
    }, 1000);

    // Event listeners for user activity
    window.onload = resetIdleTime;
    document.addEventListener('click', handleMouseClick);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      clearInterval(checkIdleTime);
      // Clean up event listeners
      window.onload = null;
      document.removeEventListener('click', handleMouseClick);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [openModal]);

  return (
    <>
      <Modal show={openModal} ref={modalRef} size="md" onClose={() => setOpenModal(false)} popup>
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamationCircle
              color="blue"
              className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200"
            />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              You will be logged out soon if you stay idle.
              <br />
              <b>{countdown}</b> seconds remaining.
            </h3>
            <div className="flex justify-center gap-4">
              <Button color="failure" onClick={logoutUser}>
                Logout
              </Button>
              <Button color="blue" onClick={resetIdleTime}>
                Keep me Login
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};
