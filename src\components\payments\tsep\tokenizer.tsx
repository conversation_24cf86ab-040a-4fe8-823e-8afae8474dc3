'use client';

import { TsepManifest } from '@/graphql/declarations/tsep';
import { useMutation } from '@apollo/client';
import Script from 'next/script';
import React, { useEffect, useMemo, useState } from 'react';
import './tsep-tokenizer.scss';
import { FieldErrors, FormProvider, useForm } from 'react-hook-form';
import { PaymentIcon } from 'react-svg-credit-card-payment-icons';
import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { env } from 'next-runtime-env';

export const RootTsepCardFieldIDs = {
  cardHolderName: 'tsep-cardHolderName',
  cardNumber: 'tsep-cardNum',
  expiryDate: 'tsep-datepicker',
  cvv: 'tsep-cvv2',
  zipCode: 'tsep-zipCode',
};

export const BrandLogos = {
  V: <PaymentIcon type="Visa" format="flatRounded" width={40} />,
  M: <PaymentIcon type="Mastercard" format="flatRounded" width={40} />,
  X: <PaymentIcon type="Amex" format="flatRounded" width={40} />,
  J: <PaymentIcon type="Jcb" format="flatRounded" width={40} />,
  R: <PaymentIcon type="Diners" format="flatRounded" width={40} />,
  U: <PaymentIcon type="Discover" format="flatRounded" width={40} />,
};

export type TsepResponse = {
  responseCode: string;
  status: string;
  message: string;
  tsepToken: string;
  maskedCardNumber: string;
  cardType: string;
  transactionID: string;
  expirationDate: string;
  cvv2: string;
  cardHolderName: string;
  zipCode: string;
};

export type TsepErrorEvent = {
  fieldName: string;
  message: string;
  responseCode: string;
  status: string;
};

type FieldProps = {
  id: string;
  label: string;
  hasLoaded: boolean;
  name: string;
  validation: object;
  dataAttributes: { [key: string]: string };
  errors?: FieldErrors;
  extraView?: React.ReactNode;
};

const Field = ({ id, label, hasLoaded, dataAttributes, errors, extraView }: FieldProps) => {
  let fieldID = id.replace('Div', '');
  return (
    <>
      {hasLoaded && <label id={`${id}-label`}>{label}</label>}
      <div className="flex w-full items-center" id={`${id}-container`}>
        <div
          id={id}
          {...dataAttributes}
          className={cn('waitingField', dataAttributes.className)}
          style={{
            ...((dataAttributes.style ?? {}) as any),
            minHeight: '40px',
            position: 'relative',
          }}
        >
          <div
            className="absolute left-0 top-0 h-full w-full animate-pulse rounded-lg border border-gray-400 bg-gray-200"
            id={`${id}-loader`}
          ></div>
        </div>
        {extraView && <div id={`${id}-extraView`}>{extraView}</div>}
      </div>
      {errors?.[fieldID] && (
        <div className="text-sm text-red-500" id={`${id}-error`}>
          {errors[fieldID].message?.toString()}
        </div>
      )}
    </>
  );
};

export interface TSEPTokenizerComponentProps {
  onEvent?: (eventType: string, event: any) => void;
  onToken?: (event: TsepResponse) => void;
  onTokenError?: (error: TsepErrorEvent) => void;
  isReady?: boolean;
  setIsReady?: (isReady: boolean) => void;
  labels?: {
    cardNumber: string;
    expiryDate: string;
    cvv?: string;
    cardHolderName?: string;
    zipCode?: string;
  };
  allowOptionals?: {
    cardHolderName?: boolean;
    zipCode?: boolean;
    cvv?: boolean;
  };
  fieldErrors?: FieldErrors;
  cssString?: string;
}

function TSEPTokenizerComponent(props: TSEPTokenizerComponentProps) {
  const [fetch, { data: manifestData, loading: manifestLoading }] = useMutation(TsepManifest, {
    variables: {},
  });

  const [hasLoaded, setHasLoaded] = useState(false);

  const [allFieldsCheck, setAllFieldsCheck] = useState({});

  const [brand, setBrand] = useState('');

  const TsepCardFieldIDs = useMemo(() => {
    const base = { ...RootTsepCardFieldIDs };
    if (!props.allowOptionals?.cardHolderName && base.cardHolderName) {
      // @ts-ignore
      delete base.cardHolderName;
    }
    if (!props.allowOptionals?.zipCode && base.zipCode) {
      // @ts-ignore
      delete base.zipCode;
    }
    if (!props.allowOptionals?.cvv && base.cvv) {
      // @ts-ignore
      delete base.cvv;
    }

    return base;
  }, [props.allowOptionals]);

  useEffect(() => {
    fetch();
  }, []);

  const checkHasLoaded = () => {
    const cardNumDiv = document.getElementById('tsep-cardNumDiv');
    if (cardNumDiv && cardNumDiv.children.length === 0) {
      setTimeout(checkHasLoaded, 500);
    }
    setTimeout(() => {
      setHasLoaded(true);
    }, 1000);
  };

  useEffect(() => {
    checkHasLoaded();
  }, [manifestData]);

  const methods = useForm({});

  async function getBrand(args: { data: string }) {
    setBrand(args.data);
  }

  const [errors, setErrors] = useState<FieldErrors>({});

  async function clearError(args: { id: string }) {
    const input = document.getElementById(args.id);
    if (input) {
      input.classList.remove('!border-red-500');
    }

    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[args.id];
      return newErrors;
    });
  }

  const [cardToken, setCardToken] = useState<string | null>(null);

  async function setError(args: { id: string; message: string }) {
    // const allInputIds = Object.values(TsepCardFieldIDs);
    // // set all to not have the border-red-500 color
    // allInputIds.forEach((id) => {
    //   const input = document.getElementById(id);
    //   if (input) {
    //     input.classList.remove('!border-red-500');
    //   }
    // });

    // clear all errors
    setErrors({});

    // const input = document.getElementById(args.id);
    // if (input) {
    //   input.classList.add('!border-red-500');
    // }

    // console.log('setError', args);

    setErrors((prev) => ({
      ...prev,
      [args.id]: {
        message: args.message,
        type: 'manual',
      },
    }));
  }

  let manifest = manifestData?.tsep_manifest;

  const tsepURL = manifest?.url;

  const handleSubmit = () => {
    // console.log(data);
  };

  // const validate = (args: { errorMessage?: boolean }) => {
  //   // check if all fields are filled
  //   const allInputIds = Object.values(TsepCardFieldIDs);
  //   let isValid = true;
  //   allInputIds.forEach((id) => {
  //     const input = document.getElementById(id);
  //     if (input) {
  //       // check if the input is empty
  //       if (!input.innerHTML) {
  //         isValid = false;
  //         if (args.errorMessage) {
  //           setError({
  //             id: id,
  //             message: 'Field is required',
  //           });
  //         }
  //       }
  //     }
  //   });
  // };

  function cardTokenReady() {
    // console.log('cardToken', cardToken);
    return cardToken !== null && cardToken !== undefined && cardToken !== '';
  }

  function allGoodCounterCheck() {
    // console.log('allGoodCounterCheck', allGoodCounter === Object.keys(TsepCardFieldIDs).length);
    return allGoodCounter === Object.keys(TsepCardFieldIDs).length;
  }

  const allGoodCounter = useMemo(() => {
    return Object.values(allFieldsCheck).filter((x) => x).length;
  }, [allFieldsCheck]);

  if (!manifestData || manifestLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoaderSquares />
      </div>
    );
  }

  return (
    <>
      <Script
        src={`${tsepURL}/transit-tsep-web/jsView/${manifest?.deviceID}?${manifest?.manifestKey}`}
        // load every time this component is loaded

        onLoad={() => {
          console.log('TSEP Loaded');
          function tsepHandler(eventType, event) {
            props.onEvent?.(eventType, event);

            switch (eventType) {
              case 'TokenEvent':
                if (event.status === 'FAILURE') {
                  props.onTokenError?.(event);
                  setCardToken(null);
                  setError({
                    id: TsepCardFieldIDs.cardNumber,
                    message: 'Invalid card details provided. Check Number or CVV',
                  });
                  setAllFieldsCheck((prev) => {
                    return { ...prev, [TsepCardFieldIDs.cardNumber]: false };
                  });
                } else {
                  props.onToken?.(event);
                  setAllFieldsCheck((prev) => {
                    return { ...prev, [TsepCardFieldIDs.cardNumber]: true };
                  });
                  clearError({
                    id: TsepCardFieldIDs.cardNumber,
                  });
                  setCardToken(event.tsepToken);
                }
                break;
              case 'FieldValidationErrorEvent':
                // console.log('xxFieldValidationErrorEvent', event);
                setError({
                  id: event['fieldName'],
                  message: event['message'],
                });
                setAllFieldsCheck((prev) => {
                  return { ...prev, [event.fieldName]: false };
                });
                props.onTokenError?.(event);
                break;
              case 'FocusOnEvent':
                // console.log('clearing error');
                clearError({
                  id: event['fieldName'],
                });
                break;
              case 'CardTypeEvent':
                // console.log('xxCardTypeEvent', event);
                getBrand({
                  data: event.cardType,
                });
                break;
              case 'FieldValidationSuccessEvent':
                // console.log('success', event.fieldName);
                setAllFieldsCheck((prev) => {
                  return { ...prev, [event.fieldName]: true };
                });
                break;
              case 'FieldValidationBlankEvent':
                // console.log('blank', event.fieldName);
                setError({
                  id: event['fieldName'],
                  message: event['message'],
                });
                setAllFieldsCheck((prev) => {
                  return { ...prev, [event.fieldName]: false };
                });
                if (event.fieldName === 'tsep-cardNum') {
                  setBrand('');
                }
                break;
            }
          }

          // @ts-ignore
          window.tsepHandler = tsepHandler;
        }}
      ></Script>

      <FormProvider {...methods}>
        <div
          className="w-full overflow-hidden"
          id="payment-form"
          onSubmit={methods.handleSubmit(handleSubmit)}
        >
          <Field
            id="tsep-cardNumDiv"
            label={props.labels?.cardNumber || ''}
            hasLoaded={hasLoaded}
            name="cardNumber"
            validation={{ required: 'Field is required' }}
            dataAttributes={{
              'data-auto-formatting': 'Y',
              'data-validate-cc': 'Y',
              'data-detect-card-type': 'Y',
            }}
            errors={{ ...props.fieldErrors, ...errors }}
            extraView={
              brand && (
                <div className="ml-2 pb-2">
                  {/* {brand && (
                  <img
                    src={`/images/cards/${brand.toLowerCase()}.png`}
                    alt={brand}
                    className="w-10 h-6"
                  />
                )} */}

                  {/* <div className="h-5 w-5 bg-green-600"></div> */}
                  {brand && BrandLogos[brand]}
                </div>
              )
            }
          />
          <div className="flex w-full gap-2" id="expiry-cvv-container">
            <div className="flex-1" id="expiry-container">
              <Field
                id="tsep-datepickerDiv"
                label={props.labels?.expiryDate || ''}
                hasLoaded={hasLoaded}
                name="expiryDate"
                validation={{ required: 'Field is required' }}
                dataAttributes={{ 'data-validate-expiry-date': 'Y' }}
                errors={{ ...props.fieldErrors, ...errors }}
              />
            </div>
            {props.allowOptionals?.cvv && (
              <div className="flex-1" id="cvv-container">
                <Field
                  id="tsep-cvv2Div"
                  label={props.labels?.cvv || ''}
                  hasLoaded={hasLoaded}
                  name="cvv"
                  validation={{ required: 'Field is required' }}
                  dataAttributes={{ 'data-validate-cvv2': 'Y' }}
                  errors={{ ...props.fieldErrors, ...errors }}
                />
              </div>
            )}
          </div>
          {props.allowOptionals?.cardHolderName && (
            <Field
              id="tsep-cardHolderNameDiv"
              label={props.labels?.cardHolderName || ''}
              hasLoaded={hasLoaded}
              name="cardHolderName"
              validation={{ required: 'Field is required' }}
              dataAttributes={{ 'data-validate-name': 'Y' }}
              errors={{ ...props.fieldErrors, ...errors }}
            />
          )}
          {props.allowOptionals?.zipCode && (
            <Field
              id="tsep-zipCodeDiv"
              label={props.labels?.zipCode || ''}
              hasLoaded={hasLoaded}
              name="zipCode"
              validation={{ required: 'Field is required' }}
              dataAttributes={{ 'data-validate-zipcode': 'N' }}
              errors={{ ...props.fieldErrors, ...errors }}
            />
          )}
        </div>
      </FormProvider>
      {allGoodCounterCheck() ? (
        cardTokenReady() ? (
          <p className="mb-1 ml-auto mt-4 text-right text-green-500" id="status-ready">
            Ready!
          </p>
        ) : (
          <p className="mb-1 ml-auto mt-4 text-right text-yellow-500" id="status-loading">
            Loading...
          </p>
        )
      ) : (
        <p className="mb-1 ml-auto mt-4 text-right text-red-500" id="status-fill-fields">
          Fill in all fields
        </p>
      )}
      <div className="progress-bar" id="progress-bar">
        <div
          className="progress-bar-fill"
          id="progress-bar-fill"
          style={{ width: `${(allGoodCounter / Object.keys(TsepCardFieldIDs).length) * 100}%` }}
        ></div>
      </div>
    </>
  );
}

export interface TSEPHostedTokenizerComponentProps {
  onEvent?: (eventType: string, event: any) => void;
  onToken?: (event: TsepResponse) => void;
  onTokenError?: (error: TsepErrorEvent) => void;
  labels?: {
    cardNumber: string;
    expiryDate: string;
    cvv?: string;
    cardHolderName?: string;
    zipCode?: string;
  };
  allowOptionals?: {
    cardHolderName?: boolean;
    zipCode?: boolean;
    cvv?: boolean;
  };
  cssString?: string;
  parentComponentAttributes?: { [key: string]: any };
  iframeComponentAttributes?: { [key: string]: any };
}

function TSEPHostedTokenizerComponent(props: TSEPHostedTokenizerComponentProps) {
  const registerListener = () => {
    // listen for token from child component
    window.addEventListener('message', (event) => {
      // console.log(event.data);
      const eventType = event.data.type;
      const data = event.data.data;

      switch (eventType) {
        case 'event': {
          props.onEvent?.(data.event, data.data);
          break;
        }
        case 'token': {
          props.onToken?.(data);
          break;
        }
        case 'error': {
          props.onTokenError?.(data);
          break;
        }
      }
    });
  };

  useEffect(() => {
    registerListener();
  }, []);

  const [hash, setHash] = useState('');

  const setNewRandomHash = (e) => {
    e.preventDefault();
    // console.log('new hash');
    setHash(Math.random().toString(36).substring(7));
  };

  // create a url param safe string of the settings object
  const settingsString = JSON.stringify({
    labels: props.labels,
    allowOptionals: props.allowOptionals,
  });

  const cssString = props.cssString || '';

  const iframeURL = useMemo(() => {
    const baseURL = env('NEXT_PUBLIC_TSEP_HOSTED_URL');
    if (!baseURL) {
      return '';
    }
    const url = new URL(baseURL);
    url.searchParams.append('hash', hash);
    url.searchParams.append('settings', settingsString);
    url.searchParams.append('css', cssString);
    return url.toString();
  }, [hash, settingsString, cssString]);

  return (
    <div {...(props.parentComponentAttributes ?? {})}>
      <iframe
        src={iframeURL}
        width={props.iframeComponentAttributes?.width ?? '100%'}
        height={props.iframeComponentAttributes?.height ?? '400px'}
        {...(props.iframeComponentAttributes ?? {})}
      />
      {/* <br /> */}
      <Button
        size="sm"
        type="button"
        variant="outline"
        className="text-red-500 hover:bg-red-50 hover:text-red-600"
        onClick={setNewRandomHash}
      >
        Reset Payment Form
      </Button>
    </div>
  );
}

export default TSEPTokenizerComponent;

export { TSEPHostedTokenizerComponent };
