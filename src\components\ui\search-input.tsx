'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';

import { Search2 } from '@/assets/svg/navigation';

const Input = React.forwardRef(({ className, icon, type, ...props }: any, ref) => {
  return (
    <div
      className={cn(
        'focus-within:text-primary flex items-center rounded-lg border border-input bg-white py-2 pl-4 text-sm ring-offset-background focus-within:ring-1',
        className,
      )}
    >
      <Search2 className="w-5" />
      <input
        {...props}
        type="search"
        ref={ref}
        className="w-full border-none placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
      />
    </div>
  );
});
Input.displayName = 'Input';

export { Input };
