'use client';

import { useLocationSelector } from '@/components/hooks';
import { message } from '@/components/shared/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Me } from '@/graphql/declarations/me';
import {
  Gateway_CreatePayLinkInputDataFormPricingTaxType,
  Paylink_CreateDocument,
} from '@/graphql/generated/graphql';
import { useMutation, useQuery } from '@apollo/client';
import { Plus, Trash2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRef, useState } from 'react';
import { toast } from 'react-toastify';
import PaymentLinkSuccess from './_components/payment-link-success';

interface Product {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

export default function CreatePaymentLink() {
  const searchParams = useSearchParams();

  const [checkingAuth, setCheckingAuth] = useState(true);
  const retries = useRef(0);
  const router = useRouter();
  const { data: meData, loading: meLoading } = useQuery(Me, { fetchPolicy: 'no-cache' });

  // const checkAuth = async () => {
  //   if (meLoading) {
  //     return;
  //   }

  //   if (meData?.authenticatedItem?.id) {
  //     setCheckingAuth(false);
  //     return;
  //   } else {
  //     if (retries.current < 3) {
  //       retries.current += 1;

  //       setTimeout(async () => {
  //         checkAuth();
  //       }, 2000);
  //       return;
  //     } else {
  //       router.push('/login?redirect=/hubspot/create/paylinks');
  //     }
  //   }
  // };

  // useEffect(() => {
  //   checkAuth();
  // }, [meLoading]);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({
    onlyActive: true,
  });

  const productName = searchParams?.get('productName')!;
  const amount = searchParams?.get('amount')!;
  const id = searchParams?.get('id')!;

  const [products, setProducts] = useState<Product[]>([
    { id: id, name: productName, price: Number(amount), quantity: 2 },
  ]);

  const [newProduct, setNewProduct] = useState<Omit<Product, 'id'>>({
    name: '',
    price: 0,
    quantity: 1,
  });

  const [allowExtraDiscount, setAllowExtraDiscount] = useState(false);
  const [allowTip, setAllowTip] = useState(false);
  const [allowEdit, setAllowEdit] = useState(false);
  const [taxType, setTaxType] = useState<'fixed' | 'percentage'>('fixed');
  const [tax, setTax] = useState(0);

  const [isAddProductOpen, setIsAddProductOpen] = useState(false);

  const [paymentLink, setPaymentLink] = useState('');

  const [createLinkMutation, { loading: createProductLoading, error }] = useMutation(
    Paylink_CreateDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Payment link'));
        setProducts([]);
        setNewProduct({ name: '', price: 0, quantity: 1 });
        setAllowExtraDiscount(false);
        setAllowTip(false);
        setAllowEdit(false);
        setTaxType('fixed');
        setTax(0);
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Payment link', error.message));
      },
    },
  );

  const handleQuantityChange = (id: string, newQuantity: number) => {
    setProducts(
      products.map((product) =>
        product.id === id ? { ...product, quantity: Math.max(1, newQuantity) } : product,
      ),
    );
  };

  const handlePriceChange = (id: string, newPrice: number) => {
    setProducts(
      products.map((product) =>
        product.id === id ? { ...product, price: Math.max(0, newPrice) } : product,
      ),
    );
  };

  const handleDeleteProduct = (id: string) => {
    setProducts(products.filter((product) => product.id !== id));
  };

  const handleAddProduct = (e: React.FormEvent) => {
    e.preventDefault();
    const id = Math.floor(1000000000 + Math.random() * 9000000000).toString();
    setProducts([...products, { ...newProduct, id }]);
    setNewProduct({ name: '', price: 0, quantity: 1 });
    setIsAddProductOpen(false);
  };

  const handleCreatePaymentLink = async () => {
    const result = await createLinkMutation({
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            autoToken: true,
            save: true,
            form: {
              allowExtraDiscount: allowExtraDiscount,
              allowTip: allowTip,
              allowEdit: allowEdit,
              pricing: {
                taxType: taxType as Gateway_CreatePayLinkInputDataFormPricingTaxType,
                tax: tax,
                lineItems: products.map((item) => {
                  return {
                    productId: item.id,
                    amount: item.price * item.quantity,
                  };
                }),
              },
            },
          },
        },
      },
    });

    const { data } = result;

    setPaymentLink(
      `https://app.ngnair.com/pay?paymentDataID=${data?.gateway_createPayLink?.payLinkID}&groupID=${locationFilter?.id}`,
    );
  };

  if (!productName || !amount || !id) {
    return <div className="text-center">No product name, amount, or id provided</div>;
  }

  if (paymentLink) {
    return <PaymentLinkSuccess paymentLink={paymentLink} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="mx-auto max-w-3xl space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold tracking-tight">Add PayLink</h1>
            <p className="text-sm text-muted-foreground">
              Create a new payment link for your customers
            </p>
          </div>
          <div>{locationSelectorElement}</div>
        </div>

        <Card>
          <CardHeader className="pb-4">
            <CardTitle>Product Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {products.length === 0 ? (
              <div className="rounded-lg border border-blue-100 bg-blue-50 p-4 text-center">
                <p className="text-sm font-medium text-blue-600">
                  Click "Add Product" to add a product
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product Name</TableHead>
                    <TableHead className="text-center">Price</TableHead>
                    <TableHead className="text-center">Quantity</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>{product.name}</TableCell>
                      <TableCell>
                        <div className="flex justify-center">
                          <Input
                            type="number"
                            value={product.price}
                            onChange={(e) =>
                              handlePriceChange(product.id, Number(e.target.value) || 0)
                            }
                            className="w-20 text-right"
                            min="0"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex justify-center">
                          <Input
                            type="number"
                            value={product.quantity}
                            onChange={(e) =>
                              handleQuantityChange(product.id, Number.parseInt(e.target.value) || 1)
                            }
                            className="w-16 text-right"
                            min="1"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        ${(product.price * product.quantity).toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleDeleteProduct(product.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}

            <div className="flex flex-col items-start justify-between gap-2 sm:flex-row sm:items-center">
              <Dialog open={isAddProductOpen} onOpenChange={setIsAddProductOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Product
                  </Button>
                </DialogTrigger>
                <DialogContent className="p-6 sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Add Product</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleAddProduct} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="productName">Product Name</Label>
                      <Input
                        id="productName"
                        value={newProduct.name}
                        onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="productPrice">Price</Label>
                      <Input
                        id="productPrice"
                        type="number"
                        min="0"
                        step="0.01"
                        value={newProduct.price}
                        onChange={(e) =>
                          setNewProduct({
                            ...newProduct,
                            price: Number.parseFloat(e.target.value) || 0,
                          })
                        }
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="productQuantity">Quantity</Label>
                      <Input
                        id="productQuantity"
                        type="number"
                        min="1"
                        value={newProduct.quantity}
                        onChange={(e) =>
                          setNewProduct({
                            ...newProduct,
                            quantity: Number.parseInt(e.target.value) || 1,
                          })
                        }
                        required
                      />
                    </div>
                    <Button
                      variant="primary"
                      type="submit"
                      className="w-full bg-slate-800 hover:bg-slate-900"
                    >
                      Add Product
                    </Button>
                  </form>
                </DialogContent>
              </Dialog>
              <div className="flex items-center gap-2">
                <Checkbox
                  id="allowChange"
                  checked={allowEdit}
                  onChange={() => setAllowEdit(!allowEdit)}
                />
                <label htmlFor="allowChange" className="text-sm leading-none text-gray-600">
                  Allow Customer to Change Amount
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-4">
            <CardTitle>Tax & Additional Options</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-1">
                <label className="text-sm font-medium">Tax Type</label>
                <Select
                  defaultValue="fixed"
                  value={taxType}
                  onValueChange={(value) => setTaxType(value as 'fixed' | 'percentage')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select tax type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fixed">Fixed</SelectItem>
                    <SelectItem value="percentage">Percentage</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium">
                  Tax {taxType === 'fixed' ? 'Amount' : 'Percentage'}
                </label>
                <Input
                  placeholder={`Enter Tax (${taxType === 'fixed' ? 'Amount' : 'Percentage'})`}
                  type="number"
                  value={tax}
                  onChange={(e) => setTax(Number(e.target.value) || 0)}
                  min="0"
                />
              </div>
            </div>

            <div className="flex flex-col justify-between gap-2 sm:flex-row">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="allowDiscount"
                  checked={allowExtraDiscount}
                  onChange={() => setAllowExtraDiscount(!allowExtraDiscount)}
                />
                <label htmlFor="allowDiscount" className="text-sm leading-none text-gray-600">
                  Allow Extra Discount
                </label>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox
                  id="allowTip"
                  checked={allowTip}
                  onChange={() => setAllowTip(!allowTip)}
                />
                <label htmlFor="allowTip" className="text-sm leading-none text-gray-600">
                  Allow Tip
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button
            onClick={handleCreatePaymentLink}
            disabled={createProductLoading}
            className="bg-blue-600 px-6 text-white hover:bg-blue-700"
          >
            {createProductLoading ? 'Creating ...' : 'Create Payment Link'}
          </Button>
        </div>
      </div>
    </div>
  );
}
