.text {
  position: relative;
  line-height: 125%;
  font-weight: 500;
}
.navItem {
  align-self: stretch;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.lineIcon {
  flex: 1;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  height: 0px;
  object-fit: contain;
}
.navItem1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.navItem3 {
  align-self: stretch;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #1a56db;
}
.stepperNavigation {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 20px;
  gap: 16px;
  color: #6b7280;
}
.div {
  position: relative;
  line-height: 20px;
}
.placeholder {
  flex: 1;
  position: relative;
  line-height: 150%;
}
.chevronDownIcon {
  width: 10px;
  position: relative;
  height: 10px;
  overflow: hidden;
  flex-shrink: 0;
}
.content1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}
.selectInput {
  width: 161.5px;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  box-sizing: border-box;
  height: 39px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 8px 10px;
}
.button {
  border-radius: 8px;
  background-color: #1a56db;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 4px 12px;
  font-size: 20px;
  color: #fff;
}
.headerSm {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}
.info {
  width: 12px;
  position: relative;
  height: 12px;
  overflow: hidden;
  flex-shrink: 0;
}
.headerSm1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}
.uploadIcon {
  width: 20px;
  position: relative;
  height: 20px;
  overflow: hidden;
  flex-shrink: 0;
}
.dragFilesHere {
  text-align: center;
  font-size: 14px;
  color: #6b7280;
  width: 173px;
  position: relative;
  line-height: 150%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.orBrowseFor {
  width: 173px;
  position: relative;
  font-size: 12px;
  text-decoration: underline;
  line-height: 150%;
  color: #1c64f2;
  display: flex;
  align-items: center;
  justify-content: center;
}
.textLink {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 2px;
}
.dropFilesToUpload {
  width: 173px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.uploadAreas {
  border-radius: 8px;
  background-color: #f9fafb;
  border: 2px dashed #e5e7eb;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 14px;
  color: #6b7280;
}
.headerSmParent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  height: fit-content;
}
.flowbiteProV220zip {
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.mb {
  position: relative;
  line-height: 150%;
}
.size {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.sizePercentage {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  font-size: 12px;
  color: #6b7280;
}
.text3 {
  width: 276.7px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.zipParent {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}
.button1 {
  border-radius: 8px;
  background-color: #6b7280;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 4px;
}
.checkCircleIcon {
  width: 16px;
  position: relative;
  height: 16px;
  overflow: hidden;
  flex-shrink: 0;
}
.buttonParent {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  text-align: left;
  color: #fff;
}
.item2 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.size2 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}
.sizePercentage1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  font-size: 12px;
  color: #6b7280;
}
.text5 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.iconText {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}
.div2 {
  width: 34.7px;
  position: relative;
  line-height: 150%;
  font-weight: 500;
  display: none;
  z-index: 0;
}
.shape {
  width: 100%;
  position: absolute;
  margin: 0 !important;
  height: 100%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  left: 0%;
  border-radius: 2px;
  background-color: #e5e7eb;
  z-index: 1;
}
.progress {
  width: 84.24%;
  position: absolute;
  margin: 0 !important;
  height: 100%;
  top: 0%;
  right: 15.76%;
  bottom: 0%;
  left: 0%;
  border-radius: 2px;
  background-color: #1c64f2;
  z-index: 2;
}
.progressBars {
  align-self: stretch;
  height: 6px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  position: relative;
  text-align: right;
  font-size: 12px;
  color: #6b7280;
}
.leftContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 6px;
  width: 100%;
}
.progress1 {
  width: 50.16%;
  position: absolute;
  margin: 0 !important;
  height: 100%;
  top: 0%;
  right: 49.84%;
  bottom: 0%;
  left: 0%;
  border-radius: 2px;
  background-color: #1c64f2;
  z-index: 2;
}
.fileUpload {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 10px;
  text-align: center;
  font-size: 14px;
}
.documentUpload {
  align-self: stretch;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: stretch;
  gap: 24px;
  font-size: 20px;
}
.content {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  font-size: 14px;
}
.button2 {
  width: 85px;
  border-radius: 8px;
  background-color: #e5e7eb;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  box-sizing: border-box;
}
.button3 {
  border-radius: 8px;
  background-color: #1a56db;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  color: #fff;
}
.sm {
  align-self: stretch;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
}
.documents {
  position: relative;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  gap: 32px;
  text-align: left;
  font-size: 20px;
  color: #111928;
  font-family: Inter;
}
