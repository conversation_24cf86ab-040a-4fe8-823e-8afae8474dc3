import { Modal } from 'flowbite-react';
import { useLocationSelector } from '@/components/hooks';
import { Column, StatusChip, TopComponent, useDataGridView } from '@/components/globals';
import {
  Gateway_DiscountsDocument,
  GatewayUniDiscountOutputType,
} from '@/graphql/generated/graphql';
import { useState, useMemo } from 'react';
import useDebounce from '@/components/hooks/useDebounce';
import DataGridView from '@/components/globals/sortable-table/data-grid-view';
import { getDiscountStatus } from '../../cart-discounts/cart-discounts-tab';

type ModalAddDiscountLinkProps = {
  isOpen: boolean;
  onClose: () => void;
  onAddDiscount: (taxType: 'fixed' | 'percentage', taxAmount: number) => void;
};

export const ModalAddDiscountLink = ({
  isOpen,
  onClose,
  onAddDiscount,
}: ModalAddDiscountLinkProps) => {
  const { locationFilter } = useLocationSelector({ readonly: true });
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    data: discountListData,
    loading: discountListLoading,
    refetch: refetchdiscountListData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    filterValue,
    setFilterValue,
    maxVariables,
  } = useDataGridView({
    query: Gateway_DiscountsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            scope: '',
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
    initialFilterField: 'status',
    initialFilterValue: 'All',
  });

  const handleSelectDiscount = (taxType: 'fixed' | 'percentage', taxAmount: number) => {
    onAddDiscount(taxType, taxAmount);
    onClose();
  };

  const columns: Column[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      onClick: (row) => handleSelectDiscount(row.type, row?.discount || 0),
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'type',
      header: 'Type',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'discount',
      header: 'Amount',
      renderCell: (row) => {
        const isPercentage = row?.type === GatewayUniDiscountOutputType.Percentage;
        return <>{isPercentage ? row.discount + '%' : '$' + row.discount}</>;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getDiscountStatus(row?.status);
        return <StatusChip variant={status} label={label} />;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];

  const rows = useMemo(() => {
    const data = discountListData?.gateway_discounts?.data;
    if (!data) return [];
    return data.filter((item): item is NonNullable<typeof item> => item !== null);
  }, [discountListData?.gateway_discounts?.data]);

  return (
    <Modal show={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        <div>
          <div className="flex justify-between">
            <TopComponent value={searchValue} setValue={setSearchValue}>
              <div></div>
            </TopComponent>
          </div>
          <div>
            <DataGridView
              columns={columns}
              rows={rows}
              pageSize={pageSize}
              currentPage={currentPage}
              isLoading={discountListLoading}
              mode="server"
              onPageChange={onPageChange}
              onPageSizeChange={onPageSizeChange}
              totalRecords={discountListData?.gateway_discounts?.page?.total ?? 0}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};
