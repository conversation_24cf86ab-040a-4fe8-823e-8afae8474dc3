import React, { useMemo } from 'react';
import { Checkbox, Label } from 'flowbite-react';
import { useFormContext, Controller } from 'react-hook-form';
import { HiUpload } from 'react-icons/hi';
import Image from 'next/image';
import { FaTrashAlt } from 'react-icons/fa';
import { FormInput } from '@/components/globals/form-input';
import { message, requiredAmountIsValid } from '@/components/shared/utils';
import { FormTextArea } from '@/components/globals/form-textarea';
import { FormSelect } from '@/components/globals';
import { ProductFormData } from '../utils/types';
import { Gateway_CategoriesDocument } from '@/graphql/generated/graphql';
import { useQuery } from '@apollo/client';
import { useLocationSelector } from '@/components/hooks';

type ProductFormProps = {
  handleImageUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  images: File[];
  removeImage: (index: number) => void;
};

export const ProductForm = ({ handleImageUpload, images, removeImage }: ProductFormProps) => {
  const { control, watch } = useFormContext<ProductFormData>();

  const { locationFilter } = useLocationSelector({});

  const variables = watch();
  const isRecurring = watch('isRecurring');
  const recurringMode = watch('recurringMode');

  const { data: categoryData } = useQuery(Gateway_CategoriesDocument, {
    variables: {
      input: {
        data: {},
        groupID: locationFilter?.id ?? '',
      },
    },
    skip: !locationFilter?.id,
  });

  let categoryList = useMemo(() => {
    return (
      categoryData?.gateway_categories?.data?.map((category) => ({
        label: category?.name ?? '',
        value: category?.name ?? '',
      })) ?? []
    );
  }, [categoryData]);

  let subCategoryList = useMemo(() => {
    let categoryList = categoryData?.gateway_categories?.data?.find(
      (category) => category?.name === variables.category,
    );
    return (
      categoryList?.subCategory?.map((subCategory) => ({
        label: subCategory ?? '',
        value: subCategory ?? '',
      })) ?? []
    );
  }, [categoryData, variables.category]);

  return (
    <>
      <div className="grid grid-cols-2 gap-4">
        <FormInput
          id="productName"
          name="productName"
          label="Product Name"
          rules={{ required: message.requiredField }}
        />

        <FormSelect
          id="category"
          name="category"
          label="Select Category"
          options={
            categoryList.map((category) => ({
              label: category?.label ?? '',
              value: category?.value ?? '',
            })) ?? []
            // [
            //   { label: 'Electronics', value: 'Electronics' },
            //   { label: 'Clothing', value: 'Clothing' },
            //   { label: 'Books', value: 'Books' },
            // ]
          }
        />
        <FormSelect
          id="subCategory"
          name="subCategory"
          label="Select Sub Category"
          options={
            subCategoryList.map((category) => ({
              label: category?.label ?? '',
              value: category?.value ?? '',
            })) ?? []
            // [
            //   { label: 'Electronics', value: 'Electronics' },
            //   { label: 'Clothing', value: 'Clothing' },
            //   { label: 'Books', value: 'Books' },
            // ]
          }
        />
        <FormInput
          id="brand"
          name="brand"
          label="Brand"
          rules={{ required: message.requiredField }}
        />
        <FormInput id="sku" name="sku" label="SKU" rules={{ required: message.requiredField }} />
        <FormInput
          id="price"
          name="price"
          label="Price"
          type="number"
          rules={requiredAmountIsValid}
        />

        {/* Recurring Fields */}
        <div className="col-span-2">
          <Controller
            name="isRecurring"
            control={control}
            render={({ field }) => (
              <div className="flex items-center">
                {/* @ts-ignore */}
                <Checkbox {...field} checked={field.value} />
                <span className="ml-2">Is Recurring Product</span>
              </div>
            )}
          />
        </div>

        {watch('isRecurring') && (
          <>
            <FormSelect
              id="recurringMode"
              name="recurringMode"
              label="Recurring Mode"
              options={[
                { label: 'By Day (every n Days)', value: '' },
                { label: 'By Month (nth of Month)', value: 'MONTHLY' },
                { label: 'By Year (nth of Year)', value: 'YEARLY' },
              ]}
            />
            {recurringMode !== 'MONTHLY' && recurringMode !== 'YEARLY' && (
              <FormInput
                id="recurringInterval"
                name="recurringInterval"
                label="Recurring Interval (days)"
                type="number"
                placeholder="Enter number of days"
                helperText="Default is 30 days (monthly subscription)"
              />
            )}
            {(recurringMode === 'MONTHLY' || recurringMode === 'YEARLY') && (
              <div className="col-span-2 rounded-lg border border-blue-100 bg-blue-50 p-3 text-sm text-blue-800">
                <p>
                  <strong>Note:</strong> With {recurringMode === 'MONTHLY' ? 'Monthly' : 'Yearly'}{' '}
                  mode, the customer will be charged on the same day of the{' '}
                  {recurringMode === 'MONTHLY' ? 'month' : 'year'} as the initial payment.
                </p>
                <p className="mt-1">
                  For payments starting on the 29th-31st, the payment will adjust to the last day of
                  shorter months to accommodate different month lengths and leap years.
                </p>
                {recurringMode === 'YEARLY' && (
                  <p className="mt-1">
                    <strong>For yearly payments:</strong> If a payment was started on February 29th
                    (leap year), it will be automatically adjusted to February 28th in non-leap
                    years.
                  </p>
                )}
              </div>
            )}
            <FormInput
              id="recurringFrequency"
              name="recurringFrequency"
              label="Recurring Frequency"
              type="number"
              placeholder="Enter when to charge"
              helperText='Default is 1. IF you want to charge every other month, enter "2"'
            />
            <FormInput
              id="recurringTotalCycles"
              name="recurringTotalCycles"
              label="Total Cycles"
              type="number"
              placeholder="Enter how many times to charge"
              helperText="Leave blank for unlimited cycles"
            />
            <FormInput
              id="recurringTrialDays"
              name="recurringTrialDays"
              label="Trial Days"
              type="number"
              helperText="Will only begin to charge after trial days"
            />
            <FormInput
              id="recurringSetupFee"
              name="recurringSetupFee"
              label="Setup Fee"
              type="number"
              helperText="Setup Fee for Subscription. Will be charged immediately even if trial days are set"
            />
          </>
        )}
      </div>
      <FormTextArea
        id="description"
        name="description"
        label="Description"
        // rules={{ required: message.requiredField }}
      />
      {/* <div>
        <Label>Taxable Item:</Label>
        <div className="flex space-x-4">
          <Controller
            name="taxableItem"
            control={control}
            render={({ field }) => (
              <>
                <div className="flex items-center">
                  <Checkbox
                    {...field}
                    checked={field.value === 'inStore'}
                    onChange={() => field.onChange('inStore')}
                  />
                  <span className="ml-2">In-store only</span>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    {...field}
                    checked={field.value === 'online'}
                    onChange={() => field.onChange('online')}
                  />
                  <span className="ml-2">Online selling only</span>
                </div>
                <div className="flex items-center">
                  <Checkbox
                    {...field}
                    checked={field.value === 'both'}
                    onChange={() => field.onChange('both')}
                  />
                  <span className="ml-2">Both in-store and online</span>
                </div>
              </>
            )}
          />
        </div>
      </div> */}
      <div>
        <Label>Product Images</Label>
        <div className="mb-4 grid grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div key={index} className="relative rounded bg-gray-100 p-2">
              <Image
                width={100}
                height={100}
                src={URL.createObjectURL(image)}
                alt={`Product ${index + 1}`}
                className="h-32 w-full rounded object-cover"
              />
              <FaTrashAlt
                className="absolute bottom-3 left-3 text-[30px] text-red-500 hover:bg-gray-500"
                onClick={() => removeImage(index)}
              />
            </div>
          ))}
        </div>
        <div className="flex w-full items-center justify-center">
          <label
            htmlFor="dropzone-file"
            className="flex h-64 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
          >
            <div className="flex flex-col items-center justify-center pb-6 pt-5">
              <HiUpload className="mb-3 h-10 w-10 text-gray-400" />
              <p className="mb-2 text-sm text-gray-500">
                <span className="font-semibold">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs text-gray-500">SVG, PNG, JPG or GIF (MAX. 800x400px)</p>
            </div>
            <input
              id="dropzone-file"
              type="file"
              className="hidden"
              onChange={handleImageUpload}
              multiple
              accept="image/*"
            />
          </label>
        </div>
      </div>
    </>
  );
};
