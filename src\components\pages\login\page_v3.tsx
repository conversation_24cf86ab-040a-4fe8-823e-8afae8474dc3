'use client';
import { useState, useRef, useEffect } from 'react';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { AUTHSTORE } from '@/lib/auth-storage';
import { useBrowserSessionID } from '@/components/hooks/useSession';
import { Login as LoginType } from '@/graphql/declarations/auth';
import { generateMFA, verifyMFA } from '@/graphql/declarations/otpValues';
import { LoginPage, MFA, GHLSetupLocation } from './components';
import { useSearchParams } from 'next/navigation';
import { toast } from 'react-toastify';
import { GHLSSOBind, GHLSSOCheck } from '@/graphql/declarations/gohighlevel-sso';
import { Authclient_Mfa_GenerateInputChannel } from '@/graphql/generated/graphql';
import { Me } from '@/graphql/declarations/me';
import { HSSetupLocation } from './components/hs-setup-location';

enum CurrentPage {
  LOGIN = 'login',
  MFA = 'mfa',
  SETGHLLOCATION = 'setGHLLocation',
  SETHSLOCATION = 'setHSLocation',
}

export default function Auth() {
  const query = useSearchParams();
  const [currentPage, setCurrentPage] = useState<CurrentPage>(CurrentPage.LOGIN);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [form, setForm] = useState({ email: '', password: '', oneTime: false });
  const [error, setError] = useState('');
  const [pendingToResend, setPendingToResend] = useState(0);
  const sidCode = useRef('');
  const { id: browserID } = useBrowserSessionID();
  const [isIntegratingGHL, setIsIntergratingGHL] = useState(false);
  const [isIntegratingHS, setIsIntergratingHS] = useState(false);
  const [isBindingGHLSSO, setIsBindingGHLSSO] = useState(false);
  const [shouldBindGHLSSO, setShouldBindGHLSSO] = useState(false);
  const [, setIsBindingExpires] = useState(false);
  const credentialObject = useRef({ email: '', password: '', oneTime: false });
  const redirectURI = query?.get('redirect');

  const bindAccountGHL = async () => {
    setIsBindingGHLSSO(true);
    const code = query?.get('token');

    if (!code) {
      toast.error('Invalid token');
      return;
    }

    // bind account
    const result = await apolloClient.mutate({
      mutation: GHLSSOBind,
      variables: {
        input: {
          ssoToken: query?.get('token') || '',
        },
      },
    });

    toast.success('Account bound successfully');
    if (result.data?.ghl_auth_bind?.localUserID) {
      window.location.href = '/dashboard';
    } else {
      toast.error('Failed to bind account');
      return false;
    }
  };

  const handleLogin = async (data: { email: string; password: string; oneTime: boolean }) => {
    setIsSubmitting(true);
    try {
      const resp = await apolloClient.mutate({
        mutation: LoginType,
        variables: {
          email: data.email,
          password: data.password,
          browserId: browserID,
        },
      });
      setForm(data);

      if (
        resp.data?.authclient_login?.__typename === 'ClientItemAuthenticationWithPasswordSuccess'
      ) {
        const token = resp.data.authclient_login.sessionToken;
        AUTHSTORE.set(token);
        await setGroupID();
        if (isIntegratingGHL) {
          setCurrentPage(CurrentPage.SETGHLLOCATION);
        } else if (isIntegratingHS) {
          setCurrentPage(CurrentPage.SETHSLOCATION);
        } else if (isBindingGHLSSO && shouldBindGHLSSO) {
          bindAccountGHL();
        } else {
          if (redirectURI) {
            window.location.href = redirectURI;
          } else {
            window.location.href = '/dashboard';
          }
        }
      } else if (
        resp.data?.authclient_login?.__typename === 'ClientItemAuthenticationWithPasswordFailure' &&
        resp.data.authclient_login.message.includes('MFA')
      ) {
        setCurrentPage(CurrentPage.MFA);
        credentialObject.current = data;
        generateOTP({
          email: data.email,
        });
      } else {
        setError('Failed to login');
      }
    } catch (err) {
      setError('An error occurred. Please try again');
    }
    setIsSubmitting(false);
  };

  const generateOTP = async (args?: { email?: string }) => {
    try {
      const response = await apolloClient.mutate({
        mutation: generateMFA,
        variables: {
          input: {
            channel: Authclient_Mfa_GenerateInputChannel.Sms,
            email: args?.email ?? form.email ?? credentialObject.current.email,
            browserID: browserID,
            oneTime: form.oneTime || credentialObject.current.oneTime,
          },
        },
      });

      if (response.data?.authclient_mfa_generate?.sid) {
        sidCode.current = response.data.authclient_mfa_generate.sid;
        setPendingToResend(60);
      } else {
        setError('No phone number associated with this account');
      }
    } catch (error) {
      // @ts-ignore
      if (error.message.includes('Phone number')) {
        setError('No phone number associated with this account.\nPlease contact support');
      } else {
        setError('Failed to generate OTP. Please try again.');
      }
    }
  };

  const validateOTP = async (code: string) => {
    if (code.length !== 6) {
      setError('Invalid code');
      return;
    }
    setIsSubmitting(true);

    try {
      const response = await apolloClient.mutate({
        mutation: verifyMFA,
        variables: {
          input: {
            sid: sidCode.current,
            code,
          },
        },
      });

      if (response.data?.authclient_mfa_verify?.status) {
        handleLogin(form);
      } else {
        setError('Invalid code');
      }
    } catch (error) {
      setError('Error verifying OTP');
    }
    setIsSubmitting(false);
  };

  const isExpired = () => {
    const expiry = localStorage.getItem('binding_expiry');
    const today = Date.now(); //+ 31 * 24 * 60 * 60 * 1000;
    if (expiry) {
      return today > parseInt(expiry);
    }
    return true;
  };

  async function checkIsIntegrating() {
    let mode = query?.get('mode');
    let code = query?.get('code');

    setIsIntergratingGHL(mode === 'ghl-setup' && !!code);
    setIsIntergratingHS(mode === 'hs-setup' && !!code);
  }

  async function checkBinding() {
    let mode = query?.get('mode');
    let token = query?.get('token');

    setIsBindingGHLSSO(mode === 'ghl-binding' && !!token);
  }

  // May God have mercy on us that this convuluted login logic doesnt break
  async function setGroupID() {
    if (!isBindingGHLSSO) {
      return;
    }
    let tok = query?.get('token');
    if (tok) {
      const locationData = await apolloClient.query({
        query: GHLSSOCheck,
        variables: {
          input: {
            ssoToken: tok,
          },
        },
      });

      const me = await apolloClient.query({
        query: Me,
      });

      localStorage.setItem(
        `location_${me.data.authenticatedItem?.id}`,
        JSON.stringify({
          id: locationData.data.ghl_auth_getSSOInfo?.groupID,
          label: locationData.data.ghl_auth_getSSOInfo?.groupName,
        }),
      );
    }
  }

  useEffect(() => {
    if (query?.get('mode') && query?.get('token')) {
      checkBinding();
    }
  }, [query]);

  useEffect(() => {
    if (query?.get('mode') && query?.get('code')) {
      checkIsIntegrating();
    }
  }, [query]);

  useEffect(() => {
    AUTHSTORE.session();
    if (typeof window !== 'undefined') {
      setIsBindingExpires(isExpired());
    }
  }, []);

  useEffect(() => {
    if (pendingToResend) {
      const interval = setInterval(() => {
        setPendingToResend((prev) => (prev > 0 ? prev - 1 : 0));
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [pendingToResend]);

  useEffect(() => {
    setError('');
  }, [currentPage]);

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <div className="w-full max-w-md border shadow-xl">
        {currentPage === CurrentPage.LOGIN && (
          <LoginPage
            handleLogin={handleLogin}
            isLoggingIn={isSubmitting}
            error={error}
            shouldBind={shouldBindGHLSSO}
            setShouldBind={setShouldBindGHLSSO}
            isBinding={isBindingGHLSSO}
          />
        )}
        {currentPage === CurrentPage.SETGHLLOCATION && <GHLSetupLocation />}
        {currentPage === CurrentPage.SETHSLOCATION && <HSSetupLocation />}
        {currentPage === CurrentPage.MFA && (
          <MFA
            onBack={() => setCurrentPage(CurrentPage.LOGIN)}
            error={error}
            onResend={generateOTP}
            onVerify={validateOTP}
            submitting={isSubmitting}
            pendingToResend={pendingToResend}
          />
        )}
      </div>
    </div>
  );
}
