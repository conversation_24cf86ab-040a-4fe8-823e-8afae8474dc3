import { Propay_Admin_BalanceDocument } from '@/graphql/generated/graphql';
import { int2DecToFloat } from '@/lib/utils';
import { useQuery } from '@apollo/client';
import { useLocationSelector } from '@/components/hooks';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useState } from 'react';
import { EarningsComponent } from './EarningsComponent';

type EarningsFiltersProps = {
  children: (filters: { isDirectedFor: boolean; groupID?: string }) => React.ReactNode;
};

const EarningsFilters = ({ children }: EarningsFiltersProps) => {
  const [isDirectedFor, setIsDirectedFor] = useState(true);
  const { locationFilter, locationSelectorElement } = useLocationSelector({
    onlyActive: true,
  });

  return (
    <div className="space-y-6">
      <div className="flex gap-4">
        {locationSelectorElement}
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="is-directed-for"
              checked={isDirectedFor}
              onCheckedChange={setIsDirectedFor}
            />
            <Label htmlFor="is-directed-for">Show Only Admin Earnings</Label>
          </div>
        </div>
      </div>

      {children({ isDirectedFor, groupID: locationFilter?.id })}
    </div>
  );
};

const EarningsData = ({ isDirectedFor, groupID }: { isDirectedFor: boolean; groupID?: string }) => {
  const { data, loading } = useQuery(Propay_Admin_BalanceDocument, {
    variables: {
      input: {
        isDirectedFor: isDirectedFor ? 'admin' : undefined,
        groupID,
        transferred: isDirectedFor ? true : false,
      },
    },
  });

  if (loading) return <div>Loading...</div>;

  const balance = data?.propay_admin_balance;

  // Calculate earnings
  const completed = int2DecToFloat((balance?.positives ?? 0) - (balance?.negatives ?? 0));
  const pending = int2DecToFloat(
    (balance?.positiveUncounted ?? 0) - (balance?.negativeUncounted ?? 0),
  );

  // Get propay balance
  const propayBalance = int2DecToFloat(balance?.propayBalance ?? 0);

  // Calculate item counts
  const completedItemCount = (balance?.positiveItemCount ?? 0) + (balance?.negativeItemCount ?? 0);
  const pendingItemCount =
    (balance?.positiveUncountedItemCount ?? 0) + (balance?.negativeUncountedItemCount ?? 0);
  const totalItemCount = balance?.actualCount ?? completedItemCount + pendingItemCount;

  return (
    <EarningsComponent
      data={{
        completed,
        pending,
        completedItemCount,
        pendingItemCount,
        totalItemCount,
        propayBalance,
      }}
      title={isDirectedFor ? 'Admin Earnings' : 'All Earnings'}
    />
  );
};

export const EarningsFetcher = () => {
  return <EarningsFilters>{(filters) => <EarningsData {...filters} />}</EarningsFilters>;
};
