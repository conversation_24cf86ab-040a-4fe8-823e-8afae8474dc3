'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import { Trash2, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { TOKEN_PERMISSIONS } from './oauth-client-manager';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { useQuery, useMutation } from '@apollo/client';
import { OAuthTokens, OAuthTokenDelete } from '@/graphql/declarations/oauth';

type TokenStatus = 'all' | 'active' | 'refresh-needed' | 'expired';

// This interface helps with TypeScript type checking
interface OAuthToken {
  id: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: string;
  refreshTokenExpiresAt?: string;
  scope?: string;
  createdAt: string;
  isExpired?: boolean;
  client?: {
    id: string;
    group?: {
      id: string;
      name?: string;
      actualName?: string;
      labelName?: string;
    };
  };
  group?: {
    id: string;
    name?: string;
    actualName?: string;
    labelName?: string;
  };
}

export default function OAuthTokenManager({ groupId }: { groupId: string }) {
  const [tokenIdToDelete, setTokenIdToDelete] = useState('');
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<TokenStatus>('all');
  const { toast } = useToast();

  // Use Apollo client to fetch tokens
  const { data, loading, refetch } = useQuery(OAuthTokens, {
    variables: {
      where: {
        group: { id: { equals: groupId } },
      },
      take: 100,
      skip: 0,
    },
    skip: !groupId,
  });

  // Setup delete token mutation
  const [deleteToken, { loading: isDeleting }] = useMutation(OAuthTokenDelete, {
    onCompleted: () => {
      toast({
        title: 'Success',
        description: 'OAuth token revoked successfully',
      });
      setConfirmDeleteOpen(false);
      refetch(); // Refetch the query after successful deletion
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to revoke OAuth token',
        variant: 'destructive',
      });
    },
  });

  // Process tokens and add isExpired flag
  const apiTokens = useMemo(() => {
    if (!data?.oAuthTokens) return [];
    return data.oAuthTokens.map((token: any) => {
      const accessTokenExpired = new Date(token.expiresAt) < new Date();
      const refreshTokenExpired = token.refreshTokenExpiresAt
        ? new Date(token.refreshTokenExpiresAt) < new Date()
        : true; // If no refresh token exists, consider it expired

      return {
        ...token,
        accessTokenExpired,
        refreshTokenExpired,
        // Only mark as fully expired if both access token AND refresh token are expired
        // or if there's no refresh token and the access token is expired
        isExpired: accessTokenExpired && refreshTokenExpired,
      };
    });
  }, [data?.oAuthTokens]);

  // Filter tokens based on the selected status
  const filteredTokens = useMemo(() => {
    if (statusFilter === 'active') {
      return apiTokens.filter((token) => !token.accessTokenExpired && !token.isExpired);
    } else if (statusFilter === 'refresh-needed') {
      return apiTokens.filter((token) => token.accessTokenExpired && !token.isExpired);
    } else if (statusFilter === 'expired') {
      return apiTokens.filter((token) => token.isExpired);
    }
    return apiTokens;
  }, [apiTokens, statusFilter]);

  // Calculate token statistics
  const tokenStats = useMemo(() => {
    const total = apiTokens.length;
    const active = apiTokens.filter(
      (token) => !token.accessTokenExpired && !token.isExpired,
    ).length;
    const refreshNeeded = apiTokens.filter(
      (token) => token.accessTokenExpired && !token.isExpired,
    ).length;
    const expired = apiTokens.filter((token) => token.isExpired).length;

    return { total, active, refreshNeeded, expired };
  }, [apiTokens]);

  // Handle token deletion/revocation
  const handleDeleteToken = () => {
    if (!tokenIdToDelete) return;

    deleteToken({
      variables: {
        where: [{ id: tokenIdToDelete }],
      },
    });
  };

  const openDeleteConfirmation = (tokenId: string) => {
    setTokenIdToDelete(tokenId);
    setConfirmDeleteOpen(true);
  };

  // Format date with time
  const formatDateTime = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP p');
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Function to mask token
  const maskToken = (token: string) => {
    if (!token) return 'N/A';
    if (token.length <= 8) return '********';
    return token.substring(0, 4) + '...' + token.substring(token.length - 4);
  };

  return (
    <div className="space-y-6 p-6">
      <DialogHeader>
        <DialogTitle className="text-2xl">OAuth Tokens</DialogTitle>
        <DialogDescription>View and manage OAuth tokens connected to this group</DialogDescription>
      </DialogHeader>

      {/* Stats Section */}
      {!loading && apiTokens.length > 0 && (
        <div className="mb-4 grid grid-cols-4 gap-4">
          <Card className="bg-slate-50">
            <CardContent className="flex flex-col items-center justify-center p-4">
              <p className="text-3xl font-bold text-slate-800">{tokenStats.total}</p>
              <p className="text-sm text-slate-600">Total Tokens</p>
            </CardContent>
          </Card>
          <Card className="bg-emerald-50">
            <CardContent className="flex flex-col items-center justify-center p-4">
              <p className="text-3xl font-bold text-emerald-600">{tokenStats.active}</p>
              <p className="text-sm text-emerald-700">Active Tokens</p>
            </CardContent>
          </Card>
          <Card className="bg-amber-50">
            <CardContent className="flex flex-col items-center justify-center p-4">
              <p className="text-3xl font-bold text-amber-600">{tokenStats.refreshNeeded}</p>
              <p className="text-sm text-amber-700">Need Refresh</p>
            </CardContent>
          </Card>
          <Card className="bg-rose-50">
            <CardContent className="flex flex-col items-center justify-center p-4">
              <p className="text-3xl font-bold text-rose-600">{tokenStats.expired}</p>
              <p className="text-sm text-rose-700">Expired Tokens</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filter Buttons */}
      {!loading && apiTokens.length > 0 && (
        <div className="mb-4 flex space-x-2">
          <Button
            variant={statusFilter === 'all' ? 'default' : 'outline'}
            onClick={() => setStatusFilter('all')}
            className="flex-1"
          >
            All Tokens
          </Button>
          <Button
            variant={statusFilter === 'active' ? 'default' : 'outline'}
            onClick={() => setStatusFilter('active')}
            className="flex-1"
          >
            Active Only
          </Button>
          <Button
            variant={statusFilter === 'refresh-needed' ? 'default' : 'outline'}
            onClick={() => setStatusFilter('refresh-needed')}
            className="flex-1"
          >
            Need Refresh
          </Button>
          <Button
            variant={statusFilter === 'expired' ? 'default' : 'outline'}
            onClick={() => setStatusFilter('expired')}
            className="flex-1"
          >
            Expired Only
          </Button>
        </div>
      )}

      <div className="space-y-4">
        {loading ? (
          <div className="flex justify-center py-4">
            <Spinner className="h-6 w-6" />
          </div>
        ) : apiTokens.length > 0 ? (
          <div className="max-h-[400px] space-y-4 overflow-y-auto pr-1">
            {filteredTokens.length === 0 ? (
              <div className="rounded-lg border bg-slate-50 py-8 text-center">
                <p className="text-slate-500">No {statusFilter} tokens found</p>
              </div>
            ) : (
              filteredTokens.map((token) => (
                <Card
                  key={token.id}
                  className={`overflow-hidden border-l-4 ${token.isExpired ? 'border-l-rose-500' : token.accessTokenExpired ? 'border-l-amber-500' : 'border-l-emerald-500'}`}
                >
                  <CardHeader
                    className={`pb-2 ${token.isExpired ? 'bg-rose-50' : token.accessTokenExpired ? 'bg-amber-50' : 'bg-emerald-50'}`}
                  >
                    <CardTitle className="flex items-center justify-between text-base">
                      <span>Token: {maskToken(token.accessToken)}</span>
                      {token.isExpired ? (
                        <Badge variant="destructive" className="ml-2 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" /> Fully Expired
                        </Badge>
                      ) : token.accessTokenExpired ? (
                        <Badge
                          variant="outline"
                          className="ml-2 flex items-center gap-1 border-amber-500 bg-amber-50 text-amber-600"
                        >
                          <Clock className="h-3 w-3" /> Refresh Needed
                        </Badge>
                      ) : (
                        <Badge
                          variant="outline"
                          className="ml-2 flex items-center gap-1 border-emerald-500 bg-emerald-50 text-emerald-600"
                        >
                          <CheckCircle className="h-3 w-3" /> Active
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pb-2 pt-3 text-sm">
                    <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                      <div>
                        <p className="text-xs text-slate-500">Client</p>
                        <p className="font-medium">
                          {token.client?.group?.actualName || 'Unknown Client'}
                        </p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-500">Expires</p>
                        <p
                          className={`flex items-center gap-1 font-medium ${token.isExpired ? 'text-rose-500' : token.accessTokenExpired ? 'text-amber-600' : 'text-emerald-600'}`}
                        >
                          {token.isExpired ? (
                            <AlertCircle className="h-3 w-3" />
                          ) : token.accessTokenExpired ? (
                            <Clock className="h-3 w-3" />
                          ) : (
                            <CheckCircle className="h-3 w-3" />
                          )}
                          {token.accessTokenExpired ? 'Access: Expired' : 'Access: Valid'} (
                          {formatDateTime(token.expiresAt)})
                        </p>
                        {token.refreshTokenExpiresAt && (
                          <p
                            className={`flex items-center gap-1 text-sm font-medium ${token.refreshTokenExpired ? 'text-rose-500' : 'text-emerald-600'}`}
                          >
                            {token.refreshTokenExpired ? (
                              <AlertCircle className="h-3 w-3" />
                            ) : (
                              <CheckCircle className="h-3 w-3" />
                            )}
                            {token.refreshTokenExpired ? 'Refresh: Expired' : 'Refresh: Valid'} (
                            {formatDateTime(token.refreshTokenExpiresAt)})
                          </p>
                        )}
                      </div>
                    </div>

                    {token.scope && (
                      <div className="mt-3">
                        <p className="mb-1 text-xs text-slate-500">Scopes:</p>
                        <div className="flex flex-wrap gap-1">
                          {token.scope.split(' ').map((scope: string) => (
                            <Badge key={scope} variant="outline" className="text-xs">
                              {scope}
                              {TOKEN_PERMISSIONS[scope as keyof typeof TOKEN_PERMISSIONS] && (
                                <span className="ml-1 text-slate-500">
                                  -{' '}
                                  {
                                    TOKEN_PERMISSIONS[scope as keyof typeof TOKEN_PERMISSIONS]
                                      .description
                                  }
                                </span>
                              )}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="mt-3 text-xs text-slate-500">
                      <p>Created: {formatDateTime(token.createdAt)}</p>
                    </div>
                  </CardContent>
                  <CardFooter
                    className={`border-t pb-2 pt-2 ${token.isExpired ? 'border-t-rose-100' : token.accessTokenExpired ? 'border-t-amber-100' : 'border-t-emerald-100'}`}
                  >
                    <div className="ml-auto flex gap-2">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => openDeleteConfirmation(token.id)}
                        className="flex items-center gap-1"
                      >
                        <Trash2 className="h-4 w-4" /> Revoke
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))
            )}
          </div>
        ) : (
          <div className="rounded-lg border bg-slate-50 py-8 text-center">
            <p className="text-slate-500">No OAuth tokens found for this group</p>
            <p className="mt-1 text-sm text-slate-400">
              Tokens will appear here when applications connect to your API
            </p>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDeleteOpen} onOpenChange={setConfirmDeleteOpen}>
        <DialogContent className="p-4 sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Token Revocation</DialogTitle>
            <DialogDescription>
              Are you sure you want to revoke this OAuth token? The application using this token
              will lose access immediately.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setConfirmDeleteOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteToken} disabled={isDeleting}>
              {isDeleting ? <Spinner className="mr-2 h-4 w-4" /> : null}
              Revoke Token
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
