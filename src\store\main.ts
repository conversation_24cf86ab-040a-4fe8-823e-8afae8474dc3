import zukeeper from 'zukeeper';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface MainStore {
  // Add properties from reservationSlice here
}

interface UIStore {
  isSidebarCollapsed: boolean;
  toggleSidebar: () => void;
}

export const useMainStore = create<MainStore>();
//   persist(
//     zukeeper((set, get, api) => ({
//     //   ...createUserSlice(set, get, api),
//     })),
//     {
//       name: 'main-store',
//       storage: createJSONStorage(() => sessionStorage),
//     }
//   )

if (typeof window !== 'undefined') {
  (window as any).store = useMainStore;
}

export const useUIStore = create<UIStore>((set) => ({
  isSidebarCollapsed: false,
  toggleSidebar: () =>
    set((state: UIStore) => ({
      isSidebarCollapsed: !state.isSidebarCollapsed,
    })),
}));

export default useUIStore;
