import { <PERSON><PERSON><PERSON><PERSON>, TopComponent, useDataGridView, Variant } from '@/components/globals';
import { Button } from 'flowbite-react';

import { useEffect, useMemo, useState } from 'react';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import { FaPlus } from 'react-icons/fa6';
import { toast } from 'react-toastify';
import { ProductAddModal } from './components/product-add-modal';
import { Gateway_ProductsDocument } from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import { ProductViewModal } from './components/product-preview';
import { moneyFormatString } from '@/lib/utils';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { StatusChip } from '@/components/globals/status-chip';

export enum ProductStatus {
  Active = 'active',
  Inactive = 'inactive',
  Withdrawn = 'withdrawn',
  Cancelled = 'cancelled',
  Completed = 'completed',
}

export const getProductStatusChip = (status: ProductStatus): [Variant, string] => {
  const statusMap: Record<ProductStatus, Variant> = {
    [ProductStatus.Active]: 'success',
    [ProductStatus.Inactive]: 'neutral',
    [ProductStatus.Withdrawn]: 'danger',
    [ProductStatus.Cancelled]: 'danger',
    [ProductStatus.Completed]: 'info',
  };

  const variant = statusMap[status] || 'neutral';
  const label = `${status?.charAt(0).toUpperCase()}${status?.slice(1)}`;

  return [variant, label];
};

type SortFieldProp = 'name' | 'price';

export const ProductsTab = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [selectedProductId, setSelectedProductID] = useState(null);
  const debouncedSearchQuery = useDebounce(searchValue, 500);
  const [] = useState<SortFieldProp>('name');

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({});

  const {
    data: productListData,
    loading: productListLoading,
    refetch: refetchproductListData,
    error: productListError,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    maxVariables,
  } = useDataGridView({
    query: Gateway_ProductsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
  });

  const rows = useMemo(() => {
    const data = productListData?.gateway_products?.data;
    if (!data) return [];
    return data.filter((item): item is NonNullable<typeof item> => item !== null);
  }, [productListData?.gateway_products?.data]);

  const handleSubmit = () => {
    setIsAddModalOpen(false);
    setSelectedProductID(null);
    refetchproductListData();
  };

  useEffect(() => {
    if (locationFilter?.id) {
      refetchproductListData();
    }
  }, [locationFilter]);

  useEffect(() => {
    if (productListError) {
      toast.error(productListError.message);
    }
  }, [productListError]);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_ProductsDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_products?.data ?? [];
  };

  const columns: Column[] = [
    {
      key: 'name',
      header: 'Name',
      onClick: (row) => setSelectedProductID(row.id ?? ''),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'price',
      header: 'Price',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      valueGetter: (row) => `${moneyFormatString(row.price)}`,
    },
    {
      key: 'isRecurring',
      header: 'Is Recurring',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      valueGetter: (row) =>
        `${row.isRecurring ? 'Yes (per ' + row.recurringInterval + ' Days)' : 'N'}`,
    },
    {
      key: 'brand',
      header: 'Brand',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'sku',
      header: 'SKU',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getProductStatusChip(row.status || ProductStatus.Inactive);
        return <StatusChip variant={status} label={label} />;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];
  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Product" />
        <Button
          className="mt-[20px] h-[38px] p-0"
          color="blue"
          onClick={() => setIsAddModalOpen(true)}
        >
          <div className="flex items-center gap-x-3">
            <FaPlus className="text-xl" />
            <span>Add Product</span>
          </div>
        </Button>
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
          <TopComponent value={searchValue} setValue={setSearchValue}>
            <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="products" />
          </TopComponent>
        </div>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={rows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={productListLoading || loadingGroupList}
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={productListData?.gateway_products?.page?.total ?? 0}
        />
        <ProductAddModal isOpen={isAddModalOpen} onClose={handleSubmit} />

        <ProductViewModal
          isOpen={selectedProductId !== null}
          onClose={() => setSelectedProductID(null)}
          refetchproductListData={refetchproductListData}
          queryData={{
            productID: selectedProductId ?? '',
            groupID: locationFilter?.id ?? '',
          }}
        />
      </div>
    </>
  );
};
