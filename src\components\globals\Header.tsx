'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import navItems from '@/consts/navigations';

import moment from 'moment';
import Submenu from './SubMenu';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { Me } from '@/graphql/declarations/me';
import { useUserStore } from '@/store/user-store';
import { User } from '@/types/User';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { AUTHSTORE } from '@/lib/auth-storage';
import { useRouter } from 'next/navigation';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '../ui/button';
import { Suspense } from 'react';

const Header = () => {
  const pathname = usePathname();

  const { user, setUser, logout } = useUserStore();
  const [date, setDate] = useState(moment());
  const router = useRouter();
  const token = AUTHSTORE.get();

  const headerData = navItems.find((item) => pathname?.startsWith(item.link));

  useEffect(() => {
    (async () => {
      const response = await apolloClient.query({
        query: Me,
      });
      setUser(response.data?.authenticatedItem as User);
    })();
  }, []);

  const logoutUser = () => {
    logout();
    router.push('/login');
  };

  return (
    <div>
      <div className="fixed flex w-full items-center justify-between px-5 py-8">
        <ul className="flex items-end justify-end transition-all duration-300 ease-in-out">
          {navItems.map(({ link, icon: Icon, label }, index: number) => (
            <li key={index}>
              <Link
                href={link}
                className={cn(
                  'hover:text-primary group flex flex-row items-center justify-center gap-2 px-5 py-3 text-center text-base font-normal transition-all duration-200 ease-in-out hover:bg-[#dbeaf7]',
                  pathname?.startsWith(link)
                    ? 'bg-primary hover:bg-primary text-[white] hover:text-[white]'
                    : 'text-[#424242]',
                )}
              >
                <div className="flex flex-row items-center justify-center gap-2">
                  <Icon />
                  <span
                    className={cn(
                      'text-center transition-all duration-200 ease-in-out',
                      !pathname?.startsWith(link) && 'group-hover:text-primary text-[#3a3a3a]',
                    )}
                  >
                    {label}
                  </span>
                </div>
              </Link>
            </li>
          ))}
        </ul>
        <div>
          <div className="mr-20 flex cursor-pointer flex-row items-center justify-center gap-3">
            <Popover>
              <PopoverTrigger asChild>
                <Avatar className={cn('h-12 w-12 transition-all duration-300 ease-in-out')}>
                  <AvatarImage src="https://cdn3.iconfinder.com/data/icons/professions-1-4/132/31-1024.png" />
                  <AvatarFallback
                    className={cn(
                      'bg-gradient-to-br from-gray-400 to-gray-100 font-bold text-white',
                    )}
                  ></AvatarFallback>
                </Avatar>
              </PopoverTrigger>
              <PopoverContent className="w-[180px] rounded-lg !py-1">
                <Button
                  variant="ghost"
                  onClick={logoutUser}
                  className="w-full font-bold text-zinc-700"
                >
                  Logout
                </Button>
              </PopoverContent>
            </Popover>

            <div className="text-center text-sm font-bold text-[#495057] 2xl:text-base">
              {/* {user?.name + ' ' + user?.lastName} */}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-screen mt-[50px] inline-flex h-20 w-full items-center justify-start p-2 md:px-8 md:pt-2 2xl:pb-4 2xl:pt-4">
        {/* <div className='mb-24 sm:mb-0 inline-flex flex-col items-start justify-start grow shrink basis-0 z-10'>
          <div className='text-[#495057] text-lg 2xl:text-2xl font-bold leading-[36px] 2xl:leading-[48px]'>
            {headerData?.title}
          </div>
          <div className='text-[#495057] text-xs sm:text-sm 2xl:text-base font-normal'>
            {headerData?.description}
          </div>
        </div>
        <div className='flex flex-col items-end '>
          <div className='text-[#495057] text-xs sm:text-sm md:text-base lg:text-lg 2xl:text-2xl font-bold leading-[36px] 2xl:leading-[48px]'>
            {date.format('h:mm A')}
          </div>
          <div className='text-[#495057] text-xs sm:text-sm 2xl:text-base font-bold'>
            {date.format('dddd, MMMM D, YYYY')}
          </div>
        </div> */}
      </div>

      <Suspense>
        {pathname?.startsWith('/dashboard') && headerData?.submenu && (
          <Submenu items={headerData.submenu} />
        )}
      </Suspense>
    </div>
  );
};

export default Header;
