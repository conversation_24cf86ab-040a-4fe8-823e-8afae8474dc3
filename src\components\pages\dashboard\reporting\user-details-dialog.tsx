'use client';

import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CardContent } from '@/components/ui/card';
import { moneyFormat } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface UserDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    id: string;
    name: string;
    title: string;
    lastLogin: string;
    earnings: number;
  };
  merchants: {
    status: string;
  }[];
}

export const UserDetailsDialog = ({ isOpen, onClose, user, merchants }: UserDetailsDialogProps) => {
  const activeCount = merchants.filter((m) => m.status === 'active').length;
  const inactiveCount = merchants.filter((m) => m.status === 'inactive').length;

  // Calculate monthly earnings (for demo, we'll divide total by 12)
  const monthlyEarnings = user.earnings / 12;
  // Calculate average per merchant
  const averagePerMerchant = merchants.length > 0 ? user.earnings / merchants.length : 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Agent Details</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <CardContent>
            <div className="space-y-6">
              <div className="border-b pb-4">
                <h3 className="text-lg font-medium">{user.name}</h3>
                <p className="text-sm text-gray-500">{user.title}</p>
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div>
                  <p className="text-sm text-gray-500">Total Earnings</p>
                  <p className="text-lg font-medium text-blue-600">{moneyFormat(user.earnings)}</p>
                  <p className="text-xs text-gray-500">Lifetime earnings</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Monthly Average</p>
                  <p className="text-lg font-medium text-green-600">
                    {moneyFormat(monthlyEarnings)}
                  </p>
                  <p className="text-xs text-gray-500">Per month</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Per Business Average</p>
                  <p className="text-lg font-medium text-amber-600">
                    {moneyFormat(averagePerMerchant)}
                  </p>
                  <p className="text-xs text-gray-500">Per business</p>
                </div>
              </div>

              <div className="rounded-lg border p-6">
                <div className="mb-4 flex items-center justify-between">
                  <h4 className="font-medium">Business Accounts</h4>
                  <div className="space-x-2">
                    <Badge variant="outline" className="bg-green-50">
                      {activeCount} Active
                    </Badge>
                    <Badge variant="outline" className="bg-gray-50">
                      {inactiveCount} Inactive
                    </Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-4 gap-4 text-sm font-medium text-gray-500">
                    <div>Status</div>
                    <div className="col-span-2">Account Info</div>
                    <div className="text-right">Monthly Est.</div>
                  </div>
                  {merchants.map((merchant, index) => (
                    <div
                      key={index}
                      className="grid grid-cols-4 gap-4 rounded-lg border p-3 text-sm"
                    >
                      <div>
                        <Badge variant={merchant.status === 'active' ? 'default' : 'secondary'}>
                          {merchant.status}
                        </Badge>
                      </div>
                      <div className="col-span-2">
                        <p className="font-medium">Business {index + 1}</p>
                        <p className="text-xs text-gray-500">
                          {merchant.status === 'active' ? 'Currently processing' : 'Not processing'}
                        </p>
                      </div>
                      <div className="text-right font-medium">
                        {merchant.status === 'active'
                          ? moneyFormat(monthlyEarnings / activeCount)
                          : '—'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="rounded-lg border p-4 text-sm">
                <p className="mb-2 font-medium">Activity</p>
                <p className="text-gray-500">
                  Last active: {new Date(parseInt(user.lastLogin)).toLocaleDateString()} at{' '}
                  {new Date(parseInt(user.lastLogin)).toLocaleTimeString()}
                </p>
              </div>
            </div>
          </CardContent>
        </div>
      </DialogContent>
    </Dialog>
  );
};
