import useLocalStorage from '@/components/hooks/use-local-storage/useLocalStorage';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { documentTypes } from '@/consts/DocumentTypes';
import { manualReviewMCCs } from '@/lib/mccs_lowRisk';
import { cn } from '@/lib/utils';
import { Promisable } from '@/types/types';
import { Upload, X } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { MutableRefObject, useEffect, useMemo, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-toastify';
import { updateData } from '../updateData';
import styles from './index.module.css';

const DocumentBatch = (args: {
  category: string;
  oldFiles: any[];
  setOldFiles: any;
  toUploadFiles: { file: File; category: string }[];
  setToUploadFiles: any;
  setCategoryList?: any;
}) => {
  const { acceptedFiles, getRootProps, getInputProps, fileRejections } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.png', '.gif', '.bmp'],
      'application/pdf': ['.pdf'],
      'image/tiff': ['.tif', '.tiff'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
  });

  useEffect(() => {
    fileRejections.forEach(({ file, errors }) => {
      errors.forEach((error) => {
        toast.error(`File "${file.name}": ${error.message}`);
      });
    });
  }, [fileRejections]);

  const [storeRecommendedMCC] = useLocalStorage<{
    id: string;
    description: string;
    riskLevel: string;
  }>('RECOMMENDED_MCC');

  useEffect(() => {
    if (acceptedFiles.length) {
      args.setToUploadFiles((prev) =>
        prev.concat(
          acceptedFiles.map((file) => {
            return {
              file,
              category: args.category,
            };
          }),
        ),
      );
    }
  }, [acceptedFiles]);

  const onDelete = (index: number) => {
    if (index >= args.toUploadFiles.length) {
      // delete from old files
      args.setOldFiles((prev) => prev.filter((_, i) => i !== index - args.toUploadFiles.length));
      return;
    }

    args.setToUploadFiles((prev) => prev.filter((_, i) => i !== index));
    // setUploading(false);
  };

  const fileList = useMemo(() => {
    return [
      ...args.toUploadFiles.map((file) => ({
        name: file.file.name,
        data: file,
        size: file.file.size,
        category: file.category,
        new: true,
      })),
      ...(args.oldFiles.map((file: any) => ({
        name: file.name,
        data: file.b64,
        size: 0,
        new: false,
        category: file.category,
      })) ?? []),
    ];
  }, [args.toUploadFiles, args.oldFiles]);

  return (
    <div className="w-full max-w-[500px] self-center rounded-md border border-slate-200 p-3 md:p-6">
      <div className="mb-4 flex items-center">
        <div className="flex flex-1 flex-col gap-0.5">
          <p className="text-2xl font-bold">
            {args.category.replace('_', '')}
            {requiredDocuments.includes(args.category) && ' *'}
          </p>
          {args.category === 'Bank Statement' &&
            manualReviewMCCs.includes(storeRecommendedMCC?.id || '') && (
              <div className="text-xs text-muted-foreground">3 months of bank statements</div>
            )}
        </div>
        {!requiredDocuments.includes(args.category) && (
          <button
            className={cn('ml-4 text-sm text-red-500')}
            onClick={() => {
              args.setCategoryList((prev: string[]) => prev.filter((cat) => cat !== args.category));
              args.setOldFiles((prev: any[]) =>
                prev.filter((file) => file.category !== args.category),
              );
              args.setToUploadFiles((prev: any[]) =>
                prev.filter((file) => file.category !== args.category),
              );
            }}
          >
            <X />
          </button>
        )}
      </div>
      <div className="flex w-full gap-2">
        <div
          className={cn(
            'relative w-full rounded-lg border-2 border-dashed',
            'transition-colors hover:bg-muted/50',
            'flex flex-col items-center justify-center gap-2',
            // section.files.length > 0 && "border-muted bg-muted/30"
          )}
        >
          {(() => {
            const files = fileList.filter((file) => file.category === args.category);

            return files.length === 0 ? (
              <div className="m-6 flex flex-col items-center gap-3" {...getRootProps()}>
                <Upload className="h-8 w-8 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Drop files here or{' '}
                  <label className="text-primary cursor-pointer hover:underline">
                    browse
                    <input
                      multiple
                      {...getInputProps()}
                      // onChange={(e) => handleFileSelect(section.id, e)}
                    />
                  </label>
                </p>
                <p className="text-xs text-muted-foreground">
                  Accepted formats: PDF, TIF, TIFF, BMP, JPEG, GIF, PNG, DOC, DOCX
                </p>
              </div>
            ) : (
              <div className="w-full p-6">
                <div className="w-full space-y-2">
                  {files.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between rounded border bg-background p-2"
                    >
                      <span
                        className="cursor-pointer truncate text-sm"
                        // onClick={() => openFile(file.data)}
                      >
                        {file.name}
                      </span>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => {
                          onDelete(index);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <label className="text-primary cursor-pointer text-sm hover:underline">
                    Add more files
                    <input multiple {...getInputProps()} />
                  </label>
                </div>
              </div>
            );
          })()}
        </div>
      </div>
    </div>
  );
};

const requiredDocuments = ['Tax Registration', 'Photo Identification'];

const DocumentsPage = (args: {
  triggerSubmit?: MutableRefObject<() => Promisable<boolean>>;
  initialData?: any;
}) => {
  const [oldFiles, setOldFiles] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categoryList, setCategoryList] = useState<string[]>([]);

  const categoryOptions = useMemo(() => {
    return documentTypes
      .filter((type) => !categoryList.includes(type.label))
      .map((type) => type.label);
  }, [categoryList]);

  useEffect(() => {
    requiredDocuments.forEach((doc) => {
      if (!categoryList.includes(doc)) {
        setCategoryList((prev) => [doc, ...prev]);
      }
    });
  }, [categoryList]);

  const isLoaded = useRef(false);
  useEffect(() => {
    if (isLoaded.current) {
      return;
    }
    // console.log('initialData', args.initialData);
    if (args.initialData?.length > 0 && oldFiles.length === 0) {
      setOldFiles(args.initialData);

      // get unique categories of each file
      const categories = args.initialData?.map((file: any) => file.category ?? 'Unknown');
      const uniqueCategories = Array.from(new Set(categories));
      setCategoryList(uniqueCategories as string[]);

      isLoaded.current = true;
    }
  }, [args.initialData, oldFiles]);

  const [toUploadFiles, setToUploadFiles] = useState<
    {
      file: File;
      category: string;
    }[]
  >([]);
  const [uploading, setUploading] = useState(false);
  const groupID = useSearchParams()?.get('groupID');

  async function uploadFiles() {
    // console.log('uploading files', acceptedFiles);
    // console.log('groupID', groupID);

    if (!groupID) {
      throw new Error('Group ID is required');
    }

    setUploading(true);

    const doesRequiredFilesExist = requiredDocuments.every((doc) => {
      return [...toUploadFiles, ...oldFiles].some(({ category }) => category === doc);
    });

    if (!doesRequiredFilesExist) {
      toast.error('Please upload all required documents');
      setUploading(false);
      return false;
    }

    // const base64Files = await Promise.all(
    //   toUploadFiles
    //     .filter(({ category }) => category === 'Bank Statement')
    //     .map(async ({ file }) => {
    //       const base64 = await fileToBase64(file);
    //       return base64;
    //     }),
    // );

    // const validation = await validateDocuments(base64Files);
    // console.log('isValid', validation);

    // if (validation.error) {
    //   toast.error('Something went wrong while validating documents');
    //   setUploading(false);
    //   return false;
    // }

    // if (!validation.isValid) {
    //   toast.error('Bank Statements are not valid');
    //   setUploading(false);
    //   return false;
    // }

    return await updateData({
      submitType: 'document',
      data: {
        new: toUploadFiles,
        old: oldFiles ?? [],
      },
      groupID: groupID,
    });
  }

  useEffect(() => {
    if (args.triggerSubmit) {
      args.triggerSubmit.current = uploadFiles;
    }
  }, [args.triggerSubmit, groupID, oldFiles, toUploadFiles, categoryList]);

  return (
    <div className={cn(styles.documents, 'p-4')}>
      <div
        className={cn(
          styles.content,
          uploading ? 'pointer-events-none opacity-50' : 'pointer-events-auto opacity-100',
        )}
      >
        <div className={cn(styles.headerSm, 'w-full')}>
          <p className="text-2xl font-bold"></p>
          <div className={cn(styles.div, 'ml-auto font-medium italic')}>
            Select a document type to upload
          </div>
          <Select
            value={selectedCategory ?? undefined}
            onValueChange={(value) => {
              setSelectedCategory(value);
            }}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select Document Type" />
            </SelectTrigger>
            <SelectContent>
              {categoryOptions.map((category) => (
                <SelectItem key={category} value={category}>
                  {category.replace('_', ' ')}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <button
            className={styles.button}
            onClick={() => {
              if (!selectedCategory) {
                return;
              }
              setCategoryList((prev) => [...prev, selectedCategory]);
            }}
          >
            <b className={styles.mb}>+</b>
          </button>
        </div>
        <div className={'flex max-h-[50vh] w-full flex-col gap-4'}>
          {categoryList.map((category) => (
            <DocumentBatch
              key={category}
              category={category}
              oldFiles={oldFiles ?? []}
              toUploadFiles={toUploadFiles ?? []}
              setToUploadFiles={setToUploadFiles}
              setOldFiles={setOldFiles}
              setCategoryList={setCategoryList}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default DocumentsPage;
