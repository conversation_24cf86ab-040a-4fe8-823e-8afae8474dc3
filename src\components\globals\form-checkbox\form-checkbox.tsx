import { ChangeEvent } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label, Checkbox, Tooltip, CustomFlowbiteTheme } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
import { cn } from '@/lib/utils';

export type FormCheckboxProps = {
  name: string;
  id: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  flex?: number;
  visible?: boolean;
  readOnly?: boolean;
  className?: string;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  disabled?: boolean;
  defaultValue?: boolean;
  tabIndex?: number;
};

export const FormCheckbox = ({
  id,
  name,
  label,
  rules,
  flex,
  disabled,
  defaultValue,
  className,
  visible = true,
  readOnly = false,
  tabIndex,
  tooltip,
  onChangeCallback = (e: ChangeEvent<HTMLInputElement>) => {
    e;
  },
  ...props
}: FormCheckboxProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || false}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={cn('relative w-full', className)}>
          <div>
            <div className="relative flex items-center gap-2">
              <Checkbox
                id={id}
                ref={ref}
                checked={value}
                onChange={(e) => {
                  onChange(e);
                  onChangeCallback(e);
                }}
                onBlur={(e) => {
                  onChange(e);
                  onBlur();
                }}
                readOnly={readOnly}
                disabled={isDisabled()}
                tabIndex={tabIndex}
                color="primary"
                {...props}
              />
              {label && (
                <Label htmlFor={id} className="flex">
                  {label} {isRequired() && '*'}
                  {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
                </Label>
              )}
            </div>
            <HelperText color={invalid ? 'failure' : 'default'}>
              {invalid ? error?.message : ''}
            </HelperText>
          </div>
        </div>
      )}
    />
  );
};

export default FormCheckbox;
