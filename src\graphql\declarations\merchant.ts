// import { graphql } from '../generated';

// export const CreateGroup = graphql(`
//   mutation CreateMerchant($data: MerchantCreateInput!) {
//     createMerchant(data: $data) {
//       id
//     }
//   }
// `);

// export const GetMerchant = graphql(`
//   query GetMerchant($where: MerchantWhereUniqueInput!) {
//     merchant(where: $where) {
//       id
//       name
//       mainProcessor
//       processorStatus
//       processorAUR {
//         id
//         applicationID
//         applicationNumber
//         signingUrl
//         applicationStatus {
//           applicationId
//           createdAt
//           applicationStatus
//           mid
//           applicationStatusName
//           achProcessorData {
//             achqBoardingStatus
//             monthlyVolumeAch
//             monthlyVolumeAchPayouts
//             highTicketAch
//             highTicketAchPayouts
//             ticketCountAch
//             ticketCountAchPayouts
//             customerType
//             paymentAuthorizationType
//             statementDescriptor
//             useOfPaymentServiceDescription
//             billingRouting
//             billingBank
//             billingAccount
//             billingAccountName
//           }
//           isLegalAddressTheSame
//           isPrimaryContactTheSame
//           achProcessor
//         }
//       }
//     }
//   }
// `);
