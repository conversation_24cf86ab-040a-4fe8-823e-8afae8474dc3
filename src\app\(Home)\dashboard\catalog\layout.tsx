'use client';
import { TabLinks } from '@/components/globals';
import { CatalogPages } from '@/components/globals/Headerv3';
import { usePathname } from 'next/navigation';
import React from 'react';

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  return (
    <div className="relative">
      <div className="absolute left-0 right-0 top-0 z-10 bg-yellow-100 p-2 text-center text-sm text-yellow-800">
        The catalog section is temporarily disabled.
      </div>
      <div className="pt-8">
        <TabLinks items={CatalogPages} />
        <div className="mx-4">{children}</div>
      </div>
    </div>
  );
}
