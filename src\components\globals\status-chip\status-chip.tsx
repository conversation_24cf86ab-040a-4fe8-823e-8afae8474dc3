import { Badge } from 'flowbite-react';
import clsx from 'clsx';

export type Variant = 'success' | 'danger' | 'warning' | 'neutral' | 'info';

export type StatusChipProps = {
  /* Tell what color the chip */
  variant?: Variant;
  /* Tell if full width */
  fullWidth?: boolean;
  /* Tell big chip and uppercase */
  big?: boolean;
  /* Tell the label displayed */
  label: string;
};

const statusStyles: Record<Variant, string> = {
  success: 'bg-green-100 text-green-900',
  danger: 'bg-red-100 text-red-900',
  warning: 'bg-yellow-100 text-yellow-900',
  neutral: 'bg-gray-300 text-gray-900',
  info: 'bg-blue-100 text-blue-900',
};

export const StatusChip = ({
  variant = 'neutral',
  fullWidth,
  big,
  label,
}: StatusChipProps): JSX.Element => {
  return (
    <Badge
      // color="gray"
      className={clsx(
        statusStyles[variant],
        'flex items-center justify-center font-medium',
        fullWidth ? 'w-full' : 'w-auto',
        big ? 'rounded-lg px-4 py-2 text-sm uppercase' : 'rounded-md px-2 py-1 text-xs',
      )}
      aria-label="status indicator"
    >
      {label}
    </Badge>
  );
};

export default StatusChip;
