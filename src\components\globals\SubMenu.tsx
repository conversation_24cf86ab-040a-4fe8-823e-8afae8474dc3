import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';

const Submenu = ({ items }: any) => {
  const currentPath = usePathname();
  const searchParams = useSearchParams();
  const search = searchParams?.get('view');
  return (
    <div className="px- hidden w-full items-start gap-4 md:flex">
      {items.map(({ label, href }: { label: string; href: string }) => (
        <Link key={href} href={href}>
          <div
            className={`flex items-center justify-center gap-2.5 self-stretch px-6 py-2 text-center text-sm 2xl:text-base ${
              (
                search
                  ? currentPath + '?view=' + ('calendar' === href || 'table' === href)
                  : currentPath === href
              )
                ? 'border-primary text-primary border-b-2 font-bold'
                : 'text-[#495057]'
            }`}
          >
            {label}
          </div>
        </Link>
      ))}
    </div>
  );
};

export default Submenu;
