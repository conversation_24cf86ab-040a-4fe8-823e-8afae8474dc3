import { FormSelect } from '@/components/globals';
import { CountryList, getStateList } from '@/data/countries';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

export function CountryForm(props: { countryKey: string; message: { requiredField: string } }) {
  const countryList = useMemo(() => {
    return CountryList();
  }, []);

  return (
    <>
      <FormSelect
        id={props.countryKey}
        name={props.countryKey}
        label="Country"
        options={countryList}
        rules={{ required: props.message.requiredField }}
      />
    </>
  );
}

export function StateForm(props: {
  stateKey: string;
  countryKey: string;
  message: { requiredField: string };
  methods: UseFormReturn<any>;
}) {
  const stateList = useMemo(() => {
    const stateList = getStateList(props.methods.watch(props.countryKey));
    return stateList;
  }, [props.methods.watch(props.countryKey)]);

  return (
    <>
      <FormSelect
        id={props.stateKey}
        name={props.stateKey}
        label="State/Province"
        rules={{ required: props.message.requiredField }}
        options={stateList}
      />
    </>
  );
}

export default function StateCountryForms(props: {
  stateKey: string;
  countryKey: string;
  methods: UseFormReturn<any>;
  disableFields?: boolean;
  message: { requiredField: string };
}) {
  const stateList = useMemo(() => {
    const stateList = getStateList(props.methods.watch(props.countryKey));
    return stateList;
  }, [props.methods.watch(props.countryKey)]);

  const countryList = useMemo(() => {
    return CountryList();
  }, []);

  return (
    <>
      <FormSelect
        id={props.countryKey}
        name={props.countryKey}
        label="Country"
        disabled={props.disableFields}
        rules={{ required: props.message.requiredField }}
        options={countryList}
      />
      <FormSelect
        id={props.stateKey}
        name={props.stateKey}
        label="State/Province"
        disabled={props.disableFields}
        rules={{ required: props.message.requiredField }}
        options={stateList}
      />
    </>
  );
}
