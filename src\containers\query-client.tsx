'use client';

import {
  QueryClient,
  QueryClientProvider as QueryClientProviderComponent,
} from '@tanstack/react-query';
import React from 'react';

const client = new QueryClient();

const QueryClientProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <QueryClientProviderComponent client={client}>{children}</QueryClientProviderComponent>
    </>
  );
};

export default QueryClientProvider;
