import { Building2, Mail, Phone, Calendar, CircleDollarSign } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { mockAccountData } from '@/mock/account-status-data';
import { BaseAccountModal } from './BaseAccountModal';
import { InfoItem } from './InfoItem';
import { Clock } from 'lucide-react';

interface DraftStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinueEditing: () => void;
}

export const DraftStatusModal = ({ isOpen, onClose, onContinueEditing }: DraftStatusModalProps) => {
  const data = mockAccountData.draft;

  return (
    <BaseAccountModal
      isOpen={isOpen}
      onClose={onClose}
      title="Draft Application"
      accountData={data}
      statusIcon={Clock}
      statusColor="text-gray-500"
    >
      {/* Business Information */}
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Business Information</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <InfoItem icon={Building2} label="Legal Name" value={data.legalBusinessName} />
          <InfoItem icon={Building2} label="DBA Name" value={data.dbaName} />
          <InfoItem icon={Mail} label="Email" value={data.businessEmail} />
          <InfoItem icon={Phone} label="Phone" value={data.businessPhone} />
          <InfoItem icon={Calendar} label="Date Established" value={data.dateBusinessEstablished} />
        </div>
      </Card>

      {/* Transaction Information */}
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Transaction Information</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <InfoItem
            icon={CircleDollarSign}
            label="Average Transaction"
            value={data.transactionInfo.avgTransactionAmount}
          />
          <InfoItem
            icon={CircleDollarSign}
            label="Monthly Volume"
            value={data.transactionInfo.monthlyVolume}
          />
          <InfoItem
            icon={Building2}
            label="Business Category"
            value={data.transactionInfo.businessCategory}
          />
        </div>
      </Card>

      {/* Document Status */}
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Required Documents</h3>
        <div className="flex items-center gap-2">
          <div className="h-2 flex-1 rounded-full bg-gray-200">
            <div
              className="h-full rounded-full bg-blue-600"
              style={{ width: `${(data.documentsSubmitted / data.documentsRequired) * 100}%` }}
            />
          </div>
          <span className="text-sm text-gray-600">
            {data.documentsSubmitted} of {data.documentsRequired} submitted
          </span>
        </div>
      </Card>

      <div className="mt-4 flex justify-end">
        <Button variant="primary" onClick={onContinueEditing}>
          Continue Editing
        </Button>
      </div>
    </BaseAccountModal>
  );
};
