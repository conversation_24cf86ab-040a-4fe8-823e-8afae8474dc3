'use client';

import { Me } from '@/graphql/declarations/me';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { Avatar, Dropdown, Navbar } from 'flowbite-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

let Links = [
  { name: 'Dashboard', href: '/dashboard' },
  { name: 'Reporting', href: '/dashboard/reporting' },
  { name: 'Customers', href: '/dashboard/customers' },
  { name: 'Catalog', href: '/dashboard/catalog' },
  { name: 'Admin', href: '/dashboard/admin' },
  { name: 'Accounts', href: '/dashboard/accounts' },
  { name: 'Support', href: '/dashboard/support' },
];

export default function Header() {
  const nav = usePathname();

  const logout = async () => {
    AUTHSTORE.clear();
    apolloClient.resetStore();
    window.location.href = '/login';
  };

  const { data } = useQuery(Me);

  return (
    <Navbar fluid rounded className="sticky left-0 top-0 z-50 mx-auto w-full bg-white shadow">
      <div className="flex gap-2">
        <Navbar.Brand>
          <Image
            src="/logo.webp"
            width={150}
            height={100}
            className="mr-3 h-6 sm:h-9"
            alt="NGnair Logo"
          />
        </Navbar.Brand>
        <Navbar.Toggle />
        <Navbar.Collapse className="mr-10 content-center">
          {Links.map((link) => (
            <Navbar.Link
              as={Link}
              href={link.href}
              key={link.name}
              active={nav === link.href || (nav === '/dashboard' && link.href === '/dashboard')}
            >
              {link.name}
            </Navbar.Link>
          ))}
        </Navbar.Collapse>
      </div>

      <div className="flex md:order-2">
        <Dropdown
          arrowIcon={false}
          inline
          label={
            <Avatar
              alt="User settings"
              img="https://flowbite.com/docs/images/people/profile-picture-5.jpg"
              rounded
            />
          }
        >
          <Dropdown.Header>
            <span className="block text-sm">{data?.authenticatedItem?.displayName}</span>
            <span className="block truncate text-sm font-medium">
              {data?.authenticatedItem?.email}
            </span>
          </Dropdown.Header>
          <Dropdown.Divider />
          <Dropdown.Item
            onClick={() => {
              logout();
            }}
          >
            Sign out
          </Dropdown.Item>
        </Dropdown>
      </div>
    </Navbar>
  );
}
