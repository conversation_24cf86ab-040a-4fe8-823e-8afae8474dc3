'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';

// This would typically be in a separate file, but for this example, we'll keep it here
async function submitTicket(formData: FormData) {
  // In a real application, you would process the form data here
  // For this example, we'll just log it and return a success message
  console.log('Ticket submitted:', Object.fromEntries(formData));
  return { success: true, message: 'Ticket submitted successfully!' };
}

export default function SupportTicketPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [submitStatus, setSubmitStatus] = useState<{ success?: boolean; message?: string } | null>(
    null,
  );

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files));
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    // Append each file to the FormData
    files.forEach((file, index) => {
      formData.append(`file-${index}`, file);
    });

    const result = await submitTicket(formData);
    setSubmitStatus(result);
  };

  return (
    <div className="container mx-auto p-4">
      <Card className="mx-auto w-full max-w-2xl">
        <CardHeader>
          <CardTitle>Submit a Support Ticket</CardTitle>
          <CardDescription>Please provide details about your issue</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                name="title"
                placeholder="Brief description of the issue"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Please provide more details about your issue"
                required
                className="min-h-[100px]"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="files">Supporting Files</Label>
              <Input
                id="files"
                name="files"
                type="file"
                multiple
                onChange={handleFileChange}
                className="cursor-pointer"
              />
              {files.length > 0 && (
                <div className="text-sm text-muted-foreground">{files.length} file(s) selected</div>
              )}
            </div>
            <Button type="submit" className="w-full">
              Submit Ticket
            </Button>
          </form>
        </CardContent>
        <CardFooter>
          {submitStatus && (
            <div
              className={`w-full text-center ${submitStatus.success ? 'text-green-600' : 'text-red-600'}`}
            >
              {submitStatus.message}
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
