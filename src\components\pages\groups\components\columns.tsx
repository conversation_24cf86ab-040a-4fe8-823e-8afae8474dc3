'use client';

import { useMemo } from 'react';

import useUIStore from '@/store/main';

export const useTableColumns = (togglePlay: any, setPauseOpen: any, setActionData: any) => {
  const { isSidebarCollapsed, toggleSidebar } = useUIStore();

  const columns = useMemo(() => {
    const baseColumns = [
      {
        accessorKey: 'group_name',
        title: 'Group Name',
        cell: (info: any) => {
          return ' - ';
        },
      },
      {
        accessorKey: 'number_of_merchants',
        title: 'Merchant Counts',
        cell: (info: any) => {
          return ' - ';
        },
      },
      {
        accessorKey: 'role',
        title: 'Role',
        cell: (info: any) => {
          return ' - ';
        },
      },
    ];

    return baseColumns;
  }, [isSidebarCollapsed]);

  return columns;
};
