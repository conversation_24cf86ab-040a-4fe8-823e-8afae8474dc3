import { graphql } from '../generated';

export const getZipCode = graphql(`
  query Processor_aur_zip_search($input: Processor_aur_zip_searchInput!) {
    processor_aur_zip_search(input: $input) {
      item {
        zip
        latitude
        longitude
        city
        state
        country
      }
    }
  }
`);

export const getBankRouting = graphql(`
  query Processor_aur_bank_routing($input: Processor_aur_bank_routingInput!) {
    processor_aur_bank_routing(input: $input) {
      items {
        routing
        bank
      }
      count
    }
  }
`);

export const getMCC = graphql(`
  query Processor_aur_mcc($input: Processor_aur_mccInput!) {
    processor_aur_mcc(input: $input) {
      items {
        id
        mccCode
        description
      }
      count
    }
  }
`);
