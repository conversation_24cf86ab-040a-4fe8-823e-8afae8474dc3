const withSvgr = require('next-svgr');
const CopyPlugin = require('copy-webpack-plugin');

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  reactStrictMode: false,
  images: {
    domains: ['flowbite.s3.amazonaws.com', 'localhost', ''],
  },
  experimental: {
    serverActions: {
      bodySizeLimit: '10mb',
    },
  },

  env: {
    NEXT_PUBLIC_DEMO: process.env.NEXT_PUBLIC_DEMO,
    NEXT_PUBLIC_MOCK_MODE: process.env.NEXT_PUBLIC_MOCK_MODE,
    NEXT_PUBLIC_COMING_SOON_DASHBOARD: process.env.NEXT_PUBLIC_COMING_SOON_DASHBOARD,
    NEXT_PUBLIC_COMING_SOON_TRANSACTIONS: process.env.NEXT_PUBLIC_COMING_SOON_TRANSACTIONS,
    NEXT_PUBLIC_COMING_SOON_CUSTOMERS: process.env.NEXT_PUBLIC_COMING_SOON_CUSTOMERS,
    NEXT_PUBLIC_SIGNUP_CUSTOM_URL: process.env.NEXT_PUBLIC_SIGNUP_CUSTOM_URL,
  },

  webpack: (config) => {
    // append the CopyPlugin to copy the file to your public dir
    config.plugins.push(
      new CopyPlugin({
        patterns: [{ from: 'node_modules/flowbite/dist/', to: 'static/chunks/public/flowbite/' }],
      }),
    );

    // Important: return the modified config
    return config;
  },
};

module.exports = withSvgr(nextConfig);
