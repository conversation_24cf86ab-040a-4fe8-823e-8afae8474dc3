// Mock data for deposits
export interface MockDepositData {
  depositID: string;
  location: string;
  date: string; // Date only, no time
  amount: number;
  status: string;
  depositAccount: string; // 4 random digits of account
  feeBreakdown: {
    interchange: number;
    processorFee: number;
    platformFee: number; // ngnair fee
    partnerRate: number; // depends on what they set
  };
  batches: Array<{
    batchID: string;
    location: string;
    total: number;
  }>;
  transactions?: Array<{
    transactionID: string;
    date: string;
    method: string;
    name: string;
    last4: string;
    customer: string;
    amount: number;
    brand: string;
    status: string;
  }>;
}

// Generate mock deposit data
const generateMockDeposit = (index: number): MockDepositData => {
  const depositStatuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'];
  const locations = ['NYC Store', 'LA Store', 'Chicago Store', 'Miami Store', 'Seattle Store'];
  const paymentMethods = ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'];

  const depositDate = new Date();
  depositDate.setDate(depositDate.getDate() - Math.floor(Math.random() * 30));

  const batchCount = Math.floor(Math.random() * 3) + 1;
  const batches = Array(batchCount)
    .fill(null)
    .map((_, batchIndex) => {
      const batchTotal = Math.floor(Math.random() * 100000) + 10000; // $100 to $1000
      return {
        batchID: `BATCH-${8000 + index * 10 + batchIndex}`,
        location: locations[Math.floor(Math.random() * locations.length)],
        total: batchTotal,
      };
    });

  const transactionCount = Math.floor(Math.random() * 15) + 5;
  const transactions = Array(transactionCount)
    .fill(null)
    .map((_, txIndex) => {
      const txDate = new Date(depositDate);
      txDate.setHours(txDate.getHours() - Math.floor(Math.random() * 24));

      return {
        transactionID: `TXN-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        date: txDate.toISOString(),
        method: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
        name: `Customer ${Math.floor(Math.random() * 1000)}`,
        last4: Math.floor(1000 + Math.random() * 9000).toString(),
        customer: `Customer ${Math.floor(Math.random() * 1000)}`,
        amount: Math.floor(Math.random() * 50000) + 1000, // $10 to $500
        brand: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
        status: 'COMPLETED',
      };
    });

  const totalSales = transactions.reduce((sum, tx) => sum + tx.amount, 0);
  const fees = Math.floor(totalSales * 0.029); // 2.9% fee
  const totalDeposit = totalSales - fees;

  return {
    depositID: `DEP-${(5000 + index).toString()}`,
    location: locations[Math.floor(Math.random() * locations.length)],
    date: depositDate.toISOString().split('T')[0], // Date only, no time
    amount: totalDeposit,
    status: depositStatuses[Math.floor(Math.random() * depositStatuses.length)],
    depositAccount: Math.floor(1000 + Math.random() * 9000).toString(), // 4 random digits
    feeBreakdown: {
      interchange: Math.floor(fees * 0.7), // 70% of fees
      processorFee: Math.floor(fees * 0.2), // 20% of fees
      platformFee: Math.floor(fees * 0.08), // 8% of fees (ngnair fee)
      partnerRate: Math.floor(fees * 0.02), // 2% of fees (partner rate)
    },
    batches,
    transactions,
  };
};

// Create static mock deposits data
export const mockDeposits: MockDepositData[] = [
  {
    depositID: 'DEP-5002',
    location: 'LA Store',
    date: '2025-07-28', // Date only, no time
    amount: 5400, // $54.00 (5500 - 100 fees)
    status: 'DEPOSITED',
    depositAccount: '5678', // 4 random digits
    feeBreakdown: {
      interchange: 70, // $0.70
      processorFee: 20, // $0.20
      platformFee: 8, // $0.08 (ngnair fee)
      partnerRate: 2, // $0.02 (partner rate)
    },
    batches: [
      {
        batchID: 'BATCH-8002',
        location: 'LA Store',
        total: 5500, // Matches BATCH-8002 amount exactly
      },
    ],
    transactions: [
      {
        transactionID: 'P-840-2025-********',
        date: '2025-07-27T14:20:00Z',
        method: 'AMEX',
        name: 'Virtual Terminal',
        last4: '1234',
        customer: 'Virtual Terminal',
        amount: 185,
        brand: 'AMEX',
        status: 'DECLINE',
      },
      {
        transactionID: 'P-840-2025-********',
        date: '2025-07-27T15:45:00Z',
        method: 'VISA',
        name: 'Virtual Terminal',
        last4: '9876',
        customer: 'Virtual Terminal',
        amount: 320,
        brand: 'VISA',
        status: 'CAPTURED',
      },
    ],
  },
  {
    depositID: 'DEP-5003',
    location: 'Boston Store',
    date: '2025-07-26', // Date only, no time
    amount: 8350, // $83.50 (8500 - 150 fees)
    status: 'DEPOSITED',
    depositAccount: '9012', // 4 random digits
    feeBreakdown: {
      interchange: 120, // $1.20
      processorFee: 20, // $0.20
      platformFee: 8, // $0.08 (ngnair fee)
      partnerRate: 2, // $0.02 (partner rate)
    },
    batches: [
      {
        batchID: 'BATCH-8003',
        location: 'Boston Store',
        total: 8500, // Matches BATCH-8003 amount exactly
      },
    ],
    transactions: [
      {
        transactionID: 'P-840-2025-********',
        date: '2025-07-26T09:15:00Z',
        method: 'DISCOVER',
        name: 'Virtual Terminal',
        last4: '6789',
        customer: 'Virtual Terminal',
        amount: 672,
        brand: 'DISCOVER',
        status: 'CAPTURED',
      },
      {
        transactionID: 'P-840-2025-********',
        date: '2025-07-26T10:30:00Z',
        method: 'VISA',
        name: 'Virtual Terminal',
        last4: '3456',
        customer: 'Virtual Terminal',
        amount: 283,
        brand: 'VISA',
        status: 'REFUND',
      },
    ],
  },
];

// Generate additional mock deposits if needed
export const generateMockDeposits = (count: number = 10): MockDepositData[] => {
  return Array(count)
    .fill(null)
    .map((_, index) => generateMockDeposit(index + 6));
};
