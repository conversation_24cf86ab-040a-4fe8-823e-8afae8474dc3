'use client';

import { useState, useRef, useEffect } from 'react';
import mockSupportTickets from '@/mock/support-tickets-data';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  CreateGroupSupportTicketMessage,
  GroupSupportTicket,
} from '@/graphql/declarations/support';
import { useQuery, useMutation } from '@apollo/client';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { headingsPlugin } from '@mdxeditor/editor';
import { HtmlEditorMdx } from '@/components/globals/html-editor/html-editor-mdx';
import { uploadFileToB64Data } from '@/lib/fileUploader';
import HtmlEditorQuill from '@/components/globals/html-editor/html-editor-quill';
import { Paperclip } from 'lucide-react';
import { UpdateGroupSupportTicketDocument } from '@/graphql/generated/graphql';

// This would typically come from your database or API
interface Ticket {
  id: string;
  title: string;
  problem: string;
  status: string;
  priority: string;
  createdAt: string;
  supportingFiles: Array<{ name: string; url: string }>;
  chatHistory: Array<{
    id: string;
    sender: string;
    message: string;
    timestamp: string;
    files: Array<{
      url: string;
      filename: string;
    }>;
  }>;
}

export default function TicketPage() {
  const params = useParams();
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  if (!params?.id) {
    return <p>No ticket ID provided</p>;
  }

  return (
    <div className="container mx-auto max-w-4xl p-4">
      <Button variant="ghost" className="mb-4" onClick={handleBack}>
        ← Back
      </Button>
      <TicketDetails ticketId={params.id as string} />
    </div>
  );
}

function TicketDetails({ ticketId }: { ticketId: string }) {
  const [ticket, setTicket] = useState<Ticket | null>(null);
  const router = useRouter();

  const {
    data: groupSupportTicketData,
    loading,
    error,
  } = useQuery(GroupSupportTicket, {
    variables: { where: { id: ticketId } },
  });

  const [updateGroupSupportTicket] = useMutation(UpdateGroupSupportTicketDocument);

  // Mock data loading for demonstration
  useEffect(() => {
    const mockTicket = mockSupportTickets.find((t) => t.id === ticketId);
    if (mockTicket) {
      setTicket(mockTicket);
    }
  }, [ticketId]);

  useEffect(() => {
    const ticket = groupSupportTicketData?.groupSupportTicket;
    if (ticket) {
      setTicket({
        id: ticket.id,
        title: ticket.title || 'Untitled Ticket',
        problem: ticket.description || 'No description provided',
        status: ticket.status || 'Unknown',
        priority: ticket.category || 'MEDIUM', // Use category as priority since that's what's available
        createdAt: ticket.createdAt,
        supportingFiles: [],
        chatHistory:
          ticket.messages?.map((message, i) => ({
            id: message.id || `${i}`,
            sender: message.actualName || '',
            message: message.message || '',
            timestamp: message.createdAt || '',
            files:
              message.files?.map((file) => ({
                url: file.url || '',
                filename: file.filename || '',
              })) ?? [],
          })) ?? [],
      });
    }
  }, [groupSupportTicketData]);

  if (loading) {
    return <p>Loading...</p>;
  }

  if (!ticket) {
    return <></>;
  }

  const isResolved = ticket.status === 'RESOLVED';
  const isClosed = ticket.status === 'CLOSED';

  const handleStatusChange = async (newStatus: 'OPEN' | 'RESOLVED' | 'CLOSED') => {
    try {
      // In a real app, this would be an API call
      setTicket((prev) => (prev ? { ...prev, status: newStatus } : null));
    } catch (error) {
      console.error('Failed to update ticket status:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      HIGH: 'bg-red-100 text-red-800',
      MEDIUM: 'bg-yellow-100 text-yellow-800',
      LOW: 'bg-green-100 text-green-800',
    };
    return colors[priority] || colors.MEDIUM;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      OPEN: 'bg-blue-100 text-blue-800',
      IN_PROGRESS: 'bg-yellow-100 text-yellow-800',
      RESOLVED: 'bg-green-100 text-green-800',
      CLOSED: 'bg-gray-100 text-gray-800',
    };
    return colors[status] || colors.OPEN;
  };

  return (
    <div>
      <Card className="mx-auto w-full max-w-4xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <span className="text-lg font-medium">Ticket #{ticket.id}</span>
              <div className="flex gap-2">
                <span
                  className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(
                    ticket.status,
                  )}`}
                >
                  {ticket.status}
                </span>
                <span
                  className={`rounded-full px-2 py-1 text-xs font-medium ${getPriorityColor(
                    ticket.priority,
                  )}`}
                >
                  Priority: {ticket.priority}
                </span>
              </div>
            </div>
            {!isClosed && (
              <div className="flex gap-2">
                {ticket.status === 'OPEN' && (
                  <Button onClick={() => handleStatusChange('RESOLVED')} variant="primary">
                    Mark as Resolved
                  </Button>
                )}
                {ticket.status === 'RESOLVED' && (
                  <Button onClick={() => handleStatusChange('CLOSED')} variant="destructive">
                    Close Ticket
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardHeader>
        <div className="mx-4 rounded-lg bg-gray-50 px-4">
          <CardDescription className="flex items-center justify-between border-y py-3">
            <div>
              <div className="text-lg text-black">{ticket.title}</div>
              <div>Via email</div>
            </div>
            <div>{new Date(ticket.createdAt).toLocaleString()}</div>
          </CardDescription>
          <CardContent className="-mx-5">
            <div className="space-y-4">
              <div>
                <h3 className="-mx-1 my-2 text-base">Description</h3>
                <HtmlEditorMdx
                  className="-mx-4 min-h-48 text-sm"
                  markdown={ticket.problem}
                  plugins={[headingsPlugin()]}
                />
              </div>
              {ticket.supportingFiles.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold">Supporting Files</h3>
                  <ul className="mt-1 space-y-1">
                    {ticket.supportingFiles.map((file, index) => (
                      <li key={index}>
                        <a
                          href={file.url}
                          className="text-blue-600 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {file.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div className="px-2">
              <div>
                <ChatHistory chatHistory={ticket.chatHistory} />
              </div>
              <div className="bg-white">
                <ReplyForm ticketId={ticket.id} />
              </div>
            </div>
          </CardContent>
        </div>
      </Card>
    </div>
  );
}

function ChatHistory({ chatHistory }: { chatHistory: Ticket['chatHistory'] }) {
  if (chatHistory.length === 0) {
    return <></>;
  }

  return (
    <div className="w-full rounded-md p-4">
      {chatHistory
        .slice()
        // .reverse()
        .map((chat) => (
          <div key={chat.id} className="mb-4 border-t">
            <div className="flex items-center space-x-2">
              <Avatar>
                <AvatarFallback>{chat.sender === 'user' ? 'U' : 'S'}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="font-semibold">{chat.sender}</p>
                <p className="text-xs text-gray-500">{new Date(chat.timestamp).toLocaleString()}</p>
              </div>
            </div>
            <div className="mt-2 rounded-lg bg-gray-100 px-2">
              {/* <p className="text-sm">{chat.message}</p> */}
              <HtmlEditorMdx
                markdown={chat.message}
                className="text-sm"
                // className="h-48"
                // contentEditableClassName="min-h-48"
                plugins={[headingsPlugin()]}
              />
              {chat.files.length > 0 && (
                <ul className="mt-1 space-y-1 p-2">
                  {chat.files.map((file, index) => (
                    <li key={index}>
                      <a
                        href={file.url}
                        className="text-blue-600 hover:underline"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {file.filename}
                      </a>
                    </li>
                  ))}
                </ul>
              )}
              {/* {<p>{JSON.stringify(chat.files)}</p>} */}
            </div>
          </div>
        ))}
    </div>
  );
}

function ReplyForm({ ticketId }: { ticketId: string }) {
  const [reply, setReply] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [fileName, setFileName] = useState(null);

  const router = useRouter();

  // This would be a server action in a real application
  async function addReply(ticketId: string, message: string, files: FileList | null) {
    // Simulate API call
    // await new Promise((resolve) => setTimeout(resolve, 1000));
    // console.log(`Adding reply to ticket ${ticketId}: ${message}`);
    let fileUploads: {
      filename: string;
      url: string;
    }[] = [];
    if (files) {
      let fileList = Array.from(files);
      const file = await uploadFileToB64Data(
        fileList.map((f) => ({
          file: f,
          mimetype: f.type,
          name: f.name,
        })),
      );
      if (file) {
        fileUploads = file.map((f) => ({
          url: f?.url!,
          filename: f?.filename!,
        }));
      }
    }

    const d = await apolloClient.mutate({
      mutation: CreateGroupSupportTicketMessage,
      variables: {
        data: {
          ticket: {
            connect: {
              id: ticketId,
            },
          },
          message: message,
          files: fileUploads,
        },
      },
    });

    await apolloClient.refetchQueries({
      include: ['GroupSupportTicket'],
    });

    return { success: true };
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!reply.trim()) return;

    const files = fileInputRef.current?.files;
    // if (!files) {
    //   alert('Please select at least one file to upload.');
    //   return;
    // }

    setIsSubmitting(true);
    try {
      await addReply(ticketId, reply, files || null);
      setReply('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      router.refresh(); // This will trigger a re-render with the updated chat history
    } catch (error) {
      console.error('Failed to send reply:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileInputClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click(); // Trigger file input when icon is clicked
    }
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0]; // Get the first selected file
    if (file) {
      setFileName(file.name); // Update the file name state
    }
  };
  return (
    <form
      onSubmit={handleSubmit}
      className="w-full"
      style={{
        opacity: isSubmitting ? 0.5 : 1,
      }}
    >
      {/* <Textarea
        value={reply}
        onChange={(e) => setReply(e.target.value)}
        placeholder="Type your reply here..."
        className="mb-2 w-full"
      /> */}
      {!isSubmitting && (
        <HtmlEditorQuill
          value={reply}
          setValue={(e) => setReply(e)}
          readOnly={isSubmitting}
          className="mb-4 h-32 border-t shadow-md"
        />
      )}
      {/* <HtmlEditorMdx
        // value={reply}
        markdown={reply}
        onChange={(e) => setReply(e)}
        readOnly={isSubmitting}
        // className="h-48"
        contentEditableClassName="h-48 border shadow-md p-2 mb-4"
        plugins={[
          headingsPlugin(),
          // imagePlugin({
          //   imageUploadHandler: (d) => {
          //     return Promise.resolve('https://picsum.photos/200/300');
          //   },
          // }),
          toolbarPlugin({
            toolbarClassName: 'my-classname',
            toolbarContents: () => (
              <>
                {' '}
                <UndoRedo />
                <BoldItalicUnderlineToggles />
                <InsertImage />
              </>
            ),
          }),
        ]}
      /> */}
      <div className="flex items-center gap-4 p-4">
        <Button type="submit" disabled={isSubmitting} variant="primary">
          {isSubmitting ? 'Sending...' : 'Send Reply'}
        </Button>

        <input
          type="file"
          ref={fileInputRef}
          disabled={isSubmitting}
          multiple
          className="hidden"
          onChange={handleFileChange} // Handle file selection
        />
        <div onClick={handleFileInputClick} className="cursor-pointer hover:text-blue-700">
          <Paperclip />
        </div>
        {fileName && <span className="text-gray-600">{fileName}</span>}
      </div>
    </form>
  );
}
