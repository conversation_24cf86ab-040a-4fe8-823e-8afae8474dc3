'use client';

import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useEffect, useState } from 'react';
import { MerchantForm } from '../types/merchart-schema';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { getMCC } from '@/graphql/declarations/formUtilities';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Search } from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import * as PopoverPrimitive from '@radix-ui/react-popover';
import { Button } from '@/components/ui/button';
import { CaretSortIcon } from '@radix-ui/react-icons';

const Transactions = ({ formState }: { formState: MerchantForm }) => {
  const [checkingZipCode, setCheckingZipCode] = useState(false);

  const { setValue, register, watch, formState: state, setError, trigger, clearErrors } = formState;
  const values = watch();

  const { errors } = state;

  useEffect(() => {
    (() => {
      if (!values.swipe || !values.keyed || !values.ecommerce) return;
      const isNotSqual =
        Number(values.swipe) + Number(values.keyed) + Number(values.ecommerce) !== 100;

      if (isNotSqual) {
        setError('swipe', { message: 'Must equal to 100' });
        setError('keyed', { message: 'Must equal to 100' });
        setError('ecommerce', { message: 'Must equal to 100' });
      } else {
        clearErrors('swipe');
        clearErrors('keyed');
        clearErrors('ecommerce');
      }
    })();
  }, [values.swipe, values.keyed, values.ecommerce]);

  const [mmcSearch, setMccSearch] = useState('');
  const [typeOfBusiness, setTypeOfBusiness] = useState<
    {
      description: string;
      mccCode: string;
    }[]
  >([]);

  useEffect(() => {
    (async () => {
      const response = await apolloClient.query({
        query: getMCC,
        variables: {
          input: {
            pattern: mmcSearch,
          },
        },
      });

      setTypeOfBusiness(
        response?.data?.processor_aur_mcc?.items as {
          description: string;
          mccCode: string;
          id: string;
        }[],
      );
    })();
  }, [mmcSearch]);

  return (
    <div className="flex flex-col gap-4">
      <FormField
        control={formState.control}
        name="business_catergory"
        render={({ field }) => (
          <FormItem className="relative flex flex-1 flex-col">
            <Label>
              Business Category<span className="text-red-400">*</span>
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="select-outline"
                    role="combobox"
                    className={cn(!field.value && 'text-gray-500')}
                  >
                    {values?.mccObject?.description ?? 'Select Business Type'}
                    <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="p-0">
                <Command>
                  <div className="relative h-fit w-full pt-5">
                    <Search className="absolute bottom-3 left-4 h-4 w-4 shrink-0 opacity-50" />
                    <Input
                      onChange={(e) => {
                        setMccSearch(e.target.value);
                      }}
                      type="search"
                      placeholder="Search Business Type..."
                      className="rounded-md border-l-transparent border-r-transparent border-t-transparent text-zinc-700"
                    />
                  </div>
                  <CommandList>
                    <CommandEmpty>No found.</CommandEmpty>
                    <CommandGroup>
                      {typeOfBusiness.map((type: any, index: number) => (
                        <PopoverPrimitive.Close key={index} className="w-full text-left">
                          <CommandItem
                            value={type.id}
                            key={type.id}
                            className="text-zinc-700 hover:!bg-gray-100"
                            onSelect={() => {
                              formState.setValue('mccObject', type, {
                                shouldValidate: true,
                              });
                              setMccSearch('');
                            }}
                          >
                            <div className="w-full">{type?.description}</div>
                          </CommandItem>
                        </PopoverPrimitive.Close>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </FormItem>
        )}
      />

      <FormField
        control={formState.control}
        name="description_of_what_you_sell"
        render={({ field }) => (
          <FormItem className="mt-1">
            <Label>
              Description of what you sell
              <span className="text-red-400">*</span>
            </Label>

            <Textarea
              {...field}
              onChange={(e) =>
                setValue('description_of_what_you_sell', e.target.value, {
                  shouldValidate: true,
                })
              }
              className={cn(
                errors.description_of_what_you_sell &&
                  'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
              )}
              placeholder="Type description here."
              id="message"
            />
          </FormItem>
        )}
      />
      <div className="flex flex-col gap-2">
        <Label className="text-base font-bold text-zinc-600">
          How do you accept card? &#40;Must equal 100%&#41;
        </Label>
        <div className="flex w-full flex-col gap-3">
          <div className="flex flex-row gap-4">
            <FormField
              control={formState.control}
              name="swipe"
              render={({ field }) => (
                <FormItem className="mt-1 flex-1">
                  <Label>
                    Swipe<span className="text-red-400">*</span> &#40;Card Present&#41;:
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      type="number"
                      {...field}
                      placeholder="%"
                      className={cn(
                        errors.swipe &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={formState.control}
              name="keyed"
              render={({ field }) => (
                <FormItem className="mt-1 flex-1">
                  <Label>
                    Swipe<span className="text-red-400">*</span> &#40;Manual Entry&#41;:
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      type="number"
                      {...field}
                      placeholder="%"
                      className={cn(
                        errors.keyed &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={formState.control}
              name="ecommerce"
              render={({ field }) => (
                <FormItem className="mt-1 flex-1">
                  <Label>
                    Ecommerce<span className="text-red-400">*</span> &#40;Card Not Present&#41;:
                  </Label>
                  <FormControl className="mt-2">
                    <Input
                      type="number"
                      {...field}
                      placeholder="%"
                      className={cn(
                        errors.ecommerce &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Label className="text-base font-bold text-zinc-600">Visa / Mastercard / Discover</Label>
        <div className="flex w-full flex-row gap-4">
          <FormField
            control={formState.control}
            name="vmd_average_transaction_amount"
            render={({ field }) => (
              <FormItem className="mt-1 flex-1">
                <Label>Average Transaction Amount</Label>
                <FormControl className="mt-1 flex-1">
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      setValue('vmd_average_transaction_amount', e.target.value as string, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="$"
                    className={cn(
                      errors.vmd_average_transaction_amount &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={formState.control}
            name="vmd_highest_transaction_amount"
            render={({ field }) => (
              <FormItem className="mt-1 flex-1">
                <Label>Highest Transaction Amount</Label>
                <FormControl className="mt-2">
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      setValue('vmd_highest_transaction_amount', e.target.value as string, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="%"
                    className={cn(
                      errors.vmd_highest_transaction_amount &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={formState.control}
            name="vmd_gross_monthly_sales_volume"
            render={({ field }) => (
              <FormItem className="mt-1 flex-1">
                <Label>Gross Montly Sales Volume</Label>
                <FormControl className="mt-2">
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      setValue('vmd_gross_monthly_sales_volume', e.target.value as string, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="%"
                    className={cn(
                      errors.vmd_gross_monthly_sales_volume &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <Label className="text-base font-bold text-zinc-600">American Express &#40;AMEX&#41;</Label>
        <div className="flex w-full flex-row gap-4">
          <FormField
            control={formState.control}
            name="amex_average_transaction_amount"
            render={({ field }) => (
              <FormItem className="mt-1 flex-1">
                <Label>Average Transaction Amount</Label>
                <FormControl className="mt-2">
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      setValue('amex_average_transaction_amount', e.target.value as string, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="$"
                    className={cn(
                      errors.amex_average_transaction_amount &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={formState.control}
            name="amex_highest_transaction_amount"
            render={({ field }) => (
              <FormItem className="mt-1 flex-1">
                <Label>Highest Transaction Amount</Label>
                <FormControl className="mt-2">
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      setValue('amex_highest_transaction_amount', e.target.value as string, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="%"
                    className={cn(
                      errors.amex_highest_transaction_amount &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={formState.control}
            name="amex_gross_monthly_sales_volume"
            render={({ field }) => (
              <FormItem className="mt-1 flex-1">
                <Label>Gross Montly Sales Volume</Label>
                <FormControl className="mt-2">
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) =>
                      setValue('amex_gross_monthly_sales_volume', e.target.value as string, {
                        shouldValidate: true,
                      })
                    }
                    placeholder="%"
                    className={cn(
                      errors.amex_gross_monthly_sales_volume &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default Transactions;
