import { graphql } from '../generated';

export const ComputeCheckoutUNI = graphql(`
  mutation Gateway_computeCheckout($input: Gateway_computeCheckoutInput!) {
    gateway_computeCheckout(input: $input) {
      breakdown {
        discount
        directDiscount
        actualDiscount
        tax
        shipping
        shippingDiscount
        shippingDirectDiscount
        shippingActualDiscount
        fees
        actualFees
        tip
        subtotal
        subscriptionTotal
        rawTotal
        total
        expectedTotal
      }
      lineItems {
        productId
        product {
          sku
          name
          description
          price
          isRecurring
          recurringMode
          recurringInterval
          recurringFrequency
          recurringTotalCycles
          recurringTrialDays
          recurringSetupFee
          recurringRefundable
        }
        amount
        total
        metadata
      }
      discountBreakdown {
        code
        amount
      }
      allowEdit
      allowExtraDiscount
      disableACH
      disableCard
      paymentInput {
        methodVerifyOrProcess
        lineItems {
          productId
          product {
            sku
            name
            description
            price
            isRecurring
            recurringMode
            recurringInterval
            recurringFrequency
            recurringTotalCycles
            recurringTrialDays
            recurringSetupFee
            recurringRefundable
          }
          amount
          total
          metadata
        }
        amount
        tip
        tipType
        tax
        taxType
        discountCodes
        shipping
      }
      surcharge
      meta {
        includeSurcharge
        encryptedCheckoutToken
        encrptedCheckoutTokenSecret
        dynamic {
          discountCodes
          paymentType
          tip {
            amount
            type
          }
          quantityAmounts {
            id
            quantity
          }
        }
        reference {
          id
          source
        }
      }
      referenceID
      onSuccessUrl
      onFailureUrl
      customerID
      customerData {
        id
        nameOnCard
        email
        phone
        billingAddress
        billingCity
        billingState
        billingZip
        billingCountry
        paymentCards {
          cardID
          type
          isDefault
          last4
          brand
          expires
          cvc
          accountNumber
          routingNumber
          accountType
          accountHolderType
          gpEcommID
        }
      }
      allowTip
      customerPaymentID
      prefilledAddress {
        email
        phone
        country
        state
        city
        zip
        address
        nameOnCard
      }
      transactionHistory {
        transactionID
        date
        amount
        status
        note
      }
      disableCustomAddress
      disableCustomPayment
      disablePreselectCard
      overideLineItemsAmount
    }
  }
`);

export const GetPaymentPageDataUNI = graphql(`
  query Ghl_api_getPaymentPageDataUNI($input: Ghl_api_getPaymentPageDataUNIInput!) {
    ghl_api_getPaymentPageDataUNI(input: $input) {
      data {
        locationID
        groupID
        liveMode
        amount
        apiKey
        createdAt
        amountSummary {
          subtotal
          discount
          tax
          shipping
        }
        subscriptionId
        lineItems {
          id
          name
          description
          image
          quantity
          price
          currency
          total
          discount
        }
        subscriptionItems {
          id
          name
          description
          image
          price
          currency
          setupFee
          recurring {
            interval
            intervalCount
            delay
            cycles
          }
          discount
          discountNoSetupFee
        }
        coupon {
          id
          name
          sessionID
          code
          discountType
          discountValue
          hasAffiliateCoupon
          userLimit
          status
          usageCount
        }
        customerData {
          name
          email
          contact
          first_name
          last_name
          company
          address_line_1
          address_line_2
          city
          state
          postal_code
          country
          phone
          fax
        }
      }
      paymentLink {
        paymentData
        payLinkID
        token
        groupID
      }
    }
  }
`);

export const GetPaymentPageData = graphql(`
  query GetPaymentPageData($input: Ghl_api_getPaymentPageDataInput!) {
    ghl_api_getPaymentPageData(input: $input) {
      data {
        locationID
        groupID
        liveMode
        amount
        apiKey
        createdAt
        amountSummary {
          subtotal
          discount
          tax
          shipping
        }
        subscriptionId
        lineItems {
          id
          name
          description
          image
          quantity
          price
          currency
          total
          discount
        }
        subscriptionItems {
          id
          name
          description
          image
          price
          currency
          setupFee
          recurring {
            interval
            intervalCount
            delay
            cycles
          }
          discount
          discountNoSetupFee
        }
        coupon {
          id
          name
          sessionID
          code
          discountType
          discountValue
          hasAffiliateCoupon
          userLimit
          status
          usageCount
        }
        customerData {
          name
          email
          contact
          first_name
          last_name
          company
          address_line_1
          address_line_2
          city
          state
          postal_code
          country
          phone
          fax
        }
      }
    }
  }
`);

// export const SubmitPaymentPageData = graphql(`
//   mutation Ghl_api_submitPayment($input: Ghl_api_submitPaymentInput!) {
//     ghl_api_submitPayment(input: $input) {
//       txID
//       order {
//         status
//         msg
//         data {
//           id
//           idempotency_time
//           type
//           amount
//           base_amount
//           amount_authorized
//           amount_captured
//           amount_settled
//           amount_refunded
//           payment_adjustment
//           tip_amount
//           settlement_batch_id
//           payment_type
//           tax_amount
//           tax_exempt
//           shipping_amount
//           surcharge
//           discount_amount
//           service_fee
//           currency
//           description
//           order_id
//           po_number
//           ip_address
//           transaction_source
//           email_receipt
//           email_address
//           customer_id
//           customer_payment_type
//           customer_payment_id
//           subscription_id
//           referenced_transaction_id
//           response_body {
//             card {
//               id
//               card_type
//               first_six
//               last_four
//               masked_card
//               expiration_date
//               response
//               response_code
//               auth_code
//               bin_type
//               type
//               avs_response_code
//               cvv_response_code
//               processor_specific
//               created_at
//               updated_at
//             }
//           }
//           status
//           response
//           response_code
//           billing_address {
//             first_name
//             last_name
//             company
//             address_line_1
//             address_line_2
//             city
//             state
//             postal_code
//             country
//             phone
//             fax
//             email
//           }
//           shipping_address {
//             first_name
//             last_name
//             company
//             address_line_1
//             address_line_2
//             city
//             state
//             postal_code
//             country
//             phone
//             fax
//             email
//           }
//           created_at
//           updated_at
//           captured_at
//           settled_at
//         }
//       }
//       subscription {
//         status
//         msg
//         data {
//           id
//           plan_id
//           status
//           description
//           customer {
//             id
//           }
//           amount
//           total_adds
//           total_discounts
//           billing_cycle_interval
//           billing_frequency
//           billing_days
//           duration
//           next_bill_date
//           add_ons {
//             id
//             name
//             description
//             amount
//             percentage
//             duration
//             created_at
//             updated_at
//           }
//           discounts {
//             id
//             name
//             description
//             amount
//             percentage
//             duration
//             created_at
//             updated_at
//           }
//           created_at
//           updated_at
//         }
//       }
//     }
//   }
// `);

export const SubmitPaymentUNIPageData = graphql(`
  mutation SubmitPaymentUNIPageData($input: Ghl_api_submitPayment_uniInput!) {
    ghl_api_submitPayment_uni(input: $input) {
      txID
      order {
        status
        msg
        data {
          id
          idempotency_time
          type
          amount
          base_amount
          amount_authorized
          amount_captured
          amount_settled
          amount_refunded
          payment_adjustment
          tip_amount
          settlement_batch_id
          payment_type
          tax_amount
          tax_exempt
          shipping_amount
          surcharge
          discount_amount
          service_fee
          currency
          description
          order_id
          po_number
          ip_address
          transaction_source
          email_receipt
          email_address
          customer_id
          customer_payment_type
          customer_payment_id
          subscription_id
          referenced_transaction_id
          response_body {
            card {
              id
              card_type
              first_six
              last_four
              masked_card
              expiration_date
              response
              response_code
              auth_code
              bin_type
              type
              avs_response_code
              cvv_response_code
              processor_specific
              created_at
              updated_at
            }
          }
          status
          response
          response_code
          billing_address {
            first_name
            last_name
            company
            address_line_1
            address_line_2
            city
            state
            postal_code
            country
            phone
            fax
            email
          }
          shipping_address {
            first_name
            last_name
            company
            address_line_1
            address_line_2
            city
            state
            postal_code
            country
            phone
            fax
            email
          }
          created_at
          updated_at
          captured_at
          settled_at
        }
      }
      subscription {
        status
        msg
        data {
          id
          plan_id
          status
          description
          customer {
            id
          }
          amount
          total_adds
          total_discounts
          billing_cycle_interval
          billing_frequency
          billing_days
          duration
          next_bill_date
          add_ons {
            id
            name
            description
            amount
            percentage
            duration
            created_at
            updated_at
          }
          discounts {
            id
            name
            description
            amount
            percentage
            duration
            created_at
            updated_at
          }
          created_at
          updated_at
        }
      }
    }
  }
`);
