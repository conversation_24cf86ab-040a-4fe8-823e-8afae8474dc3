'use client';

import { LoaderPaymentForm } from '@/components/Payment/restloader';
import { NextPage } from 'next';
import { useSearchParams } from 'next/navigation';

const Payment: NextPage = () => {
  const searchParams = useSearchParams();

  const paymentData = searchParams?.get('paymentData');
  const paymentDataID = searchParams?.get('paymentDataID');
  const token = searchParams?.get('token');
  const groupID = searchParams?.get('groupID');

  if ((!paymentData && !paymentDataID) || !groupID) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="h-16 w-16 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <LoaderPaymentForm
      input={{
        data: {
          dynamicData: {
            discountCodes: [],
            quantityAmounts: [],
          },
          paymentData: paymentData ?? undefined,
          paymentDataID: paymentDataID ?? undefined,
        },
        token: token ?? undefined,
        groupID,
      }}
      onAfterPayment={(args) => {
        if (args.success && args.onSuccessUrl) {
          window.location.href = args.onSuccessUrl;
        } else if (!args.success && args.onFailureUrl) {
          window.location.href = args.onFailureUrl;
        }
      }}
    />
  );
};

export default Payment;
