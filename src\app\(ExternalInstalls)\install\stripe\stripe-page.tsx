'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Me } from '@/graphql/declarations/me';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { useQuery } from '@apollo/client';
import {
  AlertCircle,
  ArrowRight,
  BarChart2,
  CreditCard,
  Download,
  Info,
  LoaderCircle,
  Wallet,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { axiosClient } from '@/lib/axios';
import { toast } from 'react-toastify';

const SharedLayout = ({ children }: { children: React.ReactNode }) => (
  <div className="flex h-screen w-screen items-center justify-center bg-gray-50">
    <Card className="w-fit max-w-md space-y-6 border-2 p-6 shadow-lg">
      <div className="flex justify-center space-x-4">
        <img src="/logo.png" alt="ngnair-logo" className="h-12" />
      </div>
      {children}
    </Card>
  </div>
);

export default function StripePage() {
  const router = useRouter();
  const params = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedGroup, setSelectedGroup] = useState('');
  const [step, setStep] = useState('loading');
  const [groups, setGroups] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication status
  const { data: meData, loading: loadingAuth } = useQuery(Me, {});

  // Get the user's groups using GET_GROUPS_LIST
  const { data: groupsData, loading: loadingGroups } = useQuery(GET_GROUPS_LIST, {
    skip: !meData?.authenticatedItem?.id,
    fetchPolicy: 'network-only',
    variables: {
      where: {},
      take: 100,
      skip: 0,
    },
  });

  useEffect(() => {
    if (loadingAuth) return;

    // Check login status
    if (!meData?.authenticatedItem?.id) {
      setIsAuthenticated(false);
      setLoading(false);
      setStep('not-authenticated');
      return;
    }

    setIsAuthenticated(true);

    // If we have the groups data, update state
    if (!loadingGroups && groupsData?.groups) {
      setGroups(groupsData.groups);
      setLoading(false);
      setStep('welcome');
    }
  }, [loadingAuth, loadingGroups, meData, groupsData]);

  const redirectToLogin = () => {
    const currentPath = window.location.pathname;
    const currentSearch = window.location.search;
    const encodedRedirect = encodeURIComponent(`${currentPath}${currentSearch}`);
    window.location.href = `/login?redirect=${encodedRedirect}`;
  };

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroup(groupId);
  };

  const handleConnectWithStripe = async () => {
    if (!selectedGroup) {
      toast.error('Please select an organization first');
      return;
    }

    setIsProcessing(true);
    setStep('processing');

    try {
      // Generate a random state value for CSRF protection
      const state = Math.random().toString(36).substring(2, 15);

      // The redirect URL will be back to the dashboard
      const redirectTo = window.location.origin + '/dashboard';

      const response = await axiosClient.post('/api/stripe/authorize', {
        groupID: selectedGroup,
        redirectTo,
        state,
      });

      if (response.data && response.data.authUrl) {
        // Store the state in localStorage for verification when redirected back
        localStorage.setItem('stripe_oauth_state', state);

        // Redirect to Stripe's OAuth page
        window.location.href = response.data.authUrl;
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      console.error('Failed to initiate Stripe OAuth flow:', err);
      setError('Failed to connect with Stripe. Please try again.');
      setStep('error');
      setIsProcessing(false);
    }
  };

  const handleCancel = () => {
    router.push('/dashboard');
  };

  // Loading state
  if (loading || loadingAuth || loadingGroups) {
    return (
      <SharedLayout>
        <div className="flex flex-col items-center justify-center space-y-4">
          <LoaderCircle className="h-8 w-8 animate-spin text-blue-500" />
          <p>Loading...</p>
        </div>
      </SharedLayout>
    );
  }

  // Not authenticated state
  if (step === 'not-authenticated') {
    return (
      <SharedLayout>
        <div className="space-y-6">
          <div className="text-center">
            <div className="mb-2 flex justify-center">
              <CreditCard className="h-12 w-12 text-[#635BFF]" />
            </div>
            <h2 className="text-xl font-semibold">Connect with Stripe</h2>
            <p className="text-sm text-gray-600">
              Import your Stripe data into NGNair for enhanced analytics
            </p>
          </div>

          <Alert className="border-amber-200 bg-amber-50 text-amber-800">
            <Info className="h-4 w-4" />
            <AlertTitle>Account Required</AlertTitle>
            <AlertDescription>
              You need to be signed in to an NGNair account to connect with Stripe.
            </AlertDescription>
          </Alert>

          <div className="rounded-md bg-gray-50 p-4">
            <h3 className="mb-2 font-medium">What you'll get with Stripe integration:</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Wallet className="mt-0.5 h-5 w-5 flex-shrink-0 text-[#635BFF]" />
                <p className="text-sm text-gray-700">
                  Unified dashboard for all payment providers including Stripe and NGNair
                  transactions
                </p>
              </div>
              <div className="flex items-start space-x-3">
                <BarChart2 className="mt-0.5 h-5 w-5 flex-shrink-0 text-[#635BFF]" />
                <p className="text-sm text-gray-700">
                  Powerful analytics and visualizations for your payment data
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <Button
              onClick={redirectToLogin}
              className="bg-[#635BFF] text-white hover:bg-[#635BFF]/80"
            >
              Sign in to continue <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </SharedLayout>
    );
  }

  // Error state
  if (step === 'error') {
    return (
      <SharedLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error || 'An unexpected error occurred'}</AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Link href="/dashboard">
            <Button variant="outline">Back to Dashboard</Button>
          </Link>
        </div>
      </SharedLayout>
    );
  }

  // Welcome page with benefits
  if (step === 'welcome') {
    return (
      <SharedLayout>
        <div className="space-y-6">
          <div className="text-center">
            <div className="mb-2 flex justify-center">
              <CreditCard className="h-12 w-12 text-[#635BFF]" />
            </div>
            <h2 className="text-xl font-semibold">Connect with Stripe</h2>
            <p className="text-sm text-gray-600">
              Import your Stripe data into NGNair for enhanced analytics
            </p>
          </div>

          <div className="space-y-4">
            <div className="rounded-md bg-gray-50 p-4">
              <h3 className="mb-2 font-medium">Single Click Stripe Import</h3>
              <div className="flex items-start space-x-3">
                <Wallet className="mt-0.5 h-5 w-5 flex-shrink-0 text-[#635BFF]" />
                <p className="text-sm text-gray-700">
                  We offer a dashboard that allows you to see all of your integrated payment
                  providers in one place. This includes Stripe transactions on cards and bank
                  accounts and NGNair Transactions too.
                </p>
              </div>
            </div>

            <div className="rounded-md bg-gray-50 p-4">
              <h3 className="mb-2 font-medium">Quick Actions for Data Access</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <BarChart2 className="mt-0.5 h-5 w-5 flex-shrink-0 text-[#635BFF]" />
                  <p className="text-sm text-gray-700">
                    Once imported, we can use your data to create charts for unified histograms and
                    analytics, all in one place.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <Download className="mt-0.5 h-5 w-5 flex-shrink-0 text-[#635BFF]" />
                  <p className="text-sm text-gray-700">
                    Export CSV data, create aggregations, and gain valuable insights from your
                    payment data.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <Button
              onClick={() => setStep('select-group')}
              className="bg-[#635BFF] text-white hover:bg-[#635BFF]/80"
            >
              Get Started <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </SharedLayout>
    );
  }

  // Select group state
  if (step === 'select-group') {
    return (
      <SharedLayout>
        <div className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-center text-xl font-semibold">Select Organization</h2>
            <p className="text-center text-sm text-gray-600">
              Choose which organization you'd like to connect with Stripe
            </p>
          </div>

          <div className="rounded-md bg-blue-50 p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <Info className="h-5 w-5 text-blue-400" aria-hidden="true" />
              </div>
              <div className="ml-3 flex-1 md:flex md:justify-between">
                <p className="text-sm text-blue-700">
                  This will connect Stripe to your NGNair organization
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4 py-2">
            <label className="text-sm font-medium text-gray-700">Your organization</label>
            <Select onValueChange={handleGroupSelect} value={selectedGroup}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select an organization" />
              </SelectTrigger>
              <SelectContent>
                {groups.map((group) => (
                  <SelectItem key={group.id} value={group.id}>
                    {group.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500">
              This determines which organization will have access to your Stripe data.
            </p>
          </div>

          <div className="pt-2">
            <div className="flex items-center justify-between">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                className="bg-[#635BFF] text-white hover:bg-[#635BFF]/80"
                onClick={handleConnectWithStripe}
                disabled={!selectedGroup}
              >
                Connect with Stripe <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </SharedLayout>
    );
  }

  // Processing state
  if (step === 'processing') {
    return (
      <SharedLayout>
        <div className="flex flex-col items-center justify-center space-y-4">
          <LoaderCircle className="h-8 w-8 animate-spin text-[#635BFF]" />
          <p>Connecting to Stripe...</p>
        </div>
      </SharedLayout>
    );
  }

  // Fallback
  return null;
}
