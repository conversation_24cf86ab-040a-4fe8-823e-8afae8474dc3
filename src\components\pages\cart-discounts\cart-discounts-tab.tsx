import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>po<PERSON>,
  <PERSON><PERSON><PERSON>,
  StatusFilter,
  useDataGridView,
  Variant,
} from '@/components/globals';

import { useEffect, useMemo, useState } from 'react';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import {
  Gateway_DiscountsDocument,
  GatewayUniDiscountOutputType,
} from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import { CartDiscount } from './components/cart-discount-add';
import { CartDiscountUpdate } from './components/cart-discount-update';
import { useSearchParams } from 'next/navigation';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export enum DiscountStatus {
  active = 'active',
  inactive = 'inactive',
}

export const getDiscountStatus = (status: DiscountStatus): [Variant, string] => {
  const statusMap: Record<DiscountStatus, Variant> = {
    [DiscountStatus.active]: 'success',
    [DiscountStatus.inactive]: 'neutral',
  };

  const variant = statusMap[status] || 'neutral'; // Default to neutral if status is unknown
  const label = `${status?.charAt(0).toUpperCase()}${status?.slice(1)}`; // Capitalize the status

  return [variant, label];
};

export const CartDiscountsTab = () => {
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);
  const queryParams = useSearchParams();
  const cartDiscountId = queryParams?.get('id');
  const [selectedCartDiscountId, setSelectedCartDiscountId] = useState(cartDiscountId ?? null);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({});

  const {
    data: discountListData,
    loading: discountListLoading,
    refetch: refetchdiscountListData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    filterValue,
    setFilterValue,
    maxVariables,
  } = useDataGridView({
    query: Gateway_DiscountsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            scope: '',
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
    initialFilterField: 'status',
    initialFilterValue: 'All',
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refetchdiscountListData();
    }
  }, [locationFilter]);

  const rows = useMemo(() => {
    const data = discountListData?.gateway_discounts?.data ?? [];
    return data.filter((discount): discount is NonNullable<typeof discount> => discount !== null);
  }, [discountListData]);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_DiscountsDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_discounts?.data ?? [];
  };

  const columns: Column[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      onClick: (row) => setSelectedCartDiscountId(row.id),
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'type',
      header: 'Type',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'discount',
      header: 'Amount',
      renderCell: (row) => {
        const isPercentage = row.type === GatewayUniDiscountOutputType.Percentage;
        return <>{isPercentage ? row.discount + '%' : '$' + row.discount}</>;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getDiscountStatus(row.status);
        return <StatusChip variant={status} label={label} />;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Cart Discount" />
        <CartDiscount
          refetchListPage={refetchdiscountListData}
          groupID={locationFilter?.id ?? ''}
        />
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
          <TopComponent value={searchValue} setValue={setSearchValue}>
            <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="discounts" />
          </TopComponent>
        </div>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={rows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={discountListLoading || loadingGroupList}
          actionComponent={
            <StatusFilter
              value={filterValue}
              setValue={setFilterValue}
              statusList={Object.values(DiscountStatus)}
            />
          }
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={discountListData?.gateway_discounts?.page?.total ?? 0}
        />
      </div>
      <CartDiscountUpdate
        isOpen={selectedCartDiscountId !== null}
        onClose={() => setSelectedCartDiscountId(null)}
        refetchListPage={refetchdiscountListData}
        queryData={{
          cartDiscountID: selectedCartDiscountId ?? '',
          groupID: locationFilter?.id ?? '',
        }}
      />
    </>
  );
};
