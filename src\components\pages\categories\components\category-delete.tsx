import { useState } from 'react';
import { ConfirmDialog } from '@/components/globals';
import { Button } from 'flowbite-react';
import { Gateway_DeleteCategoryDocument } from '@/graphql/generated/graphql';
import { HiTrash } from 'react-icons/hi';
import { useMutation } from '@apollo/client';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import { CategoryFormData } from '../utils';
import { useLocationSelector } from '@/components/hooks';

type CategoryDeleteProps = {
  cateory: CategoryFormData;
  onDelete?: () => void;
};

export const CategoryDelete = ({ cateory, onDelete }: CategoryDeleteProps) => {
  const [confirmDialog, setShowUpdateModal] = useState(false);
  const { locationFilter } = useLocationSelector({});

  const [deleteCategoryMutation, { loading: deleteCategoryLoading, error }] = useMutation(
    Gateway_DeleteCategoryDocument,
    {
      onCompleted: (_) => {
        setShowUpdateModal(false);
        toast.success(message.api.successDelete('Category'));
        onDelete && onDelete();
      },
      onError: (error) => {
        toast.error(message.api.errorDelete('Category', error.message));
      },
    },
  );

  const handleDeleteCategory = async () => {
    try {
      await deleteCategoryMutation({
        variables: {
          input: {
            groupID: locationFilter?.id ?? '',
            data: {
              id: cateory.id ?? '',
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Category Mutation error: ', e);
    }
  };
  return (
    <>
      <Button color="failure" onClick={() => setShowUpdateModal(true)}>
        <HiTrash className="mr-2 h-5 w-5" /> Delete
      </Button>
      <ConfirmDialog
        header={<>Delete "{cateory.name}"</>}
        body={<>Are you sure you want to delete this category?</>}
        actions={
          <div className="flex justify-center space-x-4">
            <Button color="gray" onClick={() => setShowUpdateModal(false)}>
              No, cancel
            </Button>
            <Button color="failure" onClick={handleDeleteCategory}>
              Yes, I'm sure
            </Button>
          </div>
        }
        open={confirmDialog}
        setOpen={setShowUpdateModal}
      />
    </>
  );
};
