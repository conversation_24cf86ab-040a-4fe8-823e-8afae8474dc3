'use client';

import { useEffect, useRef, useState } from 'react';
import 'quill/dist/quill.snow.css';

interface HtmlEditorQuillProps {
  value: string;
  setValue?: (value: string) => void;
  className?: string;
  readOnly?: boolean;
}

const modules = {
  toolbar: [
    [{ font: [] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ align: [] }],
    ['blockquote', 'code-block'],
    ['link'],
    [{ header: [] }],
    [{ list: 'ordered' }, { list: 'bullet' }],
  ],
};

export default function HtmlEditorQuill({
  value,
  setValue,
  className,
  readOnly,
}: HtmlEditorQuillProps) {
  const editorRef = useRef<any>(null);
  const quillRef = useRef<any>(null);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('quill').then((Quill) => {
        if (!quillRef.current && editorRef.current) {
          quillRef.current = new Quill.default(editorRef.current, {
            theme: 'snow',
            modules: readOnly ? { toolbar: false } : modules,
            readOnly: readOnly,
          });

          quillRef.current.on('text-change', () => {
            const content = quillRef.current.root.innerHTML;
            setValue?.(content);
          });

          // Set initial content
          if (value) {
            quillRef.current.root.innerHTML = value;
          }
        }
      });
    }
    setIsMounted(true);

    return () => {
      if (quillRef.current) {
        quillRef.current = null;
      }
    };
  }, [readOnly]);

  // Update content when value prop changes
  useEffect(() => {
    if (quillRef.current && value !== quillRef.current.root.innerHTML) {
      quillRef.current.root.innerHTML = value;
    }
  }, [value]);

  if (!isMounted) {
    return <div className="h-[200px] animate-pulse bg-muted" />;
  }

  return (
    <div className={`${className || ''} quill-wrapper relative min-h-[200px]`}>
      <div ref={editorRef} className="flex min-h-[200px] flex-col-reverse border-t bg-white" />
    </div>
  );
}
