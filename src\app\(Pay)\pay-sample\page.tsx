'use client';

import { LoaderPaymentForm } from '@/components/Payment/restloader';
import { NextPage } from 'next';

const Payment: NextPage = () => {
  return (
    <LoaderPaymentForm
      input={{
        data: {
          dynamicData: {
            discountCodes: [],
            quantityAmounts: [],
          },
          paymentData:
            'U2FsdGVkX1+Rk/ynphNm4MLjZRnS+WdcIXci1U1WuW9xw+5EBczFghtPv1vTcMaBnRLcXobk9jLTYWw50iWgxgNGhuCgd8R//EGR5S2JGhklM5ja/dEbyrotBDWDMFdqNeaNOBU24tc1CW+j7jCoR0xIgvO0hQcHrxeizMi9LYjcUNAwl5mv5tDc3mMYVEK8invNFFfRlCD5CYbyEILYEX37EZ8YiNiGrrftxHROvdOj0Tv1NnyC0Cg+G0BtPGK1BG+JVeAAh7DEdllu69vMPHHJ4l0NfkzFfAXe1mL5UvyEGXh9eKNH58UmyuHzxmMywQaVToYdN4IAjPYdl9ViX3gGObHOZOjNr45NSfiDISJ/pqFx0Kr1EshyxjhwrPx2+DgVZv6ZCOa0+OXl8AgjttTCZXMEQpA0Dd2gPipOIPiDJa5QjpwepZTYFgYr38VYqWCKAbcRJ/nqALMvnf16mftfGnTvAdR2I+PdO/QwGsbfbsRZ3anpXjLzFsj7ARWpnmIh+xR2irnibfP/lNA2P6poWA/iRON2pVUbaw/eNmAVNljje/OS9vRhY8vkDxkYHL+Bnt7+kqc7BjlbkjNW8klBql4apsQt6JG3s79NoUQLxQHY9m+9nVOWHKBTHvZNzYncHmdXJf8HDb2HwE6sRhRGVrg6nKVB0bo49DyColVUKjSCGBi5trdBgsG/rk/r',
        },
        token: 'bc3b23e0-a6d7-4623-8d55-4b179ee93b04',
        groupID: 'cfvqe3k3mh',
      }}
    />
  );
};

export default Payment;
