'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { GroupsDocument, Propay_LinksDocument } from '@/graphql/generated/graphql';
import { getIP } from '@/lib/getIP';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { cn } from '@/lib/utils';
import { useQuery } from '@apollo/client';
import { useEffect, useMemo, useState } from 'react';

const types = [
  'DEPOSIT_REPORT',
  'UPDATE_PRIMARY_ACCOUNT',
  'CONFIRM_VALIDATION_DEPOSITS',
  'SEND_VALIDATION_DEPOSITS',
  'UPDATE_PAYOUT_CARD',
  'FUND_PAYOUT_CARD',
  'FUND_PRIMARY_PAYOUT',
  'FMA_TRANSFER',
  'CREDIT_FMA',
  'SCHEDULED_FUND',
  'UPDATE_BUSINESS_INFORMATION',
  'UPDATE_ADDRESS_PHONE',
  'UPDATE_PIN',
  'UPDATE_EMAIL',
  'PREPAID_CARD_INDEX',
  'REQUEST_PREPAID_CARD',
  'ACTIVATE_PREPAID_CARD',
  'LOST_PREPAID_CARD',
  'UPDATE_PREPAID_CARD_PIN',
  'REISSUE_PREPAID_CARD',
  'UPDATE_PAYMENT_METHOD',
  'DOCUMENT_UPLOAD',
  'CHARGEBACK_REPORT',
  'ADVANCED_TRANSACTION_SEARCH',
  'CONSOLIDATED_FEES',
  'LIMITS_RATES_FEES',
  'TRANSACTION_REPORT',
  'GB_FEES_REPORT',
];

export default function Page() {
  const [selectedGroup, setSelectedGroup] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('TRANSACTION_REPORT');
  const [currentIP, setCurrentIP] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<string>('');
  const [expandIframe, setExpandIframe] = useState<boolean>(false);
  const [iframeUrl, setIframeUrl] = useState('');

  const { data: groupData } = useQuery(GroupsDocument, {
    variables: {
      where: {
        mainProcessor: {
          equals: 'glp',
        },
        processorGLP: {
          fundsAccountID: {
            not: {
              equals: '',
            },
          },
        },
      },
      take: 100,
      skip: 0,
    },
  });

  const groupDataList = useMemo(() => groupData?.groups || [], [groupData]);

  useEffect(() => {
    getIP().then((ip) => setCurrentIP(ip));
  }, []);

  useEffect(() => {
    // get current page url
    setCurrentPage(window.location.href);
  }, []);

  const handleGroupChange = (value: string) => {
    setSelectedGroup(value);
  };

  const handleTypeChange = (value: string) => {
    setSelectedType(value);
  };

  const visitIframeUrl = async () => {
    if (!selectedGroup || !selectedType) {
      return;
    }

    setIframeUrl('');

    const dataUrl = await apolloClient.mutate({
      mutation: Propay_LinksDocument,
      variables: {
        input: {
          groupID: selectedGroup,
          body: {
            type: 'THIRD_PARTY_PAGE',
            ip_address: currentIP,
            hostURL: currentPage,
            function: selectedType,
          },
        },
      },
    });

    const link = dataUrl.data?.propay_links?.url;

    if (link) {
      setIframeUrl(link);
    }
  };

  useEffect(() => {
    if (selectedGroup && selectedType) {
      visitIframeUrl();
    }
  }, [selectedGroup, selectedType]);

  return (
    <div className="flex flex-col p-4">
      <div className="mb-4 flex space-x-4">
        <Select value={selectedGroup} onValueChange={handleGroupChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select group" />
          </SelectTrigger>
          <SelectContent>
            {groupDataList.map((group) => (
              <SelectItem key={group.id} value={group.id}>
                {group.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedType} onValueChange={handleTypeChange}>
          <SelectTrigger className="flex-1">
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            {types.map((type) => (
              <SelectItem key={type} value={type}>
                {type}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {/* <Button onClick={() => visitIframeUrl()}>Update Iframe</Button> */}
        <Button onClick={() => setExpandIframe(!expandIframe)}>
          {expandIframe ? 'Minimize' : 'Expand'}
        </Button>
      </div>
      <div className="flex justify-between gap-2 px-2 pb-2 text-center font-mono text-sm text-gray-500">
        <p>{currentIP}</p>
        <p>{currentPage}</p>
      </div>
      <div className="flex-grow">
        {iframeUrl && (
          <iframe
            src={iframeUrl}
            className={cn(
              'border-gray w-full border shadow-md',

              expandIframe ? 'absolute inset-0 z-[100] h-screen' : 'relative h-[90vh]',
            )}
            title="Content"
            allowFullScreen
          />
        )}
        {!iframeUrl && (
          <div className="flex h-full items-center justify-center">
            <p className="text-lg">Please select group and type to load iframe</p>
          </div>
        )}
        {expandIframe && iframeUrl && (
          <Button onClick={() => setExpandIframe(false)} className="absolute right-2 top-2 z-[120]">
            Close
          </Button>
        )}
      </div>
    </div>
  );
}
