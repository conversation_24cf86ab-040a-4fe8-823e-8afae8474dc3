import { Modal, Button, Label, TextInput } from 'flowbite-react';
import { useForm, FormProvider } from 'react-hook-form';
import { message } from '@/components/shared/utils';
import { Gateway_CreateProductDocument } from '@/graphql/generated/graphql';
import { useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { useLocationSelector } from '@/components/hooks';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { floatToInt2Dec } from '@/lib/utils';

type QuickProductFormData = {
  productName: string;
  sku: string;
  price: string;
  isRecurring: boolean;
  recurringMode?: string;
  recurringInterval?: string;
  recurringFrequency?: string;
  recurringTotalCycles?: string;
  recurringTrialDays?: string;
  recurringSetupFee?: string;
};

type QuickProductAddModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (productId: string, name: string, price: number) => void;
};

export const QuickProductAddModal = ({ isOpen, onClose, onSuccess }: QuickProductAddModalProps) => {
  const { locationFilter } = useLocationSelector({ readonly: true });
  const methods = useForm<QuickProductFormData>({});
  const { register, watch, getValues } = methods;

  const isRecurring = watch('isRecurring');
  const recurringMode = watch('recurringMode');

  const [createProductMutation, { loading }] = useMutation(Gateway_CreateProductDocument, {
    onCompleted: (data) => {
      toast.success(message.api.successCreate('Product'));
      const formValues = getValues();
      onSuccess(
        data.gateway_createProduct?.productID ?? '',
        formValues.productName,
        parseFloat(formValues.price),
      );
      onClose();
    },
    onError: (error) => {
      toast.error(message.api.errorCreate('Product', error.message));
    },
  });

  const onSubmit = async (data: QuickProductFormData) => {
    try {
      await createProductMutation({
        variables: {
          input: {
            groupID: locationFilter?.id ?? '',
            data: {
              form: {
                name: data.productName,
                sku: data.sku,
                price: floatToInt2Dec(Number(data.price)),
                isInStore: true,
                isOnline: true,
                isRecurring: data.isRecurring,
                recurringMode: data.isRecurring ? '' : undefined,
                recurringInterval: data.recurringInterval
                  ? parseInt(data.recurringInterval)
                  : undefined,
                recurringFrequency: data.recurringFrequency
                  ? parseInt(data.recurringFrequency)
                  : undefined,
                recurringTotalCycles: data.recurringTotalCycles
                  ? parseInt(data.recurringTotalCycles)
                  : undefined,
                recurringTrialDays: data.recurringTrialDays
                  ? parseInt(data.recurringTrialDays)
                  : undefined,
                recurringSetupFee: data.recurringSetupFee
                  ? floatToInt2Dec(Number(data.recurringSetupFee))
                  : undefined,
              },
            },
          },
        },
      });
    } catch (e) {
      console.error('Quick Add Product Mutation error: ', e);
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="xl">
      <Modal.Header>Quick Add New Product</Modal.Header>
      <Modal.Body>
        <SpinnerLoading isLoading={loading} />
        <FormProvider {...methods}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              // prevent passthrough of the event to the form
              e.stopPropagation();
              methods.handleSubmit(onSubmit)(e);
            }}
            className="space-y-4"
          >
            <div>
              <Label htmlFor="productName">Product Name</Label>
              <TextInput
                id="productName"
                {...register('productName', { required: true })}
                placeholder="Enter product name"
              />
            </div>

            <div>
              <Label htmlFor="sku">SKU</Label>
              <TextInput
                id="sku"
                {...register('sku', { required: true })}
                placeholder="Enter SKU"
              />
            </div>

            <div>
              <Label htmlFor="price">Price</Label>
              <TextInput
                id="price"
                type="number"
                step="0.01"
                {...register('price', { required: true })}
                placeholder="Enter price"
              />
            </div>

            <div className="flex items-center gap-2">
              <input type="checkbox" id="isRecurring" {...register('isRecurring')} />
              <Label htmlFor="isRecurring">Is Recurring</Label>
            </div>

            {isRecurring && (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="recurringMode">Recurring Mode</Label>
                  <select
                    id="recurringMode"
                    className="w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900"
                    {...register('recurringMode')}
                  >
                    <option value="">By Day (every n Days)</option>
                    <option value="MONTHLY">By Month (nth of Month)</option>
                    <option value="YEARLY">By Year (nth of Year)</option>
                  </select>
                </div>

                {recurringMode !== 'MONTHLY' && recurringMode !== 'YEARLY' && (
                  <div>
                    <Label htmlFor="recurringInterval">Recurring Interval (days)</Label>
                    <TextInput
                      id="recurringInterval"
                      type="number"
                      {...register('recurringInterval')}
                      placeholder="Enter interval"
                    />
                  </div>
                )}

                {(recurringMode === 'MONTHLY' || recurringMode === 'YEARLY') && (
                  <div className="rounded-lg border border-blue-100 bg-blue-50 p-3 text-sm text-blue-800">
                    <p>
                      <strong>Note:</strong> With{' '}
                      {recurringMode === 'MONTHLY' ? 'Monthly' : 'Yearly'} mode, the customer will
                      be charged on the same day of the{' '}
                      {recurringMode === 'MONTHLY' ? 'month' : 'year'} as the initial payment.
                    </p>
                    <p className="mt-1">
                      For payments starting on the 29th-31st, the payment will adjust to the last
                      day of shorter months to accommodate different month lengths and leap years.
                    </p>
                    {recurringMode === 'YEARLY' && (
                      <p className="mt-1">
                        <strong>For yearly payments:</strong> If a payment was started on February
                        29th (leap year), it will be automatically adjusted to February 28th in
                        non-leap years.
                      </p>
                    )}
                  </div>
                )}

                <div>
                  <Label htmlFor="recurringFrequency">Recurring Frequency</Label>
                  <TextInput
                    id="recurringFrequency"
                    type="number"
                    {...register('recurringFrequency')}
                    placeholder="Enter frequency"
                  />
                </div>

                <div>
                  <Label htmlFor="recurringTotalCycles">Total Cycles</Label>
                  <TextInput
                    id="recurringTotalCycles"
                    type="number"
                    {...register('recurringTotalCycles')}
                    placeholder="Enter total cycles"
                  />
                </div>

                <div>
                  <Label htmlFor="recurringTrialDays">Trial Days</Label>
                  <TextInput
                    id="recurringTrialDays"
                    type="number"
                    {...register('recurringTrialDays')}
                    placeholder="Enter trial days"
                  />
                </div>

                <div>
                  <Label htmlFor="recurringSetupFee">Setup Fee</Label>
                  <TextInput
                    id="recurringSetupFee"
                    type="number"
                    step="0.01"
                    {...register('recurringSetupFee')}
                    placeholder="Enter setup fee"
                  />
                </div>
              </div>
            )}

            <div className="flex justify-end gap-2">
              <Button color="gray" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" color="blue">
                Create Product
              </Button>
            </div>
          </form>
        </FormProvider>
      </Modal.Body>
    </Modal>
  );
};
