'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Processor } from '@/types/processors';
import Image from 'next/image';
import { useState } from 'react';
import { ProcessorConfigModal } from './processor-config-modal';
import { Loader2 } from 'lucide-react';
import { hasProcessorConfig, getProcessorStats } from '@/lib/mock/processor-configs';
import { getProcessorConfigForModal } from '@/lib/mock/processor-configs';

interface ProcessorCardProps {
  processor: Processor;
  onToggle: (enabled: boolean) => void;
}

export function ProcessorCard({ processor, onToggle }: ProcessorCardProps) {
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<string>('');

  // Check if this processor has mock configuration
  const hasConfig = hasProcessorConfig(processor.id);
  const processorStats = getProcessorStats(processor.id);
  const processorConfigForModal = getProcessorConfigForModal(processor.id);

  // Update last sync time to current time
  const updateSyncTime = () => {
    const now = new Date();
    setLastSyncTime(
      now.toLocaleString('en-US', {
        month: 'numeric',
        day: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        second: '2-digit',
        hour12: true,
      }),
    );
  };

  // Handle processor toggle with loading effect
  const handleToggle = async (enabled: boolean) => {
    if (enabled) {
      setIsLoading(true);
      // Simulate loading time
      await new Promise((resolve) => setTimeout(resolve, 2000));
      setIsLoading(false);
      if (hasConfig) {
        updateSyncTime();
      }
    }
    onToggle(enabled);
  };

  return (
    <>
      <Card className="hover:bg-accent/5">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6">
          <div className="flex items-center space-x-6">
            {processor.logo && (
              <div className="relative flex h-20 w-32 items-center justify-center overflow-hidden rounded-xl bg-white p-3 shadow-sm">
                <Image
                  src={processor.logo}
                  alt={processor.name}
                  width={120}
                  height={80}
                  className="max-h-full max-w-full object-contain"
                  style={{
                    objectFit: 'contain',
                    width: 'auto',
                    height: 'auto',
                  }}
                />
              </div>
            )}{' '}
            <div>
              <CardTitle className="text-xl font-medium">{processor.name}</CardTitle>
              <CardDescription className="mt-1 text-sm text-muted-foreground">
                {processor.type}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setIsConfigModalOpen(true)}
              className="px-6"
            >
              Configure
            </Button>
            <Switch
              className="h-6 w-12"
              checked={processor.enabled}
              onCheckedChange={handleToggle}
            />
          </div>
        </CardHeader>
        <CardContent>
          {processor.enabled && (
            <div className="space-y-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                  <span className="ml-2 text-sm text-gray-600">Connecting to processor...</span>
                </div>
              ) : hasConfig && processorStats ? (
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="text-sm text-gray-600">Total Revenue</div>
                    <div className="text-lg font-bold text-green-600">
                      $
                      {processorStats.totalRevenue.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-600">Transactions</div>
                    <div className="text-lg font-bold text-blue-600">
                      {processorStats.transactions.toLocaleString()}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-600">Success Rate</div>
                    <div className="text-lg font-bold text-green-600">
                      {processorStats.successRate}%
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-600">Conversion Rate</div>
                    <div className="text-lg font-bold text-blue-600">
                      {processorStats.conversionRate}%
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="mb-2 text-sm text-gray-600">Configuration Required</div>
                    <div className="text-xs text-gray-500">
                      Please configure this processor to view metrics
                    </div>
                  </div>
                </div>
              )}
              {!isLoading && hasConfig && lastSyncTime && (
                <div className="mt-4 border-t pt-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-green-600">Connected</span>
                    <span className="text-xs text-gray-500">Last sync: {lastSyncTime}</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      <ProcessorConfigModal
        isOpen={isConfigModalOpen}
        onClose={() => setIsConfigModalOpen(false)}
        processor={processor}
        initialConfig={processorConfigForModal || undefined}
        onSave={(config) => {
          console.log('Saving config for', processor.name, config);
          // TODO: Implement saving configuration
        }}
      />
    </>
  );
}
