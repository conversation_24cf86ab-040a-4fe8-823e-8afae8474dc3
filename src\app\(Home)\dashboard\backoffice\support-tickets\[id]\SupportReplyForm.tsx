'use client';

import { But<PERSON> } from '@/components/ui/button';
import { CreateGroupSupportMessageDocument } from '@/graphql/generated/graphql';
import { uploadFileToB64Data } from '@/lib/fileUploader';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { Paperclip } from 'lucide-react';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { useRef, useState } from 'react';

const HtmlEditorQuill = dynamic(
  () => import('@/components/globals/html-editor/html-editor-quill'),
  {
    ssr: false,
  },
);

interface FileUpload {
  filename: string;
  url: string;
}

export const SupportReplyForm = ({ id }: { id: string }) => {
  const [reply, setReply] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const router = useRouter();
  const ticketId = id;

  async function addReply(ticketId: string, message: string, files: FileList | null) {
    const fileUploads: FileUpload[] = [];
    if (files && files.length > 0) {
      try {
        console.log('files', files);
        const fileList = Array.from(files);
        const uploadedFiles = await uploadFileToB64Data(
          fileList.map((f) => ({
            file: f,
            mimetype: f.type,
            name: f.name,
          })),
        );

        if (uploadedFiles) {
          fileUploads.push(
            ...uploadedFiles.map((f: any) => ({
              url: f?.url || null,
              filename: f?.filename || null,
            })),
          );
        }
      } catch (error) {
        console.error('File upload failed:', error);
        throw new Error('Failed to upload files. Please try again.');
      }
    }

    try {
      await apolloClient.mutate({
        mutation: CreateGroupSupportMessageDocument,
        variables: {
          data: {
            ticket: {
              connect: { id: ticketId },
            },
            message,
            files: fileUploads,
          },
        },
      });

      await apolloClient.refetchQueries({ include: ['GroupSupportTicket'] });
    } catch (error) {
      console.error('API mutation failed:', error);
      throw new Error('Failed to send reply. Please try again.');
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!reply.trim()) {
      alert('Reply cannot be empty.');
      return;
    }

    const files = fileInputRef.current?.files;
    setIsSubmitting(true);

    try {
      await addReply(ticketId, reply, files || null);
      setReply('');
      setFileName(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      router.refresh();
    } catch (error: any) {
      alert(error.message || 'An unexpected error occurred.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileInputClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileName(file.name);
    }
  };

  return (
    <div className="mt-4">
      <form onSubmit={handleSubmit} className="w-full" style={{ opacity: isSubmitting ? 0.5 : 1 }}>
        {!isSubmitting && (
          <HtmlEditorQuill
            value={reply}
            setValue={(e) => setReply(e)}
            readOnly={isSubmitting}
            className="mb-4 h-32 border-t shadow-md"
          />
        )}

        <div className="flex items-center gap-4 p-4">
          <Button type="submit" disabled={isSubmitting} variant="default">
            {isSubmitting ? 'Sending...' : 'Send Reply'}
          </Button>

          <input
            type="file"
            ref={fileInputRef}
            disabled={isSubmitting}
            multiple
            className="hidden"
            onChange={handleFileChange}
          />
          <div onClick={handleFileInputClick} className="cursor-pointer hover:text-blue-700">
            <Paperclip />
          </div>
          {fileName && <span className="text-gray-600">{fileName}</span>}
        </div>
      </form>
    </div>
  );
};
