'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  OauthClientCreate,
  OAuthClients,
  OauthClientDelete,
  OauthClientUpdate,
} from '@/graphql/declarations/oauth';
import { useMutation, useQuery } from '@apollo/client';
import {
  DialogHeader,
  DialogTitle,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Spinner } from '@/components/ui/spinner';
import { Trash2, Copy, CheckCircle2, Pencil } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';

type OAuthClient = {
  __typename?: 'OAuth_Client' | undefined;
  id: string;
  clientId?: string | null | undefined;
  redirectUri?: any;
  grantTypes?: string | null | undefined;
  scope?: string | null | undefined;
  createdAt?: any;
  authCodesCount?: number | null | undefined;
  tokensCount?: number | null | undefined;
  group?:
    | {
        id: string;
        name?: string | null | undefined;
        actualName?: string | null | undefined;
        labelName?: string | null | undefined;
      }
    | null
    | undefined;
};

type NewClientCredentials = {
  id: string;
  clientId: string;
  clientSecret: string;
} | null;

export const TOKEN_PERMISSIONS = {
  MER_PMT: {
    key: 'MER_PMT',
    description: 'Create and Accept Payments via Paylink',
  },
  READ_DATA: {
    key: 'READ_DATA',
    description: 'Read access to account data',
  },
  WRITE_DATA: {
    key: 'WRITE_DATA',
    description: 'Write access to account data',
  },
  ADMIN: {
    key: 'ADMIN',
    description: 'Full administrative access',
  },
  WEBHOOK: {
    key: 'WEBHOOK',
    description: 'Access to webhook configuration',
  },
};

export default function OAuthClientManager({ groupId }: { groupId: string }) {
  const [redirectUri, setRedirectUri] = useState('');
  const [selectedScopes, setSelectedScopes] = useState<Record<string, boolean>>({});
  const [clientIdToDelete, setClientIdToDelete] = useState('');
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [newClientCredentials, setNewClientCredentials] = useState<NewClientCredentials>(null);
  const [showCredentialsDialog, setShowCredentialsDialog] = useState(false);
  const [copiedClientId, setCopiedClientId] = useState(false);
  const [copiedClientSecret, setCopiedClientSecret] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [clientToEdit, setClientToEdit] = useState<OAuthClient | null>(null);
  const [editRedirectUri, setEditRedirectUri] = useState('');
  const [editSelectedScopes, setEditSelectedScopes] = useState<Record<string, boolean>>({});
  const [regenerateSecret, setRegenerateSecret] = useState(false);
  const [updatedCredentials, setUpdatedCredentials] = useState<NewClientCredentials>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const { toast } = useToast();

  const { data, loading, refetch } = useQuery(OAuthClients, {
    variables: {
      where: {
        group: {
          id: {
            equals: groupId,
          },
        },
      },
      take: 10,
      skip: 0,
    },
    skip: !groupId,
  });

  const [createOAuthClient, { loading: isCreating }] = useMutation(OauthClientCreate, {
    onCompleted: (data) => {
      if (data?.oauthclient_create) {
        const { id, clientId, clientSecret } = data.oauthclient_create;

        setNewClientCredentials({
          id,
          clientId,
          clientSecret,
        });

        setShowCredentialsDialog(true);
      }

      setRedirectUri('');
      setSelectedScopes({});
      refetch();
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const [updateOAuthClient, { loading: isUpdating }] = useMutation(OauthClientUpdate, {
    onCompleted: (data) => {
      if (data?.oauthclient_update) {
        toast({
          title: 'Success',
          description: 'OAuth client updated successfully',
        });

        // If secret was regenerated, show the new credentials
        if (regenerateSecret) {
          const { id, clientId, clientSecret } = data.oauthclient_update;
          setUpdatedCredentials({
            id,
            clientId,
            clientSecret: clientSecret || '',
          });
          setShowCredentialsDialog(true);
        }
      }

      setClientToEdit(null);
      setEditDialogOpen(false);
      setEditRedirectUri('');
      setEditSelectedScopes({});
      setRegenerateSecret(false);
      refetch();
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const [deleteOAuthClient, { loading: isDeleting }] = useMutation(OauthClientDelete, {
    onCompleted: () => {
      toast({
        title: 'Success',
        description: 'OAuth client deleted successfully',
      });
      refetch();
      setConfirmDeleteOpen(false);
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
      setConfirmDeleteOpen(false);
    },
  });

  const handleCreateClient = () => {
    if (!groupId) {
      toast({
        title: 'Error',
        description: 'Please select a location first',
        variant: 'destructive',
      });
      return;
    }

    // Convert selected scopes to space-separated string
    const scopeString = Object.entries(selectedScopes)
      .filter(([_, selected]) => selected)
      .map(([key, _]) => key)
      .join(' ');

    createOAuthClient({
      variables: {
        input: {
          redirectUri: redirectUri ? [redirectUri] : [],
          groupID: groupId,
          scope: scopeString || null,
        },
      },
    });

    // Close the creation dialog - the credentials dialog will show instead
    setCreateDialogOpen(false);
  };

  const handleDeleteClient = () => {
    if (!clientIdToDelete) return;

    deleteOAuthClient({
      variables: {
        where: [{ id: clientIdToDelete }],
      },
    });
  };

  const openDeleteConfirmation = (client: OAuthClient) => {
    setClientIdToDelete(client.id);
    setConfirmDeleteOpen(true);
  };

  const openEditDialog = (client: OAuthClient) => {
    setClientToEdit(client);

    // Set initial values based on the client to edit
    setEditRedirectUri(client.redirectUri?.[0] || '');

    // Initialize scope checkboxes based on the client's scope
    const scopeMap: Record<string, boolean> = {};
    if (client.scope) {
      const scopes = client.scope.split(' ');
      Object.keys(TOKEN_PERMISSIONS).forEach((key) => {
        scopeMap[key] = scopes.includes(key);
      });
    }
    setEditSelectedScopes(scopeMap);
    setRegenerateSecret(false);

    setEditDialogOpen(true);
  };

  const handleUpdateClient = () => {
    if (!clientToEdit || !clientToEdit.id || !groupId) return;

    // Convert selected scopes to space-separated string
    const scopeString = Object.entries(editSelectedScopes)
      .filter(([_, selected]) => selected)
      .map(([key, _]) => key)
      .join(' ');

    updateOAuthClient({
      variables: {
        input: {
          id: clientToEdit.id,
          redirectUri: editRedirectUri ? [editRedirectUri] : [],
          groupID: groupId,
          scope: scopeString ?? undefined,
          regenerateSecret: regenerateSecret ?? undefined,
        },
      },
    });
  };

  const toggleScope = (key: string) => {
    setSelectedScopes((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const toggleEditScope = (key: string) => {
    setEditSelectedScopes((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const copyToClipboard = async (text: string, type: 'clientId' | 'clientSecret') => {
    try {
      await navigator.clipboard.writeText(text);

      if (type === 'clientId') {
        setCopiedClientId(true);
        setTimeout(() => setCopiedClientId(false), 2000);
      } else {
        setCopiedClientSecret(true);
        setTimeout(() => setCopiedClientSecret(false), 2000);
      }

      toast({
        title: 'Copied to clipboard',
        description: `${type === 'clientId' ? 'Client ID' : 'Client Secret'} has been copied to clipboard.`,
      });
    } catch (err) {
      toast({
        title: 'Failed to copy',
        description: 'Could not copy to clipboard. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6 p-6">
      <DialogHeader>
        <DialogTitle className="text-2xl">OAuth Client Management</DialogTitle>
      </DialogHeader>

      <div className="flex items-center justify-between">
        <h3 className="font-medium">Existing OAuth Clients</h3>
        <Button onClick={() => setCreateDialogOpen(true)} className="flex items-center gap-1">
          <span className="text-sm font-medium">Create New OAuth Client</span>
        </Button>
      </div>

      <div className="space-y-4">
        {loading ? (
          <div className="flex justify-center py-4">
            <Spinner className="h-6 w-6" />
          </div>
        ) : data?.oAuthClients && data.oAuthClients.length > 0 ? (
          <div className="max-h-[300px] space-y-4 overflow-y-auto pr-1">
            {data.oAuthClients.map((client: OAuthClient) => (
              <Card key={client.id}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Client ID: {client.clientId || 'N/A'}</CardTitle>
                </CardHeader>
                <CardContent className="pb-4 pt-0 text-sm">
                  {client.redirectUri && (
                    <p className="text-slate-600">Redirect URI: {client.redirectUri}</p>
                  )}
                  {client.scope && (
                    <div className="text-slate-600">
                      <span className="font-medium">Scopes:</span>{' '}
                      {client.scope.split(' ').map((scope) => (
                        <span
                          key={scope}
                          className="mr-1 inline-flex items-center rounded-full bg-slate-100 px-2 py-0.5 text-xs"
                        >
                          {scope}
                          {TOKEN_PERMISSIONS[scope as keyof typeof TOKEN_PERMISSIONS] && (
                            <span className="ml-1 text-slate-500">
                              -{' '}
                              {
                                TOKEN_PERMISSIONS[scope as keyof typeof TOKEN_PERMISSIONS]
                                  .description
                              }
                            </span>
                          )}
                        </span>
                      ))}
                    </div>
                  )}
                  <p className="mt-2 text-slate-600">
                    Created:{' '}
                    {client.createdAt ? new Date(client.createdAt).toLocaleString() : 'N/A'}
                  </p>
                </CardContent>
                <CardFooter className="border-t border-slate-100 pt-3">
                  <div className="ml-auto flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(client)}
                      className="flex items-center gap-1"
                    >
                      <Pencil className="h-4 w-4" /> Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => openDeleteConfirmation(client)}
                      className="flex items-center gap-1"
                    >
                      <Trash2 className="h-4 w-4" /> Delete
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <p className="text-center text-slate-500">No OAuth clients found</p>
        )}
      </div>

      {/* Create OAuth Client Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="p-4 sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New OAuth Client</DialogTitle>
            <DialogDescription>
              Create a new OAuth client for your application to authenticate with.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="redirectUri">Redirect URI (Optional)</Label>
              <Input
                id="redirectUri"
                value={redirectUri}
                onChange={(e) => setRedirectUri(e.target.value)}
                placeholder="https://your-app.com/callback"
              />
            </div>

            <div className="space-y-2">
              <Label>Scopes (Optional)</Label>
              <div className="space-y-2 rounded-md border border-input p-3">
                {Object.entries(TOKEN_PERMISSIONS).map(([key, { description }]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <Checkbox
                      id={`scope-${key}`}
                      checked={!!selectedScopes[key]}
                      onChange={() => toggleScope(key)}
                    />
                    <Label htmlFor={`scope-${key}`} className="cursor-pointer text-sm font-normal">
                      {key} - {description}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateClient} disabled={isCreating}>
              {isCreating ? <Spinner className="mr-2 h-4 w-4" /> : null}
              Create Client
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDeleteOpen} onOpenChange={setConfirmDeleteOpen}>
        <DialogContent className="p-4 sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this OAuth client? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setConfirmDeleteOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteClient} disabled={isDeleting}>
              {isDeleting ? <Spinner className="mr-2 h-4 w-4" /> : null}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit OAuth Client Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="p-4 sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit OAuth Client</DialogTitle>
            <DialogDescription>
              Update the OAuth client settings. Regenerating the secret will invalidate the existing
              one.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="editRedirectUri">Redirect URI (Optional)</Label>
              <Input
                id="editRedirectUri"
                value={editRedirectUri}
                onChange={(e) => setEditRedirectUri(e.target.value)}
                placeholder="https://your-app.com/callback"
              />
            </div>

            <div className="space-y-2">
              <Label>Scopes (Optional)</Label>
              <div className="max-h-[150px] space-y-2 overflow-y-auto rounded-md border border-input p-3">
                {Object.entries(TOKEN_PERMISSIONS).map(([key, { description }]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <Checkbox
                      id={`edit-scope-${key}`}
                      checked={!!editSelectedScopes[key]}
                      onChange={() => toggleEditScope(key)}
                    />
                    <Label
                      htmlFor={`edit-scope-${key}`}
                      className="cursor-pointer text-sm font-normal"
                    >
                      {key} - {description}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-2 rounded-md border border-input bg-amber-50 p-3">
              <Checkbox
                id="regenerateSecret"
                checked={regenerateSecret}
                onChange={() => setRegenerateSecret(!regenerateSecret)}
              />
              <div className="flex flex-col">
                <Label
                  htmlFor="regenerateSecret"
                  className="cursor-pointer text-sm font-semibold text-amber-800"
                >
                  Regenerate Client Secret
                </Label>
                <p className="text-xs text-amber-600">
                  This will invalidate the current secret. All applications using this client will
                  need to be updated.
                </p>
              </div>
            </div>
          </div>

          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateClient} disabled={isUpdating}>
              {isUpdating ? <Spinner className="mr-2 h-4 w-4" /> : null}
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Credentials Dialog */}
      <Dialog
        open={showCredentialsDialog}
        onOpenChange={(open) => {
          setShowCredentialsDialog(open);
          if (!open) {
            setNewClientCredentials(null);
            setUpdatedCredentials(null);
          }
        }}
      >
        <DialogContent className="w-fit p-4">
          <DialogHeader>
            <DialogTitle>
              {updatedCredentials ? 'OAuth Client Updated' : 'OAuth Client Created Successfully'}
            </DialogTitle>
            <DialogDescription>
              Please save these credentials securely. The client secret will not be shown again.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label className="font-medium">Client ID</Label>
              <div className="flex items-center rounded-md border border-input bg-gray-50 p-2">
                <code className="flex-1 text-sm">
                  {(updatedCredentials || newClientCredentials)?.clientId}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() =>
                    copyToClipboard(
                      (updatedCredentials || newClientCredentials)?.clientId || '',
                      'clientId',
                    )
                  }
                >
                  {copiedClientId ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="font-medium">Client Secret</Label>
              <div className="flex items-center rounded-md border border-input bg-gray-50 p-2">
                <code className="flex-1 overflow-x-auto text-sm">
                  {(updatedCredentials || newClientCredentials)?.clientSecret}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() =>
                    copyToClipboard(
                      (updatedCredentials || newClientCredentials)?.clientSecret || '',
                      'clientSecret',
                    )
                  }
                >
                  {copiedClientSecret ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          <p className="mt-2 text-sm text-amber-600">
            ⚠️ This is the only time the client secret will be displayed. Store it safely.
          </p>

          <DialogFooter>
            <Button onClick={() => setShowCredentialsDialog(false)} className="mt-2">
              I've saved these credentials
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
