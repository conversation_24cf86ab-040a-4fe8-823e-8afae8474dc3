'use client';

import { useState } from 'react';

import { getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';

import DefaultTable from '@/components/globals/Tables';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/search-input';
import { useTableColumns } from './components/columns';
import Loading from './components/loading';
import { Filter, Plus, Search } from '@/assets/svg/navigation';
import { Dialog, DialogContent, DialogDescription, DialogTrigger } from '@/components/ui/dialog';

const GroupPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const pageIndex = 0;
  const pageSize = 10;
  const [selectedRow, setSelectedRow] = useState(null);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [formOpen, setFormOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [actionData, setActionData] = useState(null);
  const [formType, setFormType] = useState('');
  const [playOpen, setPlayOpen] = useState(false);
  const [pauseOpen, setPauseOpen] = useState(false);

  const togglePlay = () => {
    setPlayOpen(!playOpen);
  };

  const handleRowClick = (rowData: any) => {
    setSelectedRowData(rowData);
    setSelectedRow(rowData);
    setIsDialogOpen(true);
  };

  const columns = useTableColumns(togglePlay, setPauseOpen, setActionData);

  const table = useReactTable({
    data: [],
    columns,
    state: {},
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const handleFormSubmitComplete = () => {
    setFormOpen(false);
  };

  const handleSubmitAndCloseStart = async () => {
    setPlayOpen(false);
  };

  const handleSubmitAndCloseEnd = async () => {
    setPauseOpen(false);
  };

  const onSearchChange = () => {};
  return (
    <div>
      <div className="inline-flex h-[90px] w-full items-center justify-between gap-2 p-2 md:gap-4 md:px-8 md:py-6">
        <div className="flex w-full items-center justify-start gap-3 md:gap-6">
          <Input
            icon={Search}
            placeholder="Search for group name, id etc."
            className="flex h-9 w-3/4 items-center justify-start gap-1 rounded-lg border border-gray-400 bg-white px-2 py-1.5 text-xs text-neutral-500 2xl:h-auto 2xl:w-4/6 2xl:px-4 2xl:py-2 2xl:text-sm"
            onChange={onSearchChange}
          />

          <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
            <PopoverTrigger className="text-primary border-primary flex h-9 items-center justify-center gap-2 rounded-lg border bg-white px-2 py-1 text-xs font-bold sm:text-sm 2xl:px-4 2xl:py-2">
              <Filter />
              Filter
            </PopoverTrigger>
            <PopoverContent
              side="right"
              align="start"
              sideOffset={2}
              className="flex h-[480px] w-96 flex-col items-center justify-start overflow-y-auto rounded-lg bg-white p-0"
            >
              {/* <FilterComponent onClose={() => setIsPopoverOpen(false)} /> */}
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex w-full items-center justify-end gap-6">
          <Dialog>
            <DialogTrigger asChild>
              <Button
                variant="primary"
                className="bg-primary border-primary flex h-9 max-h-10 items-center justify-center gap-2 rounded-lg border px-2 py-1 2xl:px-4 2xl:py-2"
              >
                <Plus />
                Create or Join Group
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogDescription>test</DialogDescription>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {false ? <Loading /> : <DefaultTable body={table} head={columns} clickRow={handleRowClick} />}
    </div>
  );
};

export default GroupPage;
