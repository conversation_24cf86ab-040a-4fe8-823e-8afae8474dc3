'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';

export default function SettingsPage() {
  const [username, setUsername] = useState('johndoe');
  const [email, setEmail] = useState('<EMAIL>');

  return (
    <div className="container mx-auto mt-8">
      <div className="grid grid-cols-1 gap-3 space-y-8 sm:grid-cols-2">
        {/* Account Settings */}
        <section aria-labelledby="account-settings-heading">
          <h2
            id="account-settings-heading"
            className="mb-4 flex items-center gap-2 text-xl font-semibold"
          >
            Account Settings
          </h2>
          <div className="space-y-4">
            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="max-w-md"
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="max-w-md"
              />
            </div>
            <Button>Update Account</Button>
          </div>
        </section>
        {/* Notification Settings */}
        <section aria-labelledby="notification-settings-heading">
          <h2
            id="notification-settings-heading"
            className="mb-4 flex items-center gap-2 text-xl font-semibold"
          >
            Notification Settings
          </h2>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox id="email-notifications" />
              <Label htmlFor="email-notifications">Receive email notifications</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="push-notifications" />
              <Label htmlFor="push-notifications">Receive push notifications</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="marketing-emails" />
              <Label htmlFor="marketing-emails">Receive marketing emails</Label>
            </div>
          </div>
        </section>
        {/* Privacy Settings */}
        <section aria-labelledby="privacy-settings-heading">
          <h2
            id="privacy-settings-heading"
            className="mb-4 flex items-center gap-2 text-xl font-semibold"
          >
            Privacy Settings
          </h2>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch id="profile-visibility" />
              <Label htmlFor="profile-visibility">Make profile public</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="activity-visibility" />
              <Label htmlFor="activity-visibility">Show activity status</Label>
            </div>
          </div>
        </section>

        <Separator />

        {/* Action Buttons */}
        <div className="flex space-x-4">
          <Button variant="outline">Cancel</Button>
          <Button>Save Changes</Button>
        </div>
      </div>
    </div>
  );
}
