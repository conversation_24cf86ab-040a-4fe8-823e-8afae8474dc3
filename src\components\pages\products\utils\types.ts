export enum TaxableItem {
  inStore = 'inStore',
  online = 'online',
  both = 'both',
}

export type ProductFormData = {
  productName: string;
  category: string;
  subCategory: string;
  brand: string;
  sku: string;
  price: string;
  itemWeight: string;
  length: string;
  breadth: string;
  width: string;
  description: string;
  taxableItem: TaxableItem;
  isRecurring: boolean;
  recurringMode: string;
  recurringInterval: string; // will store number as string for form handling
  recurringFrequency: string;
  recurringTotalCycles: string;
  recurringTrialDays: string;
  recurringSetupFee: string;
};
