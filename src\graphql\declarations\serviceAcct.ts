import { graphql } from '../generated';

export const ServiceAcct_create = graphql(`
  mutation Serviceacct_create($input: Serviceacct_createInput!) {
    serviceacct_create(input: $input) {
      id
      token
      secret
    }
  }
`);

export const ServiceAcct_delete = graphql(`
  mutation ServiceAcct_delete($where: [ServiceAPIAccountWhereUniqueInput!]!) {
    deleteServiceAPIAccounts(where: $where) {
      id
    }
  }
`);

export const ServiceAcct_list = graphql(`
  query ServiceAcct_list($where: ServiceAPIAccountWhereInput!) {
    serviceAPIAccounts(where: $where) {
      active
      allowedMethods
      createdAt
      id
      ipWhitelist
      secretView
      token
      expiresAt
    }
  }
`);

export const ServiceAcct_get = graphql(`
  query ServiceAcct_get($where: ServiceAPIAccountWhereUniqueInput!) {
    serviceAPIAccount(where: $where) {
      id
      token
      secretView
      active
      ipWhitelist
      allowedMethods
      createdAt
      updatedAt
    }
  }
`);

export const ServiceAcct_update = graphql(`
  mutation ServiceAcct_update($data: [ServiceAPIAccountUpdateArgs!]!) {
    updateServiceAPIAccounts(data: $data) {
      id
    }
  }
`);
