'use client';
// import ChatwootWidget from '@/components/chatwoot/ChatwootEmbed';
import HomeLayoutComponent from '@/components/globals/HomeLayout';
import { TestModeBanner } from '@/components/globals/test-mode-banner';
import { ApolloWrapper } from '@/components/graphql/ApolloWrapper';
import { TimeIdleTracker } from '@/components/shared/components';
import { env } from 'next-runtime-env';
export default function HomeLayout({ children }: { children: React.ReactNode }) {
  // AUTHSTORE.session();
  return (
    <ApolloWrapper>
      <TimeIdleTracker />
      {env('NEXT_PUBLIC_TEST_MODE') === 'true' && <TestModeBanner />}
      <HomeLayoutComponent>{children}</HomeLayoutComponent>
      {/* <ChatwootWidget /> */}
    </ApolloWrapper>
  );
}
