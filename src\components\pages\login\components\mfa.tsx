/* eslint-disable @next/next/no-img-element */
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';

export const MFA = (props: {
  onVerify: (code: string) => void;
  onResend: () => void;
  onBack: () => void;
  error?: string;
  submitting?: boolean;
  pendingToResend?: number;
}) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      if (value !== '' && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="logo.png" />
        </CardTitle>
        <CardDescription className="text-center">
          Please enter the 6-digit code sent to your Account SMS Number.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="mx-auto flex max-w-xs justify-between">
          {code.map((digit, index) => (
            <Input
              key={index}
              id={`code-${index}`}
              type="text"
              inputMode="numeric"
              pattern="\d*"
              maxLength={1}
              className="h-12 w-12 text-center text-2xl"
              value={digit}
              onChange={(e) => handleCodeChange(index, e.target.value)}
            />
          ))}
        </div>
        <Button
          className="w-full"
          onClick={() => props.onVerify(code.join(''))}
          disabled={props.submitting}
          variant="primary"
        >
          {props.submitting ? 'Verifying...' : 'Verify'}
        </Button>
        {props.error && <p className="text-center text-xs text-red-600">{props.error}</p>}
      </CardContent>
      <CardFooter className="flex flex-col space-y-2">
        {!props.pendingToResend ? (
          <Button variant="link" onClick={props.onResend} className="text-sm text-muted-foreground">
            Resend code
          </Button>
        ) : (
          <p className="text-center text-sm text-muted-foreground">
            Resend code in {props.pendingToResend} seconds
          </p>
        )}
        <Button variant="link" onClick={props.onBack} className="text-primary text-sm">
          Go Back
        </Button>
      </CardFooter>
    </>
  );
};
