function TitleCase(str: any) {
  // Check if the input is a string
  if (typeof str !== 'string') {
    // You can either return the input as is, return a default string, or throw an error
    return str; // or return ''; or throw new Error('Input must be a string');
  }

  const smallWords = /^(a|an|and|as|at|but|by|en|for|if|in|of|on|or|the|to|v\.?|via|vs\.?)$/i;

  return str
    .toLowerCase()
    .split(' ')
    .map((word, index, array) => {
      if (index === 0 || index === array.length - 1 || !smallWords.test(word)) {
        return word.charAt(0).toUpperCase() + word.slice(1);
      }
      return word;
    })
    .join(' ')
    .replace(/([:‑–—])([a-z])/g, (match, p1, p2, offset, string) => {
      return match.slice(0, 1) + p2.toUpperCase();
    })
    .replace(/'\b/g, "'");
}

export default TitleCase;
