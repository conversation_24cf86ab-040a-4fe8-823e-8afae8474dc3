.label {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.inputText {
  flex: 1;
  position: relative;
  line-height: 125%;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.inputField {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.personal {
  align-self: stretch;
  flex: 1;
  position: relative;
  line-height: 125%;
  display: flex;
  align-items: center;
}
.chevronDownIcon {
  width: 10px;
  position: relative;
  height: 10px;
  overflow: hidden;
  flex-shrink: 0;
}
.content1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input1 {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 12px;
  color: #6b7280;
}
.column {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.label5 {
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.calendarMonthIcon {
  width: 14px;
  position: relative;
  height: 14px;
  overflow: hidden;
  flex-shrink: 0;
}
.content4 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}
.datepicker {
  width: 301.3px;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  box-sizing: border-box;
  height: 42px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.input4 {
  width: 460px;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  box-sizing: border-box;
  height: 42px;
  display: none;
}
.privacyPolicy {
  color: #111928;
}
.caption {
  width: 364px;
  position: relative;
  line-height: 125%;
  display: none;
}
.inputField4 {
  flex: 1;
  height: 42px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
  color: #6b7280;
}
.inputText3 {
  flex: 1;
  position: relative;
  line-height: 125%;
  white-space: pre-wrap;
}
.labelWrapper {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  font-size: 16px;
}
.inputField10 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 0px 4px;
}
.caption1 {
  width: 364px;
  position: relative;
  line-height: 125%;
  display: none;
  color: #6b7280;
}
.inputField11 {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 0px 0px 4px;
  gap: 8px;
}
.inputWidgetLg {
  width: 100%;
  position: relative;
  box-shadow:
    0px 1px 3px rgba(0, 0, 0, 0.1),
    0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 20px;
  box-sizing: border-box;
  gap: 8px;
  text-align: left;
  font-size: 14px;
  color: #111928;
  font-family: Inter;
}

.checkbox {
  width: 16.5px;
  border-radius: 4px;
  background-color: #1c64f2;
  border: 0.5px solid #d1d5db;
  box-sizing: border-box;
  height: 16.5px;
}
.label {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.inputField {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.checkboxParent {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 20px;
  box-sizing: border-box;
  gap: 16px;
  text-align: left;
  font-size: 14px;
  color: #111928;
  font-family: Inter;
}

.label {
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.labelWrapper {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.label1 {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.inputText {
  flex: 1;
  position: relative;
  line-height: 125%;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.inputField {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.privacyPolicy {
  color: #111928;
}
.caption {
  width: 351px;
  position: relative;
  line-height: 125%;
  display: none;
  color: #6b7280;
}
.inputField2 {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 0px 4px;
  gap: 8px;
}
.caption1 {
  width: 364px;
  position: relative;
  line-height: 125%;
  display: none;
  color: #6b7280;
}
.inputField3 {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 0px 0px 4px;
  gap: 8px;
}
.inputFieldParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  font-size: 14px;
}
