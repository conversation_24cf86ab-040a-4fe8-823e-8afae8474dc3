import {
  DocumentNode,
  OperationVariables,
  QueryHookOptions,
  TypedDocumentNode,
  useQuery,
} from '@apollo/client';
import { useState, useEffect } from 'react';

// Define types with generics for better type safety
type UseDataGridViewProps<
  TData = any,
  TVariables extends OperationVariables = OperationVariables,
> = {
  query: DocumentNode | TypedDocumentNode<TData, TVariables>;
  options?: QueryHookOptions<TData, TVariables>;
  pageSize?: number;
  currentPage?: number;
  searchParams?: string;
  sortField?: string;
  isAscendOrder?: boolean;
  initialFilterField?: string;
  initialFilterValue?: string;
};

export const useDataGridView = <
  TData = any,
  TVariables extends OperationVariables = OperationVariables,
>({
  initialFilterField,
  initialFilterValue,
  pageSize: pageSizeInitial = 10,
  currentPage: currentPageInitial = 1,
  searchParams = '',
  query: queryDocument,
  options: queryOptions = {},
  sortField: sortFieldInitial = undefined,
  isAscendOrder: isAscendOrderInitial = false,
}: UseDataGridViewProps<TData, TVariables>) => {
  const [pageSize, setPageSize] = useState(pageSizeInitial);
  const [currentPage, setCurrentPage] = useState(currentPageInitial);
  const [isRefetching, setIsRefetching] = useState(false);
  const [sortField, setSortField] = useState<string | undefined>(sortFieldInitial);
  const [isAscendOrder, setIsAscendOrder] = useState<boolean>(isAscendOrderInitial);
  const [filterField, setFilterField] = useState<string | undefined>(initialFilterField);
  const [filterValue, setFilterValue] = useState<string | undefined>(initialFilterValue);

  const variablesWithPagination: TVariables = {
    ...queryOptions.variables,
  } as unknown as TVariables;

  const { data, loading, error, refetch } = useQuery<TData, TVariables>(queryDocument, {
    ...queryOptions,
    variables: variablesWithPagination,
    skip: variablesWithPagination.input.groupID === '',
  });

  useEffect(() => {
    if (variablesWithPagination.input.groupID !== '') {
      setIsRefetching(true);
      refetch({
        ...variablesWithPagination,
        input: {
          groupID: variablesWithPagination.input.groupID,
          data: {
            ...variablesWithPagination.input.data,
            page: {
              ...variablesWithPagination.input.data.page,
              page: currentPage,
              pageSize: pageSize,
              sort: sortField
                ? {
                    field: sortField,
                    order: isAscendOrder ? 'asc' : 'desc',
                  }
                : undefined,
              search: searchParams,
              filter:
                filterValue && filterField && filterValue !== 'All'
                  ? {
                      field: filterField,
                      operation: 'equals',
                      value: filterValue,
                    }
                  : undefined,
            },
          },
        },
      }).finally(() => {
        setIsRefetching(false);
      });
    }
  }, [pageSize, currentPage, sortField, isAscendOrder, searchParams, filterField, filterValue]);

  return {
    maxVariables: {
      ...variablesWithPagination,
      input: {
        groupID: variablesWithPagination.input.groupID,
        data: {
          ...variablesWithPagination.input.data,
          page: {
            ...variablesWithPagination.input.data.page,
            page: 1,
            pageSize: 50000,
            sort: sortField
              ? {
                  field: sortField,
                  order: isAscendOrder ? 'asc' : 'desc',
                }
              : undefined,
            search: searchParams,
            filter:
              filterValue && filterField && filterValue !== 'All'
                ? {
                    field: filterField,
                    operation: 'equals',
                    value: filterValue,
                  }
                : undefined,
          },
        },
      },
    },
    data,
    loading: isRefetching || loading,
    error,
    pageSize,
    currentPage,
    refetch,
    onPageChange: setCurrentPage,
    onPageSizeChange: setPageSize,
    setSortField,
    setIsAscendOrder,
    filterField,
    filterValue,
    setFilterField,
    setFilterValue,
  };
};
