import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

const Checkbox = React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>(
  ({ className, ...props }, ref) => (
    <div className="relative flex h-4 w-4 items-center justify-center">
      <input
        type="checkbox"
        ref={ref}
        className={cn(
          'dark:border-gray-600" peer h-4 w-4 rounded border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600',
          className,
        )}
        {...props}
      />
      <Check className="pointer-events-none z-40 h-3 w-3 text-white opacity-0 peer-checked:opacity-100 dark:text-gray-900" />
    </div>
  ),
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };
