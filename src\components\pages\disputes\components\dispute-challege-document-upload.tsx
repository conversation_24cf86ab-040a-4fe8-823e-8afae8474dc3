import { Button, Select } from 'flowbite-react';
import React, { useState } from 'react';
import { FaCircleCheck } from 'react-icons/fa6';
import { HiUpload } from 'react-icons/hi';
import { DisputeChallengeDocumentParams } from '../utils';

type DisputeChallengeDocumentUpload = {
  uploadDocument: (value: DisputeChallengeDocumentParams) => void;
  disable?: boolean;
  isLoading?: boolean;
};

export const DisputeChallengeDocumentUpload = ({
  uploadDocument,
  disable = false,
  isLoading = false,
}: DisputeChallengeDocumentUpload) => {
  const [selectedFileType, setSelectedFileType] = useState('');
  const [files, setFiles] = useState<File | null>(null);

  const onFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    const dragFiles = e.dataTransfer.files[0]; // Get the files from the drag event

    // Convert FileList to an array and update the state
    if (dragFiles) {
      setFiles(dragFiles);
    }
  };

  const onFilesAttached = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target?.files?.[0];
    if (files) {
      setFiles(files);
    }
  };

  const onFileUpload = () => {
    uploadDocument({ file: files, type: selectedFileType });
    setFiles(null);
    setSelectedFileType('');
  };

  return (
    <div className="flex gap-2">
      <div
        className="rounded-lg border-2 border-dashed border-gray-300 p-2 text-center"
        onDragOver={(e) => {
          if (!disable) {
            e.preventDefault();
          }
        }}
        onDrop={(e) => {
          if (!disable) {
            onFileDrop(e);
          }
        }}
      >
        <HiUpload className="mx-auto mb-2 h-12 w-12 text-gray-400" />
        <p className="text-gray-600">Drag files here to upload</p>

        <label htmlFor="dropzone-file">
          <p className="cursor-pointer text-sm text-blue-600 hover:underline">
            or browse for files
          </p>
          <input
            id="dropzone-file"
            type="file"
            className="hidden"
            onChange={onFilesAttached}
            disabled={disable || isLoading}
            accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
          />
        </label>
      </div>
      <div className="mt-4 w-full">
        <div className="flex justify-between gap-2">
          {/* {files.length > 0 && (
       
      )} */}
          <div className="flex justify-between pb-1 pt-1">
            <div>
              <span className="font-medium">{files?.name}</span>
            </div>
            <div className="flex gap-2"></div>
          </div>
          <div className="flex items-center justify-center gap-2">
            <Button
              color="gray"
              onClick={onFileUpload}
              disabled={!files || !selectedFileType || disable || isLoading}
              size="sm"
            >
              {isLoading ? (
                <>
                  <span className="mr-2">Uploading...</span>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                </>
              ) : (
                'Upload'
              )}
            </Button>
            {files && !isLoading && <FaCircleCheck color="green" size={20} />}
          </div>
        </div>
        <div>
          <label className="text-sm font-medium">File Type:</label>
          <Select
            value={selectedFileType}
            disabled={disable || isLoading}
            onChange={(e) => setSelectedFileType(e.target.value)}
          >
            <option value="">Select type</option>
            <option value="Sales Receipt">Sales Receipt</option>
            <option value="Proof of Delivery">Proof of Delivery</option>
          </Select>
        </div>
      </div>
    </div>
  );
};
