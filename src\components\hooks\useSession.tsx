import { useEffect, useState } from 'react';

const generateRandomString = (length: number) => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

export const useBrowserSessionID = () => {
  const [sessionID, setSessionID] = useState<string>('');

  useEffect(() => {
    const session = localStorage.getItem('ngn-sessionID');
    if (session) {
      setSessionID(session);
    } else {
      const newSession = generateRandomString(20);
      localStorage.setItem('ngn-sessionID', newSession);
      setSessionID(newSession);
    }
  }, []);

  return {
    id: sessionID,
    regenerate: () => {
      const newSession = generateRandomString(20);
      localStorage.setItem('ngn-sessionID', newSession);
      setSessionID(newSession);
    },
  };
};
