import { ApolloLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { ApolloClient, InMemoryCache } from '@apollo/experimental-nextjs-app-support';
import axios from 'axios';
import { buildAxiosFetch } from '@lifeomic/axios-fetch';
import createUploadLink from 'apollo-upload-client/createUploadLink.mjs';
import { AUTHSTORE } from '../auth-storage';
import { env } from 'next-runtime-env';
import { createMockApolloClient, shouldUseMockMode } from './MockApolloClient';

// Create auth link that reads latest token on each request
const authLink = setContext(async (_, { headers }) => {
  const token = AUTHSTORE.get();
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    },
  };
});

const axiosInstance = axios.create({
  baseURL: (env('NEXT_PUBLIC_SERVER_URL') ?? '') + env('NEXT_PUBLIC_GRAPHQL_URL'),
  onUploadProgress: (progressEvent) => {},
});

// Configure file upload link
const fileUploadLink = createUploadLink({
  uri: (env('NEXT_PUBLIC_SERVER_URL') ?? '') + env('NEXT_PUBLIC_GRAPHQL_URL'),
  fetch: (input, init) => buildAxiosFetch(axiosInstance)(input as RequestInfo, init),
});

// Combine links
export const apolloLinks = ApolloLink.from([authLink, fileUploadLink]);

// Create the appropriate Apollo client based on mode
const createApolloClient = () => {
  // Use mock client if in mock mode
  if (shouldUseMockMode()) {
    return createMockApolloClient();
  }

  // Use real client for production
  return new ApolloClient({
    cache: new InMemoryCache({
      typePolicies: {
        User: {
          fields: {
            flag_canAffiliate: {
              // Always read value from cache first
              read(existing) {
                return existing;
              },
              // Merge new values into cache on write
              merge(existing, incoming) {
                return incoming;
              },
            },
          },
        },
      },
    }),
    link: apolloLinks,
  });
};

// Export the Apollo client
export const apolloClient = createApolloClient();
