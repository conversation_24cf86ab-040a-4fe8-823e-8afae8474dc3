'use client';

import { useForm, FormProvider } from 'react-hook-form';
import { emailPatternRegex, message } from '@/components/shared/utils';
import { useEffect, useState } from 'react';
import styles from './index.module.css';
import Image from 'next/image';
import { cn, int2DecToFloat, moneyFormat, moneyFormatString } from '@/lib/utils';
import { TSEPHostedTokenizerComponent } from '@/components/payments/tsep/tokenizer';
import { axiosClient } from '@/lib/axios';
import type { ProductInput, ProductComputeResponse } from './restloader';
import { PaymentSuccess } from './PaymentSuccess';
import { PaymentLoading } from './PaymentLoading';
import { PriceBreakdown } from './PriceBreakdown';
import { OrderItems } from './OrderItems';
import StateCountryForms from '../shared/components/stateCountryFormSelect';
import { ACHHostedComponent } from '@/components/payments/ach-local';
import { CardGLPHostedComponent } from '../payments/glpv2';
import { SubscriptionItems } from './SubscriptionItems';
import { useQuery } from '@apollo/client';
import { GET_BANK_INFO_BY_ROUTING } from '@/graphql/declarations/processor-test';
import { Button, Modal } from 'flowbite-react';
import { FormInput } from '../globals';

export interface PaymentDiscount {
  code: string;
  amount: number;
}

export interface PaymentLineItem {
  id: string;
  name: string;
  description: string;
  price: number;
  amount: number;
  total: number;

  isRecurring: boolean;
  recurringMode: string;
  recurringFrequency: number;
  recurringInterval: number;
  recurringSetupFee: number;
  recurringTotalCycles: number;
  recurringTrialDays: number;
}

export interface PaymentCalculations {
  'Original Price': number;
  'Processing Surcharge': number;
  Shipping: number;
  Tax: number;
  Tip: number;
  Discount: number;
  subscription: number;
}

export interface PaymentFunctionArgs {
  fullName: string;
  companyName: string;
  email: string;
  phoneNumber: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  cardToken: string;
  cvc: string;
  expiryDate: string;
  ip?: string;
  accountNumber?: string;
  routingNumber?: string;
  accountType?: string;
  holderType?: string;
  gpEcomm?: string;
  paymentMethod: 'card' | 'ach' | 'gpecomm' | '';
}

export interface PaymentFunctionReturn {
  status: string;
  transactionID: string;
}
export interface PaymentFormProps {
  paymentData: ProductInput.ProductInput;
  initialValues?: Partial<PaymentFunctionArgs>;
  initialLineItems?: PaymentLineItem[];
  initialDiscounts?: PaymentDiscount[];
  initialCalculations?: Partial<PaymentCalculations>;
  initialTotal?: number;
  initialSubscriptionTotal?: number;
  onBeforePayment?: () => Promise<boolean>;
  onAfterPayment?: (args: {
    success: boolean;
    transactionID: string;
    onSuccessUrl?: string;
    onFailureUrl?: string;
  }) => void;
  onSetDiscounts?: (discounts: PaymentDiscount[]) => void;
  customPaymentFunction?: (formData: PaymentFunctionArgs) => Promise<PaymentFunctionReturn>;
  onTipChange?: (tip: number) => void;
  customAmounts?: { [key: string]: number };
  setCustomAmounts?: (customAmounts: { [key: string]: number }) => void;
  discountCodes?: string[];
  tipAmount?: number;
  paymentType?: string;
  setPaymentType?: (paymentType: string) => void;
  options?: {
    allowEdit?: boolean;
    allowExtraDiscount?: boolean;
    allowTip?: boolean;
    disabledCC?: boolean;
    disabledACH?: boolean;
    onSuccessUrl?: string;
    onFailureUrl?: string;
    disableCustomAddress?: boolean;
    disableCustomPayment?: boolean;
    disablePreselectCard?: boolean;
    overideLineItemsAmount?: boolean;
    customerData?: ProductComputeResponse.CustomerData;
    customerID?: string;
    customerPaymentID?: string;
    transactionHistory?: {
      transactionID: string;
      date: string;
      amount: number;
      status: string;
      note: string;
    }[];
  };
}

function ConfirmTransactionModal(args: {
  showModal: boolean;
  handleCancel: () => void;
  handleConfirm: () => void;
  isProcessEntry: boolean;
  total?: number;
  calculations?: Partial<PaymentCalculations>;
  watch: any;
}) {
  // console.log(args.calculations);
  const { showModal, handleCancel, handleConfirm, isProcessEntry, watch, calculations } = args;

  const { data: bankData } = useQuery(GET_BANK_INFO_BY_ROUTING, {
    variables: {
      input: {
        code: watch['routingNumber'],
      },
    },
    skip: !watch['routingNumber'],
  });

  const getBankInfo = () => {
    if (watch.routingNumber) {
      return bankData?.processor_tst_bank_routing?.data.name || 'Unknown Bank';
    }
    return watch.brand || 'Unknown Card';
  };

  const formatAmount = (amount: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(int2DecToFloat(parseFloat(amount)) || 0);
  };

  const getPaymentMethod = () => {
    if (watch.routingNumber) {
      return `ACH Payment - ${getBankInfo()}`;
    } else if (watch.cardToken) {
      return `Card Payment - ${watch.brand}`;
    } else if (watch.gpEcomm) {
      return `Card Payment`;
    }
  };

  return (
    <Modal show={showModal} onClose={handleCancel}>
      <Modal.Header>Confirm Transaction</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          <div className="text-lg font-semibold">Transaction Details:</div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Payment Method:</span>
              <span>{getPaymentMethod()}</span>
            </div>

            {/* Add breakdown section */}
            {calculations && (
              <div className="space-y-1 border-t border-gray-200 pt-2">
                {calculations['Original Price'] !== undefined &&
                  calculations['Original Price'] > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>Original Price:</span>
                      <span>{formatAmount(calculations['Original Price']?.toString() || '0')}</span>
                    </div>
                  )}
                {calculations['Processing Surcharge'] !== undefined &&
                  calculations['Processing Surcharge'] > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>Processing Fee:</span>
                      <span>
                        {formatAmount(calculations['Processing Surcharge']?.toString() || '0')}
                      </span>
                    </div>
                  )}
                {calculations['Shipping'] !== undefined && calculations['Shipping'] > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Shipping:</span>
                    <span>{formatAmount(calculations['Shipping']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Tax'] !== undefined && calculations['Tax'] > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Tax:</span>
                    <span>{formatAmount(calculations['Tax']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Tip'] !== undefined && calculations['Tip'] > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Tip:</span>
                    <span>{formatAmount(calculations['Tip']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Discount'] !== undefined && calculations['Discount'] > 0 && (
                  <div className="flex justify-between text-sm text-red-600">
                    <span>Discount:</span>
                    <span>-{formatAmount(calculations['Discount']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Discount'] !== undefined && calculations['Discount'] > 0 && (
                  <div className="flex justify-between text-sm text-red-600">
                    <span>Discount:</span>
                    <span>-{formatAmount(calculations['Discount']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['subscription'] !== undefined && calculations['subscription'] > 0 && (
                  <div className="flex justify-between text-sm text-blue-600">
                    <span>Total Subscriptions:</span>
                    <span>{formatAmount(calculations['subscription']?.toString() || '0')}</span>
                  </div>
                )}
              </div>
            )}

            <div className="border-t pt-2">
              <div className="flex justify-between font-bold">
                <span>Total:</span>
                <span>{formatAmount((args.total ?? 0).toString())}</span>
              </div>
            </div>
          </div>
          <p className="mt-4 text-sm text-gray-600">
            Are you sure you want to proceed with this transaction?
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button color="gray" onClick={handleCancel}>
          Cancel
        </Button>
        <Button
          color={isProcessEntry ? 'blue' : 'warning'}
          className="w-full"
          onClick={handleConfirm}
        >
          Proceed
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

// Customer Card Info component to display saved payment method
function CustomerCardInfo({
  card,
  onConfirm,
}: {
  card: ProductComputeResponse.PaymentCard;
  onConfirm: () => void;
}) {
  return (
    <div className="mb-6 w-full bg-white p-4">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Saved Payment Method</h3>
        <div className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
          {card.isDefault ? 'Default' : 'Saved'}
        </div>
      </div>

      <div className="mb-4 flex items-center">
        {card.brand && (
          <div className="mr-3">
            <Image
              src={`/icons/payments/${card.brand.toLowerCase()}.png`}
              alt={card.brand}
              width={40}
              height={25}
              onError={(e) => {
                // hide the image if not found
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>
        )}
        <div>
          <p className="font-medium text-gray-800">
            {card.type === 'ach' ? 'Bank Account' : 'Card'} •••• {card.last4}
          </p>
          {card.expires && <p className="text-sm text-gray-500">Expires {card.expires}</p>}
        </div>
      </div>

      <button
        onClick={onConfirm}
        className="w-full rounded-md bg-blue-600 px-4 py-2 text-center font-medium text-white hover:bg-blue-700"
      >
        Pay with this method
      </button>
    </div>
  );
}

const PaymentForm = ({
  paymentData,
  initialValues = {},
  initialLineItems = [],
  initialDiscounts = [],
  initialCalculations = {},
  initialTotal = 0,
  initialSubscriptionTotal = 0,
  onSetDiscounts,
  onBeforePayment,
  onAfterPayment,
  customPaymentFunction,
  options,
  onTipChange,
  customAmounts,
  setCustomAmounts,
  discountCodes: di,
  tipAmount: ta,
  setPaymentType,
}: PaymentFormProps) => {
  const methods = useForm<PaymentFunctionArgs>({
    defaultValues: {
      fullName: '',
      cardToken: '',
      cvc: '',
      expiryDate: '',
      email: '',
      companyName: '',
      street: '',
      city: '',
      state: 'AZ',
      country: 'US',
      zipCode: '',
      paymentMethod: 'gpecomm',
      accountNumber: '',
      routingNumber: '',
      accountType: '',
      holderType: '',
    },
  });

  // Find the selected payment card if customerData and customerPaymentID are provided
  const selectedPaymentCard = options?.customerData?.paymentCards?.find(
    (card) => card.cardID === options.customerPaymentID,
  );

  // Check if we're using customer saved payment method
  const isUsingCustomerCard = Boolean(
    options?.customerID &&
      options?.customerPaymentID &&
      selectedPaymentCard &&
      !options?.disablePreselectCard,
  );

  const [discountInput, setDiscountInput] = useState('');
  const [discounts, setDiscounts] = useState<PaymentDiscount[]>([]);

  // Populate form with customer data or prefilled data
  useEffect(() => {
    // Immediately populate the form with initialValues regardless of customer/prefilled data
    if (initialValues) {
      Object.entries(initialValues).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          methods.setValue(key as any, value);
        }
      });
    }

    // Then override with more specific data if available
    if (options?.customerData) {
      const customerData = options.customerData;

      methods.setValue('fullName', customerData.nameOnCard || '');
      methods.setValue('email', customerData.email || '');
      methods.setValue('phoneNumber', customerData.phone || '');
      methods.setValue('street', customerData.billingAddress || '');
      methods.setValue('city', customerData.billingCity || '');
      methods.setValue('state', customerData.billingState || 'AZ');
      methods.setValue('country', customerData.billingCountry || 'US');
      methods.setValue('zipCode', customerData.billingZip || '');
    } else if (options?.disableCustomAddress && initialValues) {
      // This case ensures prefilled address data is loaded
      // (already handled by initial values above)
    }
  }, [options?.customerData, options?.disableCustomAddress, initialValues, methods]);

  useEffect(() => {
    setDiscounts(initialDiscounts);
  }, [initialDiscounts]);

  const calculations = initialCalculations;

  const total = initialTotal;
  // const [lineItems, setLineItems] = useState<PaymentLineItem[]>(initialLineItems);

  const lineItems = initialLineItems;

  const handleAddDiscount = (e) => {
    e.preventDefault();
    // Placeholder function - you can implement API call here
    const mockDiscount = { code: discountInput, amount: 0 }; // Mock amount
    if (discountInput && !discounts.some((d) => d.code === discountInput)) {
      setDiscounts([...discounts, mockDiscount]);
      setDiscountInput('');
      onSetDiscounts?.([...discounts, mockDiscount]);
    }
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [paymentMethod, setPaymentMethod] = useState<'card' | 'ach' | 'gpecomm' | ''>('');

  useEffect(() => {
    let availabelOptions: string[] = [];
    if (!options?.disabledCC) {
      availabelOptions.push('gpecomm');
    }
    if (!options?.disabledACH) {
      availabelOptions.push('ach');
    }

    // If disableCustomPayment is true and we have a selected card, use its payment method
    if (options?.disableCustomPayment && selectedPaymentCard) {
      if (selectedPaymentCard.type === 'ach') {
        setPaymentMethod('ach');
      } else if (selectedPaymentCard.gpEcommID) {
        setPaymentMethod('gpecomm');
      } else {
        setPaymentMethod('card');
      }
    } else {
      setPaymentMethod(availabelOptions[0] || ('' as any));
    }
  }, [
    options?.disabledCC,
    options?.disabledACH,
    options?.disableCustomPayment,
    selectedPaymentCard,
  ]);

  useEffect(() => {
    setPaymentType?.(paymentMethod);
    methods.setValue('paymentMethod', paymentMethod);
  }, [paymentMethod]);

  const getIP = async () => {
    try {
      const resp = await fetch('https://api.ipify.org?format=json');
      const data = await resp.json();
      return data.ip;
    } catch (error) {
      console.error('Failed to get IP:', error);
      return '';
    }
  };

  const standardPayment = async (formData: PaymentFunctionArgs): Promise<PaymentFunctionReturn> => {
    const data = await axiosClient.post('/api/gateway/createTokenizedManualEntry', {
      groupID: paymentData.groupID,
      token: paymentData.token,
      data: {
        paymentDataID: paymentData.data.paymentDataID,
        paymentData: paymentData.data.paymentData,
        customerInfo: {
          nameOnCard: formData.fullName,
          email: formData.email,
          phoneNumber: formData.phoneNumber,
          billingAddress: formData.street,
          billingCity: formData.city,
          billingState: formData.state,
          billingZip: formData.zipCode,
          billingCountry: formData.country,
          paymentType: paymentMethod,
          gpecomm:
            paymentMethod === 'gpecomm'
              ? {
                  id: formData.gpEcomm,
                }
              : undefined,
          card:
            paymentMethod === 'card'
              ? {
                  cardToken: formData.cardToken,
                  cvc: formData.cvc,
                  expiryDate: formData.expiryDate,
                }
              : undefined,
          ach:
            paymentMethod === 'ach'
              ? {
                  accountNumber: formData.accountNumber,
                  routingNumber: formData.routingNumber,
                  accountType: formData.accountType,
                  holderType: formData.holderType,
                }
              : undefined,
        },
        dynamicData: {
          discountCodes: di,
          tip: {
            amount: ta,
            type: 'percentage',
          },
          quantityAmounts: Object.keys(customAmounts ?? {}).map((key) => ({
            id: key,
            quantity: (customAmounts ?? {})[key],
          })),
        },
      },
    });

    return {
      status: data.data.status,
      transactionID: data.data.transaction,
    };
  };

  // Customer card payment function for processing existing payment methods
  const customerCardPayment = async (
    formData: PaymentFunctionArgs,
  ): Promise<PaymentFunctionReturn> => {
    const ip = await getIP();

    // Set the payment method based on customer's card
    if (selectedPaymentCard) {
      if (selectedPaymentCard.type === 'ach') {
        methods.setValue('paymentMethod', 'ach');
      } else if (selectedPaymentCard.gpEcommID) {
        methods.setValue('paymentMethod', 'gpecomm');
      } else {
        methods.setValue('paymentMethod', 'card');
      }
    }

    const data = await axiosClient.post('/api/gateway/createTokenizedManualEntry', {
      groupID: paymentData.groupID,
      token: paymentData.token,
      data: {
        paymentDataID: paymentData.data.paymentDataID,
        paymentData: paymentData.data.paymentData,
        customerInfo: {
          customerID: options?.customerID,
          customerCardID: options?.customerPaymentID,
          paymentType: paymentMethod,
          billingZip: formData.zipCode,
          ...(paymentMethod === 'gpecomm'
            ? {
                gpecomm: {
                  cvv: formData.cvc || '111', // Default CVV if not provided
                },
              }
            : {}),
          ...(paymentMethod === 'card'
            ? {
                card: {
                  cvc: formData.cvc,
                },
              }
            : {}),
        },
        dynamicData: {
          discountCodes: di,
          tip: {
            amount: ta,
            type: 'percentage',
          },
          quantityAmounts: Object.keys(customAmounts ?? {}).map((key) => ({
            id: key,
            quantity: (customAmounts ?? {})[key],
          })),
        },
      },
    });

    return {
      status: data.data.status,
      transactionID: data.data.transaction,
    };
  };

  const submitPayment = async (formData) => {
    try {
      setIsSubmitting(true);
      setPaymentStatus('idle');
      setErrorMessage('');

      if (onBeforePayment) {
        const shouldContinue = await onBeforePayment();
        if (!shouldContinue) {
          setIsSubmitting(false);
          onAfterPayment?.({
            success: false,
            transactionID: '',
            onSuccessUrl: options?.onSuccessUrl,
            onFailureUrl: options?.onFailureUrl,
          });
          return;
        }
      }

      // Your existing payment processing logic here
      const ip = await getIP();
      // ... implement your payment processing ...

      let data: PaymentFunctionReturn | undefined;
      formData.ip = ip;

      if (
        (isUsingCustomerCard || (options?.disableCustomPayment && selectedPaymentCard)) &&
        options?.customerID &&
        options?.customerPaymentID
      ) {
        data = await customerCardPayment(formData);
      } else if (customPaymentFunction) {
        data = await customPaymentFunction(formData);
      } else {
        data = await standardPayment(formData);
      }

      if (data.status !== 'CAPTURED') {
        throw new Error('Payment failed. Please try again.');
      }

      setPaymentStatus('success');
      onAfterPayment?.({
        success: true,
        transactionID: data.transactionID,
        onSuccessUrl: options?.onSuccessUrl,
        onFailureUrl: options?.onFailureUrl,
      });
    } catch (error) {
      console.error('Payment failed:', error);
      setPaymentStatus('error');
      // @ts-ignore
      setErrorMessage(error?.response?.data?.message || 'Payment failed. Please try again.');
      onAfterPayment?.({
        success: false,
        transactionID: '',
        onSuccessUrl: options?.onSuccessUrl,
        onFailureUrl: options?.onFailureUrl,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const watchedValues = methods.watch();
  const cardToken = watchedValues.cardToken;
  const cardTokenReady = cardToken !== '' && cardToken !== undefined;
  const [achTokenReady, setAchTokenReady] = useState(false);
  const canPay =
    paymentMethod === 'card'
      ? cardTokenReady
      : paymentMethod === 'ach'
        ? achTokenReady
        : paymentMethod === 'gpecomm'
          ? Boolean(watchedValues.gpEcomm)
          : false;

  const [selectedTip, setSelectedTip] = useState<number>(0);
  const [customTip, setCustomTip] = useState<string>('');

  useEffect(() => {
    if (selectedTip > 0) {
      onTipChange?.(selectedTip);
    } else if (customTip) {
      const tipAmount = parseFloat(customTip);
      onTipChange?.(tipAmount);
    } else {
      onTipChange?.(0);
    }
  }, [selectedTip, customTip, onTipChange]);

  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const handlePayButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowConfirmModal(true);
  };

  const handleConfirmPayment = () => {
    setShowConfirmModal(false);
    methods.handleSubmit(submitPayment)();
  };

  const handleCancelPayment = () => {
    setShowConfirmModal(false);
  };

  if (paymentStatus === 'success') {
    return <PaymentSuccess total={total} />;
  }

  if (!lineItems?.length && !initialTotal) {
    return <PaymentLoading />;
  }

  const oneTimePayments = lineItems.filter((item) => !item.isRecurring);
  const subscriptionPayments = lineItems.filter((item) => item.isRecurring);

  return (
    <FormProvider {...methods}>
      <form onSubmit={(e) => e.preventDefault()}>
        <div className={styles.payment}>
          <div
            className={cn(styles.container, 'relative flex w-full max-w-screen-xl flex-col p-2')}
          >
            <div className={cn(styles.cards, 'relative flex-col lg:flex-row')}>
              {/* Left Side - Order Summary */}
              <div className="flex flex-1 flex-col gap-4">
                <div className="h-full">
                  <div className="sticky left-0 top-8 flex flex-col gap-4">
                    {/* Price Breakdown */}
                    <PriceBreakdown
                      subscriptionTotal={initialSubscriptionTotal}
                      calculations={calculations}
                      total={total}
                      isOverideLineItemsAmount={options?.overideLineItemsAmount}
                      transactionHistory={options?.transactionHistory}
                    />
                    <div className="flex max-h-[60vh] flex-col gap-4 overflow-y-auto">
                      {/* Order Items */}
                      <OrderItems
                        lineItems={oneTimePayments}
                        edit={options?.allowEdit}
                        setCustomAmount={
                          options?.allowEdit
                            ? (amounts) => {
                                setCustomAmounts?.(amounts);
                              }
                            : undefined
                        }
                        currentAmount={customAmounts}
                      />

                      <SubscriptionItems lineItems={subscriptionPayments} />
                    </div>

                    {/* Tips and Discounts Section - Single Row */}
                    <div className="grid grid-cols-1 gap-4 lg:grid-cols-8">
                      {options?.allowTip && (
                        <div className={cn(styles.orderDetails, 'col-span-4 px-4 pb-6 pt-4')}>
                          <div className={cn(styles.headerSm, 'p-4 py-2')}>
                            <b className={cn(styles.b, 'text-sm text-gray-500')}>Tips</b>
                            <img className={styles.infoIcon} alt="" src="info.svg" />
                          </div>
                          <div className="w-full px-4">
                            <div className="flex flex-col gap-4">
                              <div className="flex gap-2">
                                {[3, 5, 10].map((percentage) => (
                                  <button
                                    key={percentage}
                                    type="button"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      setSelectedTip(percentage);
                                      setCustomTip('');
                                    }}
                                    className={cn(
                                      'flex-1 rounded px-4 py-2 text-sm font-medium',
                                      selectedTip === percentage
                                        ? 'bg-blue-500 text-white'
                                        : 'hz:bg-gray-200 bg-gray-100 text-gray-700',
                                    )}
                                  >
                                    {percentage}%
                                  </button>
                                ))}
                              </div>
                              <div className="flex items-center gap-2">
                                <input
                                  type="number"
                                  value={customTip}
                                  onChange={(e) => {
                                    setCustomTip(e.target.value);
                                    setSelectedTip(0);
                                  }}
                                  placeholder="Custom tip amount"
                                  className="w-full flex-1 rounded border border-gray-300 px-3 py-2 text-sm"
                                />
                                <span className="text-sm text-gray-500">$</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {options?.allowExtraDiscount && (
                        <div className={cn(styles.orderDetails, 'col-span-4 px-4 pb-6 pt-4')}>
                          <div className={cn(styles.headerSm, 'p-4 py-2')}>
                            <b className={cn(styles.b, 'text-sm text-gray-500')}>Discounts</b>
                            <img className={styles.infoIcon} alt="" src="info.svg" />
                          </div>
                          <div className="w-full px-4">
                            <div className="mb-4 flex flex-col gap-2">
                              <input
                                type="text"
                                value={discountInput}
                                onChange={(e) => setDiscountInput(e.target.value.toUpperCase())}
                                placeholder="Enter discount code"
                                className="w-fill flex-1 rounded border border-gray-300 px-3 py-2 text-sm uppercase"
                              />
                              <button
                                type="button"
                                onClick={handleAddDiscount}
                                className="rounded bg-blue-500 px-4 py-2 text-sm text-white hover:bg-blue-600"
                              >
                                Apply
                              </button>
                            </div>
                            {discounts.length > 0 && (
                              <>
                                <p className="text-sm font-semibold text-gray-500">
                                  Applied Discounts:
                                </p>
                                <div className="mt-2 space-y-2">
                                  {discounts.map((discount, index) => (
                                    <div key={index} className="flex justify-between">
                                      <span className="font-mono text-sm">{discount.code}</span>
                                      <span className="text-sm text-green-600">
                                        -{moneyFormat(int2DecToFloat(discount.amount))}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Payment and Billing */}
              <div className="flex flex-[1.2] flex-col gap-4">
                {/* Billing Details - Show for all cases */}
                <div className={cn(styles.billingDetails, 'mt-0 h-fit w-full p-4 lg:p-8')}>
                  <div className={styles.headerSm}>
                    <b className={styles.b}>Billing details</b>
                    <img className={styles.infoIcon} alt="" src="info.svg" />
                  </div>

                  {(options?.disableCustomAddress || isUsingCustomerCard) && (
                    <div className="mb-4">
                      <p className="text-xs italic text-gray-500">
                        {isUsingCustomerCard
                          ? 'Using saved customer information'
                          : 'Address information cannot be edited'}{' '}
                        - these fields cannot be edited
                      </p>
                    </div>
                  )}

                  <div className={styles.inputs1}>
                    <div className={styles.listItems}>
                      <FormInput
                        id="email"
                        name="email"
                        label="Email"
                        type="email"
                        disabled={options?.disableCustomAddress || isUsingCustomerCard}
                        inputClassName={
                          options?.disableCustomAddress || isUsingCustomerCard
                            ? 'bg-gray-50 text-gray-700'
                            : ''
                        }
                        rules={{
                          required: message.requiredField,
                          pattern: {
                            value: emailPatternRegex,
                            message: message.emailPattern,
                          },
                        }}
                      />
                    </div>
                    <div className={styles.listItems}>
                      <FormInput
                        id="phoneNumber"
                        name="phoneNumber"
                        label="Phone number"
                        disabled={options?.disableCustomAddress || isUsingCustomerCard}
                        inputClassName={
                          options?.disableCustomAddress || isUsingCustomerCard
                            ? 'bg-gray-50 text-gray-700'
                            : ''
                        }
                      />
                    </div>

                    <div className={'flex w-full gap-4'}>
                      <StateCountryForms
                        countryKey="country"
                        stateKey="state"
                        message={message}
                        methods={methods}
                        disableFields={options?.disableCustomAddress || isUsingCustomerCard}
                      />
                    </div>
                    <div className="flex w-full gap-4">
                      <div className={cn(styles.listItems, 'flex-1')}>
                        <FormInput
                          id="city"
                          name="city"
                          label="City"
                          disabled={options?.disableCustomAddress || isUsingCustomerCard}
                          inputClassName={
                            options?.disableCustomAddress || isUsingCustomerCard
                              ? 'bg-gray-50 text-gray-700'
                              : ''
                          }
                          rules={{ required: message.requiredField }}
                        />
                      </div>
                      <div className={cn(styles.listItems, 'flex-1')}>
                        <FormInput
                          id="street"
                          name="street"
                          label="Street"
                          disabled={options?.disableCustomAddress || isUsingCustomerCard}
                          inputClassName={
                            options?.disableCustomAddress || isUsingCustomerCard
                              ? 'bg-gray-50 text-gray-700'
                              : ''
                          }
                          rules={{ required: message.requiredField }}
                        />
                      </div>
                    </div>

                    {!isUsingCustomerCard && (
                      <div className={styles.listItems}>
                        <FormInput
                          id="zipCode"
                          name="zipCode"
                          label="Zip/Postal code"
                          type={isUsingCustomerCard ? 'text' : 'number'}
                          disabled={options?.disableCustomAddress && !isUsingCustomerCard}
                          inputClassName={
                            options?.disableCustomAddress && !isUsingCustomerCard
                              ? 'bg-gray-50 text-gray-700'
                              : ''
                          }
                          rules={{
                            required: message.requiredField,
                          }}
                        />
                        {isUsingCustomerCard && (
                          <p className="mt-1 text-xs text-blue-600">
                            Please confirm your billing zip code for verification
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Payment Methods Section */}
                <div className={cn(styles.cardDetails, 'h-fit w-full p-4 lg:p-8')}>
                  {isUsingCustomerCard && selectedPaymentCard ? (
                    <>
                      <div className={styles.headerSm}>
                        <b className={styles.b}>Your Payment Method</b>
                        <img className={styles.infoIcon} alt="" src="info.svg" />
                      </div>

                      {/* Payment method */}
                      <div className="mb-6 w-full bg-white p-4">
                        <div className="mb-4 flex items-center justify-between">
                          <h3 className="text-lg font-semibold text-gray-800">
                            Saved Payment Method
                          </h3>
                          <div className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
                            {selectedPaymentCard.isDefault ? 'Default' : 'Saved'}
                          </div>
                        </div>

                        <div className="mb-4 flex items-center">
                          {selectedPaymentCard.brand && (
                            <div className="mr-3">
                              <Image
                                src={`/icons/payments/${selectedPaymentCard.brand.toLowerCase()}.png`}
                                alt={selectedPaymentCard.brand}
                                width={40}
                                height={25}
                                onError={(e) => {
                                  // hide the image if not found
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                            </div>
                          )}
                          <div>
                            <p className="font-medium text-gray-800">
                              {selectedPaymentCard.type === 'ach' ? 'Bank Account' : 'Card'} ••••{' '}
                              {selectedPaymentCard.last4}
                            </p>
                            {selectedPaymentCard.expires && (
                              <p className="text-sm text-gray-500">
                                Expires {selectedPaymentCard.expires}
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Add Zip Code verification field */}
                        <div className="mb-4">
                          <FormInput
                            id="zipCode"
                            name="zipCode"
                            label="Zip/Postal code"
                            type="text"
                            rules={{
                              required: message.requiredField,
                            }}
                          />
                          <p className="mt-1 text-xs text-blue-600">
                            Please confirm your billing zip code for verification
                          </p>
                        </div>

                        {/* Add CVV if needed for card payments */}
                        {(selectedPaymentCard.type === 'card' || selectedPaymentCard.gpEcommID) && (
                          <div className="mb-4">
                            <FormInput
                              id="cvc"
                              name="cvc"
                              label="CVC/Security Code"
                              maxLength={4}
                              placeholder="123"
                            />
                          </div>
                        )}

                        <button
                          onClick={methods.handleSubmit(submitPayment)}
                          disabled={isSubmitting}
                          className="w-full rounded-md bg-blue-600 px-4 py-2 text-center font-medium text-white hover:bg-blue-700 disabled:bg-blue-400"
                        >
                          {isSubmitting ? (
                            <div className="flex items-center justify-center">
                              <svg
                                className="mr-2 h-4 w-4 animate-spin text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              Processing...
                            </div>
                          ) : (
                            'Pay with this method'
                          )}
                        </button>
                      </div>
                    </>
                  ) : options?.disableCustomPayment && selectedPaymentCard ? (
                    /* Show selected payment method but disabled for editing */
                    <>
                      <div className={cn(styles.headerSmm)}>
                        <b className={styles.b}>Payment Method</b>
                        <img className={styles.infoIcon} alt="" src="info.svg" />
                      </div>
                      <div className="mb-6 w-full bg-white p-4">
                        <div className="mb-4 flex items-center justify-between">
                          <h3 className="text-lg font-semibold text-gray-800">
                            Saved Payment Method
                          </h3>
                          <p className="text-xs italic text-gray-500">
                            Payment method selection disabled
                          </p>
                        </div>

                        <div className="mb-4 flex items-center">
                          {selectedPaymentCard.brand && (
                            <div className="mr-3">
                              <Image
                                src={`/icons/payments/${selectedPaymentCard.brand.toLowerCase()}.png`}
                                alt={selectedPaymentCard.brand}
                                width={40}
                                height={25}
                                onError={(e) => {
                                  // hide the image if not found
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                            </div>
                          )}
                          <div>
                            <p className="font-medium text-gray-800">
                              {selectedPaymentCard.type === 'ach' ? 'Bank Account' : 'Card'} ••••{' '}
                              {selectedPaymentCard.last4}
                            </p>
                            {selectedPaymentCard.expires && (
                              <p className="text-sm text-gray-500">
                                Expires {selectedPaymentCard.expires}
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Add Zip Code verification field */}
                        <div className="mb-4">
                          <FormInput
                            id="zipCode"
                            name="zipCode"
                            label="Zip/Postal code"
                            type="text"
                            rules={{
                              required: message.requiredField,
                            }}
                          />
                          <p className="mt-1 text-xs text-blue-600">
                            Please confirm your billing zip code for verification
                          </p>
                        </div>

                        {/* Add CVV if needed for card payments */}
                        {(selectedPaymentCard.type === 'card' || selectedPaymentCard.gpEcommID) && (
                          <div className="mb-4">
                            <FormInput
                              id="cvc"
                              name="cvc"
                              label="CVC/Security Code"
                              maxLength={4}
                              placeholder="123"
                            />
                          </div>
                        )}

                        <button
                          onClick={methods.handleSubmit(submitPayment)}
                          disabled={isSubmitting}
                          className="w-full rounded-md bg-blue-600 px-4 py-2 text-center font-medium text-white hover:bg-blue-700 disabled:bg-blue-400"
                        >
                          {isSubmitting ? (
                            <div className="flex items-center justify-center">
                              <svg
                                className="mr-2 h-4 w-4 animate-spin text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              Processing...
                            </div>
                          ) : (
                            'Pay with this method'
                          )}
                        </button>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className={styles.headerSm}>
                        <b className={styles.b}>Payment Method</b>
                        <img className={styles.infoIcon} alt="" src="info.svg" />
                      </div>
                      <div className="mb-4 flex w-full gap-4 pt-4">
                        {!options?.disabledCC && (
                          <button
                            type="button"
                            onClick={() => setPaymentMethod('gpecomm')}
                            className={cn(
                              'flex-1 rounded px-4 py-2',
                              ['card', 'gpecomm'].includes(paymentMethod)
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-100 text-gray-700',
                            )}
                          >
                            Credit/Debit Card
                          </button>
                        )}
                        {!options?.disabledACH && (
                          <button
                            type="button"
                            onClick={() => setPaymentMethod('ach')}
                            className={cn(
                              'flex-1 rounded px-4 py-2',
                              paymentMethod === 'ach'
                                ? 'bg-blue-500 text-white'
                                : 'bg-gray-100 text-gray-700',
                            )}
                          >
                            ACH / Bank Transfer
                          </button>
                        )}
                      </div>

                      {paymentMethod === 'card' && (
                        <>
                          <div className={cn(styles.inputs, 'mt-2')}>
                            <div className={styles.row}>
                              <div className={styles.inputField}>
                                <FormInput
                                  id="fullName"
                                  name="fullName"
                                  label="Full name (as displayed on card)"
                                  disabled={options?.disableCustomAddress}
                                  inputClassName={
                                    options?.disableCustomAddress ? 'bg-gray-50 text-gray-700' : ''
                                  }
                                  rules={{ required: message.requiredField }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className={cn(styles.inputs, '!hidden')}>
                            <div className={styles.row}>
                              <div className={styles.inputField}></div>
                            </div>
                          </div>
                          <br />
                          <TSEPHostedTokenizerComponent
                            onEvent={(eventType, event) => {
                              console.log(eventType, event);
                            }}
                            labels={{
                              cardNumber: 'Card Number',
                              expiryDate: 'Expiry Date',
                              cvv: 'CVC',
                            }}
                            allowOptionals={{
                              cvv: true,
                            }}
                            onToken={(token) => {
                              methods.setValue('cardToken', token.tsepToken);
                              methods.setValue('cvc', token.cvv2);
                              methods.setValue('expiryDate', token.expirationDate);
                            }}
                            onTokenError={() => {
                              methods.setValue('cardToken', '');
                              methods.setValue('cvc', '');
                              methods.setValue('expiryDate', '');
                            }}
                            parentComponentAttributes={{
                              style: {
                                width: '100%',
                                height: 'fit-content',
                                'margin-top': '-10px',
                              },
                            }}
                            iframeComponentAttributes={{
                              height: '225px',
                            }}
                          />
                        </>
                      )}

                      {paymentMethod === 'gpecomm' && (
                        <>
                          <div className={cn(styles.inputs, 'mt-2')}>
                            <div className={styles.row}>
                              <div className={styles.inputField}>
                                <FormInput
                                  id="fullName"
                                  name="fullName"
                                  label="Full name (as displayed on card)"
                                  disabled={options?.disableCustomAddress}
                                  inputClassName={
                                    options?.disableCustomAddress ? 'bg-gray-50 text-gray-700' : ''
                                  }
                                  rules={{ required: message.requiredField }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className={cn(styles.inputs, '!hidden')}>
                            <div className={styles.row}>
                              <div className={styles.inputField}></div>
                            </div>
                          </div>
                          <br />
                          <CardGLPHostedComponent
                            merchantID={paymentData.groupID}
                            onEvent={(eventType, event) => {
                              console.log(eventType, event);
                            }}
                            onToken={(token) => {
                              methods.setValue('gpEcomm', token.paymentID);
                              methods.setValue('paymentMethod', 'gpecomm');
                            }}
                            onError={() => {
                              methods.setValue('gpEcomm', '');
                            }}
                            parentComponentAttributes={{
                              style: {
                                width: '100%',
                                height: 'fit-content',
                                'margin-top': '-10px',
                              },
                            }}
                            iframeComponentAttributes={{
                              height: '250px',
                            }}
                          />
                        </>
                      )}
                      {paymentMethod === 'ach' && (
                        <>
                          <div className={cn(styles.inputs, 'mt-2')}>
                            <div className={styles.row}>
                              <div className={styles.inputField}>
                                <FormInput
                                  id="fullName"
                                  name="fullName"
                                  label="Account Holder Name"
                                  disabled={options?.disableCustomAddress}
                                  inputClassName={
                                    options?.disableCustomAddress ? 'bg-gray-50 text-gray-700' : ''
                                  }
                                  rules={{ required: message.requiredField }}
                                />
                              </div>
                            </div>
                          </div>
                          <ACHHostedComponent
                            onToken={(token) => {
                              methods.setValue('accountNumber', token.accountNumber);
                              methods.setValue('routingNumber', token.routingNumber);
                              methods.setValue('accountType', token.accountType);
                              methods.setValue('holderType', token.holderType);
                              methods.setValue('paymentMethod', 'ach');
                              setAchTokenReady(true);
                            }}
                            onError={() => {
                              methods.setValue('accountNumber', '');
                              methods.setValue('routingNumber', '');
                              methods.setValue('accountType', '');
                              methods.setValue('holderType', '');
                              methods.setValue('paymentMethod', 'ach');
                              setAchTokenReady(false);
                            }}
                            parentComponentAttributes={{
                              style: { width: '100%' },
                            }}
                            iframeComponentAttributes={{
                              height: '250px',
                            }}
                          />
                        </>
                      )}

                      <br />
                      <button
                        type="button"
                        className={cn(styles.button, 'p-6 text-xl')}
                        disabled={isSubmitting || !canPay}
                        onClick={handlePayButtonClick}
                      >
                        <p className={styles.text1}>
                          {isSubmitting
                            ? 'Processing...'
                            : canPay
                              ? total
                                ? `Pay ${moneyFormatString(int2DecToFloat(total))}`
                                : 'Confirm Payment'
                              : '--'}{' '}
                        </p>
                      </button>
                    </>
                  )}
                </div>

                <ConfirmTransactionModal
                  showModal={showConfirmModal}
                  handleCancel={handleCancelPayment}
                  handleConfirm={handleConfirmPayment}
                  isProcessEntry={true}
                  watch={watchedValues}
                  total={total + initialSubscriptionTotal}
                  calculations={{ ...calculations, subscription: initialSubscriptionTotal }}
                />

                {/* Add error message here */}
                {paymentStatus === 'error' && (
                  <div className="mt-4 rounded-md bg-red-50 p-3">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-5 w-5 text-red-400"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">{errorMessage}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Payment Logos */}
                <div
                  className={cn(
                    'flex w-full flex-col items-center justify-center px-4 sm:flex-row',
                  )}
                >
                  <div className="relative aspect-[4/1] h-20">
                    <Image
                      className={'w-full'}
                      layout="fill"
                      alt=""
                      src="/icons/payments/cc-logo.png"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className={cn(styles.helper, 'px-4')}>
              <div className={styles.heading}>
                <span>{`Payment processed by `}</span>
                <a href="https://ngnair.com">
                  <img className={'h-12'} alt="ngnair payments" src="/logo.webp" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default PaymentForm;
