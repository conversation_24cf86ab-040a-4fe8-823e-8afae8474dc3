import { cn, int2DecToFloat, moneyFormat } from '@/lib/utils';
import styles from './index.module.css';

export interface PriceBreakdownProps {
  calculations: Record<string, number>;
  total: number;
  subscriptionTotal: number;
  isOverideLineItemsAmount?: boolean;
  transactionHistory?: {
    transactionID: string;
    date: string;
    amount: number;
    status: string;
    note: string;
  }[];
}

export const PriceBreakdown = ({
  subscriptionTotal,
  calculations,
  total,
  isOverideLineItemsAmount,
  transactionHistory,
}: PriceBreakdownProps) => {
  if (total === 0) {
    if (subscriptionTotal !== 0) {
      return (
        <div className="w-full px-4">
          <div className={cn('mt-2 flex w-full items-end justify-between border-b text-right')}>
            <div className={cn(styles.listItem5, 'text-lg')}>Subscription Total</div>
            <div className={cn(styles.listItem5, 'text-3xl')}>
              {moneyFormat(int2DecToFloat(subscriptionTotal))}
            </div>
          </div>
        </div>
      );
    }
    return null;
  }
  return (
    <div className={cn(styles.totalPrice, 'px-4')}>
      <div className={styles.inputs}>
        <div className={cn('flex w-full items-end justify-between border-b pb-2 text-right')}>
          <div className={cn(styles.listItem5, 'text-lg')}>
            Total{subscriptionTotal ? ' (to pay now)' : ''}
          </div>
          {isOverideLineItemsAmount && (
            <div className="ml-auto mr-2 mt-1 w-fit rounded-full bg-orange-600 p-1 px-1.5 text-xs font-medium text-white">
              Custom/Partial Payment
            </div>
          )}
          <div className={cn(styles.listItem5, 'text-3xl')}>
            {moneyFormat(int2DecToFloat(total))}
          </div>
        </div>
        {!!subscriptionTotal && (
          <div
            className={cn('-mt-4 flex w-full items-end justify-between text-right text-gray-600')}
          >
            <div className={cn(styles.listItem5, 'font-semibold')}>Subscription Total</div>
            <div className={cn(styles.listItem5, 'text-lg')}>
              + {moneyFormat(int2DecToFloat(subscriptionTotal))} (next immediate cycle)
            </div>
          </div>
        )}
        <div className={styles.listItems}>
          {Object.entries(calculations)
            .filter(([_, value]) => value !== 0)
            .map(([key, value], index) => (
              <div key={index} className={styles.listItem}>
                <div className={styles.heading}>{key}</div>
                <div className={styles.heading}>{moneyFormat(int2DecToFloat(value))}</div>
              </div>
            ))}
        </div>

        {/* Transaction History Section */}
        {transactionHistory && transactionHistory.length > 0 && (
          <div className="mt-4 w-full border-t border-gray-200 pt-4">
            <h3 className="mb-2 text-sm font-semibold text-gray-700">Transaction History</h3>
            <div className="noScrollbar max-h-[20vh] overflow-y-auto">
              <div className="flex flex-col space-y-2">
                {transactionHistory.map((transaction, index) => (
                  <div
                    key={index}
                    className="w-full rounded-md border border-gray-200 bg-white p-3 shadow-sm transition-all hover:bg-gray-50"
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-mono text-xs text-gray-500">
                        #{transaction.transactionID}
                      </span>
                      <span
                        className={`inline-flex rounded-full px-2 py-0.5 text-xs font-semibold ${
                          transaction.status === 'CAPTURED'
                            ? 'bg-green-100 text-green-800'
                            : transaction.status === 'FAILED'
                              ? 'bg-red-100 text-red-800'
                              : transaction.status === 'PENDING'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {transaction.status}
                      </span>
                    </div>
                    <div className="flex items-baseline justify-between">
                      <span className="text-lg font-medium">
                        {moneyFormat(int2DecToFloat(transaction.amount))}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(transaction.date).toLocaleDateString()}{' '}
                        {new Date(transaction.date).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit',
                        })}
                      </span>
                    </div>
                    {transaction.note && (
                      <div className="mt-2 text-xs text-gray-600">
                        <span className="font-medium">Note:</span> {transaction.note}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
