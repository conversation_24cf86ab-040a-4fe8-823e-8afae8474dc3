import { AutoCompleteInput, AutoCompleteOption } from '@/components/globals';
import { Button } from '@/components/ui/button';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { GetGroupListQuery } from '@/graphql/generated/graphql';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { Checkbox, HelperText, Label } from 'flowbite-react';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import { axiosClient } from '@/lib/axios';
import { env } from 'next-runtime-env';

export const HSSetupLocation = () => {
  const query = useSearchParams();
  const [isChecked, setIsChecked] = useState(false);
  const [syncProducts, setSyncProducts] = useState(false);
  const [isProceeding, setIsProceeding] = useState(false);
  const handleCheckboxChange = () => setIsChecked(!isChecked);
  const [data, setData] = useState<GetGroupListQuery['groups']>();
  const [selectedLocation, setSelectedLocation] = useState<AutoCompleteOption | null>(null);
  const [appName, setAppName] = useState<string | false>(false);
  const [accountID, setAccountID] = useState<string>('');

  const getLocationList = async () => {
    const name = query?.get('name');
    if (name) {
      setAppName(name);
    }

    const locations = await apolloClient.query({
      query: GET_GROUPS_LIST,
      variables: {
        where: {
          mainGateway: {
            not: {
              // empty
              equals: '',
            },
          },
        },
      },
    });
    setData(locations.data.groups);
  };

  // Memoized options for the AutoCompleteInput
  const locationOption = useMemo<AutoCompleteOption[]>(() => {
    if (!data) return [];
    return data.map((group) => ({
      label: group.name ?? '',
      id: group.id ?? '',
    }));
  }, [data]);

  const handleSubmit = () => {
    installApp({ groupID: selectedLocation?.id ?? '' });
  };

  const installApp = async (args: { groupID: string }) => {
    const code = query?.get('code');

    if (!code) {
      return;
    }

    if (!args.groupID) {
      return;
    }

    // do binding here
    let isSuccessful = true;

    const url = new URL(env('NEXT_PUBLIC_SERVER_URL') + '/api/hubspot/auth/bind');
    url.searchParams.append('bind_id', code);
    url.searchParams.append('group_id', args.groupID);
    try {
      let res = await axiosClient.get(url.toString());
      if (res.data.success) {
        setAccountID(args.groupID);
      } else {
        isSuccessful = false;
      }
    } catch (error) {
      isSuccessful = false;
    }
    if (isSuccessful) {
      localStorage.setItem('location', JSON.stringify(selectedLocation));
      setIsProceeding(true);
    } else {
      toast.error('Failed to install app');
    }
  };

  useEffect(() => {
    if (query?.get('mode') && query?.get('code')) {
      getLocationList();
    }
  }, [query]);

  return (
    <>
      {!isProceeding && (
        <div className="flex items-center justify-center bg-gray-100">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-md">
            <div className="mb-4 text-center">
              <Image
                src="/logo.webp"
                alt="NGnair Payments"
                width={200}
                height={50}
                className="mx-auto mb-4 h-12"
              />
              <h1 className="text-2xl font-bold text-gray-900">Select Location to Link</h1>
              <p className="text-sm text-gray-500">
                Linking your payment location will allow you to accept and manage payments from{' '}
                {appName} in {selectedLocation?.label}
              </p>
            </div>

            <div className="mb-4">
              <Label
                htmlFor="locationName"
                className="mb-2 block text-left font-medium text-gray-700"
              >
                Location Name
              </Label>
              <AutoCompleteInput
                options={locationOption}
                value={selectedLocation}
                placeholder="Search Location"
                onChange={setSelectedLocation}
                inputValue={selectedLocation?.label ?? ''}
                className="w-full"
              />
            </div>

            <div className="mb-4 flex items-start">
              <Checkbox
                id="confirmation"
                className="mr-2"
                checked={isChecked}
                onChange={handleCheckboxChange}
              />
              <Label htmlFor="confirmation" className="text-sm text-gray-700">
                I confirm that I am linking {selectedLocation?.label} location to {appName}.
              </Label>
            </div>

            {/* <div className="mb-4 flex items-start">
              <Checkbox
                id="syncProducts"
                className="mr-2"
                checked={syncProducts}
                onChange={handleSyncProductsChange}
              />
              <Label htmlFor="syncProducts" className="text-sm text-gray-700">
                Sync my GHL shop products to NGNair
              </Label>
            </div> */}

            <Button
              type="submit"
              variant="primary"
              className="w-full rounded bg-blue-600 px-4 py-2 font-semibold text-white hover:bg-blue-700"
              disabled={!isChecked || !selectedLocation}
              onClick={handleSubmit}
            >
              Proceed
            </Button>
            <div className="mt-4 text-center text-sm">
              <HelperText>
                If you need help, contact{' '}
                <a href="#" className="text-blue-600">
                  NGnair Payments Support
                </a>
                .
              </HelperText>
            </div>
          </div>
        </div>
      )}

      {isProceeding && (
        <div className="flex min-h-screen items-center justify-center bg-gray-100">
          <div className="w-full max-w-md rounded-lg bg-white p-8 text-center shadow-md">
            <div className="mb-6">
              <div className="mb-4 text-6xl text-green-500">✓</div>
              <h2 className="mb-2 text-2xl font-bold text-gray-900">Setup Complete!</h2>
              <p className="text-gray-600">
                Your GoHighLevel account has been successfully connected to NGnair Payments.
              </p>
            </div>
            <Button
              onClick={() => (window.location.href = '/dashboard/admin/integrations')}
              className="w-full bg-blue-600 text-white hover:bg-blue-700"
            >
              Go to Dashboard
            </Button>
          </div>
        </div>
      )}
    </>
  );
};
