'use client';

import { TestModeBanner } from '@/components/globals/test-mode-banner';
import { ApolloWrapper } from '@/components/graphql/ApolloWrapper';
import { env } from 'next-runtime-env';

export default function HomeLayout({ children }: { children: React.ReactNode }) {
  return (
    <ApolloWrapper>
      {env('NEXT_PUBLIC_TEST_MODE') === 'true' && <TestModeBanner />}
      {children}
    </ApolloWrapper>
  );
}
