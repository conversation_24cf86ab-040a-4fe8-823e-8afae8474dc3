type DetailItemprops = {
  label: string;
  value: string | null | undefined;
  className?: string;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
};

type GeneralDetailsSectionProps = {
  details: DetailItemprops[];
};

export const GeneralDetailsSection = ({ details }: GeneralDetailsSectionProps) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      {details.map((item, index) => (
        <DetailItem key={index} {...item} />
      ))}
    </div>
  );
};

const DetailItem = ({ label, value, className: classes, onClick }: DetailItemprops) => (
  <div className="">
    <p className="font-medium">{`${label}`} </p>
    <p
      className={`flex items-center text-sm text-gray-500 ${onClick !== undefined && 'cursor-pointer hover:underline'} ${classes}`}
      onClick={onClick}
    >
      {value || 'N/A'}
    </p>
  </div>
);
