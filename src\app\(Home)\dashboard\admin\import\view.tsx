'use client';

import { PageHeader } from '@/components/globals';
import { useLocationSelector } from '@/components/hooks';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { DOWNLOAD_STRIPE_TEMPLATE_CSV } from '@/graphql/declarations/integrations';
import {
  Import_StripeDocument,
  Import_StripeInput,
  Import_StripeMutation,
} from '@/graphql/generated/graphql';
import { useMutation, useQuery } from '@apollo/client';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

const ImportCSV = () => {
  const methods = useForm();
  const { isSubmitting } = methods.formState;

  const { locationSelectorElement, locationFilter } = useLocationSelector({});
  const [uploadedFiles, setUploadedFiles] = useState({
    transactionCSV: null,
    customersCSV: null,
    productsCSV: null,
  });

  const [importType, setImportType] = useState<'payment' | 'product' | null>(null);

  const [importCSVMutate, { loading }] = useMutation(Import_StripeDocument, {
    onCompleted: (e: Import_StripeMutation) => {
      const { processedItems, type } = e.import_stripe || {};
      toast.success(`Processed ${processedItems} items of type ${type}.`);
      // Clear the field based on the type
      if (type === 'import::payment') {
        setUploadedFiles((prev) => ({ ...prev, transactionCSV: null }));
      } else if (type === 'import::customer') {
        setUploadedFiles((prev) => ({ ...prev, customersCSV: null }));
      } else if (type === 'import::product') {
        setUploadedFiles((prev) => ({ ...prev, productsCSV: null }));
      }
    },
    onError: (error) => {
      console.log(error);
      toast.error('Error uploading the csv: ' + error.message);
    },
  });

  const {
    data,
    error,
    loading: downloading,
  } = useQuery(DOWNLOAD_STRIPE_TEMPLATE_CSV, {
    variables: {
      input: {
        type: importType!,
      },
    },
    skip: !importType,
  });

  const handleDownloadTemplate = async () => {
    const sampleData = data?.import_stripe_sample?.sampleData;

    if (!sampleData) {
      toast.error('No template available for this type.');
      return;
    }

    if (error) {
      toast.error('Error downloading the template');
      return;
    }

    // Create blob from the CSV data
    const blob = new Blob([sampleData], { type: 'text/csv' });

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${importType}-template.csv`;

    // Trigger download
    document.body.appendChild(link);
    link.click();

    // Cleanup
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.success('Template downloaded successfully');
    setImportType(null);
  };

  const useCsvDropzone = (type: string, displayName: string) => {
    const { getRootProps, getInputProps } = useDropzone({
      accept: { 'text/csv': ['.csv'] },
      multiple: false,
      onDrop: (acceptedFiles) => {
        if (acceptedFiles.length === 0) return;
        const file = acceptedFiles[0];
        if (file.size > 5 * 1024 * 1024) {
          toast.error('File size exceeds 5MB limit.');
          return;
        }
        if (!file.type.includes('csv')) {
          toast.error('Please upload a valid CSV file.');
          return;
        }

        setUploadedFiles((prev) => ({
          ...prev,
          [type]: file,
        }));
      },
    });

    return (
      <div className="flex items-center gap-x-5">
        <div className="min-w-40">{displayName}:</div>
        <div
          {...getRootProps()}
          className={clsx(
            'min-h-16 flex-1 cursor-pointer rounded-md border border-gray-400 p-4 text-center',
            uploadedFiles[type] ? 'border-3 border-double bg-gray-500' : 'border-dashed',
          )}
        >
          <input {...getInputProps()} />
          {uploadedFiles[type] ? (
            <p className="mt-2 text-white">{uploadedFiles[type].name} added</p>
          ) : (
            <p className="text-gray-600">Drag & drop or click to upload {displayName}</p>
          )}
        </div>
      </div>
    );
  };

  const onSync = async (data: any) => {
    const readFileContent = (file: File | null): Promise<string | null> => {
      return new Promise((resolve, reject) => {
        if (!file) return resolve(null);
        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target && typeof event.target.result === 'string') {
            resolve(event.target.result);
          } else {
            reject(new Error('File reading error: Unable to read the CSV file.'));
          }
        };

        reader.onerror = () => reject(new Error('File reading failed.'));
        reader.readAsText(file);
      });
    };

    try {
      const transactionCSV = await readFileContent(uploadedFiles.transactionCSV);
      const customersCSV = await readFileContent(uploadedFiles.customersCSV);
      const productsCSV = await readFileContent(uploadedFiles.productsCSV);

      const mutationInputs: Import_StripeInput[] = [];

      if (transactionCSV) {
        mutationInputs.push({
          groupID: locationFilter?.id ?? '',
          dataCSV: transactionCSV,
          type: 'payment',
        });
      }

      if (customersCSV) {
        mutationInputs.push({
          groupID: locationFilter?.id ?? '',
          dataCSV: customersCSV,
          type: 'customer',
        });
      }

      if (productsCSV) {
        mutationInputs.push({
          groupID: locationFilter?.id ?? '',
          dataCSV: productsCSV,
          type: 'product',
        });
      }

      if (mutationInputs.length === 0) {
        toast.error('Please upload at least one CSV file.');
        return;
      }

      for (const input of mutationInputs) {
        await importCSVMutate({
          variables: { input },
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
      toast.error(`Error reading CSV file: ${errorMessage}`);
    }
  };

  useEffect(() => {
    if (importType && !downloading) {
      handleDownloadTemplate();
    }
  }, [importType, downloading]);

  return (
    <>
      <div>
        <div className="flex justify-between">
          <PageHeader text="CSV Import" />
        </div>

        <div className="border-b border-gray-300 sm:flex">
          <div className="w-1/4">{locationSelectorElement}</div>
          <div className="ml-auto flex items-center space-x-2 sm:space-x-3"></div>
        </div>

        <div className="py-10">
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSync)}>
              <div className="space-y-6">
                <div>{useCsvDropzone('transactionCSV', 'Transactions CSV')}</div>
                <div>{useCsvDropzone('customersCSV', 'Customers CSV')}</div>
                <div>{useCsvDropzone('productsCSV', 'Products CSV')}</div>
              </div>

              <div className="mx-5 mt-8 flex justify-end gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="default"
                      type="button"
                      disabled={downloading}
                      className="w-[160px]"
                    >
                      {downloading ? 'Downloading...' : 'Download Template'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="flex w-[160px] flex-col p-0 py-1">
                    <Button
                      className="rounded-none"
                      type="button"
                      variant="ghost"
                      onClick={() => {
                        setImportType('product');
                      }}
                    >
                      Product CSV
                    </Button>
                    <Separator />
                    <Button
                      className="rounded-none"
                      type="button"
                      variant="ghost"
                      onClick={() => {
                        setImportType('payment');
                      }}
                    >
                      Payment CSV
                    </Button>
                  </PopoverContent>
                </Popover>

                <Button
                  type="submit"
                  disabled={
                    isSubmitting || !Object.values(uploadedFiles).some((file) => file !== null)
                  }
                  variant={'primary'}
                >
                  Sync
                </Button>
              </div>
            </form>
          </FormProvider>
        </div>
      </div>
    </>
  );
};

export default ImportCSV;
