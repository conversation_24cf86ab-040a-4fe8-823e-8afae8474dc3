'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useLocationSelector } from '@/components/hooks';
import { useMutation, useQuery } from '@apollo/client';
import { Bell, Copy, Loader2, Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { cn } from '@/lib/utils';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Label } from '@/components/ui/label';
import { WebhookCreate, WebhookDelete, WebhookList } from '@/graphql/declarations/webhook';
import { apolloClient } from '@/lib/graphql/ApolloClient';

const formSchema = z.object({
  url: z.string().url({ message: 'Please enter a valid URL' }),
});

type FormSchema = z.infer<typeof formSchema>;

interface WebhookManagerProps {
  groupId: string;
}

export default function WebhookManager({ groupId }: WebhookManagerProps) {
  const [showNewWebhookModal, setShowNewWebhookModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newWebhook, setNewWebhook] = useState<{ url: string; secret: string } | null>(null);

  const [groupID, setGroupID] = useState<string | null>(null);

  const { locationSelectorElement, locationFilter } = useLocationSelector({});
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
  });

  useEffect(() => {
    setGroupID(locationFilter?.id ?? groupId);
  }, [locationFilter?.id, groupId]);

  const { data, loading, refetch } = useQuery(WebhookList, {
    variables: {
      where: {
        group: { id: { equals: groupID } },
      },
      orderBy: [],
      skip: 0,
    },
    skip: !groupID,
  });

  const [createWebhook] = useMutation(WebhookCreate);
  const [deleteWebhook] = useMutation(WebhookDelete);

  const onSubmit = async (data: FormSchema) => {
    try {
      if (!groupID) return;

      const result = await createWebhook({
        variables: {
          input: {
            url: data.url,
            groupID: groupID,
          },
        },
      });

      await apolloClient.refetchQueries({ include: ['WebhookList'] });

      setNewWebhook({
        url: data.url,
        secret: result.data?.group_createWebhook?.secret ?? '',
      });
      setShowCreateModal(false);
      setShowNewWebhookModal(true);
      refetch();
    } catch (error) {
      toast.error('Failed to create webhook');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteWebhook({
        variables: {
          where: { id },
        },
      });
      refetch();
      toast.success('Webhook deleted successfully');
    } catch (error) {
      toast.error('Failed to delete webhook');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  return (
    <div className="space-y-4 p-4">
      <div className="space-y-2">
        <div>
          <h2 className="text-xl font-semibold">Webhooks</h2>
          <p className="mt-1 text-sm text-slate-600">
            Configure webhook endpoints to receive real-time notifications for events in your group.
          </p>
        </div>

        <div>
          <label className="text-sm font-medium">Select Group</label>
          <div className="flex items-center gap-2">
            {locationSelectorElement}
            <Button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center gap-2"
              disabled={!groupID}
            >
              <Plus className="h-4 w-4" />
              Add Webhook
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
          </div>
        ) : data?.groupWebhooks?.length === 0 ? (
          <div
            className={cn(
              'flex flex-col items-center justify-center rounded-lg border border-dashed py-8',
              'bg-slate-50 text-center',
            )}
          >
            <Bell className="h-8 w-8 text-slate-400" />
            <h3 className="mt-2 text-sm font-medium text-slate-900">No webhooks configured</h3>
            <p className="mt-1 text-sm text-slate-500">
              Add a webhook endpoint to receive event notifications
            </p>
          </div>
        ) : (
          data?.groupWebhooks?.map((webhook) => (
            <div
              key={webhook.id}
              className="flex items-center justify-between rounded-lg border p-4"
            >
              <div className="flex items-center gap-3">
                <Bell className="h-5 w-5 text-slate-600" />
                <div>
                  <p className="font-medium">{webhook.webhookURL}</p>
                  <p className="text-sm text-slate-500">Group: {webhook.group?.name}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDelete(webhook.id)}
                className="text-red-500 hover:text-red-600"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))
        )}
      </div>

      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="max-w-screen-sm p-4">
          <DialogHeader>
            <DialogTitle>Add Webhook Endpoint</DialogTitle>
            <DialogDescription>
              Enter the URL where you want to receive webhook events
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="url">Webhook URL</Label>
              <Input id="url" placeholder="https://your-domain.com/webhook" {...register('url')} />
              {errors.url && <p className="text-sm text-red-500">{errors.url.message}</p>}
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowCreateModal(false)}>
                Cancel
              </Button>
              <Button type="submit">Create Webhook</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={showNewWebhookModal} onOpenChange={setShowNewWebhookModal}>
        <DialogContent className="max-w-screen-sm p-4">
          <DialogHeader>
            <DialogTitle>Webhook Created Successfully</DialogTitle>
            <DialogDescription>
              Store this webhook secret securely. It will not be shown again.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Webhook URL</Label>
              <div className="flex gap-2">
                <Input readOnly value={newWebhook?.url} />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(newWebhook?.url ?? '')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Webhook Secret</Label>
              <div className="flex gap-2">
                <Input readOnly value={newWebhook?.secret} />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(newWebhook?.secret ?? '')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={() => setShowNewWebhookModal(false)}>Close</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
