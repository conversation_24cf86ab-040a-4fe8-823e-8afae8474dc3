'use client';

import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { GHLIntegDetails, GHLSetupCommit } from '@/graphql/declarations/gohighlevel-setup';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { Me } from '@/graphql/declarations/me';
import { GetGroupListQuery } from '@/graphql/generated/graphql';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { ExtractArrayType } from '@/lib/typescript_utils';
import { useQuery } from '@apollo/client';
import { ArrowRightIcon } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { ReactNode, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

function MainInstallCard(props: { appName: string; children?: ReactNode }) {
  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <Card className="max-h-[80vh] w-full max-w-2xl overflow-y-auto border border-gray-200 shadow-lg">
        <CardContent className="p-8">
          <h2 className="text-2xl font-semibold">
            Install NGnair Payments{' '}
            {props.appName && (
              <>
                to <span className="text-primary-500">{props.appName}</span>
              </>
            )}
          </h2>
          <p className="text-sm text-gray-500">Custom Payment Methods</p>
          <p className="mt-4 text-sm text-gray-600">
            Installing NGnair Payments will allow you to accept payments using custom payment
            methods.{' '}
            {props.appName && (
              <>
                You can install this app in your{' '}
                <span className="text-primary-500">{props.appName}</span> account. methods.{' '}
              </>
            )}
          </p>
          {props.children}
        </CardContent>
      </Card>
    </div>
  );
}

function MerchantSelector() {
  // get code from query params
  const query = useSearchParams();

  const [step, setStep] = useState(0);
  const [selectedGroup, setSelectedGroup] = useState<ExtractArrayType<
    GetGroupListQuery['groups']
  > | null>(null);

  const { data: groupData, loading: groupLoading } = useQuery(GET_GROUPS_LIST, {
    variables: {
      where: {
        processorStatus: {
          equals: 'active',
        },
      },
    },
  });

  const [installing, setInstalling] = useState(false);

  const installApp = async () => {
    const code = query?.get('code');

    if (!code) {
      return;
    }

    if (!selectedGroup?.id) {
      return;
    }

    const res = await apolloClient.mutate({
      mutation: GHLSetupCommit,
      variables: {
        input: {
          code: code,
          groupID: selectedGroup.id,
        },
      },
    });

    if (res.data?.ghl_auth_completeIntegration) {
      setInstalling(false);
      setStep(3);
    } else {
      toast.error('Failed to install app');
    }
  };

  const changeAccount = () => {
    AUTHSTORE.clear();
    apolloClient.clearStore();
    // reload page
    window.location.reload();
  };

  if (step === 0) {
    return (
      <div className="mt-8">
        <button
          className="flex w-full items-center justify-between rounded-lg bg-primary-500 p-4 text-white"
          onClick={() => {
            setStep(1);
          }}
        >
          <span>Install</span>
          <ArrowRightIcon size={24} />
        </button>
        {/* logout */}
        <button
          className="flex w-full items-center justify-between rounded-lg bg-gray-50 p-4 text-gray-500"
          onClick={() => {
            changeAccount();
          }}
        >
          <span>Change Account</span>
          <ArrowRightIcon size={24} />
        </button>
      </div>
    );
  } else if (step === 1) {
    return (
      <div className="mt-8">
        {groupData?.groups?.map((group) => (
          <div
            key={group.id}
            className="mt-2 flex items-center justify-between rounded-lg bg-gray-50 p-4"
          >
            <div>
              <h3 className="text-lg font-semibold">{group.name}</h3>
              <p className="text-sm text-gray-500">{group.einNumber}</p>
            </div>
            <button
              className="flex items-center justify-between rounded-lg bg-primary-500 p-4 text-white"
              onClick={() => {
                setSelectedGroup(group);
                setStep(2);
              }}
            >
              <span>Install</span>
              <ArrowRightIcon size={24} />
            </button>
          </div>
        ))}
      </div>
    );
  } else if (step === 2) {
    return (
      <div className="mt-8">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">{selectedGroup?.name}</h3>
            <p className="text-sm text-gray-500">{selectedGroup?.einNumber}</p>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              You are about to install NGnair to{' '}
              <span className="text-primary-500">{selectedGroup?.name}</span>. Click the button
              below to continue.
            </p>
            <div className="mt-4">
              <button
                className="flex w-full items-center justify-between rounded-lg bg-primary-500 p-4 text-white"
                onClick={() => {
                  installApp();
                }}
                disabled={installing}
              >
                {installing ? <span>Installing...</span> : <span>Install</span>}
                <ArrowRightIcon size={24} />
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  } else if (step === 3) {
    return (
      <div className="mt-8">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">{selectedGroup?.name}</h3>
            <p className="text-sm text-gray-500">{selectedGroup?.einNumber}</p>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              You have successfully installed NGnair to{' '}
              <span className="text-primary-500">{selectedGroup?.name}</span>.
            </p>
            <div className="mt-4">
              <button
                className="flex w-full items-center justify-between rounded-lg bg-primary-500 p-4 text-white"
                onClick={() => {
                  window.location.href = '/dashboard';
                }}
              >
                <span>Go to Dashboard</span>
                <ArrowRightIcon size={24} />
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
}

function AppInstallCard() {
  const query = useSearchParams();
  const [appName, setAppName] = useState<string | false>(false);
  const [error, setError] = useState<string | false>(false);

  const { data: installData } = useQuery(GHLIntegDetails, {
    variables: {
      input: {
        code: query?.get('code') || '',
      },
    },
    skip: !query?.get('code'),
    onCompleted: (data) => {
      setAppName(data?.ghl_auth_getIntegrationDetails?.locationName || false);
    },
    onError: () => {
      setError('Invalid code');
    },
  });

  useEffect(() => {
    // Check if window is defined to ensure code runs only on the client
    if (typeof window !== 'undefined') {
      localStorage.setItem('isIntergrated', 'true');
    }
  }, []);

  const { data: userData } = useQuery(Me, {});

  let isLoggedIn = false;
  let accountSelector = false;

  if (userData?.authenticatedItem?.email) {
    isLoggedIn = true;
    accountSelector = true;
  }

  const redirectToLogin = () => {
    // redirect to login but add a return url
    let currentUrl = window.location.href;
    let returnUrl = encodeURIComponent(currentUrl);
    window.location.href = `/login?redirect=${returnUrl}`;
  };

  if (isLoggedIn && error) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <Card className="max-h-[80vh] w-full max-w-2xl overflow-y-auto border border-gray-200 shadow-lg">
          <CardContent className="p-8">
            <h2 className="text-2xl font-semibold">Error</h2>
            <p className="text-sm text-gray-500">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }
  if (isLoggedIn && !appName) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <LoaderSquares />
      </div>
    );
  }
  return (
    <MainInstallCard appName={appName || ''}>
      {!isLoggedIn && (
        <div className="mt-8">
          <button
            className="flex w-full items-center justify-between rounded-lg bg-primary-500 p-4 text-white"
            onClick={redirectToLogin}
          >
            <span>Login To Continue</span>
            <ArrowRightIcon size={24} />
          </button>
        </div>
      )}
      {accountSelector && <MerchantSelector />}
    </MainInstallCard>
  );
}

export default function IntegrateGHLPage() {
  return (
    <div className="mx-auto py-0">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <AppInstallCard />
      </div>
    </div>
  );
}
