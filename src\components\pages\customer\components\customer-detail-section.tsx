import { Button } from 'flowbite-react';
import { useRouter } from 'next/navigation';
import { FaRegFlag } from 'react-icons/fa';
import { HiOutlineExternalLink } from 'react-icons/hi';

type CustomerDetailSectionProps = {
  customer: {
    customerID: string | null | undefined;
    name: string | null | undefined;
    email: string | null | undefined;
    phone: string | null | undefined;
    country: string | null | undefined;
    billingAddress: string | null | undefined;
  };
};

export const CustomerDetailSection = ({ customer }: CustomerDetailSectionProps) => {
  const router = useRouter();
  const handleEditCustomer = () => {
    router.push(`/dashboard/customers?id=${customer?.customerID}`);
  };
  return (
    <div className="mb-6 rounded-lg bg-gray-50 p-4">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold">Customer details</h3>
        <Button color="gray" size="xs" onClick={handleEditCustomer}>
          <HiOutlineExternalLink className="mr-2 h-4 w-4" />
          Edit
        </Button>
      </div>
      <div className="space-y-2">
        <CustomerDetailItem label="Name" value={customer?.name} />
        <CustomerDetailItem label="Email" value={customer?.email} />
        <CustomerDetailItem label="Phone" value={customer?.phone} />
        <CustomerDetailItem
          label="Country"
          value={
            <span className="flex items-center">
              <FaRegFlag className="mr-2 text-red-500" />
              {customer?.country}
            </span>
          }
        />
        <CustomerDetailItem label="Billing Address" value={customer?.billingAddress} />
      </div>
    </div>
  );
};

const CustomerDetailItem = ({ label, value }) => (
  <div className="border-bottom flex justify-between border-b border-gray-200 py-1 text-center text-sm">
    <span className="font-medium">{`${label} : `} </span>
    <div className="flex items-center text-sm text-gray-500">{value}</div>
  </div>
);
