// import { graphql } from '../generated';

// export const ProcessorAurCreate = graphql(`
//   mutation ProcessorAurCreate($input: Processoraur_draft_createInput!) {
//     processoraur_draft_create(input: $input)
//   }
// `);

// export const ProcessorAurUpdate = graphql(`
//   mutation ProcessorAurUpdate($input: Processoraur_draft_updateInput!) {
//     processoraur_draft_update(input: $input)
//   }
// `);

// export const ProcessorAurSubmit = graphql(`
//   mutation ProcessorAurSubmit($input: Processoraur_draft_submitInput!) {
//     processoraur_draft_submit(input: $input) {
//       urlForSigning
//       applicationId
//       applicationNumber
//     }
//   }
// `);
