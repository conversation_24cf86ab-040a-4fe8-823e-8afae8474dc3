'use client';

import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

function AppInstallCard() {
  const query = useSearchParams();

  const redirectToLogin = () => {
    // redirect to login but add a return url
    // let currentUrl = window.location.href;
    // let returnUrl = encodeURIComponent(currentUrl);
    window.location.href = `/login?code=${query?.get('code')}&mode=ghl-setup`;
  };

  useEffect(() => {
    if (query?.get('code')) {
      redirectToLogin();
    }
  }, [query]);

  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <LoaderSquares />
    </div>
  );
}

export default function IntegrateGHLPage() {
  return (
    <div className="mx-auto py-0">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <AppInstallCard />
      </div>
    </div>
  );
}
