'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Login as LoginType } from '@/graphql/declarations/auth';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormMessage, Form } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import { useState } from 'react';
// import Spinner from '@/components/ui/spinner';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { AUTHSTORE } from '@/lib/auth-storage';
import { useRouter } from 'next/navigation';
import { Spinner } from '@/components/ui/spinner';

const FormSchema = z
  .object({
    email: z.string().email(),
    password: z.string(),
  })
  .strict();

const Login = () => {
  const { formState, ...form } = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const router = useRouter();

  const [isLoggingIn, setIsLoggingIn] = useState(false);

  const handleLogin = async (data?: { email: string; password: string }) => {
    setIsLoggingIn(true);

    try {
      const resp = await apolloClient.mutate({
        mutation: LoginType,
        variables: {
          email: data?.email ?? '<EMAIL>',
          password: data?.password ?? 'QpLoXGy6y3',
        },
      });

      if (
        resp.data?.authclient_login?.__typename === 'ClientItemAuthenticationWithPasswordSuccess'
      ) {
        const token = resp.data.authclient_login.sessionToken;

        AUTHSTORE.set(token);

        // console.log("redirecting to dashboard");
        // window.location.href = "/dashboard";
        router.replace('/dashboard');
        // console.log(window.location.href);
      }

      // console.log(resp);
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <Form formState={formState} {...form}>
      <form
        onSubmit={form.handleSubmit(handleLogin)}
        className="flex min-h-[100vh] items-center justify-center"
      >
        <div
          className="min-w-4/6 m-auto flex w-full max-w-[520px] flex-col items-start justify-start gap-6 rounded-2xl p-8 shadow 2xl:w-9/12 2xl:gap-8 2xl:p-10"
          style={{ boxShadow: '0px 8px 16px 0px rgba(20, 20, 20, 0.05)' }}
        >
          <div className="flex h-5 flex-col items-start justify-start gap-4">
            <div className="self-stretch text-3xl font-bold text-[#1C1C1C]">
              <span className="text-primary"> Sign in</span> to Your Account.
            </div>
          </div>
          <div className="mt-6 flex w-full flex-col items-start justify-start gap-2 self-stretch">
            <FormField
              name="email"
              control={form.control}
              render={({ field }) => (
                <FormItem className="w-full">
                  <p className="font-inter text-base font-medium !text-[#495057] text-background">
                    Email address
                  </p>
                  <FormControl className="mt-2 !w-full">
                    <Input
                      placeholder="Enter Address"
                      className={`inline-flex w-full items-start justify-start gap-2.5 self-stretch rounded-md border border-gray-300 px-3 py-2 text-base font-normal text-[#1C1C1C] ${
                        formState.errors.email &&
                        'border-[#F46A6A] text-[#F46A6A] placeholder:text-[#F46A6A]'
                      } `}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="font-inter text-sm font-normal text-[#F46A6A]" />
                </FormItem>
              )}
            />
            <FormField
              name="password"
              control={form.control}
              render={({ field }) => (
                <FormItem className="w-full">
                  <p className="font-inter text-base font-medium !text-[#495057] text-background">
                    Password
                  </p>
                  <FormControl className="mt-2 !w-full">
                    <Input
                      type="password"
                      placeholder="Enter Password"
                      className={`inline-flex w-full items-start justify-start gap-2.5 self-stretch rounded-md border border-gray-300 px-3 py-2 text-base font-normal text-[#1C1C1C] ${
                        formState.errors.password &&
                        'border-[#F46A6A] text-[#F46A6A] placeholder:text-[#F46A6A]'
                      } `}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage className="font-inter text-sm font-normal text-[#F46A6A]" />
                </FormItem>
              )}
            />
          </div>

          <Button
            variant="primary"
            type="submit"
            className={cn(
              'leading-0 flex !h-auto w-full items-center justify-center rounded-lg px-12 py-3 text-center text-lg font-bold text-white',
              isLoggingIn && 'opacity-70',
            )}
          >
            Sign In {isLoggingIn && <Spinner />}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default Login;
