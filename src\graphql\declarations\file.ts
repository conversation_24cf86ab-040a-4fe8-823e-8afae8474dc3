import { graphql } from '../generated';

export const File_upload = graphql(`
  mutation File_upload($input: File_uploadInput!) {
    file_upload(input: $input) {
      files {
        url
        filename
      }
    }
  }
`);

export const Report_download = graphql(`
  mutation Reportdownload_download($input: Reportdownload_downloadInput!) {
    reportdownload_download(input: $input) {
      data
    }
  }
`);
