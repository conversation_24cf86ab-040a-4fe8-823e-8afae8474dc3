import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { AUTHSTORE } from '../auth-storage';
import { env } from 'next-runtime-env';

// Mock axios client for when backend is disabled
class MockAxiosClient {
  private baseURL: string;

  constructor(config: { baseURL?: string } = {}) {
    this.baseURL = config.baseURL || '';
  }

  async get(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.mockResponse({ url, method: 'GET', ...config });
  }

  async post(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.mockResponse({ url, method: 'POST', data, ...config });
  }

  async put(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.mockResponse({ url, method: 'PUT', data, ...config });
  }

  async delete(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.mockResponse({ url, method: 'DELETE', ...config });
  }

  async patch(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse> {
    return this.mockResponse({ url, method: 'PATCH', data, ...config });
  }

  private async mockResponse(config: any): Promise<AxiosResponse> {
    // Simulate network delay
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Mock responses based on URL patterns
    const { url, method, data } = config;

    if (url.includes('/api/oauth/token')) {
      return {
        data: {
          access_token: 'mock-access-token',
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token: 'mock-refresh-token',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config,
      };
    }

    if (url.includes('/api/test')) {
      return {
        data: { success: true, message: 'Mock API response' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config,
      };
    }

    // Default mock response
    return {
      data: { success: true, mock: true },
      status: 200,
      statusText: 'OK',
      headers: {},
      config,
    };
  }

  // Add interceptors property for compatibility
  interceptors = {
    request: {
      use: (onFulfilled?: any, onRejected?: any) => {
        // Mock interceptor - do nothing in mock mode
        return 0;
      },
    },
    response: {
      use: (onFulfilled?: any, onRejected?: any) => {
        // Mock interceptor - do nothing in mock mode
        return 0;
      },
    },
  };
}

// Check if we should use mock mode
const shouldUseMockMode = () => {
  return env('NEXT_PUBLIC_MOCK_MODE') === 'true' || env('NEXT_PUBLIC_DEMO') === 'true';
};

// Create the appropriate client based on mode
const createAxiosClient = (): AxiosInstance => {
  if (shouldUseMockMode()) {
    return new MockAxiosClient() as any;
  }

  // Real axios client for production
  const client = axios.create({
    baseURL: env('NEXT_PUBLIC_SERVER_URL'),
    headers: {
      Authorization: 'updatelater',
    },
  });

  client.interceptors.request.use(
    (config) => {
      // Update the Authorization header with the latest session_token
      config.headers.Authorization = AUTHSTORE.get();
      return config;
    },
    (error) => {
      return Promise.reject(error);
    },
  );

  return client;
};

export const axiosClient: AxiosInstance = createAxiosClient();
