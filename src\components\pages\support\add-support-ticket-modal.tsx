import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { <PERSON><PERSON>, Button } from 'flowbite-react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { useMutation } from '@apollo/client';
import { CreateGroupSupportTicketDocument } from '@/graphql/generated/graphql';
import { message } from '@/components/shared/utils';
import { SuportCaseFormData, supportCaseDefault } from './utils';
import { toast } from 'react-toastify';
import { FormAutoComplete, FormInput } from '@/components/globals';
import { useCustomerSelector, useLocationSelector } from '@/components/hooks';
import dynamic from 'next/dynamic';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { supportCategories } from '@/lib/mock/support-mock';

export const supportCategoryList = [{ label: 'General Inquiry', id: 'GENERAL' }];

const HtmlEditorQuill = dynamic(
  () => import('@/components/globals/html-editor/html-editor-quill'),
  { ssr: false },
);

type AddSuportTicketModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const AddSuportTicketModal = ({ isOpen, onClose }: AddSuportTicketModalProps) => {
  const methods = useForm<SuportCaseFormData>({
    defaultValues: { ...supportCaseDefault },
  });

  const { control, reset, setValue } = methods;
  const locationWatcher = useWatch({
    control: control,
    name: 'location',
  });
  const descriptionsWatcher = useWatch({
    control: control,
    name: 'descriptions',
  });

  const { locationOption } = useLocationSelector({});
  const { customerOptions } = useCustomerSelector({ groupdId: locationWatcher?.id ?? '' });

  const [createSupportTicketMutation, { loading: createCaseLoading, error }] = useMutation(
    CreateGroupSupportTicketDocument,
    {
      onCompleted: (data) => {
        toast.success(message.api.successCreate('Ticket'));
        reset();
        if (!data.createGroupSupportTicket?.id) return;
        // open on new tab
        window.open(`/dashboard/support/${data.createGroupSupportTicket?.id}`, '_blank');
        apolloClient.refetchQueries({
          include: ['GroupSupportTickets'],
        });
        onClose();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Ticket', error.message));
      },
    },
  );

  const onSubmitForm = async (data: SuportCaseFormData) => {
    // toast.success(`API INGRATION IN PROGRESS: ${data} `);
    try {
      await createSupportTicketMutation({
        variables: {
          data: {
            status: 'OPEN',
            description: data.descriptions,
            category: data.category?.id,
            title: data.subject,
            group: data.location?.id
              ? {
                  connect: {
                    id: data.location.id,
                  },
                }
              : undefined,
            // messages: {
            //   create: [
            //     {
            //       message: null,
            //       ticket: null,
            //     },
            //   ],
            // },
          },
        },
      });
    } catch (e) {
      console.error('Add Customer Mutation error: ', e);
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="5xl">
      <Modal.Header className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">New case</h3>
      </Modal.Header>
      <Modal.Body>
        <SpinnerLoading isLoading={createCaseLoading} />

        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmitForm)} className="space-y-2">
            <div className="grid grid-cols-2 gap-6">
              <FormAutoComplete
                id="location"
                name="location"
                label="Location"
                // rules={{ required: message.requiredField }}
                options={[...locationOption]}
                // optionsLoading={}
              />
              <FormAutoComplete
                id="category"
                name="category"
                label="Category"
                options={supportCategories}
                rules={{ required: message.requiredField }}
              />
              {/* <FormAutoComplete
                id="user"
                name="user"
                label="User"
                rules={
                  {
                    // required: message.requiredField
                  }
                }
                options={[...customerOptions]}
                // optionsLoading={}
              /> */}
            </div>
            <FormInput
              id="subject"
              name="subject"
              label="Subject"
              rules={{ required: message.requiredField }}
            />
            {/* TODO:  */}
            {/* <div className="borde flex items-center gap-2 rounded-se-xl rounded-ss-xl border-x border-t p-2 pb-0 text-xs text-gray-700">
              <div className="rounded-md bg-gray-200 p-2">Reply:</div>
              <div>Joseph McFail (<EMAIL>)</div>
            </div> */}
            <HtmlEditorQuill
              // id="description"
              // init={{
              //   height: 300,
              // }}
              // onEditorChange={(e) => setValue('descriptions', e)}
              value={descriptionsWatcher}
              setValue={(e) => setValue('descriptions', e)}
              className="h-48"
            />
            {/* <HtmlEditorMdx
              markdown={descriptionsWatcher}
              onChange={(e) => setValue('descriptions', e)}
              // className="h-48"
              contentEditableClassName="h-48 border shadow-md p-2"
              plugins={[
                headingsPlugin(),
                // imagePlugin({
                //   imageUploadHandler: (d) => {
                //     return Promise.resolve('https://picsum.photos/200/300');
                //   },
                // }),
                toolbarPlugin({
                  toolbarClassName: 'my-classname',
                  toolbarContents: () => (
                    <>
                      {' '}
                      <UndoRedo />
                      <BoldItalicUnderlineToggles />
                      <InsertImage />
                    </>
                  ),
                }),
              ]}
            /> */}
            <br />
            <br />
            <div className="flex gap-5">
              <Button type="submit" color="blue">
                Create case
              </Button>
            </div>
          </form>
        </FormProvider>
      </Modal.Body>
    </Modal>
  );
};
