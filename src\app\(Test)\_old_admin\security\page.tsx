'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { LogOut, Search, UserPlus } from 'lucide-react';
import { Switch } from '@/components/ui/switch';

// Mock data for demonstration
const mockMembers = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', type: 'Admin' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', type: 'Member' },
  { id: 3, name: '<PERSON>', email: '<EMAIL>', type: 'Contributor' },
  // Add more mock members as needed
];

const memberTypes = ['All', 'Admin', 'Member', 'Contributor'];

export default function GroupMembersPage() {
  const [dailyLimit, setDailyLimit] = useState(1000);
  const [ipLimit, setIpLimit] = useState(100);
  const [accountPaused, setAccountPaused] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  const handleLimitChange =
    (setter: React.Dispatch<React.SetStateAction<number>>) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = parseInt(e.target.value);
      if (!isNaN(value) && value >= 0) {
        setter(value);
      }
    };

  const handleAction = (action: string) => {
    setToastMessage(`${action} action completed`);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  return (
    <div className="container mx-auto">
      <div className="mb-6 grid gap-6">
        <div>
          <h2 className="mb-2 text-xl font-semibold">Transaction Limits</h2>
          <div className="flex items-center gap-4">
            <Label htmlFor="daily-limit">Daily Transaction Limit ($)</Label>
            <Input
              id="daily-limit"
              type="number"
              value={dailyLimit}
              onChange={handleLimitChange(setDailyLimit)}
              className="max-w-[150px]"
            />
          </div>
        </div>

        <div>
          <h2 className="mb-2 text-xl font-semibold">Account Management</h2>
          <div className="mb-4 flex items-center gap-4">
            <Switch
              id="account-status"
              checked={accountPaused}
              onCheckedChange={setAccountPaused}
            />
            <Label htmlFor="account-status">
              {accountPaused ? 'Account Paused' : 'Account Active'}
            </Label>
          </div>
          <Button
            variant="destructive"
            onClick={() => handleAction('Force MFA eject')}
            className="mr-2"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Force Eject All Users&apos; MFA
          </Button>
        </div>
      </div>

      <div>
        <h2 className="mb-2 text-xl font-semibold">Recent Activity</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Action</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>{new Date().toLocaleDateString()}</TableCell>
              <TableCell>Limit Changed</TableCell>
              <TableCell>Daily transaction limit updated to ${dailyLimit}</TableCell>
            </TableRow>
            <TableRow>
              <TableCell>{new Date().toLocaleDateString()}</TableCell>
              <TableCell>IP Limit Changed</TableCell>
              <TableCell>IP request limit updated to {ipLimit}</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
