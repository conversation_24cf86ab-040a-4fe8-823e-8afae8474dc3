import { create } from 'zustand';
import { User } from '@/types/User';
import { AUTHSTORE } from '@/lib/auth-storage';
export interface UserState {
  user: User | null;
  setUser: (user: User | null) => void;
  logout: () => void;
}

export const useUserStore = create<UserState>((set, get) => ({
  user: null,

  setUser: (user) => {
    set({ user });
  },
  logout: () => {
    AUTHSTORE.clear();
    set({ user: null });
  },
}));
