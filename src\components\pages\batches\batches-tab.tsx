import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>omponent,
  <PERSON><PERSON>hip,
  StatusFilter,
  useDataGridView,
  Variant,
} from '@/components/globals';

import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { BatchDetailsModal } from './batch-details-modal';
import { Gateway_BatchesDocument } from '@/graphql/generated/graphql';
import { useSearchParams } from 'next/navigation';
import useDebounce from '@/components/hooks/useDebounce';
import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
import { moneyFormat } from '@/lib/utils';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { mockBatches } from '@/mock/batches-data';

export enum BatchStatus {
  DECLINCED = 'DECLINED',
  CAPTURED = 'CAPTURED',
  BATCHED = 'BATCHED',
  REVERSED = 'REVERSED',
  PENDING = 'PENDING',
  DEPOSITED = 'DEPOSITED',
}
export const getBatchStatusChip = (status: BatchStatus): [Variant, string] => {
  const statusMap: Record<BatchStatus, Variant> = {
    [BatchStatus.DECLINCED]: 'danger',
    [BatchStatus.CAPTURED]: 'info',
    [BatchStatus.BATCHED]: 'success',
    [BatchStatus.REVERSED]: 'warning',
    [BatchStatus.PENDING]: 'warning',
    [BatchStatus.DEPOSITED]: 'success',
  };

  const variant = statusMap[status] || 'neutral';
  const label = status?.charAt(0).toUpperCase() + status?.slice(1);

  return [variant, label];
};

export const BatchesTab = () => {
  const queryParams = useSearchParams();
  const batchId = queryParams?.get('id');
  const [statusFilter, setStatusFilter] = useState('All');
  const [selectedBatchID, setSelectedBatchID] = useState(batchId ?? null); // Will show the modal if not nul
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({
    onlyActive: true,
  });

  const {
    data: batchesData,
    loading: batchesLoading,
    refetch: refetchBatchesData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    filterValue,
    setFilterValue,
    maxVariables,
  } = useDataGridView({
    query: Gateway_BatchesDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
    initialFilterField: 'status',
    initialFilterValue: 'All',
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refetchBatchesData();
    }
  }, [locationFilter]);

  const batchesRows = useMemo(() => {
    // Use mock data for testing the batch-deposit connections
    return mockBatches;
  }, []);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_BatchesDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_batches?.data ?? [];
  };

  const columns: Column[] = [
    {
      key: 'batchID',
      header: 'Batch #',
      onClick: (row) => setSelectedBatchID(row.batchID),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'location',
      header: 'Location',
    },
    {
      key: 'date',
      header: 'Date',
      valueGetter: (row) => moment(row?.date).format('MM/DD/YYYY'),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'amount',
      header: 'Amount',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      renderCell: (row) => <span>{moneyFormat(row.amount)}</span>,
    },
    {
      key: 'status',
      header: 'Status',
      width: '160px',
      renderCell: (row) => {
        const [status, label] = getBatchStatusChip(row.status);
        return <StatusChip variant={status} label={label} />;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];

  return (
    <>
      <StaticInfoBox />
      <PageHeader text="Batches" />
      <div className="mt-2 flex justify-between border-b border-gray-300">
        <div className="w-1/4">{locationSelectorElement}</div>
        <TopComponent value={searchValue} setValue={setSearchValue}>
          <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="batches" />
        </TopComponent>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={batchesRows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={false}
          mode="client"
          actionComponent={
            <StatusFilter
              value={filterValue}
              setValue={setFilterValue}
              statusList={Object.values(BatchStatus)}
            />
          }
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={mockBatches.length}
          onRowClick={(row) => setSelectedBatchID(row.batchID ?? null)}
        />
        <BatchDetailsModal
          isOpen={selectedBatchID !== null}
          onClose={() => setSelectedBatchID(null)}
          queryData={{ groupID: locationFilter?.id ?? '', batchID: selectedBatchID ?? '' }}
        />
      </div>
    </>
  );
};
