'use client';

import { Button } from '@/components/ui/button';
import { useMemo } from 'react';

export const Style = (status: string) => {
  switch (status) {
    case 'Signed':
      return { color: '#34C38F', backgroundColor: '#DAF4EB' };
    case 'Underwriting (Unassigned)':
      return { color: '#F1B44C', backgroundColor: '#FEF5E2' };
    default:
      return { color: '#000000', backgroundColor: '#FFFFFF' };
  }
};

export const useDraftColumn = (togglePlay: any, setPauseOpen: any, setActionData: any) => {
  const columns = useMemo(() => {
    const baseColumns = [
      // {
      //   accessorKey: 'Group Name',
      //   title: 'group_name',
      //   cell: (data: any) => {
      //     console.log(data);
      //     return data?.row?.original?.name ?? ' - ';
      //   },
      // },
      {
        accessorKey: 'Group Name',
        title: 'DBA name',
        cell: (data: any) => {
          return data?.row?.original?.name ?? ' - ';
        },
      },
      // {
      //   accessorKey: 'Members Count',
      //   title: 'members_count',
      //   cell: (data: any) => {
      //     return data?.row?.original?.membersCount ?? ' - ';
      //   },
      // },
      // {
      //   accessorKey: 'Processor Status',
      //   title: 'processor_status',
      //   cell: (data: any) => {
      //     return data?.row?.original?.processorStatus ?? ' - ';
      //   },
      // },

      {
        accessorKey: 'ID',
        title: 'ID',
        cell: (data: any) => {
          return data?.row?.original?.id ?? ' - ';
        },
      },
      {
        accessorKey: 'Status',
        title: 'Status',
        cell: (data: any) => {
          return (
            <div className="flex justify-between gap-2">
              <Button className="cursor-default bg-[#FEF5E2] text-xs font-bold text-[#F1B44C] hover:bg-[#FEF5E2] 2xl:text-sm">
                Draft
              </Button>
            </div>
          );
        },
      },
    ];

    return baseColumns;
  }, []);

  return columns;
};
