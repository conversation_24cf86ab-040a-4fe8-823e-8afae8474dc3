export interface AccountData {
  id: string;
  name: string;
  businessLocation: string; // DBA/Location name
  accountId: string;
  partnerName: string;
  totalEarnings: number;
  lastActive: string;
  status: string;
  email: string;
  phoneNumber: string;
  rate?: string;
  address?: string;
}

export const mockAccounts: AccountData[] = [
  {
    id: 'ACC007',
    name: '<PERSON>',
    businessLocation: "Chen's Digital Services",
    accountId: 'ACCT-24007',
    partnerName: 'Vantage Payments',
    totalEarnings: 0,
    lastActive: '2025-06-02',
    status: 'draft',
    email: '<EMAIL>',
    phoneNumber: '+****************',
    rate: '2.9%',
    address: '123 Tech Street, San Francisco, CA 94105',
  },
  {
    id: 'ACC001',
    name: '<PERSON>',
    businessLocation: 'Main Store',
    accountId: 'ACCT-24001',
    partnerName: 'Vantage Payments',
    totalEarnings: 32456.78,
    lastActive: '2025-06-02',
    status: 'active',
    email: '<EMAIL>',
    phoneNumber: '+****************',
    rate: '2.5%',
    address: '456 Business Ave, New York, NY 10001',
  },
  {
    id: 'ACC002',
    name: '<PERSON>',
    businessLocation: 'Online Store',
    accountId: 'ACCT-24002',
    partnerName: 'Vantage Payments',
    totalEarnings: 28934.56,
    lastActive: '2025-06-01',
    status: 'pending',
    email: '<EMAIL>',
    phoneNumber: '+****************',
    rate: '3.1%',
    address: '789 Commerce Blvd, Los Angeles, CA 90210',
  },
  {
    id: 'ACC003',
    name: 'Michael Brown',
    businessLocation: 'Mobile Sales',
    accountId: 'ACCT-24003',
    partnerName: 'Vantage Payments',
    totalEarnings: 19876.45,
    lastActive: '2025-05-30',
    status: 'disabled',
    email: '<EMAIL>',
    phoneNumber: '+****************',
  },
  {
    id: 'ACC004',
    name: 'Emily Davis',
    businessLocation: 'Boston Store',
    accountId: 'ACCT-24004',
    partnerName: 'Vantage Payments',
    totalEarnings: 24567.89,
    lastActive: '2025-05-29',
    status: 'approved',
    email: '<EMAIL>',
    phoneNumber: '+****************',
  },
  {
    id: 'ACC005',
    name: 'David Wilson',
    businessLocation: 'LA Store',
    accountId: 'ACCT-24005',
    partnerName: 'Vantage Payments',
    totalEarnings: 20708.21,
    lastActive: '2025-05-28',
    status: 'reject',
    email: '<EMAIL>',
    phoneNumber: '+****************',
  },
  {
    id: 'ACC006',
    name: 'Lisa Anderson',
    businessLocation: 'NYC Store',
    accountId: 'ACCT-24006',
    partnerName: 'Vantage Payments',
    totalEarnings: 15789.32,
    lastActive: '2025-05-27',
    status: 'sent',
    email: '<EMAIL>',
    phoneNumber: '+****************',
  },
];

// No longer using roles

export const getAccountStats = () => {
  const totalAccounts = mockAccounts.length;
  const totalActiveAccounts = mockAccounts.filter((acc) => acc.status === 'active').length;
  const totalEarnings = mockAccounts.reduce((sum, acc) => sum + acc.totalEarnings, 0);
  const totalLocations = 0; // Removing activeLocations as it's no longer tracked
  const totalMerchants = mockAccounts.length; // Total number of accounts

  return {
    totalAccounts,
    totalActiveAccounts,
    totalEarnings,
    totalLocations,
    totalMerchants,
    averageEarningsPerAccount: totalEarnings / totalAccounts,
    averageLocationsPerAccount: totalLocations / totalAccounts,
    averageMerchantsPerAccount: totalMerchants / totalAccounts,
  };
};
