'use client';

import { GHLSSOSignIn } from '@/graphql/declarations/gohighlevel-sso';
import { Me } from '@/graphql/declarations/me';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { useEffect } from 'react';
import { toast } from 'react-toastify';

export default function TestPage() {
  const { data: userData } = useQuery(Me, {});

  async function getUserID() {
    const key = (await new Promise((resolve) => {
      window.parent.postMessage({ message: 'REQUEST_USER_DATA' }, '*');
      window.addEventListener('message', ({ data }) => {
        if (data.message === 'REQUEST_USER_DATA_RESPONSE') {
          resolve(data.payload);
        }
      });
    })) as string;
    loginViaSSO({ ssoToken: key });
  }

  async function loginViaSSO(args: { ssoToken: string }) {
    const resp = await apolloClient.mutate({
      mutation: GHLSSOSignIn,
      variables: {
        input: {
          ssoToken: args.ssoToken,
        },
      },
    });

    if (resp.data?.ghl_auth_sso?.sessionToken) {
      const token = resp.data.ghl_auth_sso.sessionToken;

      AUTHSTORE.set(token);

      toast.success('Logged in via SSO');

      apolloClient.resetStore();
    } else {
      toast.error('Failed to login via SSO');
    }
  }

  // setup listener for parent webpage iframe message
  useEffect(() => {
    getUserID();
  }, []);

  return (
    <div>
      <p>NGNair</p>

      <p className="whitespace-pre-wrap">{JSON.stringify(userData, null, 2)}</p>
    </div>
  );
}
