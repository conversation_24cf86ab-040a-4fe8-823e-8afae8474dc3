'use client';

import { useState } from 'react';
import { POS_PROCESSORS } from '@/consts/processors';
import { ProcessorCard } from '@/components/pages/manage/processor/processor-card';
import { Processor } from '@/types/processors';

export default function POSPage() {
  const [posProcessors, setPosProcessors] = useState(POS_PROCESSORS);

  const handleToggle = (processor: Processor, enabled: boolean) => {
    const updateProcessor = (p: Processor) => ({
      ...p,
      enabled: p.id === processor.id ? enabled : p.enabled,
    });

    setPosProcessors(posProcessors.map(updateProcessor));
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">POS Systems Configuration</h1>
        <p className="text-muted-foreground">Enable and configure your Point of Sale systems.</p>
      </div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-6">
          {posProcessors.map((processor) => (
            <ProcessorCard
              key={processor.id}
              processor={processor}
              onToggle={(enabled) => handleToggle(processor, enabled)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
