{
  "editor.tabSize": 2,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.formatOnSave": true
  },
  "[javascriptreact]": {
    "editor.formatOnSave": true
  },
  "[typescript]": {
    "editor.formatOnSave": true
  },
  "[typescriptreact]": {
    "editor.formatOnSave": true
  },
  "[json]": {
    "editor.formatOnSave": true
  },
  "[html]": {
    "editor.formatOnSave": true
  },
  "[css]": {
    "editor.formatOnSave": true
  },
  "[markdown]": {
    "editor.formatOnSave": true
  },
  "[graphql]": {
    "editor.formatOnSave": true
  },
  // "editor.codeActionsOnSave": {
  //   "source.fixAll.eslint": true
  // },
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "json"],
  "typescript.tsdk": "node_modules/typescript/lib",
  "git.mergeEditor": false,
  "cSpell.words": [
    "Apim",
    "brreg",
    "flpay",
    "gronn",
    "grønn",
    "jira",
    "jobb",
    "Leder",
    "nager",
    "nrwl",
    "pangea",
    "Promisable",
    "Stoffkartotek",
    "testid",
    "uuidv",
    "Vite",
    "vitest",
    "xpand"
  ],
  "cSpell.allowCompoundWords": true,
  "cSpell.ignorePaths": [
    "**/package-lock.json",
    "**/node_modules/**",
    "**/vscode-extension/**",
    "**/.git/objects/**",
    ".vscode",
    "**/dist/**",
    "**/.cache/**",
    "**/coverage/**",
    ".gitignore",
    ".dockerignore",
    ".prettierrc",
    "yarn.lock",
    "pnpm-lock.yaml",
    "package.json"
  ],
  "git-blame.gitWebUrl": "",
  "editor.formatOnSave": true,
  "command-runner.terminal.name": "Build Commands",
  "command-runner.terminal.autoClear": true,
  "command-runner.terminal.autoFocus": true,
  "command-runner.commands": {
    "Sync": "\"github-scripts/sync.bat\"",
    "Sync Build": "\"github-scripts/sync-build.bat\"",
    "Push": "\"github-scripts/push-and-pr.bat\"",
    "Push Build": "\"github-scripts/push-and-pr-build.bat\""
  },
  "editor.codeActionsOnSave": {
    "source.removeUnusedImports": "explicit"
  },
  "files.associations": {
    "plyconfig.json": "jsonc"
  }
}
