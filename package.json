{"name": "ngnair-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "gen": "prettier --write ./src/graphql/declarations/ && graphql-codegen --config ./src/graphql/codegen.ts", "prepare": "husky"}, "dependencies": {"@apollo/client": "^3.11.3", "@apollo/experimental-nextjs-app-support": "^0.11.2", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@graphql-typed-document-node/core": "^3.2.0", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.0", "@lifeomic/axios-fetch": "^3.1.0", "@mdxeditor/editor": "^3.20.0", "@mui/material": "^5.16.7", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.51.21", "@tanstack/react-table": "^8.20.1", "@tinymce/tinymce-react": "^5.1.1", "apollo-upload-client": "^18.0.1", "axios": "^1.7.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^0.2.0", "copy-webpack-plugin": "6.2.1", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "elliptic": "^6.6.1", "flowbite": "^2.5.1", "flowbite-react": "^0.10.1", "graphql": "^16.9.0", "lucide-react": "^0.424.0", "moment": "^2.30.1", "next": "^15.3.2", "next-runtime-env": "^3.3.0", "next-svgr": "^0.0.2", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "quill": "^2.0.3", "react": "^18", "react-datepicker": "^7.3.0", "react-day-picker": "8.10.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.52.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.3.0", "react-input-mask": "^2.0.4", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.5", "react-quill": "^2.0.0", "react-rainbow-components": "^1.32.0", "react-svg-credit-card-payment-icons": "^3.1.1", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "sass": "^1.77.8", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zukeeper": "^1.0.2", "zustand": "^4.5.4"}, "devDependencies": {"@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.3.3", "@tailwindcss/typography": "^0.5.15", "@types/apollo-upload-client": "^18.0.0", "@types/node": "^22.15.29", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-icons": "^3.0.0", "@types/react-input-mask": "^3.0.5", "@types/validator": "^13.12.0", "@typescript-eslint/eslint-plugin": "^8.6.0", "eslint": "^8", "eslint-config-next": "14.2.5", "husky": "^9.1.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.5.4"}}