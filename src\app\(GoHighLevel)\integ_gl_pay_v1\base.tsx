'use client';

import FLPayTokenizerComponent, {
  FLPayTokenizerComponentConfigSample,
} from '@/components/payments/flp/tokenizer';
import { Button } from '@/components/ui/button';
import { useEffect, useRef, useState } from 'react';

interface PaymentInitiateProps {
  type: 'payment_initiate_props';
  publishableKey: String; // Publishable key sent while connecting integration API
  amount: Number; // Amount in decimal currency with max 2 decimal places
  currency: String; // Standard 3 letter notation for currencies ex. USD, INR
  mode: String; // Payment mode: subscription/payment
  productDetails: { productId: string; priceId: string }; // productId and priceId for recurring products. More details can be fetched using the public api for Products/Prices
  contact?: {
    // Customer details for customer placing the order
    id: String; // Customer id in GHL
    name: String; // Full name of the customer
    email: String;
    contact: String; // Contact Number of customer with country code
  };
  orderId: String; // GHL internal orderId for given order
  transactionId: String; // GHL internal transactionId for the given transaction
  subscriptionId: String; // GHL internal subscriptionId passed in case of a recurring product
  locationId: String; // Sub-account id for which the given order is created.
}

export default function GLPayPage() {
  const [isReady, setIsReady] = useState(false);
  async function providerReadyFlag() {
    const key = await new Promise((resolve) => {
      window.parent.postMessage(
        JSON.stringify({
          type: 'custom_provider_ready',
          loaded: true,
        }),
        '*',
      );
    });
  }

  async function postTransactionsFlag(args: { type: String; message?: String }) {
    switch (args.type) {
      case 'success': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_success_response',
            chargeId: args.message || '',
          }),
          '*',
        );
        break;
      }
      case 'error': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_error_response',
            error: {
              description: args.message || '',
            },
          }),
          '*',
        );
        break;
      }
      case 'cancel': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_close_response',
          }),
          '*',
        );
        break;
      }
    }
  }

  function processEvents(rawdata: any) {
    try {
      const data = JSON.parse(rawdata);
      const dType = data.type;
      if (!dType) {
        console.log('No type found in data');
        return;
      }

      switch (dType) {
        case 'payment_initiate_props': {
          let d = data as PaymentInitiateProps;
          console.log('Payment Initiate Props', d);
          setIsReady(true);
          break;
        }
      }
    } catch (e) {
      console.error('Error in parsing data', e);
    }
  }

  useEffect(() => {
    providerReadyFlag();
    window.addEventListener('message', ({ data }) => {
      processEvents(data);
    });
  }, []);

  const [hasPToken, setHasPToken] = useState(false);

  const tokenizerRef = useRef<any>(null);

  return (
    <div>
      <p>Payments</p>
      {!isReady && <p>Loading...</p>}
      {isReady && (
        <>
          {' '}
          <Button variant="primary" onClick={() => postTransactionsFlag({ type: 'success' })}>
            Success
          </Button>
          <Button variant="secondary" onClick={() => postTransactionsFlag({ type: 'error' })}>
            Error
          </Button>
          <Button variant="secondary" onClick={() => postTransactionsFlag({ type: 'cancel' })}>
            Cancel
          </Button>
        </>
      )}
      <div id="container" className="min-h-[20vh] p-4"></div>
      <FLPayTokenizerComponent
        apiKey=""
        containerSelector="#container"
        liveMode={false}
        onSubmission={(resp) => {
          console.log('Tokenizer Response', resp);
        }}
        tokenizerConfig={FLPayTokenizerComponentConfigSample}
        tokenizerRef={tokenizerRef}
      />
      <button onClick={() => tokenizerRef.current?.submit()}>Submit</button>
    </div>
  );
}
