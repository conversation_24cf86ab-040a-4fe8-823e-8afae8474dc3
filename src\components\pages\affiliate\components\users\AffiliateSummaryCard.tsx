import { ChartTab } from '@/components/pages/dashboard/components/financial-dashboard';
import { Button } from '@/components/ui/button';
import { moneyFormat } from '@/lib/utils';
import { BookCopy, ChartNoAxesCombined, Banknote, User } from 'lucide-react';
import { FaMoneyBill } from 'react-icons/fa';

export const AffiliateSummaryCard = ({ totals, handleOpenModal }) => {
  const metrics = [
    {
      label: 'Withdrawable',
      icon: FaMoneyBill,
      color: 'blue',
    },
    {
      label: 'Earnings',
      icon: BookCopy,
      color: 'blue',
    },
    {
      label: 'Withdrawn',
      icon: ChartNoAxesCombined,
      color: 'red',
    },
    {
      label: 'Users',
      icon: User,
      color: 'gray',
      notMoney: true,
    },
  ];

  return (
    <div className="mb-6 grid grid-cols-1 gap-4 rounded-lg p-2 shadow-md sm:grid-cols-2 lg:grid-cols-6">
      {metrics.map(({ label, icon: Icon, color, notMoney }) => (
        <div key={label} className="flex items-center gap-4 bg-white p-4">
          <div
            className={`rounded-full p-2 ${
              color === 'red' ? 'bg-red-100' : color === 'blue' ? 'bg-blue-100' : 'bg-gray-100'
            }`}
          >
            <Icon
              className={`h-5 w-5 ${
                color === 'red'
                  ? 'text-red-500'
                  : color === 'blue'
                    ? 'text-blue-500'
                    : 'text-gray-500'
              }`}
            />
          </div>
          <div>
            <p className="text-sm text-gray-500">{label}</p>
            <p
              className={`text-xl font-bold ${
                label === ChartTab.Refunds ? 'text-red-500' : 'text-gray-900'
              }`}
            >
              {notMoney ? totals[label] : moneyFormat(totals[label])}
            </p>
          </div>
        </div>
      ))}

      <div className="col-span-2 my-auto ml-auto">
        <Button
          onClick={handleOpenModal}
          className="mx-4 flex items-center gap-x-1 rounded-lg bg-blue-600 px-4 py-2 text-xs font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300"
        >
          <Banknote className="p-0.5" /> Change Payout Method
        </Button>
      </div>
    </div>
  );
};
