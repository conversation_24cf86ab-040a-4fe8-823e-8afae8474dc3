import { useState, useEffect } from 'react';
import { useDebounce } from './useDebounce';

export function useTable(
  fetchFunction: (_a: {
    page: number;
    table: string;
    sort?: Record<string, number>;
    query?: Record<string, string>;
  }) => Promise<{ data: any[]; totalPages: number }>,
  table: string,
) {
  const [data, setData] = useState<any[]>([]);
  const [columns, setColumns] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sort, setSort] = useState<Record<string, number>>({});
  const [query, setQuery] = useState<Record<string, string>>({});
  const debouncedQuery = useDebounce(query, 300); // 300ms debounce

  useEffect(() => {
    setQuery({});
  }, [table]);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const result = await fetchFunction({
          page,
          table,
          sort,
          query: query,
        });
        setData(result.data);
        setColumns(Object.keys(result.data[0] || {}));
        setTotalPages(result.totalPages);
      } catch (err) {
        setError('Failed to fetch data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
    // Reset to first page when table changes
    setPage(1);
  }, [page, table, sort, query]); // Changed query to debouncedQuery

  const pagination = {
    page,
    totalPages,
    nextPage: () => setPage((prev) => Math.min(prev + 1, totalPages)),
    previousPage: () => setPage((prev) => Math.max(prev - 1, 1)),
    canNextPage: page < totalPages,
    canPreviousPage: page > 1,
  };

  return {
    data,
    columns,
    pagination,
    isLoading,
    error,
    setSort,
    setQuery,
    sort,
    query: debouncedQuery, // Return debounced query instead of raw query
  };
}
