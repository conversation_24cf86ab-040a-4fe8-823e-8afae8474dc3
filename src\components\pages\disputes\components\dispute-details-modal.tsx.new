// Existing code remains the same until handleUploadDocument function

  const handleUploadDocument = async (params: DisputeChallengeDocumentParams) => {
    try {
      if (!params.file) {
        toast.error('No file provided');
        return;
      }

      const parsedFiles = await parseFilesToBase64([params.file]);
      if (!parsedFiles.length) {
        toast.error('Failed to parse file');
        return;
      }

      const file = parsedFiles[0];

      // Upload the file using File_upload mutation
      const uploadResult = await fileUploadMutation({
        variables: {
          input: {
            files: [{
              filename: params.file.name,
              mimetype: params.file.type,
              b64: file.b64,
            }],
          },
        },
      });

      const fileUrl = uploadResult.data?.file_upload?.files?.[0]?.url;
      if (!fileUrl) {
        toast.error('Failed to get file URL from upload');
        return;
      }

      // Add the file to the state with the URL
      const newFile: DisputeChallengeDocumentParams = {
        file: params.file,
        type: params.type,
        url: fileUrl,
      };

      // Now use the URL to register the file in the dispute system
      const disputeUploadResult = await uploadDisputeFileMutation({
        variables: {
          input: {
            groupID,
            data: {
              disputeID: disputeData?.disputeID ?? '',
              upload: {
                url: fileUrl,
                size: params.file.size,
                type: params.type,
                filetype: params.file.type,
                purpose: params.type,
              },
            },
          },
        },
      });

      if (disputeUploadResult.data?.gateway_uploadDisputeDocument?.itemID) {
        const itemID = disputeUploadResult.data.gateway_uploadDisputeDocument.itemID;
        const finalFile = { ...newFile, itemID };
        setFiles((prevFiles) => [...prevFiles, finalFile]);
        setUploadedItemIDs((prevIds) => [...prevIds, itemID]);
        toast.success('File uploaded successfully');
      } else {
        setFiles((prevFiles) => [...prevFiles, newFile]);
        toast.warning('File uploaded but not linked properly');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload file');
    }
  };
