'use client';

import React from 'react';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface Props extends React.ComponentProps<typeof Button> {
  isLoading: boolean;
  icon?: React.ReactNode;
  loadingText?: string | React.ReactNode;
}

export default function LoadingButton({ isLoading, loadingText, icon, ...props }: Props) {
  const { children, className, ...rest } = props;

  return (
    <Button
      disabled={rest.disabled || isLoading}
      type={rest.type}
      className={cn(
        'relative flex gap-2 text-white disabled:cursor-not-allowed disabled:opacity-50',
        className,
      )}
      {...rest}
    >
      <div className={cn('flex items-center gap-2', isLoading && 'invisible')}>
        {children}
        {icon}
      </div>

      <div
        className={cn(
          'absolute inset-0 flex items-center justify-center gap-2',
          !isLoading && 'hidden',
        )}
      >
        {loadingText && <span>{loadingText}</span>}
        <AiOutlineLoading3Quarters className="animate-spin" />
      </div>
    </Button>
  );
}
