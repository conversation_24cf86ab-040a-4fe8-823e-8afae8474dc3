'use client';

import { ACHFields } from '@/components/payments/ach-local';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function ACHFieldPage() {
  const query = useSearchParams();
  const querySettings = query?.get('settings') ? JSON.parse(query.get('settings') ?? '{}') : {};
  const cssString = query?.get('css') ?? '';

  const settings = {
    labels: {
      accountNumber: querySettings?.labels?.accountNumber ?? 'Account Number',
      routingNumber: querySettings?.labels?.routingNumber ?? 'Routing Number',
      accountType: querySettings?.labels?.accountType ?? 'Account Type',
      holderType: querySettings?.labels?.holderType ?? 'Holder Type',
    },
  };

  const sendMessage = (message: any) => {
    window.parent.postMessage(message, '*');
  };

  useEffect(() => {
    // parentMessageListener();
    // console.log('cssString', cssString);
    if (cssString) {
      // Create a new <style> element
      let style = document.createElement('style');

      // Add the <style> element to the document's <head> element
      document.head.appendChild(style);

      // Append a CSS string to the <style> element
      style.textContent = cssString;
    }
  }, []);

  return (
    <div className="flex h-full w-full flex-col items-center justify-center" id="ach-parent">
      <ACHFields
        labels={settings.labels}
        onError={(error) => {
          sendMessage({ type: 'error', data: error });
        }}
        onToken={(token) => {
          sendMessage({ type: 'token', data: token });
        }}
        onEvent={(eventType, event) => {
          sendMessage({ type: 'event', data: { event: eventType, data: event } });
        }}
      />
    </div>
  );
}
