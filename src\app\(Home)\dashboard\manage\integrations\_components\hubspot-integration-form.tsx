'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { axiosClient } from '@/lib/axios';
import { env } from 'next-runtime-env';
import { useState } from 'react';

interface HubspotIntegrationFormProps {
  groupId: string;
}

export default function HubspotIntegrationForm({}: HubspotIntegrationFormProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleConnect = async () => {
    try {
      setIsLoading(true);
      let oauthURL = `${env('NEXT_PUBLIC_SERVER_URL')}/api/hubspot/auth/install`;
      let state = JSON.stringify({ redirect: `${window.location.origin}/integ_hs_bind` });

      oauthURL += `?state=${encodeURIComponent(state)}`;

      const res = await axiosClient.get(oauthURL);
      const authURL = res.data.authUrl;
      window.open(authURL, '_blank');
    } catch (error) {
      console.error('Error connecting to Hubspot:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Connect to Hubspot</CardTitle>
        <CardDescription>
          Integrate with Hubspot to sync your contacts and manage your CRM data seamlessly.
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        <div className="text-sm text-slate-600">
          <p>By connecting to Hubspot, you&apos;ll be able to:</p>
          <ul className="list-disc pl-5 pt-2">
            <li>Automatically sync contacts</li>
            <li>Create and update deals</li>
            <li>Access Hubspot CRM data</li>
            <li>Manage marketing campaigns</li>
          </ul>
        </div>
        <Button onClick={handleConnect} className="w-full" disabled={isLoading}>
          {isLoading ? 'Connecting...' : 'Connect to Hubspot'}
        </Button>
      </CardContent>
    </Card>
  );
}
