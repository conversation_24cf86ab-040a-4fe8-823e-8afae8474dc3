'use client';

import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function IntegHSBindPage() {
  const params = useSearchParams();

  const redirectToLogin = async () => {
    window.location.href = `/login?mode=hs-setup&code=${params?.get('bind_id')}&name=${params?.get('name')}`;
  };

  useEffect(() => {
    if (params?.get('bind_id')) {
      redirectToLogin();
    }
  }, [params]);

  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <LoaderSquares />
    </div>
  );
}
