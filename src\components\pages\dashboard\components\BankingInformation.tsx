'use client';

import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useEffect, useState } from 'react';
import { MerchantForm } from '../types/merchart-schema';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { getBankRouting } from '@/graphql/declarations/formUtilities';
const BankingInformation = ({ formState }: { formState: MerchantForm }) => {
  const [checkingRoutingBank, setCheckingRoutingBank] = useState(false);

  const { setValue, register, watch, formState: state, setError, trigger } = formState;
  const values = watch();

  const { errors } = state;

  console.log(errors);
  console.log(values.city);
  useEffect(() => {
    const controller = new AbortController();

    (async () => {
      try {
        console.log(values?.zip_code);

        if (values.routing_number.length < 9) return;
        setCheckingRoutingBank(true);
        const { data } = await apolloClient.query({
          query: getBankRouting,
          variables: {
            input: {
              pattern: values.routing_number,
            },
          },
          context: {
            fetchOptions: {
              signal: controller.signal,
            },
          },
        });

        let bankName;

        if ((bankName = data.processor_aur_bank_routing?.items?.[0]?.bank)) {
          console.log(bankName);
          setValue('bank_name', bankName);
        } else {
          setError('bank_name', {
            message: 'No bank found for this routing number.',
          });
          setValue('bank_name', '');
        }

        trigger('bank_name');
      } catch (err: any) {
        console.log(err);
        if (err?.response?.status === 404) {
          setCheckingRoutingBank(false);
          setValue('bank_name', '');
        }
        if (err?.name !== 'AbortError') {
          setCheckingRoutingBank(true);

          return;
        }
      } finally {
        setCheckingRoutingBank(false);
      }
    })();

    return () => {
      controller.abort();
    };
  }, [values?.routing_number]);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex w-full flex-row gap-8">
        <FormField
          control={formState.control}
          name="routing_number"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Routing Number<span className="text-red-400">*</span>
              </Label>
              <FormControl className="mt-2">
                <Input
                  {...field}
                  onChange={(e) =>
                    setValue('routing_number', e.target.value as string, {
                      shouldValidate: true,
                    })
                  }
                  placeholder="Routing Number"
                  className={cn(
                    errors.routing_number &&
                      'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                  )}
                />
              </FormControl>
            </FormItem>
          )}
        />{' '}
        <div className="mt-5 flex flex-1 flex-row items-center gap-2">
          <Label>Bank Name: </Label>
          {values?.routing_number?.length === 9 && !values.bank_name ? (
            <p className="text-base text-[#F46A6A]">No bank found for this routing number.</p>
          ) : (
            <p className="text-sm font-bold text-zinc-600">{values.bank_name}</p>
          )}
        </div>
      </div>
      <div className="flex w-[calc(50%-16px)] flex-col gap-4">
        <FormField
          control={formState.control}
          name="account_number"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Account number<span className="text-red-400">*</span>
              </Label>
              <Input
                {...field}
                onChange={(e) =>
                  setValue('account_number', e.target.value as string, {
                    shouldValidate: true,
                  })
                }
                placeholder="Account number"
                className={cn(
                  errors.account_number &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />
        <FormField
          control={formState.control}
          name="name_on_account"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Name on Account<span className="text-red-400">*</span>
              </Label>
              <Input
                {...field}
                onChange={(e) =>
                  setValue('name_on_account', e.target.value as string, {
                    shouldValidate: true,
                  })
                }
                placeholder="Name on Account"
                className={cn(
                  errors.name_on_account &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default BankingInformation;
// {values?.zip_code && !values?.city && !checkingZipCode && (
//     <p> Invalid</p>
//   )}
