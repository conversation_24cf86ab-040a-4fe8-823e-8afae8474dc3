import { useCallback, useMemo, useState } from 'react';
import { Table } from 'flowbite-react';
type DataType = {
  [key: string]: string; // Dynamic key-value pairs
};
enum Direction {
  desc = 'desc',
  asc = 'asc',
}

type PaginatedTableProps = {
  pageSize: number;
  rows: DataType[];
};

export const PaginatedTable = ({ pageSize, rows }: PaginatedTableProps) => {
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: Direction }>({
    key: 'transaction',
    direction: Direction.asc,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const columnKeys = useMemo(() => Object.keys(rows[0]), []);

  // Calculate total pages
  const totalPages = Math.ceil(rows.length / pageSize);

  // Get the data for the current page
  const paginatedData = rows.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  // Function to handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getStatusColor = (status: string) => {
    if (status === 'Pending' || status === 'Inactive') return 'gray';
    if (status === 'Failed') return 'red';
    return 'green';
  };

  const handleSort = (column: string) => {
    const direction =
      sortConfig.key === column && sortConfig.direction === Direction.desc
        ? Direction.asc
        : Direction.desc;
    setSortConfig({ key: column, direction });
  };

  const getSortIcon = useCallback(
    (column: string) => {
      if (sortConfig.key === column) return sortConfig.direction === Direction.asc ? ' 🔼' : ' 🔽';
      return '  ';
    },
    [sortConfig],
  );

  return (
    <div>
      <Table className="w-full text-left text-sm text-gray-500 dark:text-gray-400">
        <Table.Head className="bg-gray-50 text-xs uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400">
          {columnKeys.map((key) => (
            <Table.HeadCell key={key} className="px-4 py-3" onClick={() => handleSort(key)}>
              {key.charAt(0).toUpperCase() + key.slice(1)}
              {getSortIcon(key)}
            </Table.HeadCell>
          ))}
        </Table.Head>
        <Table.Body>
          {paginatedData.map((item, index) => (
            <Table.Row
              key={index}
              className="border-b hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
            >
              {columnKeys.map((key) => (
                <Table.Cell
                  key={key}
                  className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white"
                >
                  {key === 'status' ? (
                    <span
                      className={`bg-${getStatusColor(item[key])}-100 text-${getStatusColor(item[key])}-800 mr-2 rounded px-2.5 py-0.5 text-xs font-medium dark:bg-${getStatusColor(item[key])}-900 dark:text-${getStatusColor(item[key])}-300`}
                    >
                      {item[key]}
                    </span>
                  ) : (
                    item[key]
                  )}
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table>

      <div className="mt-4 flex items-center justify-between">
        <button
          disabled={currentPage === 1}
          onClick={() => handlePageChange(currentPage - 1)}
          className="rounded bg-gray-300 px-4 py-2 disabled:opacity-50"
        >
          Previous
        </button>

        <span>
          Page {currentPage} of {totalPages}
        </span>

        <button
          disabled={currentPage === totalPages}
          onClick={() => handlePageChange(currentPage + 1)}
          className="rounded bg-gray-300 px-4 py-2 disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  );
};
