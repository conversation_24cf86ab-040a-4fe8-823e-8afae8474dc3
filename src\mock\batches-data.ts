// Mock data for batches
export interface MockBatchData {
  batchID: string;
  location: string;
  date: string;
  amount: number;
  status: string;
  locationID?: string;
  transactions?: Array<{
    transactionID: string;
    date: string;
    method: string;
    name: string;
    last4: string;
    customer: string;
    amount: number;
    brand: string;
    status: string;
  }>;
}

// Generate mock batch data
const generateMockBatch = (index: number): MockBatchData => {
  const batchStatuses = ['PENDING', 'DEPOSITED']; // Only pending and deposited
  const locations = ['NYC Store', 'LA Store', 'Chicago Store', 'Miami Store', 'Seattle Store'];
  const paymentMethods = ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'];

  const batchDate = new Date();
  batchDate.setDate(batchDate.getDate() - Math.floor(Math.random() * 30));

  const transactionCount = Math.floor(Math.random() * 10) + 1;
  const transactions = Array(transactionCount)
    .fill(null)
    .map((_, txIndex) => {
      const txDate = new Date(batchDate);
      txDate.setHours(txDate.getHours() + txIndex);

      // Generate transaction number in P-840-2025-XXXXXXXX format
      const transactionNumber = `P-840-2025-${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`;

      return {
        transactionID: transactionNumber,
        date: txDate.toISOString(),
        method: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
        name: 'Virtual Terminal', // Changed from customer name to source
        last4: Math.floor(1000 + Math.random() * 9000).toString(),
        customer: 'Virtual Terminal', // Source instead of customer
        amount: Math.floor(Math.random() * 90000) + 1000, // $10 to $900 (less than 1000)
        brand: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
        status: ['CAPTURED', 'REFUND', 'DECLINE'][Math.floor(Math.random() * 3)], // captured/refund/decline
      };
    });

  const totalAmount = transactions.reduce((sum, tx) => sum + tx.amount, 0);

  return {
    batchID: `BATCH-${(8000 + index).toString()}`,
    location: locations[Math.floor(Math.random() * locations.length)],
    date: batchDate.toISOString(),
    amount: Math.min(totalAmount, 999900), // Ensure less than 10000 (in cents)
    status: batchStatuses[Math.floor(Math.random() * batchStatuses.length)],
    locationID: `LOC-${Math.floor(Math.random() * 100)}`,
    transactions,
  };
};

// Create static mock batches data
export const mockBatches: MockBatchData[] = [
  {
    batchID: 'BATCH-8001',
    location: 'Boston Store',
    date: '2025-07-28T10:30:00Z',
    amount: 7500, // $75.00 (500-10000 range)
    status: 'PENDING',
    locationID: 'LOC-001',
    transactions: [
      {
        transactionID: 'P-840-2025-00000437',
        date: '2025-07-28T10:30:00Z',
        method: 'VISA',
        name: 'Virtual Terminal',
        last4: '4242',
        customer: 'Virtual Terminal',
        amount: 450,
        brand: 'VISA',
        status: 'CAPTURED',
      },
      {
        transactionID: 'P-840-2025-00000438',
        date: '2025-07-28T11:15:00Z',
        method: 'MASTERCARD',
        name: 'Virtual Terminal',
        last4: '5555',
        customer: 'Virtual Terminal',
        amount: 325,
        brand: 'MASTERCARD',
        status: 'REFUND',
      },
    ],
  },
  {
    batchID: 'BATCH-8002',
    location: 'LA Store',
    date: '2025-07-27T14:20:00Z',
    amount: 5500, // $55.00 (500-10000 range)
    status: 'DEPOSITED',
    locationID: 'LOC-002',
    transactions: [
      {
        transactionID: 'P-840-2025-00000439',
        date: '2025-07-27T14:20:00Z',
        method: 'AMEX',
        name: 'Virtual Terminal',
        last4: '1234',
        customer: 'Virtual Terminal',
        amount: 185,
        brand: 'AMEX',
        status: 'DECLINE',
      },
      {
        transactionID: 'P-840-2025-00000440',
        date: '2025-07-27T15:45:00Z',
        method: 'VISA',
        name: 'Virtual Terminal',
        last4: '9876',
        customer: 'Virtual Terminal',
        amount: 320,
        brand: 'VISA',
        status: 'CAPTURED',
      },
    ],
  },
  {
    batchID: 'BATCH-8003',
    location: 'Boston Store',
    date: '2025-07-26T09:15:00Z',
    amount: 8500, // $85.00 (500-10000 range)
    status: 'DEPOSITED',
    locationID: 'LOC-003',
    transactions: [
      {
        transactionID: 'P-840-2025-00000441',
        date: '2025-07-26T09:15:00Z',
        method: 'DISCOVER',
        name: 'Virtual Terminal',
        last4: '6789',
        customer: 'Virtual Terminal',
        amount: 672,
        brand: 'DISCOVER',
        status: 'CAPTURED',
      },
      {
        transactionID: 'P-840-2025-00000442',
        date: '2025-07-26T10:30:00Z',
        method: 'VISA',
        name: 'Virtual Terminal',
        last4: '3456',
        customer: 'Virtual Terminal',
        amount: 283,
        brand: 'VISA',
        status: 'REFUND',
      },
    ],
  },
  {
    batchID: 'BATCH-8004',
    location: 'LA Store',
    date: '2025-07-25T16:45:00Z',
    amount: 6800, // $68.00 (500-10000 range)
    status: 'PENDING',
    locationID: 'LOC-004',
    transactions: [
      {
        transactionID: 'P-840-2025-00000443',
        date: '2025-07-25T16:45:00Z',
        method: 'MASTERCARD',
        name: 'Virtual Terminal',
        last4: '7890',
        customer: 'Virtual Terminal',
        amount: 287,
        brand: 'MASTERCARD',
        status: 'CAPTURED',
      },
      {
        transactionID: 'P-840-2025-00000444',
        date: '2025-07-25T17:20:00Z',
        method: 'VISA',
        name: 'Virtual Terminal',
        last4: '2345',
        customer: 'Virtual Terminal',
        amount: 445,
        brand: 'VISA',
        status: 'DECLINE',
      },
    ],
  },
];

// Generate additional mock batches if needed
export const generateMockBatches = (count: number = 10): MockBatchData[] => {
  return Array(count)
    .fill(null)
    .map((_, index) => generateMockBatch(index + 6));
};

// Export all batches (static + generated)
export const allMockBatches = mockBatches;
