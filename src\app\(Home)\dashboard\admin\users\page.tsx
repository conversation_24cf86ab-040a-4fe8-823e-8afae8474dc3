'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const Page = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect from old admin URL to new manage URL
    router.replace('/dashboard/manage/users');
  }, [router]);

  return (
    <div className="mx-auto mt-8 py-0">
      <div className="mb-4 text-sm text-gray-600">Redirecting to the new management page...</div>
    </div>
  );
};

export default Page;
