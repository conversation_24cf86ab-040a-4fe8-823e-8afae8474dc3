'use client';

import { FC, useEffect, useState } from 'react';

import * as DialogPrimitive from '@radix-ui/react-dialog';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { useDraftColumn } from './components/useDraftsColumn';
import MerchantForm from '../dashboard/components/merchantForm';
import { X } from 'lucide-react';
import { Button, Label, TextInput } from 'flowbite-react';
import { GroupWhereInput, QueryMode } from '@/graphql/generated/graphql';

import { useSearchParams, useRouter } from 'next/navigation';
// import { submitAuroraDraft } from '@/graphql/declarations/createDraft';
import useDebounce from '@/components/hooks/useDebounce';
import { Tabs } from 'flowbite-react';

const SearchAccounts = function (args: { searchValue: string; setSearchValue: any }) {
  return (
    <form className="mb-4 sm:mb-0 sm:pr-3" action="#" method="GET">
      <Label htmlFor="products-search" className="sr-only">
        Search Accounts
      </Label>
      <div className="relative mt-1 sm:w-64 xl:w-96">
        <TextInput
          id="products-search"
          name="products-search"
          placeholder="Search Accounts"
          value={args.searchValue}
          onChange={(e) => args.setSearchValue(e.target.value)}
        />
      </div>
    </form>
  );
};

const AccountTableList: FC<{
  queryObject: GroupWhereInput;
  setUrlToSign?: any;
  setPostSuccess?: any;
}> = function (args) {
  // const router = useRouter();
  // const { data: groupData, loading: groupLoading } = useQuery(getGroupList, {
  //   variables: {
  //     where: args.queryObject,
  //   },
  // });

  // const [isSubmitting, setIsSubmitting] = useState(false);

  // const fxDeleteGroup = async (id: string) => {
  //   await apolloClient.mutate({
  //     mutation: deleteGroup,
  //     variables: {
  //       where: {
  //         id,
  //       },
  //     },
  //   });

  //   await apolloClient.resetStore();
  // };

  // const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  // const [currentToSubmit, setCurrentToSubmit] = useState<any>(null);
  // const currentFormID = useRef<string | null>(null);

  // const submitForm = async (args: any) => {
  //   // submit form
  //   setIsPopoverOpen(true);
  //   setCurrentToSubmit(args);
  //   currentFormID.current = args.id;
  // };

  // const finalizeSubmit = async () => {
  //   setIsSubmitting(true);
  //   try {
  //     const currentForm = await apolloClient.query({
  //       query: getGroups,
  //       variables: {
  //         where: {
  //           id: {
  //             equals: currentToSubmit.id,
  //           },
  //         },
  //         skip: 0,
  //       },
  //     });
  //     const urlToSign = currentForm.data.groups?.[0]?.processorAUR?.[0].processor_aur_signingUrl;
  //     if (urlToSign) {
  //       // open new tab
  //       // window.open(redirectUrl, "_blank");
  //       if (args.setUrlToSign) {
  //         setIsPopoverOpen(false);
  //         args.setUrlToSign(urlToSign);
  //       }
  //     } else {
  //       const req = await apolloClient.mutate({
  //         mutation: submitAuroraDraft,
  //         variables: {
  //           input: {
  //             groupId: currentToSubmit.id,
  //           },
  //         },
  //       });

  //       const redirectUrl = req.data?.processor_aur_draft_submit?.urlForSigning;

  //       if (redirectUrl) {
  //         // open new tab
  //         // window.open(redirectUrl, "_blank");
  //         if (args.setUrlToSign) {
  //           setIsPopoverOpen(false);
  //           args.setUrlToSign(redirectUrl);
  //         }
  //       }
  //     }
  //   } catch (err) {
  //     console.error(err);
  //     toast.error('An error occurred while submitting the form');
  //   } finally {
  //     setIsSubmitting(false);
  //   }
  // };

  // // create an iframe event listener to listen for the redirect url
  // useEffect(() => {
  //   // listen for events coming from the iframe
  //   window.addEventListener('message', (event) => {
  //     if (event.origin !== 'https://app-eval.signnow.com') return;
  //     let redirectPath = event.data?.path ?? '';
  //     if (redirectPath.includes('/static-page/thank-you')) {
  //       // console.log("User has signed the document");
  //       args.setUrlToSign?.(null);
  //       (async () => {
  //         if (!currentFormID.current) return;
  //         await apolloClient.mutate({
  //           mutation: signGroup,
  //           variables: {
  //             input: {
  //               groupID: currentFormID.current,
  //               signingURL: redirectPath,
  //             },
  //           },
  //         });
  //         await apolloClient.resetStore();
  //       })();
  //       args.setPostSuccess?.(true);
  //     }
  //   });
  // }, []);

  // const approveShop = async (args: { id: string }) => {
  //   await apolloClient.mutate({
  //     mutation: GatewayInitialize,
  //     variables: {
  //       input: {
  //         groupID: args.id,
  //       },
  //     },
  //   });

  //   toast.success('Merchant has been approved');
  //   await apolloClient.resetStore();
  // };

  // const connectGHL = async (args: { id: string }) => {
  //   const response = await axiosClient.get(`/api/ghapi/auth/signin?groupID=${args.id}`);

  //   const redirectUrl = response?.data?.url;

  //   if (!redirectUrl) {
  //     toast.error('An error occurred while connecting to GHL');
  //     return;
  //   }

  //   // open new tab
  //   window.open(redirectUrl, '_blank');
  // };

  // return (
  //   <div className="overflow-x-auto">
  //     <Table className="min-w-[75vw]">
  //       <Table.Head>
  //         <Table.HeadCell>Location</Table.HeadCell>
  //         <Table.HeadCell>Merchant ID</Table.HeadCell>
  //         <Table.HeadCell>Status</Table.HeadCell>
  //         <Table.HeadCell>PCI Status</Table.HeadCell>
  //         <Table.HeadCell>Date</Table.HeadCell>
  //         <Table.HeadCell>
  //           <span className="sr-only">Edit</span>
  //         </Table.HeadCell>
  //       </Table.Head>
  //       <Table.Body className="divide-y">
  //         {groupData?.groups?.map((group: any) => {
  //           return (
  //             <Table.Row key={group.id} className="bg-white dark:border-gray-700 dark:bg-gray-800">
  //               <Table.Cell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
  //                 {group.name}
  //               </Table.Cell>
  //               <Table.Cell>{group.displayMerchantID ?? '-pending-'}</Table.Cell>
  //               <Table.Cell>{group.processorStatus}</Table.Cell>
  //               <Table.Cell>{group.pciStatus}</Table.Cell>
  //               <Table.Cell>{moment(group.createdAt).format('MMM Do YYYY h:mm a')}</Table.Cell>
  //               <Table.Cell className="flex gap-2">
  //                 {['active'].includes(group.processorStatus ?? '') && (
  //                   <>
  //                     <button
  //                       onClick={() => {
  //                         connectGHL(group);
  //                       }}
  //                       disabled={isSubmitting}
  //                       className="font-medium text-purple-600 hover:underline dark:text-green-500"
  //                     >
  //                       Connect GHL
  //                     </button>
  //                   </>
  //                 )}
  //                 {['signed'].includes(group.processorStatus ?? '') && (
  //                   <>
  //                     <button
  //                       onClick={() => {
  //                         approveShop(group);
  //                       }}
  //                       disabled={isSubmitting}
  //                       className="font-medium text-purple-600 hover:underline dark:text-green-500"
  //                     >
  //                       [Approve]
  //                     </button>
  //                   </>
  //                 )}
  //                 {['draft'].includes(group.processorStatus ?? '') && (
  //                   <>
  //                     <button
  //                       onClick={() => {
  //                         submitForm(group);
  //                       }}
  //                       disabled={isSubmitting}
  //                       className="font-medium text-green-600 hover:underline dark:text-green-500"
  //                     >
  //                       Submit
  //                     </button>
  //                   </>
  //                 )}
  //                 {['draft', ''].includes(group.processorStatus ?? '') && (
  //                   <>
  //                     <button
  //                       onClick={() => {
  //                         // update current route query
  //                         const curLink = window.location.href;
  //                         // get params and update modal to group id
  //                         const url = new URL(curLink);
  //                         url.searchParams.set('modal', group.id || '');
  //                         router.push(url.toString());
  //                       }}
  //                       className="font-medium text-cyan-600 hover:underline dark:text-cyan-500"
  //                     >
  //                       Edit
  //                     </button>
  //                   </>
  //                 )}
  //                 <button
  //                   onClick={() => fxDeleteGroup(group.id)}
  //                   className="font-medium text-red-600 hover:underline dark:text-red-500"
  //                 >
  //                   Delete
  //                 </button>
  //               </Table.Cell>
  //             </Table.Row>
  //           );
  //         })}
  //       </Table.Body>
  //     </Table>
  //     {groupLoading && (
  //       <div className="flex h-32 items-center justify-center">
  //         <LoaderSquares />
  //       </div>
  //     )}
  //     <Dialog open={isPopoverOpen}>
  //       <DialogTrigger asChild></DialogTrigger>
  //       <DialogContent className="w-full py-4">
  //         <DialogPrimitive.Close
  //           onClick={() => {
  //             const curPath = window.location.pathname;
  //             // clear modal query string
  //             const newUrl = `${curPath}`;
  //             router.push(newUrl);
  //             setIsPopoverOpen(false);
  //           }}
  //           className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 data-[state=open]:text-gray-500 dark:ring-offset-gray-950 dark:focus:ring-gray-300 dark:data-[state=open]:bg-gray-800 dark:data-[state=open]:text-gray-400"
  //         >
  //           <X className="h-4 w-4" />
  //           <span className="sr-only">Close</span>
  //         </DialogPrimitive.Close>
  //         <h1 className="text-xl">Submit Form?</h1>
  //         <p>
  //           By clicking submit, you are sure about the information provided. You will not be able to
  //           edit this information again.
  //         </p>
  //         <p>Name: {currentToSubmit?.name}</p>
  //         <div className="mt-4 flex justify-end gap-4">
  //           <Button
  //             onClick={() => {
  //               setIsPopoverOpen(false);
  //             }}
  //           >
  //             Cancel
  //           </Button>
  //           <Button
  //             disabled={isSubmitting}
  //             onClick={() => {
  //               finalizeSubmit();
  //             }}
  //           >
  //             {isSubmitting ? <Spinner /> : 'Submit'}
  //           </Button>
  //         </div>
  //       </DialogContent>
  //     </Dialog>
  //   </div>
  // );

  return <></>;
};

const AccountsPageList = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [urlToSign, setUrlToSign] = useState<string | null>(null);
  const [postSuccess, setPostSuccess] = useState<boolean>(false);

  useEffect(() => {
    console.log(searchParams);
    if (searchParams?.has('modal')) {
      setIsPopoverOpen(true);
    }
  }, [searchParams]);

  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const [pendingSearchQuery, setPendingSearchQuery] = useState('');
  const debouncedPendingSearchQuery = useDebounce(pendingSearchQuery, 500);
  const pageIndex = 0;
  const pageSize = 10;
  const [selectedRow, setSelectedRow] = useState(null);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const [formOpen, setFormOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [actionData, setActionData] = useState(null);
  const [formType, setFormType] = useState('');
  const [playOpen, setPlayOpen] = useState(false);
  const [pauseOpen, setPauseOpen] = useState(false);

  const togglePlay = () => {
    setPlayOpen(!playOpen);
  };

  const handleRowClick = (rowData: any) => {
    setSelectedRowData(rowData);
    setSelectedRow(rowData);
    setIsDialogOpen(true);
  };

  const columns = useDraftColumn(togglePlay, setPauseOpen, setActionData);

  const onSearchChange = () => {};
  return (
    <div className="mx-auto w-full max-w-screen-xl p-2 pb-24">
      <div className="text-primary pl-4 text-2xl font-semibold">My Merchant Accounts</div>
      <div className="h-12 w-full"></div>
      <div>
        <div className="pl-4 text-lg text-[#1C1C1C]">Active Accounts</div>
        <div className="flex h-[90px] items-center justify-between gap-2 p-2 md:gap-4 md:py-6">
          <div className="block w-full items-center">
            <SearchAccounts searchValue={searchQuery} setSearchValue={setSearchQuery} />
          </div>

          <div className="flex w-full items-center justify-end gap-6">
            <Button
              onClick={() => {
                // add modal=new on the query string

                // update current route query
                const curLink = window.location.href;
                // get params and update modal to group id
                const url = new URL(curLink);
                url.searchParams.set('modal', 'new');
                router.push(url.toString());
              }}
            >
              + Create
            </Button>
            <Dialog open={isPopoverOpen}>
              <DialogTrigger asChild></DialogTrigger>
              <DialogContent className="h-fit w-full p-10">
                <DialogPrimitive.Close
                  onClick={() => {
                    const curPath = window.location.pathname;
                    // clear modal query string
                    const newUrl = `${curPath}`;
                    router.push(newUrl);
                    setIsPopoverOpen(false);
                  }}
                  className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 data-[state=open]:text-gray-500 dark:ring-offset-gray-950 dark:focus:ring-gray-300 dark:data-[state=open]:bg-gray-800 dark:data-[state=open]:text-gray-400"
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </DialogPrimitive.Close>
                <MerchantForm
                  setOpen={setIsPopoverOpen}
                  currentID={
                    searchParams?.get('modal') === 'new' ? null : searchParams?.get('modal')
                  }
                />
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <AccountTableList
          queryObject={{
            actualName: {
              contains: debouncedSearchQuery,
              mode: QueryMode.Insensitive,
            },
            processorStatus: {
              equals: 'active',
            },
          }}
        />
      </div>
      <div className="border-b-2 py-4"></div>
      <div className="mx-auto h-8 w-full max-w-screen-xl p-2" />
      <div>
        <div className="pl-4 text-lg text-[#1C1C1C]">Pending Accounts</div>
        <div className="flex h-[90px] items-center justify-between gap-2 p-2 md:gap-4 md:py-6">
          <div className="block w-full items-center">
            <SearchAccounts
              searchValue={pendingSearchQuery}
              setSearchValue={setPendingSearchQuery}
            />
          </div>
        </div>

        <AccountTableList
          queryObject={{
            actualName: {
              contains: debouncedPendingSearchQuery,
              mode: QueryMode.Insensitive,
            },
            processorStatus: {
              not: {
                equals: 'active',
              },
            },
          }}
          setUrlToSign={setUrlToSign}
          setPostSuccess={setPostSuccess}
        />
      </div>
      <Dialog open={!!urlToSign}>
        <DialogTrigger asChild></DialogTrigger>
        <DialogContent className="h-[85vh] w-fit !max-w-screen-2xl py-8">
          <DialogPrimitive.Close
            onClick={() => {
              setUrlToSign(null);
            }}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 data-[state=open]:text-gray-500 dark:ring-offset-gray-950 dark:focus:ring-gray-300 dark:data-[state=open]:bg-gray-800 dark:data-[state=open]:text-gray-400"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
          {urlToSign && <iframe src={urlToSign} className="h-full w-[50vw]" />}
        </DialogContent>
      </Dialog>
      <Dialog open={postSuccess}>
        <DialogTrigger asChild></DialogTrigger>
        <DialogContent className="w-fit py-8">
          <DialogPrimitive.Close
            onClick={() => {
              setPostSuccess(false);
            }}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-gray-950 focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-gray-100 data-[state=open]:text-gray-500 dark:ring-offset-gray-950 dark:focus:ring-gray-300 dark:data-[state=open]:bg-gray-800 dark:data-[state=open]:text-gray-400"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
          <h1 className="text-2xl font-bold">Success</h1>
          <p>Thank you for signing the document</p>
          <p>
            We will now review your document. We might request additional document or information
            from you. Please check your email for further instructions.
          </p>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const TabsComponent = function () {
  return (
    <Tabs aria-label="Default tabs" variant="default" className="mt-1 items-center">
      <Tabs.Item title="Accounts">
        <AccountsPageList />
      </Tabs.Item>
      <Tabs.Item title="Equipments">Equipments</Tabs.Item>
    </Tabs>
  );
};

const DraftPage = () => {
  return <TabsComponent />;
};

export default DraftPage;
