import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from 'flowbite-react';
import { useState } from 'react';

interface SimplifiedAddAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { email: string; businessName: string }) => void;
}

export function SimplifiedAddAccountModal({
  isOpen,
  onClose,
  onSubmit,
}: SimplifiedAddAccountModalProps) {
  const [email, setEmail] = useState('');
  const [businessName, setBusinessName] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = () => {
    if (!email || !businessName) {
      setError('Both email and business name are required');
      return;
    }

    if (!email.includes('@')) {
      setError('Please enter a valid email address');
      return;
    }

    onSubmit({ email, businessName });
    setEmail('');
    setBusinessName('');
    setError('');
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="md">
      <Modal.Header>Add New Account</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">Business Name</label>
            <Input
              value={businessName}
              onChange={(e) => setBusinessName(e.target.value)}
              placeholder="Enter business name"
            />
          </div>
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">Email</label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email address"
            />
          </div>
          {error && <p className="text-sm text-red-500">{error}</p>}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSubmit}>
            Add Account
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
}
