'use client';

import { useForm, FormProvider } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useCallback, useEffect, useState, useMemo } from 'react';
import debounce from 'lodash/debounce';
import { env } from 'next-runtime-env';
import { encryptECC } from '@/lib/encryptions/ecc';

export type CardResponse = {
  cardNumber: string;
  cvv: string;
  expiration: string;
};

interface CardFieldsProps {
  onEvent?: (eventType: string, event: any) => void;
  onToken?: (response: CardResponse) => void;
  onError?: (error: any) => void;
  labels?: {
    cardNumber?: string;
    cvv?: string;
    expiration?: string;
  };
}

export const CardFields = ({ onEvent, onToken, onError, labels = {} }: CardFieldsProps) => {
  const methods = useForm<CardResponse>({
    mode: 'onBlur',
  });
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = methods;
  const [fieldValues, setFieldValues] = useState<Partial<CardResponse>>({});

  const debouncedFieldChange = useCallback(
    debounce((field: string, value: string) => {
      onEvent?.('fieldChange', { field, value });
      setFieldValues((prev) => ({ ...prev, [field]: value }));
    }, 500),
    [onEvent],
  );

  const handleFieldChange = (field: string, value: string) => {
    debouncedFieldChange(field, value);
  };

  const formatCardNumber = (value: string) => {
    const digits = value.replace(/\D/g, '');
    const groups = digits.match(/.{1,4}/g);
    return groups ? groups.join(' ') : digits;
  };

  const formatExpiration = (value: string) => {
    const digits = value.replace(/\D/g, '');
    if (digits.length >= 2) {
      return `${digits.slice(0, 2)}/${digits.slice(2, 4)}`;
    }
    return digits;
  };

  const onSubmit = (data: CardResponse) => {
    try {
      if (data.cardNumber.length < 13 || data.cardNumber.length > 19) {
        throw new Error('Invalid card number');
      }
      if (data.cvv.length < 3 || data.cvv.length > 4) {
        throw new Error('Invalid CVV');
      }

      data.cardNumber = encryptECC(data.cardNumber, env('NEXT_PUBLIC_CARD_KEY') ?? '');
      data.cvv = encryptECC(data.cvv, env('NEXT_PUBLIC_CARD_KEY') ?? '');

      console.log('Card Fields submitted:', data);
      onToken?.(data);
    } catch (error) {
      onError?.(error);
    }
  };

  useEffect(() => {
    const isComplete =
      (fieldValues?.cardNumber?.length ?? 0) >= 13 &&
      (fieldValues?.cvv?.length ?? 0) >= 3 &&
      fieldValues?.expiration;

    if (isComplete) {
      handleSubmit(onSubmit)();
    }
  }, [fieldValues]);

  const handleClearFields = () => {
    reset();
    setFieldValues({});
    onEvent?.('clearFields', {});
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2 space-y-2">
            <Label htmlFor="cardNumber">{labels.cardNumber || 'Card Number'}</Label>
            <Input
              id="cardNumber"
              type="text"
              maxLength={19}
              {...register('cardNumber', {
                required: 'Card number is required',
                validate: (value) => {
                  const digits = value.replace(/\D/g, '');
                  return (
                    (digits.length >= 13 && digits.length <= 19) ||
                    'Card number must be between 13 and 19 digits'
                  );
                },
              })}
              onChange={(e) => {
                const formatted = formatCardNumber(e.target.value);
                e.target.value = formatted;
                setValue('cardNumber', formatted); //.replace(/\s/g, ''));
                handleFieldChange('cardNumber', formatted); //.replace(/\s/g, ''));
              }}
            />
            {errors.cardNumber && (
              <p className="text-sm text-red-500">{errors.cardNumber.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="expiration">{labels.expiration || 'Expiration (MM/YY)'}</Label>
            <Input
              id="expiration"
              placeholder="MM/YY"
              maxLength={5}
              {...register('expiration', {
                required: 'Expiration date is required',
                pattern: {
                  value: /^(0[1-9]|1[0-2])\/([0-9]{2})$/,
                  message: 'Please enter a valid expiration date (MM/YY)',
                },
              })}
              onChange={(e) => {
                const formatted = formatExpiration(e.target.value);
                e.target.value = formatted;
                setValue('expiration', formatted);
                handleFieldChange('expiration', formatted);
              }}
            />
            {errors.expiration && (
              <p className="text-sm text-red-500">{errors.expiration.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="cvv">{labels.cvv || 'CVV'}</Label>
            <Input
              id="cvv"
              type="number"
              {...register('cvv', {
                required: 'CVV is required',
                validate: (value) =>
                  (value.length >= 3 && value.length <= 4) || 'CVV must be 3 or 4 digits',
              })}
              onChange={(e) => {
                setValue('cvv', e.target.value);
                handleFieldChange('cvv', e.target.value);
              }}
            />
            {errors.cvv && <p className="text-sm text-red-500">{errors.cvv.message}</p>}
          </div>
        </div>

        <div className="flex justify-end pt-2">
          <Button
            onClick={handleClearFields}
            size="sm"
            type="button"
            variant="outline"
            className="text-red-500 hover:bg-red-50 hover:text-red-600"
          >
            Clear Fields
          </Button>
        </div>
      </form>
    </FormProvider>
  );
};

interface CardHostedComponentProps {
  onEvent?: (eventType: string, event: any) => void;
  onToken?: (response: CardResponse) => void;
  onError?: (error: any) => void;
  labels?: {
    cardNumber?: string;
    cvv?: string;
    expiration?: string;
  };
  parentComponentAttributes?: { [key: string]: any };
  iframeComponentAttributes?: { [key: string]: any };
}

export const CardHostedComponent = (props: CardHostedComponentProps) => {
  const registerListener = () => {
    window.addEventListener('message', (event) => {
      const eventType = event.data.type;
      const data = event.data.data;

      switch (eventType) {
        case 'event': {
          props.onEvent?.(data.event, data.data);
          break;
        }
        case 'token': {
          props.onToken?.(data);
          break;
        }
        case 'error': {
          props.onError?.(data);
          break;
        }
      }
    });
  };

  useEffect(() => {
    registerListener();
  }, []);

  const settingsString = JSON.stringify({
    labels: props.labels,
  });

  const iframeURL = useMemo(() => {
    const baseURL = env('NEXT_PUBLIC_CARD_HOSTED_URL');
    if (!baseURL) {
      return '';
    }
    const url = new URL(baseURL);
    url.searchParams.append('settings', settingsString);
    return url.toString();
  }, [settingsString]);

  return (
    <div {...(props.parentComponentAttributes ?? {})}>
      <iframe
        src={iframeURL}
        width={props.iframeComponentAttributes?.width ?? '100%'}
        height={props.iframeComponentAttributes?.height ?? '450px'}
        {...(props.iframeComponentAttributes ?? {})}
      />
    </div>
  );
};
