'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Check, Copy } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';

export default function PaymentLinkSuccess({ paymentLink }: { paymentLink: string }) {
  const [copied, setCopied] = useState(false);
  const handleCopy = async () => {
    try {
      // Try to write to clipboard directly first
      await navigator.clipboard.writeText(paymentLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // If direct write fails, show manual copy message
      console.error('Failed to copy text: ', err);

      toast.error('Unable to copy automatically. Please select and copy the link manually.');
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Payment Link Generated</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input value={paymentLink} readOnly className="text-sm font-medium" />
          <Button onClick={handleCopy} className="w-full" variant="primary">
            {copied ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                Copied
              </>
            ) : (
              <>
                <Copy className="mr-2 h-4 w-4" />
                Copy Link
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
