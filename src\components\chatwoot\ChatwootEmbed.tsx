import { env } from 'next-runtime-env';
import React from 'react';

// class ChatwootWidget extends React.Component {
//   componentDidMount() {
//     // Add Chatwoot Settings
//     // @ts-ignore
//     window.chatwootSettings = {
//       hideMessageBubble: false,
//       position: 'right', // This can be left or right
//       locale: 'en', // Language to be set
//       type: 'standard', // [standard, expanded_bubble]
//     };

//     // Paste the script from inbox settings except the <script> tag
//     (function (d, t) {
//       var BASE_URL = env('NEXT_PUBLIC_CHAT_URL');
//       var g = d.createElement(t),
//         s = d.getElementsByTagName(t)[0];
//       // @ts-ignore
//       // g.src = BASE_URL + '/packs/js/sdk.js';
//       // @ts-ignore
//       s.parentNode.insertBefore(g, s);
//       // @ts-ignore
//       g.async = !0;
//       g.onload = function () {
//         // @ts-ignore
//         window.chatwootSDK.run({
//           websiteToken: env('NEXT_PUBLIC_CHAT_KEY'),
//           baseUrl: BASE_URL,
//         });
//       };
//     })(document, 'script');
//   }

//   render() {
//     return null;
//   }
// }

// export default ChatwootWidget;
