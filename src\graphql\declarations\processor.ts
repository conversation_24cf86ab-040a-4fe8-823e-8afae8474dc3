import { graphql } from '../generated';

export const Processor_draft_create = graphql(`
  mutation Processor_draft_create($input: Processor_draft_createInput!) {
    processor_draft_create(input: $input) {
      submissionID
      groupID
    }
  }
`);

export const Processor_draft_update = graphql(`
  mutation Processor_draft_update($input: Processor_draft_updateInput!) {
    processor_draft_update(input: $input) {
      submissionID
      groupID
    }
  }
`);

export const Processor_draft_submit = graphql(`
  mutation Processor_draft_submit($input: Processor_draft_submitInput!) {
    processor_draft_submit(input: $input) {
      urlForSigning
      applicationId
      applicationNumber
    }
  }
`);

export const Processor_draft_documentUpload = graphql(`
  mutation Processor_draft_documentUpload($input: Processor_draft_documentUploadInput!) {
    processor_draft_documentUpload(input: $input) {
      documentID {
        name
        id
      }
    }
  }
`);

export const Processor_draft_documentForward = graphql(`
  mutation Processor_draft_documentForward($input: Processor_draft_documentForwardInput!) {
    processor_draft_documentForward(input: $input) {
      success
    }
  }
`);

export const Processor_draft_status = graphql(`
  query Processor_draft_status($input: Processor_draft_statusInput!) {
    processor_draft_status(input: $input) {
      attestations {
        name
        ip_address
        time_of_attestation
        url
        signature
      }
      businessInfo {
        legalBusinessName
        typeOfBusiness
        dbaName
        ein
        dateBusinessEstablished
        businessEmail
        businessPhone
        businessPhoneCountryCode
        website
        customerServicePhone
        customerServicePhoneCountryCode
        street
        zipCode
        city
        state
        country
        differentLegalAddress
        legalMailingStreet
        legalMailingZipCode
        legalMailingCity
        legalMailingState
        legalMailingCountry
      }
      transactionInfo {
        businessCategory
        description
        swipe
        keyed
        ecommerce
        avgTransactionAmount
        highestTransactionAmount
        grossMonthlySalesVolume
        amexAvgTransactionAmount
        amexHighestTransactionAmount
        amexGrossMonthlySalesVolume
      }
      owners {
        isControlOwner
        firstName
        lastName
        title
        ownershipPercentage
        phoneNumber
        phoneNumberCountryCode
        homeAddress
        country
        state
        city
        zipCode
        dateOfBirth
        ssn
        email
      }
      files {
        id
        name
        category
        purpose
        mimetype
        b64
        submittedOn
      }
      bankInfo {
        routingNumber
        accountNumber
        bankName
        nameOnAccount
      }
      status {
        message
      }
    }
  }
`);
