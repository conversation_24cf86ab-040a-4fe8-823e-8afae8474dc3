import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Promisable } from '@/types/types';
import {
  BanknoteIcon as Bank,
  Building2,
  Calendar,
  CreditCard,
  Globe,
  Mail,
  MapPin,
  Phone,
  User,
  Wallet,
} from 'lucide-react';
import React, { MutableRefObject, useState } from 'react';
import { MerchantFormInput } from '../updateData';

const ReviewPage = (args: {
  triggerSubmit?: MutableRefObject<() => Promisable<boolean>>;
  initialData?: any;
}) => {
  const [formData, setFormData] = useState<MerchantFormInput>(args.initialData ?? {});

  return (
    <div className="min-h-screen bg-gray-50/50 py-8 dark:bg-gray-900/50">
      <div className="container mx-auto max-w-6xl space-y-8 px-4">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Business Profile</h1>
          <p className="text-muted-foreground">View and manage your business information</p>
        </div>

        {/* Business Info Section */}
        <Card className="transition-all hover:shadow-md">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Building2 className="text-primary h-5 w-5" />
              <CardTitle>Business Information</CardTitle>
            </div>
            <CardDescription>Basic business details and contact information</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-8">
            <div className="grid gap-6 sm:grid-cols-2">
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Legal Business name</div>
                <div className="font-medium">{formData.businessInfo?.legalBusinessName ?? '-'}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Type of Business</div>
                <Badge variant="secondary">{formData.businessInfo?.typeOfBusiness ?? '-'}</Badge>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">DBA Name</div>
                <div className="font-medium">{formData.businessInfo?.dbaName ?? '-'}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">EIN</div>
                <div className="font-mono font-medium">{formData.businessInfo?.ein ?? '-'}</div>
              </div>
            </div>

            <Separator />

            <div className="grid gap-6 sm:grid-cols-2">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Date Established</div>
                  <div className="font-medium">
                    {formData.businessInfo?.dateBusinessEstablished ?? '-'}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Business Email</div>
                  <div className="font-medium">{formData.businessInfo?.businessEmail ?? '-'}</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Business Phone</div>
                  <div className="font-medium">{formData.businessInfo?.businessPhone ?? '-'}</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Website</div>
                  <div className="font-medium">{formData.businessInfo?.website ?? '-'}</div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <div className="mb-4 flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <div className="text-sm font-medium text-muted-foreground">Location Address</div>
              </div>
              <div className="grid gap-4 sm:grid-cols-4">
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Street</div>
                  <div className="font-medium">{formData.businessInfo?.street ?? '-'}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">City</div>
                  <div className="font-medium">{formData.businessInfo?.city ?? '-'}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">State</div>
                  <div className="font-medium">{formData.businessInfo?.state ?? '-'}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Zip Code</div>
                  <div className="font-medium">{formData.businessInfo?.zipCode ?? '-'}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Transactions Section */}
        <Card className="transition-all hover:shadow-md">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <CreditCard className="text-primary h-5 w-5" />
              <CardTitle>Transaction Details</CardTitle>
            </div>
            <CardDescription>Payment processing information and volumes</CardDescription>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="grid gap-6 sm:grid-cols-3">
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Business Category</div>
                <div className="font-medium">
                  {formData.transactionInfo?.businessCategory ?? '-'}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-medium text-muted-foreground">Description</div>
                <div className="font-medium">{formData.transactionInfo?.description ?? '-'}</div>
              </div>
            </div>

            <div className="grid gap-4 sm:grid-cols-3">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger className="cursor-help">
                    <Card className="border-dashed">
                      <CardHeader className="pb-2">
                        <div className="text-sm font-medium text-muted-foreground">Swiped</div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {formData.transactionInfo?.swipe ?? '-'}
                        </div>
                      </CardContent>
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Number of card-present transactions</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger className="cursor-help">
                    <Card className="border-dashed">
                      <CardHeader className="pb-2">
                        <div className="text-sm font-medium text-muted-foreground">Keyed</div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {formData.transactionInfo?.keyed ?? '-'}
                        </div>
                      </CardContent>
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Number of manually entered transactions</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger className="cursor-help">
                    <Card className="border-dashed">
                      <CardHeader className="pb-2">
                        <div className="text-sm font-medium text-muted-foreground">Ecommerce</div>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {formData.transactionInfo?.ecommerce ?? '-'}
                        </div>
                      </CardContent>
                    </Card>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Number of online transactions</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            <Separator />

            <div className="space-y-6">
              <div className="flex items-center space-x-2">
                <Wallet className="h-4 w-4 text-muted-foreground" />
                <div className="font-medium">Visa / Mastercard / Discover Transactions</div>
              </div>
              <div className="grid gap-4 sm:grid-cols-3">
                <Card className="bg-primary/5">
                  <CardHeader className="pb-2">
                    <div className="text-sm text-muted-foreground">Average Amount</div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {' '}
                      {formData.transactionInfo?.avgTransactionAmount ?? '-'}
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-primary/5">
                  <CardHeader className="pb-2">
                    <div className="text-sm text-muted-foreground">Highest Amount</div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {' '}
                      {formData.transactionInfo?.highestTransactionAmount ?? '-'}
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-primary/5">
                  <CardHeader className="pb-2">
                    <div className="text-sm text-muted-foreground">Monthly Sales</div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {' '}
                      {formData.transactionInfo?.grossMonthlySalesVolume}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="space-y-6">
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <div className="font-medium">American Express (AMEX) Transactions</div>
              </div>
              <div className="grid gap-4 sm:grid-cols-3">
                <Card className="bg-primary/5">
                  <CardHeader className="pb-2">
                    <div className="text-sm text-muted-foreground">Average Amount</div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {' '}
                      {formData.transactionInfo?.amexAvgTransactionAmount}
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-primary/5">
                  <CardHeader className="pb-2">
                    <div className="text-sm text-muted-foreground">Highest Amount</div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {' '}
                      {formData.transactionInfo?.amexHighestTransactionAmount}
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-primary/5">
                  <CardHeader className="pb-2">
                    <div className="text-sm text-muted-foreground">Monthly Sales</div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {' '}
                      {formData.transactionInfo?.amexGrossMonthlySalesVolume}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Owner Info Section */}
        <Card className="transition-all hover:shadow-md">
          {formData.owners?.map((owner, index) => (
            <React.Fragment key={`owner-${index}`}>
              <CardHeader>
                <div className="flex items-center space-x-2">
                  <User className="text-primary h-5 w-5" />
                  <CardTitle>
                    {owner.isControlOwner && index === 0
                      ? 'Control prong'
                      : `Owner ${index + 1} Information`}
                  </CardTitle>
                </div>
                <CardDescription>Primary business owner details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="grid gap-6 sm:grid-cols-3">
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">First Name</div>
                    <div className="font-medium"> {owner.firstName ?? '-'}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">Last Name</div>
                    <div className="font-medium"> {owner.lastName ?? '-'}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Title / Position
                    </div>
                    <div className="font-medium"> {owner.title ?? '-'}</div>
                  </div>
                </div>

                <div className="grid gap-6 sm:grid-cols-3">
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">Ownership %</div>
                    <Badge variant="outline" className="font-mono">
                      {owner.ownershipPercentage ?? '-'}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">SSN</div>
                    <div className="font-mono font-medium"> {owner.ssn ?? '-'}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">Phone Number</div>
                    <div className="font-medium"> {owner.phoneNumber ?? '-'}</div>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">Email</div>
                  <div className="font-medium"> {owner.email ?? '-'}</div>
                </div>

                <Separator />

                <div>
                  <div className="mb-4 flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div className="text-sm font-medium text-muted-foreground">Address</div>
                  </div>
                  <div className="grid gap-4 sm:grid-cols-4">
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Street</div>
                      <div className="font-medium"> {owner.homeAddress ?? '-'}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">City</div>
                      <div className="font-medium"> {owner.city ?? '-'}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">State</div>
                      <div className="font-medium"> {owner.state ?? '-'}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Zip Code</div>
                      <div className="font-medium"> {owner.zipCode ?? '-'}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </React.Fragment>
          ))}
        </Card>

        {/* Bank Info Section */}
        <Card className="transition-all hover:shadow-md">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Bank className="text-primary h-5 w-5" />
              <CardTitle>Banking Information</CardTitle>
            </div>
            <CardDescription>Business banking and account details</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">Bank Name</div>
              <div className="font-medium">{formData.bankInfo?.bankName ?? '-'}</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">Name on Account</div>
              <div className="font-medium">{formData.bankInfo?.nameOnAccount ?? '-'}</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">Routing Number</div>
              <div className="font-mono font-medium">{formData.bankInfo?.routingNumber ?? '-'}</div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">Account Number</div>
              <div className="font-mono font-medium">{formData.bankInfo?.accountNumber ?? '-'}</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // return (
  //   <div className={cn(styles.review, 'pb-8')}>
  //     <div className={styles.inputWidgetLg}>
  //       <div className={styles.frameParent}>
  //         <div className={styles.labelWrapper}>
  //           <div className={styles.label}>Business Info</div>
  //         </div>
  //         <div className={styles.inputFieldWrapper}>
  //           <div className={styles.inputField}>
  //             <div className={styles.label}>Legal Business name:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.legalBusinessName}</div>
  //           </div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Type of Business:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.typeOfBusiness}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>DBA Name:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.dbaName}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>EIN:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.ein}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Date Business Established:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.dateBusinessEstablished}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Business Email:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.businessEmail}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Business Phone:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.businessPhone}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Website:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.website}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Customer Service Phone:</div>
  //           <div className={styles.label2}>{formData.businessInfo?.customerServicePhone}</div>
  //         </div>
  //         <div className={styles.labelWrapper}>
  //           <div className={styles.label}>Location Address:</div>
  //         </div>
  //         <div className={styles.inputFieldWrapper}>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Street:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.street}</div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>City:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.city}</div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>State:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.state}</div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Zip Code:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.zipCode}</div>
  //           </div>
  //         </div>
  //         <div className={styles.labelWrapper}>
  //           <div className={styles.label}>Legal Mailing Address:</div>
  //         </div>
  //         <div className={styles.inputFieldWrapper}>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Street:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.legalMailingStreet}</div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>City:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.legalMailingCity}</div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>State:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.state}</div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Zip Code:</div>
  //             <div className={styles.label2}>{formData.businessInfo?.legalMailingZipCode}</div>
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //     <div className={styles.inputWidgetLg}>
  //       <div className={styles.frameParent}>
  //         <div className={styles.labelWrapper}>
  //           <div className={styles.label}>Transactions</div>
  //         </div>
  //         <div className={styles.inputFieldWrapper}>
  //           <div className={styles.inputField}>
  //             <div className={styles.label}>Business Category:</div>
  //             <div className={styles.label2}>{formData.transactionInfo?.businessCategory}</div>
  //           </div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Description:</div>
  //           <div className={styles.label2}>{formData.transactionInfo?.description}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Swiped:</div>
  //           <div className={styles.label2}>{formData.transactionInfo?.swipe}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Keyed:</div>
  //           <div className={styles.label2}>{formData.transactionInfo?.keyed}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Ecommerce:</div>
  //           <div className={styles.label2}>{formData.transactionInfo?.ecommerce}</div>
  //         </div>
  //         <div className={styles.labelWrapper}>
  //           <div className={styles.label}>Visa / Mastercard / Discover Transactions:</div>
  //         </div>
  //         <div className={styles.inputFieldWrapper}>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Average Amount:</div>
  //             <div className={styles.label2}>
  //               {formData.transactionInfo?.grossMonthlySalesVolume}
  //             </div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Highest Amount:</div>
  //             <div className={styles.label2}>
  //               {formData.transactionInfo?.highestTransactionAmount}
  //             </div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Gross Monthly Sales Amount</div>
  //             <div className={styles.label2}>
  //               {formData.transactionInfo?.grossMonthlySalesVolume}
  //             </div>
  //           </div>
  //         </div>
  //         <div className={styles.labelWrapper}>
  //           <div className={styles.label}>American Express (AMEX) Transactions:</div>
  //         </div>
  //         <div className={styles.inputFieldWrapper}>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Average Amount:</div>
  //             <div className={styles.label2}>
  //               {formData.transactionInfo?.amexAvgTransactionAmount}
  //             </div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Highest Amount:</div>
  //             <div className={styles.label2}>
  //               {formData.transactionInfo?.amexHighestTransactionAmount}
  //             </div>
  //           </div>
  //           <div className={styles.inputField9}>
  //             <div className={styles.label}>Gross Monthly Sales Amount</div>
  //             <div className={styles.label2}>
  //               {formData.transactionInfo?.amexGrossMonthlySalesVolume}
  //             </div>
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //     {formData?.owners?.map((owner, index) => (
  //       <div className={styles.inputWidgetLg} key={`owner-${index}`}>
  //         <div className={styles.frameParent}>
  //           <div className={styles.labelWrapper}>
  //             <div className={styles.label}>
  //               {owner.isControlOwner && index === 0 ? 'Control prong' : `Owner ${index + 1}`}
  //             </div>
  //           </div>
  //           <div className={styles.inputFieldWrapper}>
  //             <div className={styles.inputField}>
  //               <div className={styles.label}>First Name:</div>
  //               <div className={styles.label2}>{owner?.firstName}</div>
  //             </div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>Last Name:</div>
  //             <div className={styles.label2}>{owner?.lastName}</div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>Title / Position:</div>
  //             <div className={styles.label2}>{owner?.title}</div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>Ownership %:</div>
  //             <div className={styles.label2}>{owner?.ownershipPercentage}</div>
  //           </div>
  //           <div className={styles.inputField32}>
  //             <div className={styles.label}>SSN:</div>
  //             <div className={styles.label2}>{owner?.ssn}</div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>Phone Number:</div>
  //             <div className={styles.label2}>{owner?.phoneNumber}</div>
  //           </div>
  //           <div className={styles.inputField32}>
  //             <div className={styles.label}>Email:</div>
  //             <div className={styles.label2}>{owner?.email}</div>
  //           </div>
  //           <div className={styles.inputField35}>
  //             <div className={styles.label}>Street:</div>
  //             <div className={styles.label2}>{owner?.homeAddress}</div>
  //           </div>
  //           <div className={styles.inputField36}>
  //             <div className={styles.label}>City:</div>
  //             <div className={styles.label2}>{owner?.city}</div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>State:</div>
  //             <div className={styles.label2}>{owner?.state}</div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>Zip Code:</div>
  //             <div className={styles.label2}>{owner?.zipCode}</div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>Country:</div>
  //             <div className={styles.label2}>{owner?.country}</div>
  //           </div>
  //           <div className={styles.inputField1}>
  //             <div className={styles.label}>Date of Birth:</div>
  //             <div className={styles.label2}>{owner?.dateOfBirth}</div>
  //           </div>
  //         </div>
  //       </div>
  //     ))}
  //     <div className={styles.inputWidgetLg}>
  //       <div className={styles.frameParent}>
  //         <div className={styles.labelWrapper}>
  //           <div className={styles.label}>Bank Info</div>
  //         </div>
  //         <div className={styles.inputFieldWrapper}>
  //           <div className={styles.inputField}>
  //             <div className={styles.label}>Bank Name:</div>
  //             <div className={styles.label2}>{formData.bankInfo?.bankName}</div>
  //           </div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Name on Account:</div>
  //           <div className={styles.label2}>{formData.bankInfo?.nameOnAccount}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Routing Number:</div>
  //           <div className={styles.label2}>{formData.bankInfo?.routingNumber}</div>
  //         </div>
  //         <div className={styles.inputField1}>
  //           <div className={styles.label}>Account Number:</div>
  //           <div className={styles.label2}>{formData.bankInfo?.accountNumber}</div>
  //         </div>
  //       </div>
  //     </div>
  //   </div>
  // );
};

export default ReviewPage;
