'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useEffect, useState } from 'react';
import { useQuery } from '@apollo/client';
import { GetQuickUserInfo } from '@/graphql/declarations/user-members';

type UserDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  userId?: string;
  inviteId?: string;
  groupId?: string;
};

export const UserDetailsModal = ({
  isOpen,
  onClose,
  userId,
  inviteId,
  groupId,
}: UserDetailsModalProps) => {
  console.log('UserDetailsModal - Render:', { isOpen, userId, inviteId, groupId });

  const isInvite = userId === 'invite';
  const isNewEntry = isInvite && !inviteId;

  const [selectedGroup, setSelectedGroup] = useState<string>(groupId || '');
  const [userFlags, setUserFlags] = useState<string[]>([]);
  const [adminInfo, setAdminInfo] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    title: '',
    role: 'agent' as 'support' | 'agent' | 'admin',
  });

  // Fetch user data
  const { data: userData } = useQuery(GetQuickUserInfo, {
    variables: {
      where: {
        id: userId,
      },
    },
    skip: !userId || isInvite,
  });

  // Update form data when user data changes
  useEffect(() => {
    console.log('UserDetailsModal - User data changed:', userData);
    if (userData?.user) {
      // Map GraphQL role to our local role types
      let mappedRole: 'support' | 'agent' | 'admin' = 'agent';
      if (userData.user.title) {
        const title = userData.user.title.toLowerCase();
        if (title === 'admin') mappedRole = 'admin';
        else if (title === 'support') mappedRole = 'support';
        else mappedRole = 'agent';
      }

      setFormData({
        firstName: userData.user.name || '',
        lastName: userData.user.lastName || '',
        email: userData.user.email || '',
        title: userData.user.title || '',
        role: mappedRole,
      });
    }
  }, [userData]);

  const handleClose = () => {
    console.log('UserDetailsModal - Closing modal');
    if (onClose) onClose();
  };

  const handleSave = () => {
    console.log('UserDetailsModal - Saving:', formData);
    // TODO: Implement save logic
    handleClose();
  };

  console.log('UserDetailsModal - Current form data:', formData);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        console.log('Dialog open change:', open);
        if (!open) handleClose();
      }}
    >
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{userId === 'new' ? 'Add New User' : 'Edit User'}</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="firstName">First Name</Label>
              <input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData((prev) => ({ ...prev, firstName: e.target.value }))}
                className="w-full rounded-md border p-2"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="lastName">Last Name</Label>
              <input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData((prev) => ({ ...prev, lastName: e.target.value }))}
                className="w-full rounded-md border p-2"
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
              className="w-full rounded-md border p-2"
              disabled={isInvite && !isNewEntry}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="title">Title</Label>
            <input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              className="w-full rounded-md border p-2"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="role">Role</Label>
            <select
              id="role"
              value={formData.role}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  role: e.target.value as 'support' | 'agent' | 'admin',
                }))
              }
              className="w-full rounded-md border p-2"
            >
              <option value="support">Support</option>
              <option value="agent">Agent</option>
              <option value="admin">Admin</option>
            </select>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
