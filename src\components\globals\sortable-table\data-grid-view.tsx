import { useState, useMemo } from 'react';
import { Table, Button, Select } from 'flowbite-react';
import { HiChe<PERSON>ronLeft, HiChevronRight } from 'react-icons/hi';
import { cn } from '@/lib/utils';
import { SpinnerLoading } from '../spinner-loading/spinner-loading';

export type Column<TObj = Record<string, any>> = {
  /* Key is a property from  the  API return ie: user.fullName if  your targeting fullName property under user */
  key: string;
  /* Header that will display in  UI */
  header: string;
  /* Width of the column */
  width?: string;
  /* Tell if the column is editable. Not sure yet how it's  ganna be but this will till*/
  editable?: boolean;
  /* Tell if column is sortable */
  sortable?: boolean;
  /* Tell of value is number of text,, OPTIONAL */
  type?: 'number' | 'text';
  /* Tell how you want to display th column ei.*/
  valueGetter?: (row: TObj) => any;
  /* renderCell - is  where you can throw a JSX element  like buttons  or link */
  renderCell?: (row: TObj) => JSX.Element;
  /* click event call back for the whole row, NOTED should be define in only 1 column */
  onClick?: (row: TObj) => any;
  onServerSort?: (key: string) => void;
  isHidden?: boolean;
};

type DataGridViewProps<T = any> = {
  /* Defination of the Column */
  columns: Column<T>[];
  /* Row values, can be a list from API and mapped data */
  rows: T[];
  /* How many rows in per page */
  pageSize?: number;
  /* Total records */
  totalRecords?: number;
  /* Component that will display above the table */
  actionComponent?: JSX.Element;
  /* Tell if table is waiting for data */
  isLoading?: boolean;
  /* Tell if table is  getting more data */
  isRefeching?: boolean;
  /* Current page number */
  currentPage?: number;
  /* List of classnames */

  className?: string;
  /* Tell if tables action should be hidden */
  disablePagination?: boolean;
  /* mode of pagination  */
  /* mode = server best work  with useDataGidView hook */
  /* mode = client must  needs two state at page level to manipulate pageSize and currentPage  */
  // const [pageSize, setPageSize] = useState(10);
  // const [currentPage, setCurrentPage] = useState(1);
  mode?: 'client' | 'server';
  /* Function to handle page change */
  onPageChange?: (page: number) => void;
  /* Function to handle page size change */
  onPageSizeChange?: (pageSize: number) => void;
  onRowClick?: (row: T) => void; // Add this prop for row clicks
};

const headerSortIcon = () => {
  return (
    <svg
      className="-mt-0.5 ml-1 inline-block h-4 w-4"
      fill="currentColor"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        clipRule="evenodd"
        fillRule="evenodd"
        d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z"
      />
    </svg>
  );
};

const DataGridView = <T extends Record<string, any>>({
  columns: renderColumns,
  rows,
  pageSize = 10,
  totalRecords = 0,
  currentPage = 1,
  actionComponent,
  isLoading,
  mode = 'client',
  className,
  disablePagination = false,
  onPageChange,
  onPageSizeChange,
  onRowClick,
}: DataGridViewProps<T>) => {
  const [sortedColumn, setSortedColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (key: string) => {
    if (sortedColumn === key) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortedColumn(key);
      setSortDirection('asc');
    }
  };

  const columns = useMemo(() => renderColumns.filter((col) => !col.isHidden), [renderColumns]);

  const sortedRows = useMemo(() => {
    if (!sortedColumn) return rows;
    return [...rows].sort((a, b) => {
      const aValue = columns.find((col) => col.key === sortedColumn)?.valueGetter
        ? columns.find((col) => col.key === sortedColumn)?.valueGetter?.(a)
        : a[sortedColumn];
      const bValue = columns.find((col) => col.key === sortedColumn)?.valueGetter
        ? columns.find((col) => col.key === sortedColumn)?.valueGetter?.(b)
        : b[sortedColumn];

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [rows, sortedColumn, sortDirection, columns]);

  const paginatedRows = useMemo(() => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return mode === 'server' ? rows : sortedRows?.slice(start, end);
  }, [sortedRows, currentPage, pageSize, mode]);

  const totalPages = Math.ceil(totalRecords / pageSize);

  const renderPageNumbers = () => {
    const pageNumbers: JSX.Element[] = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(
        <Button
          key={i}
          color={i === currentPage ? 'blue' : 'gray'}
          onClick={() => onPageChange && onPageChange(i)}
          className={`m-0 rounded-none px-2 py-1 ${i === currentPage ? 'bg-blue-300 transition hover:text-gray-300' : 'duration-1 bg-white text-gray-700 transition hover:text-gray-900'}`}
        >
          {i}
        </Button>,
      );
    }

    return pageNumbers;
  };

  return (
    <div className={cn('relative w-full pt-2', className)}>
      <div className="flex justify-between">
        {actionComponent ? actionComponent : <div />}
        {!disablePagination && (
          <div className="flex items-center">
            <span className="mr-2">Rows per page:</span>
            <Select
              id="pageSize"
              value={pageSize.toString()}
              onChange={(e) => {
                onPageChange && onPageChange(1);
                onPageSizeChange && onPageSizeChange(Number(e.target.value));
              }}
            >
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </Select>
          </div>
        )}
      </div>
      <div className="relative max-w-screen-xl overflow-auto">
        <SpinnerLoading isLoading={isLoading} />

        <Table className="w-full">
          <Table.Head>
            {columns.map(({ key, header, sortable, width, onServerSort }) => (
              <Table.HeadCell
                key={key}
                className="min-w-max cursor-pointer whitespace-nowrap px-2"
                onClick={() => {
                  if (sortable && mode === 'server' && onServerSort) {
                    onServerSort(key);
                  } else if (sortable && mode === 'client') {
                    handleSort(key);
                  }
                }}
              >
                {header}
                {sortable && <span>{headerSortIcon()}</span>}
              </Table.HeadCell>
            ))}
          </Table.Head>
          <Table.Body>
            {paginatedRows?.length === 0 ? (
              <Table.Row>
                <Table.Cell colSpan={renderColumns.length} className="h-[100px] text-center">
                  No data
                </Table.Cell>
              </Table.Row>
            ) : (
              paginatedRows?.map((row, rowIndex) => (
                <Table.Row
                  key={rowIndex}
                  className="cursor-pointer border-b transition-colors hover:bg-gray-100"
                  onClick={() => {
                    console.log('DataGridView - Row clicked:', { row, rowIndex });
                    if (onRowClick) {
                      onRowClick(row);
                    }
                  }}
                >
                  {renderColumns
                    .filter((column) => !column.isHidden)
                    .map((column, columnIndex) => (
                      <Table.Cell key={columnIndex} className="whitespace-nowrap px-4 py-3">
                        {column.renderCell
                          ? column.renderCell(row)
                          : column.valueGetter
                            ? column.valueGetter(row)
                            : row[column.key]}
                      </Table.Cell>
                    ))}
                </Table.Row>
              ))
            )}
          </Table.Body>
        </Table>
      </div>
      {!disablePagination && (
        <div className="mt-4 flex items-center justify-between">
          <div>
            Showing {Math.min((currentPage - 1) * pageSize + 1, totalRecords)}-
            {Math.min(currentPage * pageSize, totalRecords)} of {totalRecords}
          </div>
          <div className="flex items-center">
            <Button
              color="gray"
              onClick={() => onPageChange && onPageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="m-0 rounded-none px-2 py-1"
            >
              <HiChevronLeft className="h-5 w-5" />
            </Button>
            {renderPageNumbers()}
            <Button
              color="gray"
              onClick={() => onPageChange && onPageChange(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="m-0 rounded-none px-2 py-1"
            >
              <HiChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataGridView;
