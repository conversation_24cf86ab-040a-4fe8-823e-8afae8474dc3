import { Calendar, Clock, CheckCircle2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { mockAccountData } from '@/mock/account-status-data';
import { BaseAccountModal } from './BaseAccountModal';
import { InfoItem } from './InfoItem';

interface SubmittedStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SubmittedStatusModal = ({ isOpen, onClose }: SubmittedStatusModalProps) => {
  const data = mockAccountData.submitted;

  return (
    <BaseAccountModal
      isOpen={isOpen}
      onClose={onClose}
      title="Submitted"
      accountData={data}
      statusIcon={Clock}
      statusColor="text-yellow-500"
    >
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Submission Details</h3>
        <div className="space-y-4">
          <div className="mb-4 flex items-center gap-2">
            <Clock className="h-5 w-5 text-yellow-500" />
            <p className="text-yellow-700">
              Your application has been submitted and is pending review.
            </p>
          </div>
          <div className="grid gap-4">
            <InfoItem
              icon={Calendar}
              label="Submission Date"
              value={data.submissionDetails.submissionDate}
            />
            <InfoItem
              icon={Calendar}
              label="Review Start Date"
              value={data.submissionDetails.reviewStartDate}
            />
            <InfoItem
              icon={Calendar}
              label="Estimated Completion"
              value={data.submissionDetails.estimatedCompletionDate}
            />
          </div>
          <div>
            <h4 className="mb-2 font-medium">Documents Submitted</h4>
            <ul className="list-inside list-disc space-y-1">
              {data.submissionDetails.documentsSubmitted.map((doc, index) => (
                <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  {doc}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </Card>
    </BaseAccountModal>
  );
};
