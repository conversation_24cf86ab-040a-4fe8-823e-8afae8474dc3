import { Button } from '@/components/ui/button';
import { Affiliation_initiate } from '@/graphql/declarations/affiliate';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useState } from 'react';
import { Me } from '@/graphql/declarations/me';

export default function AffiliateSetupPage() {
  const [loading, setLoading] = useState(false);

  const handleGetStarted = async () => {
    setLoading(true);
    try {
      const d = await apolloClient.mutate({
        mutation: Affiliation_initiate,
        variables: {},
      });

      if (d.data?.affiliation_initiate?.id) {
        // Reset Apollo cache to force refetch of Me query
        await apolloClient.resetStore();

        // Explicitly refetch Me query to update flags
        await apolloClient.query({
          query: Me,
          fetchPolicy: 'network-only',
        });

        window.location.reload();
      }
    } catch (error) {
      console.error('Error initiating affiliate account:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex h-[90vh] flex-col items-center justify-center gap-2">
      <h1 className="text-3xl font-bold">Welcome to the Affiliate Program</h1>
      <p className="text-lg">Join our affiliate program and start earning today!</p>
      <br />
      <Button variant={'primary'} onClick={handleGetStarted} disabled={loading}>
        {loading ? 'Processing...' : 'Get Started'}
      </Button>
      <p>By clicking "Get Started", you agree to our terms and conditions.</p>
    </div>
  );
}
