'use client';

import LoadingButton from '@/components/globals/loading-button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SYNC_STRIPE_INTEGRATION } from '@/graphql/declarations/integrations';
import { StripeDataApplications } from '@/graphql/declarations/stripeAuth';
import { useMutation, useQuery } from '@apollo/client';
import { AlertCircle, RefreshCcw, RotateCw } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';

interface StripeSyncManagerProps {
  groupId: string;
}

export default function StripeSyncManager({ groupId }: StripeSyncManagerProps) {
  const [syncingType, setSyncingType] = useState<'payment' | 'product' | null>(null);

  const { data: stripeDataApplications, loading: loadingStripeData } =
    useQuery(StripeDataApplications);
  const [syncStripeIntegration, { loading: syncing }] = useMutation(SYNC_STRIPE_INTEGRATION);

  const keysConfigured = !!stripeDataApplications?.stripeDataApplications?.length;

  const handleSync = async ({ type }: { type: 'payment' | 'product' }) => {
    if (!keysConfigured) {
      toast.error('Please configure your Stripe API keys first');
      return;
    }

    const syncResult = await syncStripeIntegration({
      variables: {
        input: {
          groupId,
          type,
        },
      },
    });

    if (syncResult.errors) {
      console.error(`Failed to sync stripe integration`, syncResult.errors);
      toast.error('Failed to sync stripe integration');
      return;
    }

    toast.success('Stripe integration synced successfully');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <RotateCw className="text-primary h-6 w-6" />
          Stripe Sync
        </CardTitle>
        <CardDescription>Sync your Stripe payments and products</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {loadingStripeData ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>Loading Stripe configuration...</AlertDescription>
            </Alert>
          ) : (
            !keysConfigured && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  You need to configure your Stripe API keys before syncing data.
                </AlertDescription>
              </Alert>
            )
          )}

          <Alert variant="default" className="bg-[#F4F4F5]">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Sync your Stripe payments and products to keep your data up to date.
            </AlertDescription>
          </Alert>

          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div>
                <h3 className="font-medium">Payments & Transactions</h3>
                <p className="text-sm text-slate-600">
                  Sync all Stripe payments, transactions, and customer data
                </p>
              </div>
              <LoadingButton
                type="button"
                disabled={
                  (syncingType === 'payment' && syncing) || !keysConfigured || loadingStripeData
                }
                isLoading={syncingType === 'payment' && syncing}
                icon={<RefreshCcw size={14} />}
                loadingText="Syncing"
                className="text-slate-800"
                onClick={() => {
                  setSyncingType('payment');
                  handleSync({ type: 'payment' });
                }}
              >
                Sync Payments
              </LoadingButton>
            </div>

            <div className="flex items-center justify-between rounded-lg border p-4">
              <div>
                <h3 className="font-medium">Products & Pricing</h3>
                <p className="text-sm text-slate-600">
                  Sync all Stripe products, prices, and subscription plans
                </p>
              </div>
              <LoadingButton
                type="button"
                disabled={
                  (syncingType === 'product' && syncing) || !keysConfigured || loadingStripeData
                }
                isLoading={syncingType === 'product' && syncing}
                icon={<RefreshCcw size={14} />}
                loadingText="Syncing"
                className="text-slate-800"
                onClick={() => {
                  setSyncingType('product');
                  handleSync({ type: 'product' });
                }}
              >
                Sync Products
              </LoadingButton>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
