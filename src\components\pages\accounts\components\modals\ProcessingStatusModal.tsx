import { Clock, CheckCircle2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { mockAccountData } from '@/mock/account-status-data';
import { BaseAccountModal } from './BaseAccountModal';

interface ProcessingStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProcessingStatusModal = ({ isOpen, onClose }: ProcessingStatusModalProps) => {
  const data = mockAccountData.processing;

  return (
    <BaseAccountModal
      isOpen={isOpen}
      onClose={onClose}
      title="Processing"
      accountData={data}
      statusIcon={Clock}
      statusColor="text-blue-500"
    >
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Application Status</h3>
        <div className="space-y-4">
          <div className="mb-4 flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-500" />
            <p className="text-blue-700">
              Your application is being processed. Estimated completion time:{' '}
              {data.applicationProgress.estimatedTime}
            </p>
          </div>
          <div className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium">Current Step</h4>
              <p className="text-sm text-blue-600">{data.applicationProgress.currentStep}</p>
            </div>
            <div>
              <h4 className="mb-2 font-medium">Completed Steps</h4>
              <ul className="list-inside list-disc space-y-1">
                {data.applicationProgress.completedSteps.map((step, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-green-600">
                    <CheckCircle2 className="h-4 w-4" />
                    {step}
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="mb-2 font-medium">Pending Steps</h4>
              <ul className="list-inside list-disc space-y-1">
                {data.applicationProgress.pendingSteps.map((step, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    {step}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </Card>
    </BaseAccountModal>
  );
};
