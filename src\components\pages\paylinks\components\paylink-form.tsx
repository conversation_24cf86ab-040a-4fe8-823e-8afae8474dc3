import { useFormContext, use<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, useWatch } from 'react-hook-form';
import { FormInput } from '@/components/globals/form-input';
import { <PERSON><PERSON>, Card, Checkbox, HelperText, Label, Select } from 'flowbite-react';
import { useState } from 'react';
import { ModalAddProductLink } from './modal-product-link';
import { FaPlus, FaChevronDown, FaChevronRight } from 'react-icons/fa6';
import { moneyFormat } from '@/lib/utils';
import { QuickProductAddModal } from './quick-product-add-modal';

export type PayLinkForm = {
  allowExtraDiscount: boolean;
  allowTip: boolean;
  allowEdit: boolean;
  referenceID?: string;
  onSuccessURL?: string;
  onFailureURL?: string;
  customerID?: string;
  customerPaymentID?: string;
  prefilledAddress?: {
    email?: string;
    phone?: string;
    country?: string;
    state?: string;
    city?: string;
    zip?: string;
    address?: string;
    nameOnCard?: string;
  };
  disableCustomAddress?: boolean;
  disableCustomPayment?: boolean;
  disablePreselectCard?: boolean;
  disableACH?: boolean;
  disableCC?: boolean;
  validUntil?: string;
  pricing: {
    taxType: 'fixed' | 'percentage';
    tax: number;
    lineItems: [
      {
        productId: string;
        amount: number;
        name: string;
        price: number;
      },
    ];
  };
};

const DisplayLineItems = ({ fields, remove, control }) => {
  const watch = useWatch({
    control,
    name: 'pricing.lineItems',
    defaultValue: [],
  });

  if (fields.length === 0)
    return (
      <HelperText color="info" className="bg-gray-200 py-2 text-center">
        Click "Add Product" to search a product
      </HelperText>
    );

  return (
    <div className="space-y-4">
      <Label>Product Items</Label>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
            >
              Product
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
            >
              Price
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
            >
              Amount
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
            >
              Total
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white">
          {fields.map((item, index) => (
            <tr key={item.id}>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                <p className="text-base font-semibold">{item.name}</p>
                <p className="text-xs">{item.productId}</p>
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                {moneyFormat(item.price)}
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                <Controller
                  name={`pricing.lineItems.${index}.amount`}
                  control={control}
                  render={({ field }) => (
                    <input
                      type="number"
                      min="1"
                      {...field}
                      className="w-20 rounded-lg border border-gray-300 p-1 text-sm"
                    />
                  )}
                />
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                {moneyFormat(Number((item.price * watch[index]?.amount || 0).toFixed(2)))}
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-left text-sm font-medium">
                <button
                  type="button"
                  className="text-red-500 hover:underline"
                  onClick={() => remove(index)}
                >
                  Remove
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export const PaylinkForm = (args?: { addProductModal?: any }) => {
  const { control } = useFormContext<PayLinkForm>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'pricing.lineItems',
  });

  const AddProductModal = args?.addProductModal || ModalAddProductLink;

  const [isProductModalOpen, setProductModalOpen] = useState(false);
  const [isDiscountModalOpen, setDiscountModalOpen] = useState(false);
  const [showQuickAdd, setShowQuickAdd] = useState(false);
  const [showAddressFields, setShowAddressFields] = useState(false);
  const [showAdditionalSettings, setShowAdditionalSettings] = useState(false);

  const handleAddProductId = (productId: string, amount: number, name: string, price: number) => {
    append({ productId, amount, name, price });
    setProductModalOpen(false);
  };

  const handleQuickAddSuccess = (productId: string, name: string, price: number) => {
    append({ productId, amount: 0, name, price });
    setShowQuickAdd(false);
  };

  return (
    <>
      <DisplayLineItems fields={fields} remove={remove} control={control} />
      <div className="flex justify-end space-x-2">
        <Button color="light" onClick={() => setProductModalOpen(true)}>
          <FaPlus className="mr-1.5 text-xl" />
          Add From Catalog
        </Button>
        <Button color="light" onClick={() => setShowQuickAdd(true)}>
          <FaPlus className="mr-1.5 text-xl" />
          Add New Product
        </Button>
      </div>
      <Label className="flex items-center justify-end space-x-2">
        <Controller
          name="allowEdit"
          control={control}
          render={({ field }) => (
            <Checkbox {...field} checked={field.value} value={field.value ? 'true' : 'false'} />
          )}
        />
        <span>Allow Customer to Change Amount</span>
      </Label>

      <Card>
        <div className="grid grid-cols-2 gap-4">
          <div className="pt-4">
            <Label htmlFor="pricing.taxType">Tax Type</Label>
            <Controller
              name="pricing.taxType"
              control={control}
              render={({ field }) => (
                <Select {...field} className="mt-1.5">
                  <option value="fixed">Fixed</option>
                  <option value="percentage">Percentage</option>
                </Select>
              )}
            />
          </div>
          <FormInput
            className="mt-4"
            id="pricing.tax"
            name="pricing.tax"
            label="Tax Amount"
            type="number"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Label className="flex items-center space-x-2">
            <Controller
              name="allowExtraDiscount"
              control={control}
              render={({ field }) => (
                <Checkbox {...field} checked={field.value} value={field.value ? 'true' : 'false'} />
              )}
            />
            <span>Allow Extra Discount</span>
          </Label>

          <Label className="flex items-center space-x-2">
            <Controller
              name="allowTip"
              control={control}
              render={({ field }) => (
                <Checkbox {...field} checked={field.value} value={field.value ? 'true' : 'false'} />
              )}
            />
            <span>Allow Tip</span>
          </Label>
        </div>
      </Card>

      <Card>
        <div
          className="flex cursor-pointer items-center justify-between"
          onClick={() => setShowAdditionalSettings(!showAdditionalSettings)}
        >
          <h3 className="text-lg font-semibold">Additional Settings</h3>
          {showAdditionalSettings ? <FaChevronDown /> : <FaChevronRight />}
        </div>

        {showAdditionalSettings && (
          <>
            <div className="grid grid-cols-1 gap-4">
              <Label className="flex items-center space-x-2">
                <Controller
                  name="disableCustomAddress"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      {...field}
                      checked={field.value}
                      value={field.value ? 'true' : 'false'}
                    />
                  )}
                />
                <span>Disable Custom Address</span>
              </Label>
              {/* <Label className="flex items-center space-x-2">
                <Controller
                  name="disablePreselectCard"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      {...field}
                      checked={field.value}
                      value={field.value ? 'true' : 'false'}
                    />
                  )}
                />
                <span>Disable Preselect Card</span>
              </Label>
              <Label className="flex items-center space-x-2">
                <Controller
                  name="disableCustomPayment"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      {...field}
                      checked={field.value}
                      value={field.value ? 'true' : 'false'}
                    />
                  )}
                />
                <span>Disable Custom Payment</span>
              </Label> */}
              <Label className="flex items-center space-x-2">
                <Controller
                  name="disableACH"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      {...field}
                      checked={field.value}
                      value={field.value ? 'true' : 'false'}
                    />
                  )}
                />
                <span>Disable ACH</span>
              </Label>
              <Label className="flex items-center space-x-2">
                <Controller
                  name="disableCC"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      {...field}
                      checked={field.value}
                      value={field.value ? 'true' : 'false'}
                    />
                  )}
                />
                <span>Disable Credit Card</span>
              </Label>
            </div>

            <div className="mt-4 grid grid-cols-1 gap-4">
              <FormInput
                id="validUntil"
                name="validUntil"
                label="Valid Until"
                type="datetime-local"
                placeholder="Select expiration date and time"
              />
            </div>

            <div className="">
              <Label className="flex items-center space-x-2">
                <Checkbox
                  checked={showAddressFields}
                  onChange={() => setShowAddressFields(!showAddressFields)}
                />
                <span>Enable Address Prefill</span>
              </Label>
            </div>
            {showAddressFields && (
              <>
                <h3 className="text-lg font-semibold">Address Prefill</h3>
                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    id="prefilledAddress.email"
                    name="prefilledAddress.email"
                    label="Email"
                    placeholder="<EMAIL>"
                  />
                  <FormInput
                    id="prefilledAddress.phone"
                    name="prefilledAddress.phone"
                    label="Phone"
                    placeholder="+15551234567"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    id="prefilledAddress.nameOnCard"
                    name="prefilledAddress.nameOnCard"
                    label="Name on Card"
                    placeholder="John Doe"
                  />
                  <FormInput
                    id="prefilledAddress.address"
                    name="prefilledAddress.address"
                    label="Address"
                    placeholder="123 Market St"
                  />
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <FormInput
                    id="prefilledAddress.city"
                    name="prefilledAddress.city"
                    label="City"
                    placeholder="San Francisco"
                  />
                  <FormInput
                    id="prefilledAddress.state"
                    name="prefilledAddress.state"
                    label="State"
                    placeholder="CA"
                  />
                  <FormInput
                    id="prefilledAddress.zip"
                    name="prefilledAddress.zip"
                    label="Zip Code"
                    placeholder="94105"
                  />
                </div>
                <FormInput
                  className=""
                  id="prefilledAddress.country"
                  name="prefilledAddress.country"
                  label="Country"
                  placeholder="US"
                />
              </>
            )}

            <div className="grid grid-cols-2 gap-4">
              <FormInput
                id="onSuccessURL"
                name="onSuccessURL"
                label="Success URL"
                placeholder="https://example.com/success"
              />
              <FormInput
                id="onFailureURL"
                name="onFailureURL"
                label="Failure URL"
                placeholder="https://example.com/failure"
              />
            </div>
          </>
        )}
      </Card>

      <AddProductModal
        isOpen={isProductModalOpen}
        onClose={() => setProductModalOpen(false)}
        onAddProduct={handleAddProductId}
      />

      <QuickProductAddModal
        isOpen={showQuickAdd}
        onClose={() => setShowQuickAdd(false)}
        onSuccess={handleQuickAddSuccess}
      />
    </>
  );
};
