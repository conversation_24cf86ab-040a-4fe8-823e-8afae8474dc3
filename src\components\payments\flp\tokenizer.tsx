'use client';

import <PERSON>rip<PERSON> from 'next/script';
import { MutableRefObject } from 'react';

interface PaymentConfig {
  calculateFees?: boolean;
  showTitle?: boolean;
  placeholderCreditCard?: string;
  showExpDate?: boolean;
  showCVV?: boolean;
  types?: string[];
  ach?: {
    sec_code?: string;
    showSecCode?: boolean;
    verifyAccountRouting?: boolean;
    user_required?: {
      first_name?: boolean;
      last_name?: boolean;
      email?: boolean;
      phone?: boolean;
    };
    billing_required?: {
      address?: boolean;
      city?: boolean;
      state?: boolean;
      zip?: boolean;
      country?: boolean;
    };
  };
  card?: {
    strict_mode?: boolean;
    requireCVV?: boolean;
    user_required?: {
      first_name?: boolean;
      last_name?: boolean;
      email?: boolean;
      phone?: boolean;
    };
    billing_required?: {
      address?: boolean;
      city?: boolean;
      state?: boolean;
      zip?: boolean;
      country?: boolean;
    };
  };
}

interface UserConfig {
  showInline?: boolean;
  showName?: boolean;
  showEmail?: boolean;
  showPhone?: boolean;
  showTitle?: boolean;
}

interface ShippingConfig {
  show?: boolean;
  showTitle?: boolean;
}

interface BillingConfig {
  show?: boolean;
  showTitle?: boolean;
}

interface StylesConfig {
  [key: string]: Record<string, any>;
}

interface Config {
  payment?: PaymentConfig;
  user?: UserConfig;
  shipping?: ShippingConfig;
  billing?: BillingConfig;
  styles?: StylesConfig;
}

export const FLPayTokenizerComponentConfigSample: Config = {
  payment: {
    showTitle: true,
    placeholderCreditCard: '0000 0000 0000 0000',
    showExpDate: true,
    showCVV: true,
    types: ['card', 'ach'], // Default ['card']
    ach: {
      sec_code: 'web', // Default web - web, ccd, ppd, tel
      showSecCode: false, // Default false - true to show sec code dropdown
      verifyAccountRouting: true, // Default false - true to verify account routing numbers
    },
    card: {
      strict_mode: false, // Set to true to allow for 19 digit cards
      requireCVV: true, // Default false - true to require cvv
    },
  },
  styles: {
    body: {
      color: '#000',
      'background-color': '#ffffff40',
    },
  },
};

export default function FLPayTokenizerComponent(props: {
  containerSelector: string;
  apiKey: string;
  liveMode: boolean;
  onSubmission: (resp: any) => void;
  tokenizerRef: MutableRefObject<any>;
  tokenizerConfig: Config;
}) {
  const fpayURL = props.liveMode
    ? 'https://sandbox.fluidpay.com' // ? "https://app.fluidpay.com"
    : 'https://sandbox.fluidpay.com';
  return (
    <>
      <Script
        src={`${fpayURL}/tokenizer/tokenizer.js`}
        onLoad={() => {
          // @ts-ignore
          props.tokenizerRef.current = new Tokenizer({
            url: fpayURL,
            apikey: props.apiKey,
            container: props.containerSelector,
            submission: props.onSubmission,
            settings: props.tokenizerConfig,
          });
        }}
      ></Script>
    </>
  );
}
