import styles from './index.module.css';
import { cn } from '@/lib/utils';
import UpdateUserSettingsRightDra from '../user-settings-modal/index';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

const UserDropdown = (props: {
  name?: string;
  email?: string;
  onUserSettingsClick?: () => void;
  onSignOutClick?: () => void;
}) => {
  let userProfileModalID = 'userProfileModal';

  return (
    <>
      <div className={cn(styles.dropdownMenu, 'z-30')}>
        <div className={styles.navItem}>
          <div className={styles.jeseLeos}>{props.name}</div>
          <div className={styles.nameexamplecom}>{props.email}</div>
        </div>
        <div className={styles.separator} />
        <div className={styles.navItem1}>
          <div className={styles.dropdownmenuItem}>
            <Sheet>
              <SheetTrigger className={styles.nameexamplecom}>User Settings</SheetTrigger>
              <SheetContent className="z-[120] max-w-[50vw] rounded-xl">
                <UpdateUserSettingsRightDra />
              </SheetContent>
            </Sheet>
          </div>
        </div>
        <div className={styles.separator} />
        <div className={styles.navItem2}>
          <div className={styles.dropdownmenuItem}>
            <button className={styles.nameexamplecom} onClick={props.onSignOutClick}>
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default UserDropdown;
