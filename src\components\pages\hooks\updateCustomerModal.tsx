import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { CustomerViewModal } from '../customer/components/customer-view-modal';

export const useUpdateCustomerModalHook = (locationFilter, refetchCustomerListData) => {
  const queryParams = useSearchParams();
  const customerID = queryParams?.get('id') || null;
  const [costumerIdToUpdate, setCostumerIdToUpdate] = useState(customerID);

  useEffect(() => {
    if (customerID) {
      setCostumerIdToUpdate(customerID);
    }
  }, [customerID]);

  const handleCloseModals = () => {
    setCostumerIdToUpdate(null);
    refetchCustomerListData();
  };

  // CustomerViewModal currently returns null, so we don't need to pass props
  const CustomerViewModalComponent = <CustomerViewModal />;

  return { setCostumerIdToUpdate, viewModal: CustomerViewModalComponent };
};
