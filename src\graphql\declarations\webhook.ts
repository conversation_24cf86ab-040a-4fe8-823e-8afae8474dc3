import { graphql } from '../generated';

export const WebhookCreate = graphql(`
  mutation WebhookCreate($input: Group_createWebhookInput!) {
    group_createWebhook(input: $input) {
      id
      url
      secret
    }
  }
`);

export const WebhookList = graphql(`
  query GroupWebhooks(
    $where: GroupWebhookWhereInput!
    $orderBy: [GroupWebhookOrderByInput!]!
    $take: Int
    $skip: Int!
  ) {
    groupWebhooks(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      webhookURL
      scope
      group {
        name
        id
      }
    }
  }
`);

export const WebhookDelete = graphql(`
  mutation WebhookDelete($where: [GroupWebhookWhereUniqueInput!]!) {
    deleteGroupWebhooks(where: $where) {
      id
    }
  }
`);
