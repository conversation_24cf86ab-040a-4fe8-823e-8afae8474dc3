// import { <PERSON>Header, TopComponent, useDataGridView } from '@/components/globals';
// import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
// import { useLocationSelector } from '@/components/hooks';
// import { useEffect, useState } from 'react';
// import { FaCircleCheck } from 'react-icons/fa6';
// import {
//   Gateway_CustomersDocument,
//   GatewayUniCustomersOutputData,
// } from '@/graphql/generated/graphql';
// import useDebounce from '@/components/hooks/useDebounce';
// import { useSearchParams } from 'next/navigation';
// import { CardBrand } from '@/components/shared/components';
// import { CustomerAdd } from '../components/customer-add';
// import { CustomerViewModal } from '../components/customer-view-modal';
// import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
// import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
// import { apolloClient } from '@/lib/graphql/ApolloClient';
export const CustomersTab = () => {
  // const queryParams = useSearchParams();
  // const customerID = queryParams?.get('id');
  // const [selectedCustomerId, setSelectedCustomerID] = useState(customerID ?? null);
  // const [searchValue, setSearchValue] = useState('');
  // const debouncedSearchQuery = useDebounce(searchValue, 500);

  // const {
  //   locationFilter,
  //   locationSelectorElement,
  //   loading: loadingGroupList,
  // } = useLocationSelector({
  //   onlyActive: true,
  // });

  // const {
  //   data: customerListData,
  //   loading: customerListLoading,
  //   refetch: refetchCustomerListData,
  //   pageSize,
  //   currentPage,
  //   onPageChange,
  //   onPageSizeChange,
  //   setSortField,
  //   setIsAscendOrder,
  //   maxVariables,
  // } = useDataGridView({
  //   query: Gateway_CustomersDocument,
  //   options: {
  //     variables: {
  //       input: {
  //         groupID: locationFilter?.id ?? '',
  //         data: {
  //           page: {
  //             page: 1,
  //             pageSize: 10,
  //           },
  //         },
  //       },
  //     },
  //     skip: !locationFilter?.id || locationFilter?.id !== '',
  //   },
  //   searchParams: debouncedSearchQuery,
  // });

  // useEffect(() => {
  //   if (locationFilter?.id) {
  //     refetchCustomerListData();
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [locationFilter]);

  // const fetchExportData = async () => {
  //   const result = await apolloClient.query({
  //     query: Gateway_CustomersDocument,
  //     variables: maxVariables,
  //   });
  //   return result?.data?.gateway_customers?.data ?? [];
  // };

  // const columns: Column<GatewayUniCustomersOutputData>[] = [
  //   {
  //     key: 'isDefault',
  //     header: 'Ready to Use',
  //     renderCell: ({ isDefault }) => {
  //       return <FaCircleCheck color={isDefault ? 'green' : 'gray'} size={20} />;
  //     },
  //     onClick: (row) => {
  //       setSelectedCustomerID(row.id ?? '');
  //     },
  //   },
  //   {
  //     key: 'last4',
  //     header: 'Last 4',
  //   },
  //   {
  //     key: 'brand',
  //     header: 'Brand',
  //     renderCell: (row) => <CardBrand brand={row.brand} />,
  //   },
  //   {
  //     key: 'expires',
  //     header: 'Expires',
  //     width: '80px',

  //     // valueGetter: ({ expires }) => moment(expires).format('MM/DD/YYYY'),
  //   },
  //   {
  //     key: 'name',
  //     header: 'Name',
  //     width: '120px',
  //     sortable: true,
  //     onServerSort: (key) => {
  //       setSortField(key);
  //       setIsAscendOrder((v) => !v);
  //     },
  //   },
  //   {
  //     key: 'city_state',
  //     header: 'City/State',
  //     width: '90px',
  //   },
  //   {
  //     key: 'zip',
  //     header: 'Zip',
  //     width: '70px',
  //   },
  //   {
  //     key: 'email',
  //     header: 'Email',
  //     width: '110px',
  //     sortable: true,
  //     onServerSort: (key) => {
  //       setSortField(key);
  //       setIsAscendOrder((v) => !v);
  //     },
  //   },
  //   {
  //     key: 'phone',
  //     header: 'Phone',
  //     width: '90px',
  //   },
  // ];

  // const handleCloseModals = () => {
  //   setSelectedCustomerID(null);
  //   refetchCustomerListData();
  // };

  // return (
  //   <>
  //     <StaticInfoBox />
  //     <div className="items-bottom flex justify-between">
  //       <PageHeader text="Customer" />
  //       <CustomerAdd refetchListPage={refetchCustomerListData} />
  //     </div>
  //     <div className="border-b border-gray-300 sm:flex">
  //       <div className="w-1/4">{locationSelectorElement}</div>
  //       <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
  //         <TopComponent value={searchValue} setValue={setSearchValue}>
  //           <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="customers" />
  //         </TopComponent>
  //       </div>
  //     </div>

  //     <div>
  //       <DataGridView
  //         columns={columns}
  //         rows={customerListData?.gateway_customers?.data ?? []}
  //         pageSize={pageSize}
  //         currentPage={currentPage}
  //         isLoading={customerListLoading || loadingGroupList}
  //         mode="server"
  //         onPageChange={onPageChange}
  //         onPageSizeChange={onPageSizeChange}
  //         totalRecords={customerListData?.gateway_customers?.page?.total ?? 0}
  //       />
  //     </div>

  //     <CustomerViewModal
  //       isOpen={selectedCustomerId !== null}
  //       onClose={handleCloseModals}
  //       queryData={{
  //         customerID: selectedCustomerId ?? '',
  //         groupID: locationFilter?.id ?? '',
  //       }}
  //     />
  //   </>
  // );
  return null;
};
