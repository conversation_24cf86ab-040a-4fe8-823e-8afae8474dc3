import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { ReactElement } from 'react';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const removeSpaces = (value: string) => {
  return value.replace(/\D/g, '');
};

export const formatToUS = (value: string) => {
  const val = removeSpaces(value);

  return '+1' + val;
};

export function formatEIN(ein: string): string {
  const cleaned = ein.replace(/\D/g, '');
  if (cleaned.length <= 2) {
    return cleaned;
  } else {
    return cleaned.slice(0, 2) + '-' + cleaned.slice(2, 9);
  }
}

export const formatSSN = (value: string) => {
  const cleaned = value.replace(/\D/g, '');
  const match = cleaned.match(/^(\d{0,3})(\d{0,2})(\d{0,4})$/);
  if (match) {
    return [match[1], match[2], match[3]].filter(Boolean).join('-').slice(0, 11);
  }
  return cleaned;
};

export function formatPhoneNumber(phoneNumberString) {
  let cleaned = ('' + phoneNumberString).replace(/\D/g, '');
  let match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return '(' + match[1] + ') ' + match[2] + '-' + match[3];
  }
  return '';
}

export function moneyFormat(value?: string | number | null): ReactElement {
  const numValue = value ? parseFloat(value.toString()) : 0;
  const isNegative = numValue < 0;
  const formattedValue = Math.abs(numValue)
    .toFixed(2)
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return (
    <span className={isNegative ? 'text-red-600' : undefined}>
      {isNegative ? '- ' : ''}
      {`$${formattedValue}`}
    </span>
  );
}

export const floatToInt2Dec = (value: string | number | null | undefined) => {
  if (!value) {
    return 0;
  }
  if (typeof value === 'string') {
    return parseInt(parseFloat(value) * 100 + '');
  }
  return parseInt(value * 100 + '');
};

export const int2DecToFloat = (value: string | number | null | undefined) => {
  if (!value) {
    return 0;
  }
  if (typeof value === 'string') {
    return parseFloat(value) / 100;
  }
  return value / 100;
};

export function moneyFormatString(value?: string | number | null): string {
  const numValue = value ? parseFloat(value.toString()) : 0;
  const isNegative = numValue < 0;
  const formattedValue = Math.abs(numValue)
    .toFixed(2)
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return `${isNegative ? '- ' : ''}$${formattedValue}`;
}
