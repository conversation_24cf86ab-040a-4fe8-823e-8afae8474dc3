'use client';

import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { Card, CardContent } from '@/components/ui/card';
import { GHLSSOBind, GHLSSOCheck } from '@/graphql/declarations/gohighlevel-sso';
import { Me } from '@/graphql/declarations/me';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { ArrowRightIcon } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

export default function IntegGLBindPage() {
  const params = useSearchParams();

  const { data: ssoData } = useQuery(GHLSSOCheck, {
    variables: {
      input: {
        ssoToken: params?.get('token') || '',
      },
    },
    skip: !params?.get('token'),
  });

  const { data: userMe } = useQuery(Me);

  const [isDone, setIsDone] = useState(false);

  const isReadyToBind =
    !isDone && ssoData?.ghl_auth_getSSOInfo?.email && userMe?.authenticatedItem?.email;

  const isForLoginPrompting = !isDone && !isReadyToBind && ssoData?.ghl_auth_getSSOInfo?.email;

  const isLoading = !isDone && !isReadyToBind && !isForLoginPrompting;

  const redirectToLogin = async () => {
    // copy current url and pass it as redirect query param
    const redirectUrl = window.location.href;
    const redirectParam = encodeURIComponent(redirectUrl);
    window.location.href = `/login?redirect=${redirectParam}`;
  };

  const [isBinding, setIsBinding] = useState(false);
  const bindAccount = async () => {
    setIsBinding(true);
    const code = params?.get('token');

    if (!code) {
      toast.error('Invalid token');
      return;
    }

    if (!userMe?.authenticatedItem?.email) {
      toast.error('Please login first');
      return;
    }

    // bind account
    const result = await apolloClient.mutate({
      mutation: GHLSSOBind,
      variables: {
        input: {
          ssoToken: params?.get('token') || '',
        },
      },
    });

    toast.success('Account bound successfully');
    if (result.data?.ghl_auth_bind?.localUserID) {
      setIsDone(true);
    }
  };

  useEffect(() => {
    // Check if window is defined to ensure code runs only on the client
    if (typeof window !== 'undefined') {
      localStorage.setItem('isIntergrated', 'true');
    }
  }, []);

  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <Card className="w-full max-w-screen-sm p-8 px-2 text-center shadow-lg">
        <CardContent>
          <h2 className="text-2xl font-semibold">Bind User Account</h2>
          <p className="mt-4 text-sm text-gray-600">
            Bind your account to your GoHighLevel SSO to enable fast login.
          </p>

          {isDone && (
            <div className="py-4">
              <p className="text-2xl">Setup Installation Complete!</p>
              <Link href="/dashboard" className="text-primary-500 underline">
                Redirect to Dashboard
              </Link>
            </div>
          )}
          {isLoading && (
            <div className="flex items-center justify-center gap-4 px-4 py-4">
              <LoaderSquares />
            </div>
          )}
          {isReadyToBind && (
            <>
              <div className="flex items-center justify-center gap-4 px-4 py-4">
                <div className="flex flex-col items-center rounded-md bg-gray-200 p-4">
                  <p className="font-bold">{userMe?.authenticatedItem?.email}</p>
                  <p>NGnair Payments Account</p>
                  <button className="text-sm text-primary-600 underline" onClick={redirectToLogin}>
                    Change Account
                  </button>
                </div>
                <ArrowRightIcon className="h-6 w-6 text-gray-400" />
                <div className="flex flex-col items-center rounded-md bg-gray-200 p-4">
                  <p className="font-bold">{ssoData?.ghl_auth_getSSOInfo?.email}</p>
                  <p>GoHighLevel Account</p>
                  <p className="text-sm">{ssoData?.ghl_auth_getSSOInfo?.locationID}</p>
                </div>
              </div>
              <div className="px-8">
                <button
                  onClick={bindAccount}
                  disabled={isBinding}
                  className="mt-4 w-full rounded-md bg-primary-500 p-2 text-white"
                >
                  {isBinding ? 'Binding Account...' : 'Bind Account'}
                </button>
              </div>
            </>
          )}
          {isForLoginPrompting && (
            <div className="px-8">
              <p>
                Select an Account you would like to use when you are managing NGnair Payments inside
                GoHighLevel Bind an Account to location.
              </p>
              <br />
              <p>
                Location ID:{' '}
                <span className="font-bold">{ssoData.ghl_auth_getSSOInfo?.locationID}</span>
              </p>
              <p>
                Email: <span className="font-bold">{ssoData.ghl_auth_getSSOInfo?.email}</span>
              </p>
              <button
                className="mt-4 w-full rounded-md bg-primary-500 p-2 text-white"
                onClick={redirectToLogin}
              >
                Login to Select Account
              </button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
