// 'use client';

// import { useEffect, useRef, useState } from 'react';
// import type { NextPage } from 'next';
// import styles from './index.module.css';
// import Image from 'next/image';
// import { apolloClient } from '@/lib/graphql/ApolloClient';
// import { GetPaymentPageData, SubmitPaymentPageData } from '@/graphql/declarations/payments';
// import { toast } from 'react-toastify';
// import { cn } from '@/lib/utils';
// import dynamic from 'next/dynamic';
// const GLPayTokenizerTemplate = dynamic(
//   () => import('@/components/payments/glp/glp_tokenizer_template'),
//   {
//     ssr: false,
//     loading(loadingProps) {
//       return <p>Loading...</p>;
//     },
//   },
// );
// interface PaymentInitiateProps {
//   type: 'payment_initiate_props';
//   publishableKey: string; // Publishable key sent while connecting integration API
//   amount: number; // Amount in decimal currency with max 2 decimal places
//   currency: string; // Standard 3 letter notation for currencies ex. USD, INR
//   mode: string; // Payment mode: subscription/payment
//   productDetails: { productId: string; priceId: string }; // productId and priceId for recurring products. More details can be fetched using the public api for Products/Prices
//   contact?: {
//     // Customer details for customer placing the order
//     id: string; // Customer id in GHL
//     name: string; // Full name of the customer
//     email: string;
//     contact: string; // Contact number of customer with country code
//   };
//   orderId: string; // GHL internal orderId for given order
//   transactionId: string; // GHL internal transactionId for the given transaction
//   subscriptionId: string; // GHL internal subscriptionId passed in case of a recurring product
//   locationId: string; // Sub-account id for which the given order is created.
// }

// const Payment: NextPage = () => {
//   const [paymentData, setPaymentData] = useState<PaymentInitiateProps | null>(null);

//   const [calculations, setCalculations] = useState({
//     subtotal: 0,
//     discount: 0,
//     total: 0,
//   });

//   const [payAPIKey, setPayAPIKey] = useState('');
//   const [liveMode, setLiveMode] = useState(false);

//   async function loadInvoiceData() {
//     if (!paymentData) return;

//     const d = await apolloClient.query({
//       query: GetPaymentPageData,
//       variables: {
//         input: {
//           apiKey: paymentData?.publishableKey,
//           transactionID: paymentData?.transactionId,
//         },
//       },
//     });

//     let data = d.data.ghl_api_getPaymentPageData?.data;
//     if (!data) return;

//     setCalculations({
//       total: data.amount / 100,
//       discount: (data.amountSummary?.discount ?? 0) / 100,
//       subtotal: (data.amountSummary?.subtotal ?? 0) / 100,
//     });

//     setPayAPIKey(data.apiKey);
//     setLiveMode(data.liveMode);
//   }

//   useEffect(() => {
//     loadInvoiceData();
//   }, [paymentData]);

//   const isReady = paymentData !== null;

//   const [payToken, setPayToken] = useState<string | false>(false);

//   const paymentRef = useRef(null);

//   async function postTransactionsFlag(args: { type: string; message?: string }) {
//     switch (args.type) {
//       case 'success': {
//         window.parent.postMessage(
//           JSON.stringify({
//             type: 'custom_element_success_response',
//             chargeId: args.message || '',
//           }),
//           '*',
//         );
//         break;
//       }
//       case 'error': {
//         window.parent.postMessage(
//           JSON.stringify({
//             type: 'custom_element_error_response',
//             error: {
//               description: args.message || '',
//             },
//           }),
//           '*',
//         );
//         break;
//       }
//       case 'cancel': {
//         window.parent.postMessage(
//           JSON.stringify({
//             type: 'custom_element_close_response',
//           }),
//           '*',
//         );
//         break;
//       }
//     }
//   }

//   async function providerReadyFlag() {
//     const key = await new Promise((resolve) => {
//       window.parent.postMessage(
//         JSON.stringify({
//           type: 'custom_provider_ready',
//           loaded: true,
//         }),
//         '*',
//       );
//     });
//   }

//   function processEvents(rawdata: any) {
//     try {
//       const data = JSON.parse(rawdata);
//       const dType = data.type;
//       if (!dType) {
//         return;
//       }

//       switch (dType) {
//         case 'payment_initiate_props': {
//           let d = data as PaymentInitiateProps;
//           setPaymentData(d);
//           break;
//         }
//       }
//     } catch (e) {
//       console.error('Error in parsing data', e);
//     }
//   }

//   useEffect(() => {
//     providerReadyFlag();
//     window.addEventListener('message', ({ data }) => {
//       processEvents(data);
//     });
//   }, []);

//   const getIP = async () => {
//     const resp = await fetch('https://api.ipify.org?format=json');
//     const data = await resp.json();
//     return data.ip;
//   };

//   const submitPayment = async (e) => {
//     e.preventDefault();
//     if (!paymentData || !payToken) return;
//     let ip = await getIP();
//     try {
//       const resp = await apolloClient.mutate({
//         mutation: SubmitPaymentPageData,
//         variables: {
//           input: {
//             apiKey: paymentData?.publishableKey,
//             transactionID: paymentData?.transactionId,
//             paymentToken: payToken,
//             ipAddress: ip,
//           },
//         },
//       });
//       toast.success('Payment Successful');

//       let txID = resp.data?.ghl_api_submitPayment?.txID;

//       if (!txID) {
//         throw new Error('Payment Failed');
//       }

//       postTransactionsFlag({
//         type: 'success',
//         message: txID,
//       });
//     } catch (error) {
//       console.error(error);
//     }
//   };

//   return (
//     <form className={cn(styles.payment, '!px-4')}>
//       <div className={cn(styles.container, 'max-w-screen-lg')}>
//         <div className={styles.payment1}>Payment</div>
//         <div className={styles.cards}>
//           <GLPayTokenizerTemplate
//             apiKey="oNYX9cKYeML3QY5nFpnYqEcbGPSV"
//             liveMode={false}
//             onSubmission={(token) => {}}
//             tokenizerRef={paymentRef}
//           />
//           <div className={cn(styles.priceLogos, 'w-full')}>
//             <div className={styles.totalPrice}>
//               <div className={styles.inputs}>
//                 <div className={styles.listItems}>
//                   <div className={styles.listItem}>
//                     <div className={styles.heading}>Subtotal</div>
//                     <div className={styles.heading}>${calculations.subtotal.toFixed(2)}</div>
//                   </div>
//                   <div className={styles.listItem}>
//                     <div className={styles.heading}>Discount</div>
//                     <div className={styles.heading3}>-${calculations.discount.toFixed(2)}</div>
//                   </div>
//                 </div>
//                 <div className={styles.listItem4}>
//                   <div className={styles.listItem5}>Total</div>
//                   <div className={styles.listItem5}>${calculations.total.toFixed(2)}</div>
//                 </div>
//               </div>
//             </div>
//             <div className={cn(styles.brandLogos, 'flex-col sm:flex-row')}>
//               <div className="relative aspect-[95/32] h-12">
//                 <Image
//                   className={styles.brandLogospaypalIcon}
//                   alt=""
//                   src="/icons/payments/paypal.png"
//                   layout="fill"
//                 />
//               </div>
//               <div className="relative aspect-[58/32] h-12">
//                 <Image
//                   className={styles.brandLogosvisaIcon}
//                   alt=""
//                   src="/icons/payments/visa.png"
//                   layout="fill"
//                 />
//               </div>
//               <div className="relative aspect-[40/32] h-12">
//                 <Image
//                   className={styles.brandLogosmastercardIcon}
//                   alt=""
//                   src="/icons/payments/mastercard.png"
//                   layout="fill"
//                 />
//               </div>
//             </div>
//           </div>
//         </div>
//         <div className={cn(styles.helper, 'w-full text-center')}>
//           <div className={cn(styles.heading, 'w-full')}>
//             <span>{`Payment processed by `}</span>
//             <span className={styles.payu}>PayU</span>
//             <span>{` for `}</span>
//             <span className={styles.payu}>Flowbite LLC</span>
//             <span> - United States Of America</span>
//           </div>
//         </div>
//       </div>
//     </form>
//   );
// };

// export default Payment;

export default function Page() {
  return <div></div>;
}
