import styles from './index.module.css';
import { FormProvider, useForm } from 'react-hook-form';
import { FormInput, FormFormattedInput, FormAutoComplete } from '@/components/globals';
import { emailPatternRegex, message, phoneNumberIsValid } from '@/components/shared/utils';
import { MutableRefObject, useEffect } from 'react';
import { Promisable } from '@/types/types';
import { updateData } from '../updateData';
import { FormDatepickerv2 } from '@/components/globals/form-date-picker-v2';
import FormCheckbox from '@/components/globals/form-checkbox/form-checkbox';
import FormPhoneNumber from '@/components/globals/form-phone-number/form-phonenumber.index';
import { useSearchParams } from 'next/navigation';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { ProcessorTestDraftZipSearchDocument } from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import FormTextDisplay from '@/components/globals/form-text/form-text';

const BusinessInfoPage = (args: {
  triggerSubmit?: MutableRefObject<() => Promisable<boolean>>;
  initialData?: any;
}) => {
  const methods = useForm({
    defaultValues: args.initialData ?? {},
  });

  const searchParams = useSearchParams();

  useEffect(() => {
    if (args.triggerSubmit) {
      args.triggerSubmit.current = async () => {
        const isValid = await methods.trigger();
        if (isValid) {
          const val = methods.getValues();
          try {
            return await updateData({
              submitType: 'business',
              data: val,
              groupID: searchParams?.get('groupID') ?? undefined,
              form: methods,
            });
          } catch (error) {
            console.log(error);
            return false;
          }
        }
        return false;
      };
    }
  }, [args.triggerSubmit]);

  const zipCode = methods.watch('zipCode');
  let debouncedZipCode = useDebounce(zipCode, 750);
  const fetchAddressViaZip = async () => {
    if (debouncedZipCode?.length === 5) {
      const response = await apolloClient.query({
        query: ProcessorTestDraftZipSearchDocument,
        variables: {
          input: {
            zipcode: debouncedZipCode,
          },
        },
      });
      const data = await response.data?.processor_tst_zip_search?.item;
      if (data) {
        methods.setValue('city', data.city);
        methods.setValue('state', data.state);
        methods.setValue('country', data.country);
      }
    }
  };
  useEffect(() => {
    fetchAddressViaZip();
  }, [debouncedZipCode]);

  const legalMailingZipCode = methods.watch('legalMailingZipCode');
  let debouncedLegalMailingZipCode = useDebounce(legalMailingZipCode, 750);
  const fetchLegalAddressViaZip = async () => {
    if (debouncedLegalMailingZipCode?.length === 5) {
      const response = await apolloClient.query({
        query: ProcessorTestDraftZipSearchDocument,
        variables: {
          input: {
            zipcode: debouncedLegalMailingZipCode,
          },
        },
      });
      const data = await response.data?.processor_tst_zip_search?.item;
      if (data) {
        methods.setValue('legalMailingCity', data.city);
        methods.setValue('legalMailingState', data.state);
        methods.setValue('legalMailingCountry', data.country);
      }
    }
  };
  useEffect(() => {
    fetchLegalAddressViaZip();
  }, [debouncedLegalMailingZipCode]);

  const website = methods.watch('website');
  useEffect(() => {
    if (website) {
      if (!website.includes('https://')) {
        methods.setValue('website', `https://${website}`);
      }
    }
  }, [website]);

  return (
    <FormProvider {...methods}>
      <form>
        <div className="space-y-1">
          <div className={styles.inputWidgetLg}>
            <div className={styles.column}>
              <FormInput
                id="legalBusinessName"
                name="legalBusinessName"
                label="Legal Business Name"
                rules={{ required: message.requiredField }}
              />
              <FormAutoComplete
                id="typeOfBusiness"
                name="typeOfBusiness"
                label="Type of Business"
                rules={{ required: message.requiredField }}
                onChangeStrategy="id"
                options={[
                  {
                    id: 'LIMITED_COMPANY',
                    label: 'Limited Liability Corporation',
                  },
                  {
                    id: 'PUBLICLY_TRADED_CORPORATION',
                    label: 'Publicly Traded Corporation',
                  },
                  {
                    id: 'NON_PUBLICLY_TRADED_CORPORATION',
                    label: 'Non-Publicly Traded Corporation',
                  },
                  {
                    id: 'NON_PROFIT_ORGANIZATION',
                    label: 'Non-Profit Organization',
                  },
                  {
                    id: 'GOVERNMENT_ORGANIZATION',
                    label: 'Government Organization',
                  },
                  {
                    id: 'NON_PUBLICLY_TRADED_LLC',
                    label: 'Non-Publicly Traded LLC',
                  },
                  {
                    id: 'PUBLICLY_TRADED_LLC',
                    label: 'Publicly Traded LLC',
                  },
                  {
                    id: 'GENERAL_PARTNERSHIP',
                    label: 'General Partnership',
                  },
                  {
                    id: 'PUBLICLY_TRADED_PARTNERSHIP',
                    label: 'Publicly Traded Partnership',
                  },
                  {
                    id: 'SOLE_PROPRIETORSHIP',
                    label: 'Sole Proprietorship',
                  },
                  {
                    id: 'AMERICAN_EXPRESS',
                    label: 'American Express',
                  },
                ]}
              />
            </div>
            <div className={styles.column}>
              <FormInput
                id="dbaName"
                name="dbaName"
                label="DBA Name: (can be same as above)"
                rules={{ required: message.requiredField }}
              />
              <FormFormattedInput
                id="ein"
                name="ein"
                label="EIN"
                mask="**-*******"
                rules={{
                  required: message.requiredField,
                  minLength: {
                    value: 9,
                    message: message.minLength(9),
                  },
                }}
              />
            </div>
            <div className={styles.column}>
              <div className="flex-1">
                <FormDatepickerv2
                  id="dateBusinessEstablished"
                  name="dateBusinessEstablished"
                  label="Date Business Established"
                  rules={{ required: message.requiredField }}
                />
              </div>
              <div className="flex-1">&nbsp;</div>
            </div>
            <div className={styles.column}>
              <FormInput
                id="businessEmail"
                name="businessEmail"
                label="Business Email"
                type="email"
                rules={{
                  required: message.requiredField,
                  pattern: {
                    value: emailPatternRegex,
                    message: message.emailPattern,
                  },
                }}
              />
              <FormPhoneNumber
                id="businessPhone"
                name="businessPhone"
                label="Business Phone"
                country="us"
                preferredCountries={['us', 'ca']}
                onCountryChange={(country) => {
                  methods.setValue('businessPhoneCountryCode', country);
                }}
                rules={phoneNumberIsValid}
              />
            </div>
            <div className={styles.column}>
              <FormInput
                id="website"
                name="website"
                label="Website"
                rules={{ required: message.requiredField }}
              />
              <FormPhoneNumber
                id="customerServicePhone"
                name="customerServicePhone"
                label="Customer Service Phone"
                country="us"
                onCountryChange={(country) => {
                  methods.setValue('customerServicePhoneCountryCode', country);
                }}
                preferredCountries={['us', 'ca']}
              />
            </div>
            <div className={styles.labelWrapper}>
              <div className={styles.label5}>Location Address:</div>
            </div>
            <div className={styles.column}>
              <FormInput
                id="street"
                name="street"
                label="Street"
                rules={{ required: message.requiredField }}
              />
              <FormInput
                id="zipCode"
                name="zipCode"
                label="Zip Code"
                type="number"
                rules={{
                  required: message.requiredField,
                  maxLength: {
                    value: 9,
                    message: message.maxLength(9),
                  },
                  minLength: {
                    value: 5,
                    message: message.minLength(5),
                  },
                }}
              />
              <FormTextDisplay
                id="city"
                name="city"
                label="City"
                rules={{
                  required: message.requiredField,
                  minLength: {
                    value: 2,
                    message: message.minLength(2),
                  },
                  maxLength: {
                    value: 30,
                    message: message.maxLength(30),
                  },
                }}
              />
              <FormTextDisplay
                id="state"
                name="state"
                label="State"
                rules={{
                  required: message.requiredField,
                  minLength: {
                    value: 2,
                    message: message.minLength(2),
                  },
                  maxLength: {
                    value: 2,
                    message: message.maxLength(2),
                  },
                }}
              />
            </div>
          </div>
          <div className="flex items-center space-x-2 px-5">
            <FormCheckbox
              id="differentLegalAddress"
              name="differentLegalAddress"
              label="Use different legal address"
            />
          </div>
          {methods.watch('differentLegalAddress') && (
            <div className={styles.inputWidgetLg}>
              <div className={styles.labelWrapper}>
                <div className={styles.label}>Legal Mailing Address:</div>
              </div>
              <div className={styles.inputFieldParent}>
                <FormInput
                  id="legalMailingStreet"
                  name="legalMailingStreet"
                  label="Street"
                  rules={{ required: message.requiredField }}
                />
                <FormInput
                  id="legalMailingZipCode"
                  name="legalMailingZipCode"
                  label="Zip Code"
                  type="number"
                  rules={{
                    required: message.requiredField,

                    maxLength: {
                      value: 9,
                      message: message.maxLength(9),
                    },
                    minLength: {
                      value: 5,
                      message: message.minLength(5),
                    },
                  }}
                />
                <FormTextDisplay
                  id="legalMailingCity"
                  name="legalMailingCity"
                  label="City"
                  rules={{
                    required: message.requiredField,
                    minLength: {
                      value: 2,
                      message: message.minLength(2),
                    },
                    maxLength: {
                      value: 30,
                      message: message.maxLength(30),
                    },
                  }}
                />
                <FormTextDisplay
                  id="legalMailingState"
                  name="legalMailingState"
                  label="State"
                  rules={{
                    required: message.requiredField,
                    minLength: {
                      value: 2,
                      message: message.minLength(2),
                    },
                    maxLength: {
                      value: 2,
                      message: message.maxLength(2),
                    },
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </form>
    </FormProvider>
  );
};

export default BusinessInfoPage;
