'use client';

import { cn, formatEIN } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { But<PERSON> } from '@/components/ui/button';
import { CalendarIcon, Search } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandItem } from '@/components/ui/command';

import { CommandList } from '@/components/ui/command';

import { useEffect, useState } from 'react';
import { MerchantForm } from '../types/merchart-schema';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { Calendar } from '@/components/ui/calendar';
import moment from 'moment';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { getZipCode } from '@/graphql/declarations/formUtilities';
import { Checkbox } from '@/components/ui/checkbox';
import { CaretSortIcon } from '@radix-ui/react-icons';
import * as PopoverPrimitive from '@radix-ui/react-popover';
import { formatPhoneNumber } from '@/lib/utils';

const BusinessInformation = ({ formState }: { formState: MerchantForm }) => {
  const [checkingZipCode, setCheckingZipCode] = useState(false);
  const [checkingDiffZipCode, setCheckingDiffZipCode] = useState(false);
  const [typeOfBusiness, setTypeOfBusiness] = useState<{ mccCode: string; description: string }[]>(
    [],
  );
  const [mmcSearch, setMccSearch] = useState('');

  const {
    setValue,
    register,
    watch,
    formState: state,
    setError,
    trigger,
    reset,
    clearErrors,
  } = formState;
  const values = watch();

  const { errors } = state;

  console.log(errors);
  console.log(values.city);

  useEffect(() => {
    const controller = new AbortController();

    (async () => {
      try {
        if (!values?.zip_code) return;
        setCheckingZipCode(true);
        const { data } = await apolloClient.query({
          query: getZipCode,
          variables: {
            input: {
              zipcode: values?.zip_code,
            },
          },
          context: {
            fetchOptions: {
              signal: controller.signal,
            },
          },
        });

        console.log(data?.processor_aur_zip_search);

        setValue('state', data?.processor_aur_zip_search?.item?.state as string);
        setValue('city', data?.processor_aur_zip_search?.item?.city as string);

        setCheckingZipCode(false);

        trigger('zip_code');
      } catch (err: any) {
        console.log(err);
        if (err?.response?.status === 404) {
          setCheckingZipCode(false);
          setValue('city', '');
          setValue('state', '');

          setError('zip_code', { message: 'Invalid Zip Code' });
          setError('state', { message: 'Invalid Zip Code' });
        }
        if (err?.name !== 'AbortError') {
          setCheckingZipCode(true);
          // Handle non-abort errors here
          return;
        }
      } finally {
        setCheckingZipCode(false);
      }
    })();

    return () => {
      controller.abort();
    };
  }, [values?.zip_code]);

  useEffect(() => {
    const controller = new AbortController();

    (async () => {
      try {
        if (!values.use_different_legal_email) return;
        setCheckingDiffZipCode(true);
        const { data } = await apolloClient.query({
          query: getZipCode,
          variables: {
            input: {
              zipcode: values?.diff_zip_code,
            },
          },
          context: {
            fetchOptions: {
              signal: controller.signal,
            },
          },
        });

        console.log(data?.processor_aur_zip_search);

        setValue('diff_state', data?.processor_aur_zip_search?.item?.state as string);
        setValue('diff_city', data?.processor_aur_zip_search?.item?.city as string);

        setCheckingDiffZipCode(false);

        trigger('diff_zip_code');
      } catch (err: any) {
        console.log(err);
        if (err?.response?.status === 404) {
          setCheckingDiffZipCode(false);
          setValue('diff_city', '');
          setValue('diff_state', '');

          setError('diff_zip_code', { message: 'Invalid Zip Code' });
          setError('diff_state', { message: 'Invalid Zip Code' });
        }
        if (err?.name !== 'AbortError') {
          setCheckingDiffZipCode(true);
          // Handle non-abort errors here
          return;
        }
      } finally {
        setCheckingDiffZipCode(false);
      }
    })();

    return () => {
      controller.abort();
    };
  }, [values?.diff_zip_code]);

  // useEffect(() => {
  //   (async () => {
  //     const response = await apolloClient.query({
  //       query: getMCC,
  //       variables: {
  //         input: {
  //           pattern: mmcSearch,
  //         },
  //       },
  //     });

  //     console.log(response?.data?.processor_aur_mcc?.items);
  //     setTypeOfBusiness(
  //       response?.data?.processor_aur_mcc?.items as {
  //         description: string;
  //         mccCode: string;
  //       }[]
  //     );
  //   })();
  // }, [mmcSearch]);

  useEffect(() => {
    (async () => {
      const response = [
        {
          mccCode: 'LIMITED_COMPANY',
          description: 'Limited Liability Corporation',
        },
        {
          mccCode: 'PUBLICLY_TRADED_CORPORATION',
          description: 'Publicly Traded Corporation',
        },
        {
          mccCode: 'NON_PUBLICLY_TRADED_CORPORATION',
          description: 'Non-Publicly Traded Corporation',
        },
        {
          mccCode: 'NON_PROFIT_ORGANIZATION',
          description: 'Non-Profit Organization',
        },
        {
          mccCode: 'GOVERNMENT_ORGANIZATION',
          description: 'Government Organization',
        },
        {
          mccCode: 'NON_PUBLICLY_TRADED_LLC',
          description: 'Non-Publicly Traded LLC',
        },
        {
          mccCode: 'PUBLICLY_TRADED_LLC',
          description: 'Publicly Traded LLC',
        },
        {
          mccCode: 'GENERAL_PARTNERSHIP',
          description: 'General Partnership',
        },
        {
          mccCode: 'PUBLICLY_TRADED_PARTNERSHIP',
          description: 'Publicly Traded Partnership',
        },
        {
          mccCode: 'SOLE_PROPRIETORSHIP',
          description: 'Sole Proprietorship',
        },
        {
          mccCode: 'AMERICAN_EXPRESS',
          description: 'American Express',
        },
      ].filter((type) => type.description.toLowerCase().includes(mmcSearch.toLowerCase()));

      setTypeOfBusiness(
        response as {
          description: string;
          mccCode: string;
        }[],
      );
    })();
  }, [mmcSearch]);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-row gap-4">
        <FormField
          control={formState.control}
          name="legal_business_name"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Legal Business Name<span className="text-red-400">*</span>
              </Label>
              <FormControl className="mt-2">
                <Input
                  {...field}
                  onChange={(e) => {
                    setValue('legal_business_name', e.target.value, {
                      shouldValidate: true,
                    });
                  }}
                  placeholder="Legal Business Name"
                  className={cn(
                    '',
                    errors.legal_business_name &&
                      'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                  )}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={formState.control}
          name="type_of_business"
          render={({ field }) => (
            <FormItem className="relative flex flex-1 flex-col">
              <Label htmlFor="type_of_business">
                Type of Business<span className="text-red-400">*</span>
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      role="combobox"
                      variant="select-outline"
                      className={cn(
                        '',
                        !field.value && 'text-gray-500',
                        errors.type_of_business &&
                          'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                      )}
                    >
                      {values?.type_of_business?.description ?? 'Select Business Type'}
                      <CaretSortIcon className="ml-2 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="p-0">
                  <Command>
                    <div className="relative h-fit w-full px-1 pt-5">
                      <Search className="absolute bottom-3 left-4 h-4 w-4 shrink-0 opacity-50" />
                      <Input
                        onChange={(e) => {
                          setMccSearch(e.target.value);
                        }}
                        type="search"
                        placeholder="Search Business Type..."
                        className="rounded-md border-l-transparent border-r-transparent border-t-transparent text-zinc-700"
                      />
                    </div>
                    <CommandList>
                      <CommandEmpty>No found.</CommandEmpty>
                      <CommandGroup>
                        {typeOfBusiness.map((type: any, index: number) => (
                          <PopoverPrimitive.Close key={index} className="w-full text-left">
                            <CommandItem
                              value={type.mccCode}
                              key={type.mccCode}
                              className="text-zinc-700 hover:!bg-gray-100"
                              onSelect={() => {
                                formState.setValue('type_of_business', type, {
                                  shouldValidate: true,
                                });
                                setMccSearch('');
                              }}
                            >
                              <div className="w-full">{type?.description}</div>
                            </CommandItem>
                          </PopoverPrimitive.Close>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </FormItem>
          )}
        />
      </div>

      <div className="flex flex-row gap-4">
        <FormField
          control={formState.control}
          name="dba_name"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label htmlFor="dba_name">
                DBA Name<span className="text-red-400">*</span>
              </Label>

              <Input
                {...field}
                placeholder="DBA Name"
                onChange={(e) =>
                  setValue('dba_name', e.target.value, {
                    shouldValidate: true,
                  })
                }
                variant="default"
                className={cn(
                  errors.dba_name &&
                    '!hover:text-red-400 !border-red-400 !text-red-400 hover:bg-red-50',
                )}
              />
            </FormItem>
          )}
        />

        <FormField
          control={formState.control}
          name="ein"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label htmlFor="EIN">
                EIN<span className="text-red-400">*</span>
              </Label>

              <Input
                {...field}
                value={formatEIN(field.value)}
                onChange={(e) =>
                  setValue('ein', e.target.value, {
                    shouldValidate: true,
                  })
                }
                placeholder="##-#######"
                className={cn(
                  errors.ein &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />

        <div className="flex flex-1 flex-col gap-2">
          <Label htmlFor="business_established_date">
            Business Established Date
            <span className="text-red-400">*</span>
          </Label>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="picker"
                className={cn(
                  !values.business_established_date && 'text-gray-500',
                  errors?.business_established_date &&
                    'border-red-400 text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {values.business_established_date ? (
                  moment(values.business_established_date).format('MMMM DD, YYYY')
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent align="start" className="w-auto p-0">
              <Calendar
                mode="single"
                captionLayout="dropdown-buttons"
                selected={new Date(values.business_established_date)}
                onSelect={(date) => {
                  date &&
                    setValue('business_established_date', date.toISOString(), {
                      shouldValidate: true,
                    });
                }}
                fromYear={1960}
                toYear={2030}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex flex-row gap-4">
        <FormField
          control={formState.control}
          name="website_link"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Website<span className="text-red-400"></span>
              </Label>

              <Input
                {...field}
                onChange={(e) => {
                  const value = e.target.value;
                  setValue('website_link', value, {
                    shouldValidate: !!value,
                  });

                  if (!value) {
                    clearErrors('website_link');
                  }
                }}
                id="website_link"
                placeholder="Add Website Link"
                className={cn(
                  errors.website_link &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />
        <FormField
          control={formState.control}
          name="business_email"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Business Email<span className="text-red-400">*</span>
              </Label>

              <Input
                {...field}
                onChange={(e) =>
                  setValue('business_email', e.target.value, {
                    shouldValidate: true,
                  })
                }
                placeholder="Business Email"
                className={cn(
                  errors.business_email &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />
      </div>

      <div className="flex flex-row gap-4">
        <FormField
          control={formState.control}
          name="phone_number"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Business Phone Number<span className="text-red-400">*</span>
              </Label>

              <Input
                {...field}
                value={formatPhoneNumber(values.phone_number)}
                onChange={(e) =>
                  setValue('phone_number', e.target.value, {
                    shouldValidate: true,
                  })
                }
                placeholder="Business Phone Number"
                className={cn(
                  errors.phone_number &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />
        <FormField
          control={formState.control}
          name="phone_number2"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Customer Phone Number<span className="text-red-400">*</span>
              </Label>

              <Input
                {...field}
                value={formatPhoneNumber(values.phone_number2)}
                onChange={(e) =>
                  setValue('phone_number2', e.target.value, {
                    shouldValidate: true,
                  })
                }
                placeholder="Customer Phone Number"
                className={cn(
                  errors.phone_number2 &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />
      </div>

      <div className="flex flex-1 flex-row gap-8">
        <FormField
          control={formState.control}
          name="street"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Street<span className="text-red-400">*</span>
              </Label>

              <Input
                {...field}
                onChange={(e) =>
                  setValue('street', e.target.value, {
                    shouldValidate: true,
                  })
                }
                placeholder="Enter Street"
                className={cn(
                  errors.street &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />

        <FormField
          control={formState.control}
          name="zip_code"
          render={({ field }) => (
            <FormItem className="flex flex-1 flex-col">
              <Label>
                Zip Code<span className="text-red-400">*</span>
              </Label>

              <Input
                {...field}
                onChange={(e) =>
                  setValue('zip_code', e.target.value, {
                    shouldValidate: true,
                  })
                }
                placeholder="Zip Code"
                className={cn(
                  errors.zip_code &&
                    'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                )}
              />
            </FormItem>
          )}
        />
      </div>
      <div className="flex flex-1 flex-row items-end gap-8 pb-2">
        <div className="flex flex-1 flex-row gap-2">
          <Label>City: </Label>
          {values?.zip_code && !values?.city && !checkingZipCode ? (
            <p className="text-sm text-[#F46A6A]"> Invalid Zip Code</p>
          ) : (
            <p className="text-sm text-gray-900">{values.city}</p>
          )}
        </div>
        <div className="flex flex-1 flex-row gap-2">
          <Label>State: </Label>
          {values?.zip_code && !values?.state && !checkingZipCode ? (
            <p className="text-sm text-[#F46A6A]"> Invalid Zip Code</p>
          ) : (
            <p className="text-sm text-gray-900">{values.state}</p>
          )}
        </div>
      </div>
      <FormField
        control={formState.control}
        name="use_different_legal_email"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center gap-2">
            <FormControl>
              <Checkbox
                checked={field.value}
                onChange={(e) => {
                  const checked = e.target.checked;
                  checked
                    ? reset(
                        {
                          ...values,
                          ownership: [values.ownership?.[0]],
                          use_different_legal_email: checked,
                        },
                        { keepDefaultValues: true },
                      )
                    : setValue('use_different_legal_email', checked);
                }}
                className="bg-white"
              />
            </FormControl>
            <span className="!mt-0 text-sm font-normal text-gray-700">
              Use Different Legal Email Address
            </span>
          </FormItem>
        )}
      />
      {values.use_different_legal_email && (
        <>
          <div className="flex flex-1 flex-row gap-8">
            <FormField
              control={formState.control}
              name="diff_street"
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Street<span className="text-red-400">*</span>
                  </Label>

                  <Input
                    {...field}
                    placeholder="Enter Street"
                    className={cn(
                      errors.diff_street &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormItem>
              )}
            />

            <FormField
              control={formState.control}
              name="diff_zip_code"
              render={({ field }) => (
                <FormItem className="flex flex-1 flex-col">
                  <Label>
                    Zip Code<span className="text-red-400">*</span>
                  </Label>

                  <Input
                    {...field}
                    placeholder="Zip Code"
                    className={cn(
                      errors.diff_zip_code &&
                        values?.diff_zip_code &&
                        'border-red-400 text-red-400 placeholder:text-red-400 hover:bg-red-50 hover:text-red-400',
                    )}
                  />
                </FormItem>
              )}
            />
          </div>
          <div className="flex flex-1 flex-row items-end gap-8 pb-2">
            <div className="flex flex-1 flex-row gap-2">
              <Label>City: </Label>
              {values?.diff_zip_code && !values?.diff_city && !checkingDiffZipCode ? (
                <p className="text-base text-[#F46A6A]"> Invalid Zip Code</p>
              ) : (
                <p className="text-zinc-600">{values.diff_city}</p>
              )}
            </div>
            <div className="flex flex-1 flex-row gap-2">
              <Label>State: </Label>
              {values?.diff_zip_code && !values?.diff_state && !checkingDiffZipCode ? (
                <p className="text-base text-[#F46A6A]"> Invalid Zip Code</p>
              ) : (
                <p className="text-zinc-600">{values.diff_state}</p>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default BusinessInformation;
// {values?.zip_code && !values?.city && !checkingZipCode && (
//     <p> Invalid</p>
//   )}
