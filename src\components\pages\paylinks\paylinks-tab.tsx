import { PageHeader, TopComponent, useDataGridView } from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { Paylink_list } from '@/graphql/declarations/paylinks';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { useState, useMemo } from 'react';
import { useLocationSelector } from '@/components/hooks';
import useDebounce from '@/components/hooks/useDebounce';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { Button } from 'flowbite-react';
import { FaPlus } from 'react-icons/fa6';
import { GatewayUniPayLinksOutputData } from '@/graphql/generated/graphql';
import { PaylinkAddModal } from './components/paylink-add-modal';
import { int2DecToFloat, moneyFormat } from '@/lib/utils';

export const PaylinksTab = () => {
  const router = useRouter();

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [seledtedLink, setSelectedLink] = useState(null);
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({
    onlyActive: true,
  });

  const {
    data,
    loading,
    refetch: refetchPaylinkList,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    maxVariables,
  } = useDataGridView({
    query: Paylink_list,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
  });

  const rows = useMemo(() => {
    const paylinksData = data?.gateway_payLinks?.data;
    if (!paylinksData) return [];
    return paylinksData.filter((item): item is NonNullable<typeof item> => item !== null);
  }, [data?.gateway_payLinks?.data]);

  const columns: Column<GatewayUniPayLinksOutputData>[] = [
    {
      key: 'id',
      header: 'ID',
      width: '80px',
      onClick: (row) => {
        router.push(`/dashboard/catalog/paylink/${row.id}`);
      },
    },
    {
      key: 'items',
      header: 'Products',
      width: '200px',
      renderCell: (row) => {
        return <>{row.items}</>;
      },
    },
    {
      key: 'total',
      header: 'Total',
      width: '200px',
      renderCell: (row) => {
        return <>{moneyFormat(int2DecToFloat(row.total))}</>;
      },
    },
    {
      key: 'createdAt',
      header: 'Created At',
      width: '200px',
      valueGetter: (row) => moment(row.createdAt).format('MM/DD/YYYY'),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'link',
      header: 'Link',
      width: '200px',
      valueGetter: (row) => {
        const payLink = new URL('/pay', window.location.origin);
        payLink.searchParams.set('paymentDataID', row?.id || '');
        payLink.searchParams.set('groupID', locationFilter?.id ?? '');
        return payLink.toString();
      },
      renderCell: (row) => {
        const payLink = new URL('/pay', window.location.origin);
        payLink.searchParams.set('paymentDataID', row?.id || '');
        payLink.searchParams.set('groupID', locationFilter?.id ?? '');

        return (
          <a href={payLink.toString()} target="_blank" rel="noreferrer">
            Open In New Tab
          </a>
        );
      },
    },
  ];

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Paylink_list,
      variables: maxVariables,
    });
    return result?.data?.gateway_payLinks?.data ?? [];
  };

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Payment Links" />
        <Button
          className="mt-[20px] h-[38px] p-0"
          color="blue"
          onClick={() => setIsAddModalOpen(true)}
        >
          <div className="flex items-center gap-x-3">
            <FaPlus className="text-xl" />
            <span>Add Payment Link</span>
          </div>
        </Button>
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
          <TopComponent value={searchValue} setValue={setSearchValue}>
            <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="paylinks" />
          </TopComponent>
        </div>
      </div>

      <DataGridView
        columns={columns}
        rows={rows}
        pageSize={pageSize}
        currentPage={currentPage}
        isLoading={loading || loadingGroupList}
        mode="server"
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        totalRecords={data?.gateway_payLinks?.page?.total ?? 0}
      />

      <PaylinkAddModal
        isOpen={isAddModalOpen}
        refetchPaylinkList={refetchPaylinkList}
        onClose={() => {
          setIsAddModalOpen(false);
        }}
      />
    </>
  );
};
