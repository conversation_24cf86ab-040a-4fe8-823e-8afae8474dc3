import { Label } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import PhoneInput, { CountryData } from 'react-phone-input-2';
import 'react-phone-input-2/lib/bootstrap.css';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { cn } from '@/lib/utils';

export type FormPhoneNumberRawProps = {
  name: string;
  helperText?: string;
  id: string;
  visible?: boolean;
  readOnly?: boolean;
  className?: string;
  maxLength?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (value: string) => void;
  onCountryChange?: (country: string) => void;
  label?: string;
  disabled?: boolean;
  value: string;
  error?: string;
  endAdornment?: React.ReactNode;
  onlyCountries?: string[];
  preferredCountries?: string[];
  excludeCountries?: string[];
  country?: string;
  autoFormat?: boolean;
  disableDropdown?: boolean;
  countryCodeEditable?: boolean;
  required?: boolean;
};

export const FormPhoneNumberRaw = ({
  id,
  name,
  label,
  disabled,
  helperText = '',
  value,
  className,
  visible = true,
  readOnly = false,
  maxLength,
  tooltip,
  endAdornment,
  autoFormat = true,
  disableDropdown = false,
  onlyCountries,
  country,
  excludeCountries,
  preferredCountries,
  countryCodeEditable,
  required,
  error,
  onChangeCallback = () => {},
  onCountryChange,
  ...props
}: FormPhoneNumberRawProps): JSX.Element => {
  return (
    <div className={cn('relative w-full', className)}>
      {label && (
        <Label htmlFor={id} className="mb-2 flex">
          {label} {required && '*'}
          {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
        </Label>
      )}
      <div>
        <div className="relative">
          <PhoneInput
            value={value}
            inputClass="!py-2.5 !text-sm !w-full bg-gray disabled:opacity-70"
            onChange={(phone, country) => {
              if (maxLength && phone.length > maxLength) return;
              onChangeCallback(phone);
              if (onCountryChange) {
                onCountryChange((country as CountryData).dialCode);
              }
            }}
            disabled={disabled}
            placeholder={`Enter ${label}`}
            country={country}
            onlyCountries={onlyCountries}
            countryCodeEditable={countryCodeEditable}
            disableDropdown={disableDropdown}
            autoFormat={autoFormat}
            excludeCountries={excludeCountries}
            preferredCountries={preferredCountries}
            {...props}
          />
          {endAdornment && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">{endAdornment}</div>
          )}
        </div>
        <HelperText color={error ? 'failure' : 'default'}>{error || helperText}</HelperText>
      </div>
    </div>
  );
};

export default FormPhoneNumberRaw;
