import { Button } from 'flowbite-react';
import { FormInput } from '@/components/globals';
import { FormProvider, UseFormReturn, useWatch } from 'react-hook-form';
import { message } from '@/components/shared/utils';
import { CategoryFormData } from '../utils';
import { CategoryDelete } from './category-delete';
import { capitalizeFirstLetter } from '@/components/shared/utils/strings';

interface CategoryFormProps {
  methods: UseFormReturn<CategoryFormData>;
  onSubmit: (data: CategoryFormData) => void;
  isEdit?: boolean;
  onDelete?: () => void;
}

export const CategoryForm = ({ methods, onSubmit, isEdit, onDelete }: CategoryFormProps) => {
  const {
    control,
    formState: { errors },
    setValue,
    getValues,
    setError,
    clearErrors,
  } = methods;

  const subCategories = useWatch({
    control: control,
    name: 'subCategories',
  });
  const handleAddSubCategory = () => {
    const newSubCategory = capitalizeFirstLetter(getValues('newSubCategory').trim());
    if (newSubCategory && !subCategories.includes(newSubCategory)) {
      setValue('subCategories', [...subCategories, newSubCategory]);
      setValue('newSubCategory', '');
      clearErrors('newSubCategory');
    } else {
      setError('newSubCategory', {
        message: `You already added ${newSubCategory}.`,
      });
    }
  };

  const handleRemoveSubCategory = (subCategory: string) => {
    setValue(
      'subCategories',
      subCategories.filter((item) => item !== subCategory),
    );
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <div className="w-1/2">
          <FormInput
            id="name"
            name="name"
            label="Category"
            rules={{ required: message.requiredField }}
          />
        </div>
        <FormInput
          id="description"
          name="description"
          label="Description"
          multiple
          type="textArea"
          rules={{ required: message.requiredField }}
        />
        <div className="w-1/2">
          <div className="ju flex items-end gap-2">
            <FormInput id="newSubCategory" name="newSubCategory" label="Add Sub-Category" />
            <Button
              color="success"
              className={`mb-${errors.newSubCategory ? '6' : '2'} flex-none`}
              onClick={handleAddSubCategory}
            >
              Add
            </Button>
          </div>
          <ul className="mt-2 flex-wrap space-y-2">
            {subCategories?.map((subCategory) => (
              <li key={subCategory} className="flex items-center gap-1">
                <button
                  onClick={() => handleRemoveSubCategory(subCategory)}
                  className="border border-red-500 px-1 text-red-500"
                >
                  ✕
                </button>
                <span className="rounded bg-gray-200 px-2 py-1">{subCategory}</span>
              </li>
            ))}
          </ul>
        </div>
        <div className="flex gap-2">
          <Button color="blue" type="submit">
            {isEdit ? 'Update' : 'Create'}
          </Button>

          {isEdit && <CategoryDelete cateory={methods.getValues()} onDelete={onDelete} />}
        </div>
      </form>
    </FormProvider>
  );
};
