export const cardDateValidation = (value: string) => {
  const currentDate = new Date();
  const cardMonth = parseInt(value.split('/')[0]);
  const cardYear = parseInt(value.split('/')[1]);
  if (cardMonth > 12) return false;
  if (currentDate.getFullYear() > cardYear) return false;
  if (currentDate.getFullYear() === cardYear && currentDate.getMonth() + 1 > cardMonth)
    return false;
  return true;
};
