export const AUTHSTORE = {
  get: () => {
    if (typeof window !== 'undefined') {
      return localStorage?.getItem('health-token') || '';
    }
  },
  set: (token: string) => {
    if (typeof window !== 'undefined') {
      return localStorage?.setItem('health-token', token);
    }
  },
  clear: () => {
    if (typeof window !== 'undefined') {
      return localStorage?.removeItem('health-token');
    }
  },
  session: () => {
    if (typeof window !== 'undefined') {
      // const hasSession = localStorage?.getItem('health-token');
      // const currentPath = window.location.pathname;
      // let redirect = hasSession ? currentPath : '/login';
      // if (currentPath === '/login') {
      //   redirect = '/dashboard';
      // }
      // if (currentPath !== redirect) {
      //   window.location.href = redirect;
      // }
    }
  },
};
