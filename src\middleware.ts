import { NextRequest, NextResponse } from 'next/server';

const hostRoute = [];
export async function middleware(request: NextRequest) {
  // const prevent = ["/favicon.ico", "/manifest.json", "/assets"];
  // const token = AUTHSTORE.get();
  const pathName = request.nextUrl.pathname;

  // Handle redirects from /dashboard/admin to /dashboard/manage
  if (pathName.startsWith('/dashboard/admin')) {
    const newPath = pathName.replace('/dashboard/admin', '/dashboard/manage');
    return NextResponse.redirect(new URL(newPath, request.url));
  }

  // const pathName = request.nextUrl.pathname;
  // if (typeof window !== "undefined") {
  //   if (pathName === "/") {
  //     return NextResponse.redirect(new URL("/login", request.url));
  //   }

  //   if (!token && !pathName.includes("/login")) {
  //     return NextResponse.redirect(new URL("/login", request.url));
  //   } else if (token && pathName.startsWith("/login")) {
  //     return NextResponse.redirect(new URL("/", request.url));
  //   }
  // }

  return NextResponse.next();
}

export const config = {
  matcher: ['/dashboard/:path*', '/account', '/'],
};
