.progress-bar {
  width: 100%;
  background-color: #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
}
.progress-bar-fill {
  height: 3px;
  background-color: #76c7c0;
  transition: width 0.3s ease-in-out;
}

#payment-form {
  #tsep-cardNumDiv,
  #tsep-datepickerDiv,
  #tsep-cvv2Div,
  #tsep-cardHolderNameDiv,
  #tsep-zipCodeDiv {
    margin-bottom: 10px;
    width: 100%;
  }

  .tsep-validation-error:not(:focus) {
    @apply border-red-500 text-sm text-red-500;
  }

  button {
    margin-top: 10px;
    width: 100%;
  }

  label {
    @apply mb-2 flex text-sm font-medium text-gray-900 dark:text-white;
  }

  input {
    @apply block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500;
  }

  .waitingField {
    & > *:not(:last-child) {
      display: none;
    }

    & > *:last-child {
      display: relative;
    }
  }
}
