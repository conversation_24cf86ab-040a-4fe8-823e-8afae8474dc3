import { useEffect } from 'react';
import { Modal } from 'flowbite-react';
import { CategoryForm } from './category-form';
import { useMutation, useQuery } from '@apollo/client';
import {
  Gateway_UpdateCategoryInputDataStatus,
  Gateway_UpdateCategoryDocument,
  Gateway_CategoryDocument,
} from '@/graphql/generated/graphql';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import { CategoryFormData, categoryFormDataDefault } from '../utils';
import { useForm } from 'react-hook-form';
import { SpinnerLoading } from '@/components/globals/spinner-loading';

type CategoryUpdateProps = {
  isOpen: boolean;
  onClose: () => void;
  refetchListPage: () => void;
  queryData: {
    categoryID: string;
    groupID: string;
  };
};

export const CategoryUpdate = ({
  isOpen,
  onClose,
  refetchListPage,
  queryData,
}: CategoryUpdateProps) => {
  const { categoryID, groupID } = queryData;

  const {
    data: categoryData,
    loading: categoryDataLaoding,
    error: categoryDataError,
    refetch: refetchCategoryData,
  } = useQuery(Gateway_CategoryDocument, {
    variables: {
      input: {
        data: {
          id: categoryID,
        },
        groupID,
      },
    },
    skip: !categoryID || !groupID,
  });

  const methods = useForm<CategoryFormData>();

  const { reset } = methods;

  useEffect(() => {
    if (categoryDataError) {
      toast.error(categoryDataError.message);
    }
  }, [categoryDataError]);

  useEffect(() => {
    refetchCategoryData();
  }, [categoryID]);

  useEffect(() => {
    const category = categoryData?.gateway_category;
    if (category) {
      reset({
        id: category.id ?? '',
        name: category.name ?? '',
        description: category.description ?? '',
        newSubCategory: '',
        subCategories: category.subCategory as string[],
      });
    }
  }, [categoryData]);

  const [updateCategoyMutation, { loading: updateCategoyLoading }] = useMutation(
    Gateway_UpdateCategoryDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successUpdate('Category'));
        reset({ ...categoryFormDataDefault });
        onClose();
        refetchListPage();
      },
      onError: (error) => {
        toast.error(message.api.errorUpdate('Category', error.message));
      },
    },
  );

  const onSubmitForm = async (data: CategoryFormData) => {
    try {
      await updateCategoyMutation({
        variables: {
          input: {
            groupID,
            data: {
              id: data.id ?? '',
              name: data.name,
              status: Gateway_UpdateCategoryInputDataStatus.Active,
              color: '',
              description: data.description,
              subCategory: data.subCategories,
              colors: data.subCategories,
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  const onDeleteCallBack = () => {
    onClose();
    refetchListPage();
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="4xl">
        <Modal.Header className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-blue-600">Update Category</h3>
        </Modal.Header>
        <Modal.Body>
          <SpinnerLoading isLoading={categoryDataLaoding || updateCategoyLoading} />
          <CategoryForm
            methods={methods}
            onSubmit={onSubmitForm}
            isEdit
            onDelete={onDeleteCallBack}
          />
        </Modal.Body>
      </Modal>
    </>
  );
};
