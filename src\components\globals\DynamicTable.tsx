'use client';

import { useState } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  X,
  Arrow<PERSON>pDown,
  Arrow<PERSON><PERSON>,
  ArrowDown,
  Search,
} from 'lucide-react';
import { useTable } from '../hooks/useTable';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { Propay_QueryDocument } from '@/graphql/generated/graphql';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Input } from '../ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';

interface TableConfig {
  displayName: string;
  key: string;
}

interface TableConfigurations {
  [tableName: string]: {
    displayFields: TableConfig[];
  };
}

interface TableOption {
  key: string;
  displayName: string;
}

interface DynamicTableProps {
  initialTables: TableOption[];
  tableConfigurations?: TableConfigurations;
  onRowClick?: (rowData: any) => void;
}

export function DynamicTable({
  initialTables,
  tableConfigurations,
  onRowClick,
}: DynamicTableProps) {
  const [selectedTable, setSelectedTable] = useState(initialTables[0]?.key);
  const [selectedRow, setSelectedRow] = useState<any | null>(null);
  const { data, columns, pagination, isLoading, error, setSort, setQuery, sort } = useTable(
    async ({ page, table, sort, query }) => {
      if (!table) {
        return {
          data: [],
          totalPages: 0,
        };
      }

      const searchString = query?.search ? query.search : '';

      const data = await apolloClient.mutate({
        mutation: Propay_QueryDocument,
        variables: {
          input: {
            table,
            page,
            queries:
              sort && columns.length
                ? JSON.stringify({
                    $or: columns.map((field) => ({
                      [field]: {
                        $regex: searchString,
                        $options: 'i', // case-insensitive
                      },
                    })),
                  })
                : undefined,
            sorts: query ? JSON.stringify(sort) : undefined,
          },
        },
      });

      const d = data.data?.propay_query;

      return {
        data: d.data,
        totalPages: d.pagination.totalPages,
      };
    },
    selectedTable,
  );

  const handleTableChange = (newTable: string) => {
    setSelectedTable(newTable);
    setSort({});
    setQuery({});
  };

  const handleColumnClick = (columnKey: string) => {
    setSort((prev) => {
      const currentDirection = prev[columnKey];
      // Clear other sorts and toggle direction: none -> asc -> desc -> none
      if (!currentDirection) return { [columnKey]: 1 };
      if (currentDirection === 1) return { [columnKey]: -1 };
      return {};
    });
  };

  const getSortIcon = (columnKey: string) => {
    const sortDirection = sort[columnKey];
    if (!sortDirection) return <ArrowUpDown className="h-4 w-4 text-muted-foreground/70" />;
    return sortDirection === 1 ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    );
  };

  const getDisplayColumns = () => {
    if (tableConfigurations?.[selectedTable]) {
      return tableConfigurations[selectedTable].displayFields;
    }
    return columns.map((col) => ({ displayName: col, key: col }));
  };

  const displayColumns = getDisplayColumns();

  const handleRowClick = (row: any) => {
    if (onRowClick) {
      onRowClick(row);
    } else {
      setSelectedRow(row);
    }
  };

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div>
      <div className="mb-4 space-y-4">
        <Select value={selectedTable} onValueChange={handleTableChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select a table" />
          </SelectTrigger>
          <SelectContent>
            {initialTables.map((table) => (
              <SelectItem key={table.key} value={table.key}>
                {table.displayName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="relative w-full">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search table..."
            className="pl-8"
            onChange={(e) => setQuery({ search: e.target.value })}
          />
        </div>
      </div>

      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <>
          <div className="relative w-full max-w-[75vw]">
            <div className="overflow-auto">
              <div className="w-max min-w-full rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {displayColumns.map((column) => (
                        <TableHead
                          key={column.key}
                          className="cursor-pointer hover:bg-muted"
                          onClick={() => handleColumnClick(column.key)}
                        >
                          <div className="flex items-center justify-between">
                            {column.displayName}
                            {getSortIcon(column.key)}
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.map((row, rowIndex) => (
                      <TableRow
                        key={rowIndex}
                        onClick={() => handleRowClick(row)}
                        className="cursor-pointer hover:bg-muted"
                      >
                        {displayColumns.map((column) => (
                          <TableCell key={column.key}>{row[column.key]}</TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-2 py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.previousPage()}
              disabled={!pagination.canPreviousPage}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.nextPage()}
              disabled={!pagination.canNextPage}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Only render Dialog if no onRowClick provided */}
          {!onRowClick && (
            <Dialog
              open={!!selectedRow}
              onOpenChange={(open: any) => !open && setSelectedRow(null)}
            >
              <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center justify-between">
                    <span>Row Details</span>
                    <button onClick={() => setSelectedRow(null)}>
                      <X className="h-4 w-4" />
                    </button>
                  </DialogTitle>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4">
                  {selectedRow &&
                    Object.entries(selectedRow).map(([key, value]) => (
                      <div key={key} className="space-y-1">
                        <div className="text-sm font-medium">{key}</div>
                        <div className="text-sm text-muted-foreground">
                          {value?.toString() || 'N/A'}
                        </div>
                      </div>
                    ))}
                </div>
              </DialogContent>
            </Dialog>
          )}
        </>
      )}
    </div>
  );
}
