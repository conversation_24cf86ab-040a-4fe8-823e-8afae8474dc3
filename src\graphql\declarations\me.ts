import { graphql } from '../generated';

export const Me = graphql(`
  query Me {
    authenticatedItem {
      ... on User {
        id
        name
        lastName
        displayName
        email
        phone
        title
        role
        createdAt
        groupsCount
        flag_canAffiliate
      }
    }
  }
`);

export const UpdateMe = graphql(`
  mutation UpdateUser($where: UserWhereUniqueInput!, $data: UserUpdateInput!) {
    updateUser(where: $where, data: $data) {
      id
    }
  }
`);

export const UpdatePassword = graphql(`
  mutation Authclient_changePassword($oldPassword: String!, $newPassword: String!) {
    authclient_changePassword(oldPassword: $oldPassword, newPassword: $newPassword)
  }
`);

export const RequestPasswordReset = graphql(`
  mutation Authclient_requestPasswordReset($email: String!) {
    authclient_requestPasswordReset(email: $email)
  }
`);

export const TokenPasswordReset = graphql(`
  mutation Authclient_resetPassword($token: String!, $password: String!) {
    authclient_resetPassword(token: $token, password: $password)
  }
`);

export const GetGHLSSOBindings = graphql(`
  query GHLSSOBindings {
    gHLSSOBindings {
      id
      locationID
      ghlUserID
      createdAt
    }
  }
`);

export const DeleteGHLSSOBinding = graphql(`
  mutation DeleteGHLSSOBinding($where: GHLSSOBindingWhereUniqueInput!) {
    deleteGHLSSOBinding(where: $where) {
      id
    }
  }
`);
