import {
  Gateway_CreateDiscountInput,
  Gateway_CreateDiscountInputDataApplicableFor,
  Gateway_CreateDiscountInputDataStatus,
  Gateway_CreateDiscountInputDataType,
  Gateway_UpdateDiscountInput,
  Gateway_UpdateDiscountInputDataApplicableFor,
  Gateway_UpdateDiscountInputDataStatus,
  Gateway_UpdateDiscountInputDataType,
  GatewayUniDiscountOutput,
  GatewayUniDiscountOutputStatus,
  GatewayUniDiscountOutputType,
} from '@/graphql/generated/graphql';
import { CartDiscountFormData } from './types';

export const cartDiscountFormDataDefault: CartDiscountFormData = {
  name: '',
  type: GatewayUniDiscountOutputType.Fixed,
  amount: '',
  status: false,
};

const getDiscount = (data: CartDiscountFormData) => {
  const isPercentage = data.type === Gateway_CreateDiscountInputDataType.Percentage;
  const amount = isPercentage ? data.amount.slice(0, -1) : data.amount.slice(1, data.amount.length);
  return Number(amount);
};

export const cardDiscountFormToCreateApiMapper = (
  data: CartDiscountFormData,
  groupID,
): Gateway_CreateDiscountInput => {
  const discount = getDiscount(data);
  return {
    groupID,
    data: {
      scope: '',
      applicableFor: Gateway_CreateDiscountInputDataApplicableFor.All,
      name: data.name,
      type: data.type as Gateway_CreateDiscountInputDataType,
      discount,
      status: data.status
        ? Gateway_CreateDiscountInputDataStatus.Active
        : Gateway_CreateDiscountInputDataStatus.Inactive,
    },
  };
};

export const cardDiscountApiToFormMapper = (
  data: GatewayUniDiscountOutput,
): CartDiscountFormData => {
  return {
    id: data.id ?? '',
    name: data.name ?? '',
    type: data.type as GatewayUniDiscountOutputType,
    amount: data.discount?.toString() ?? '',
    status: data.status === GatewayUniDiscountOutputStatus.Active,
  };
};

export const cardDiscountFormToUpdteApiMapper = (
  data: CartDiscountFormData,
  groupID,
): Gateway_UpdateDiscountInput => {
  const discount = getDiscount(data);
  return {
    groupID,
    data: {
      scope: '',
      id: data.id ?? '',
      name: data.name,
      applicableFor: Gateway_UpdateDiscountInputDataApplicableFor.All,
      type: data.type as Gateway_UpdateDiscountInputDataType,
      discount,
      status: data.status
        ? Gateway_UpdateDiscountInputDataStatus.Active
        : Gateway_UpdateDiscountInputDataStatus.Inactive,
    },
  };
};
