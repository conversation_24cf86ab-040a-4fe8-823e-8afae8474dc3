import { StatusChip } from '@/components/globals';
import LoadingButton from '@/components/globals/loading-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { GET_BANK_INFO_BY_ROUTING } from '@/graphql/declarations/processor-test';
import {
  GET_BANK_ACCOUNT_INFO,
  UPDATE_BANK_ACCOUNT_INFO,
} from '@/graphql/declarations/user-members';
import {
  Gateway_AccountFundsDocument,
  Otp_Auth_GenerateInputChannel,
} from '@/graphql/generated/graphql';
import { cn, moneyFormat } from '@/lib/utils';
import { useMutation, useQuery } from '@apollo/client';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Modal, TextInput, ToggleSwitch } from 'flowbite-react';
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';
import { FaRegFlag } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { z } from 'zod';
import { generateAuthenticatedOtp, verifyOtp } from '@/graphql/declarations/otpValues';
import { apolloClient } from '@/lib/graphql/ApolloClient';

const bankAccountSchema = z
  .object({
    bank_code: z.string().min(1, { message: 'Bank code is required' }),
    account_number: z.string().min(1, { message: 'Account number is required' }),
    account_number_reenter: z.string().min(1, { message: 'Account number reenter is required' }),
  })
  .refine((data) => data.account_number === data.account_number_reenter, {
    message: 'Account number and re-enter account number must match',
    path: ['account_number_reenter'],
  });

type BankAccountSchema = z.infer<typeof bankAccountSchema>;

export const AccountDetailsModal = ({ isOpen, onClose, accountData }) => {
  const [isPaymentEnabled, setIsPaymentEnabled] = useState(true);
  const [batchTime, setBatchTime] = useState('12:00 AM');
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showOtpModal, setShowOtpModal] = useState(false);
  const [otpSid, setOtpSid] = useState('');
  const [pendingBankUpdate, setPendingBankUpdate] = useState<{
    bank_code: string;
    account_number: string;
    account_number_reenter: string;
  } | null>(null);

  const { data: fundsAccount, loading: fundsLoading } = useQuery(Gateway_AccountFundsDocument, {
    variables: {
      input: {
        groupID: accountData.groupID,
        data: {
          id: undefined,
        },
      },
    },
  });

  const { data: bankAccountData } = useQuery(GET_BANK_ACCOUNT_INFO, {
    variables: {
      input: {
        groupId: accountData.groupID,
      },
    },
  });

  const [updateBankAccountInfo] = useMutation(UPDATE_BANK_ACCOUNT_INFO);

  const {
    register,
    handleSubmit,
    formState: { isDirty, isSubmitting, dirtyFields, errors },
    watch,
  } = useForm<BankAccountSchema>({
    values: {
      bank_code: bankAccountData?.processor_account_status?.bank.bank_code ?? '',
      account_number: bankAccountData?.processor_account_status?.bank.last4
        ? `**** **** ${bankAccountData?.processor_account_status?.bank.last4}`
        : '',
      account_number_reenter: bankAccountData?.processor_account_status?.bank.last4
        ? `**** **** ${bankAccountData?.processor_account_status?.bank.last4}`
        : '',
    },
    resolver: zodResolver(bankAccountSchema),
  });

  const { data: bankData } = useQuery(GET_BANK_INFO_BY_ROUTING, {
    variables: {
      input: {
        code: watch('bank_code'),
      },
    },
    skip: !watch('bank_code'),
  });

  const handleUpdateBankAccountInfo: SubmitHandler<BankAccountSchema> = async (data) => {
    try {
      // First generate OTP
      const otpResult = await apolloClient.mutate({
        mutation: generateAuthenticatedOtp,
        variables: {
          input: {
            purpose: 'funds_update',
            metadata: accountData.groupID,
            channel: Otp_Auth_GenerateInputChannel.Sms,
          },
        },
      });

      if (!otpResult.data?.otp_auth_generate?.sid) {
        toast.error('Failed to generate OTP');
        return;
      }

      // Store the OTP sid and pending update data
      setOtpSid(otpResult.data.otp_auth_generate.sid);
      setPendingBankUpdate(data);
      setShowOtpModal(true);
    } catch (error) {
      console.error('Failed to initiate bank account update:', error);
      toast.error('Failed to initiate bank account update');
    }
  };

  const handleVerifyOtp = async (code: string) => {
    try {
      // Verify OTP
      const verifyResult = await apolloClient.mutate({
        mutation: verifyOtp,
        variables: {
          input: {
            sid: otpSid,
            code,
          },
        },
      });

      if (!verifyResult.data?.otp_verify?.status) {
        toast.error('Invalid OTP code');
        return;
      }

      if (!pendingBankUpdate) {
        console.error('No pending bank update found');
        toast.error('No pending bank update found');
        return;
      }

      // If OTP is verified, proceed with the bank account update
      const updateResult = await updateBankAccountInfo({
        variables: {
          input: {
            groupId: accountData.groupID,
            data: {
              bankInfo: {
                accountNumber: pendingBankUpdate.account_number,
                nameOnAccount: '',
                bankName: bankData?.processor_tst_bank_routing?.data?.name ?? '',
                routingNumber: pendingBankUpdate.bank_code,
              },
              otpSid,
            },
          },
        },
      });

      if (updateResult.errors) {
        console.error('Failed to update bank account info', updateResult.errors);
        toast.error('Failed to update bank account info');
        return;
      }

      toast.success('Bank account info updated successfully');
      setShowOtpModal(false);
      setPendingBankUpdate(null);
      setOtpSid('');
    } catch (error) {
      console.error('Failed to update bank account:', error);
      toast.error('Failed to update bank account');
    }
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="5xl">
        <Modal.Header className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text text-xl font-semibold text-blue-600">
              {accountData.friendlyName}{' '}
            </h3>
            <StatusChip variant="success" label={accountData.accountStatus} big />
          </div>
        </Modal.Header>
        <Modal.Body>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <Card className="mb-1">
                <PaymentDetail label="Date Added" value={accountData.dateAdded} />

                <PaymentDetail
                  label="Account Status"
                  value={
                    accountData.paymentType.charAt(0).toUpperCase() +
                    accountData.paymentType.slice(1)
                  }
                />
                <div className="flex flex-col gap-3 rounded border-t py-2">
                  <div className="font-semibold text-gray-700">Summary</div>
                  {!fundsLoading ? (
                    <>
                      <PaymentDetail
                        label="This Month Sales"
                        className="space-x-4 pl-4"
                        value={moneyFormat(
                          parseFloat(
                            fundsAccount?.gateway_accountFunds
                              ?.card_month_to_date_processed_amount || '0',
                          ) / 100,
                        )}
                      />
                      <PaymentDetail
                        className="space-x-4 pl-4"
                        label="Closed Transactions"
                        value={moneyFormat(
                          parseFloat(
                            fundsAccount?.gateway_accountFunds?.pending_balance_amount || '0',
                          ) / 100,
                        )}
                      />
                      <PaymentDetail
                        label="Available Balance"
                        className="space-x-4 pl-4"
                        value={moneyFormat(
                          parseFloat(
                            fundsAccount?.gateway_accountFunds?.available_balance_amount || '0',
                          ) / 100,
                        )}
                      />
                    </>
                  ) : (
                    <p className="animate-pulse">Loadng...</p>
                  )}
                </div>

                <div className="flex flex-col gap-3 rounded border-t py-2">
                  <div className="font-semibold text-gray-700">Limits</div>

                  {!fundsLoading ? (
                    <>
                      <PaymentDetail
                        label="[Card] Per Transaction"
                        className="space-x-4 pl-4"
                        value={moneyFormat(
                          parseFloat(
                            fundsAccount?.gateway_accountFunds?.card_transaction_limit_amount ||
                              '0',
                          ) / 100,
                        )}
                      />
                      <PaymentDetail
                        label="[Card] Monthly"
                        className="space-x-4 pl-4"
                        value={moneyFormat(
                          parseFloat(
                            fundsAccount?.gateway_accountFunds?.card_monthly_limit_amount || '0',
                          ) / 100,
                        )}
                      />
                      <PaymentDetail
                        label="[Card] Monthly Limit Percenetage"
                        className="space-x-4 pl-4"
                        value={
                          Number(
                            fundsAccount?.gateway_accountFunds
                              ?.card_month_to_date_processed_percentage,
                          ) *
                            100 +
                          '%'
                        }
                      />
                      <PaymentDetail
                        label="[Bank Transfer] Per Transaction"
                        className="space-x-4 pl-4"
                        value={moneyFormat(
                          parseFloat(
                            fundsAccount?.gateway_accountFunds
                              ?.bank_transfer_per_transaction_limit_amount || '0',
                          ) / 100,
                        )}
                      />
                      <PaymentDetail
                        label="[Bank Transfer] Monthly"
                        className="space-x-4 pl-4"
                        value={moneyFormat(
                          parseFloat(
                            fundsAccount?.gateway_accountFunds
                              ?.bank_transfer_monthly_limit_amount || '0',
                          ) / 100,
                        )}
                      />
                      <PaymentDetail
                        label="[Bank Transfer] Monthly Limit Percenetage"
                        className="space-x-4 pl-4"
                        value={
                          Number(
                            fundsAccount?.gateway_accountFunds
                              ?.bank_transfer_month_to_date_processed_percentage,
                          ) *
                            100 +
                          '%'
                        }
                      />
                    </>
                  ) : (
                    <p className="animate-pulse">Loadng...</p>
                  )}
                </div>
                {/* <PaymentDetail
                  label="Bank Transfer Per Transaction Limit"
                  value={moneyFormat(
                    int2DecToFloatFromString(
                      fundsAccount?.gateway_accountFunds
                        ?.bank_transfer_per_transaction_limit_amount,
                    ),
                  )}
                />
                <PaymentDetail
                  label="Bank Transfer Monthly Limit Amount"
                  value={moneyFormat(
                    int2DecToFloatFromString(
                      fundsAccount?.gateway_accountFunds?.bank_transfer_monthly_limit_amount,
                    ),
                  )}
                /> */}
              </Card>
            </div>
            <div>
              <Card>
                <DetailItem label="Business Name" value={accountData.dbaName} />
                <DetailItem label="Email" value={accountData.email} />
                <DetailItem label="Phone" value={accountData.phone} />
                <DetailItem
                  label="Country"
                  value={
                    <span className="flex items-center">
                      <FaRegFlag className="mr-2 text-red-500" />
                      {accountData.country}
                    </span>
                  }
                />
                <DetailItem label="Billing Address" value={accountData.billingAddress} />
              </Card>
              <Card>
                <div className="mt-6 flex items-center justify-between">
                  <span className="font-medium">Payment Acceptance:</span>
                  <ToggleSwitch
                    checked={isPaymentEnabled}
                    onChange={setIsPaymentEnabled}
                    label={isPaymentEnabled ? 'Enable' : 'Disable'}
                  />
                </div>
                {/* <div className="mt-4 flex items-center justify-between">
                  <span className="font-medium">Settle Batch Time:</span>
                  <div className="flex items-center">
                    <TextInput
                      type="time"
                      value={batchTime}
                      onChange={(e) => setBatchTime(e.target.value)}
                      className="w-32"
                    />
                    <FaCircleCheck color="green" size={20} className="ml-2" />
                    <Button color="blue" size="sm" className="ml-2" onClick={handleUpdate}>
                      Update
                    </Button>
                  </div>
                </div> */}
              </Card>
            </div>
            <div className="col-span-2">
              <form
                onSubmit={handleSubmit(handleUpdateBankAccountInfo)}
                className="grid grid-cols-2 gap-4 rounded-lg border border-gray-200 p-6 shadow-md"
              >
                <h3 className="col-span-2 text-center text-xl font-semibold text-blue-600">
                  Bank Account
                </h3>
                <div className="flex flex-col gap-4">
                  <div className="flex flex-col gap-2">
                    <Label className="text-gray-700">Routing # *</Label>
                    <div className="5 flex gap-0">
                      <Input
                        {...register('bank_code')}
                        defaultValue={
                          bankAccountData?.processor_account_status?.bank.bank_code ?? ''
                        }
                        placeholder="Enter your Bank's Routing Number"
                        className={cn(errors.bank_code && 'border-red-500')}
                      />
                      {errors.bank_code && (
                        <p className="text-sm text-red-500">{errors.bank_code.message}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Label className="text-gray-700">Account Number *</Label>
                    <div className="5 flex flex-col gap-0">
                      <Input
                        {...register('account_number')}
                        type={dirtyFields.account_number ? 'password' : 'text'}
                        defaultValue={
                          bankAccountData?.processor_account_status?.bank.last4
                            ? `**** **** ${bankAccountData?.processor_account_status?.bank.last4}`
                            : ''
                        }
                        placeholder="Enter your Bank Account Number"
                        className={cn(errors.account_number && 'border-red-500')}
                      />
                      {errors.account_number && (
                        <p className="text-sm text-red-500">{errors.account_number.message}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Label className="text-gray-700">Re-enter Account Number *</Label>
                    <div className="5 flex flex-col gap-0">
                      <Input
                        {...register('account_number_reenter')}
                        type={dirtyFields.account_number_reenter ? 'password' : 'text'}
                        defaultValue={
                          bankAccountData?.processor_account_status?.bank.last4
                            ? `**** **** ${bankAccountData?.processor_account_status?.bank.last4}`
                            : ''
                        }
                        placeholder="Re-enter your Bank Account Number"
                        className={cn(errors.account_number_reenter && 'border-red-500')}
                      />
                      {errors.account_number_reenter && (
                        <p className="text-sm text-red-500">
                          {errors.account_number_reenter.message}
                        </p>
                      )}
                    </div>
                  </div>
                  {/* <div className="flex flex-col gap-2">
                    <Label className="text-gray-700">Name on Account *</Label>
                    <Input placeholder="Enter your Name on Account" />
                  </div> */}
                </div>
                <div className="flex flex-col gap-2">
                  <p className="font-medium text-gray-700">Bank Name</p>
                  <p>{bankData?.processor_tst_bank_routing?.data?.name ?? '-'}</p>
                </div>
                <div className="col-span-2 flex justify-center">
                  <LoadingButton
                    disabled={!isDirty}
                    variant="primary"
                    isLoading={isSubmitting}
                    type="submit"
                  >
                    Update
                  </LoadingButton>
                </div>
              </form>
            </div>
          </div>
        </Modal.Body>
      </Modal>

      <OtpVerificationModal
        isOpen={showOtpModal}
        onClose={() => setShowOtpModal(false)}
        onVerify={handleVerifyOtp}
      />

      <ConfirmationModal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={() => {
          setShowConfirmation(false);
          onClose();
        }}
        locationName={accountData.dbaName}
      />
    </>
  );
};

const OtpVerificationModal = ({ isOpen, onClose, onVerify }) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      if (value !== '' && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setError('');
    try {
      await onVerify(code.join(''));
    } catch (err) {
      setError('Failed to verify OTP');
    }
    setSubmitting(false);
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="md">
      <Modal.Header>Verify OTP</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          <p className="text-center">Please enter the 6-digit code sent to your phone</p>
          <div className="flex justify-center space-x-2">
            {code.map((digit, index) => (
              <Input
                key={index}
                id={`otp-${index}`}
                type="text"
                maxLength={1}
                className="h-12 w-12 text-center text-2xl"
                value={digit}
                onChange={(e) => handleCodeChange(index, e.target.value)}
              />
            ))}
          </div>
          {error && <p className="text-center text-sm text-red-500">{error}</p>}
          <div className="flex justify-center">
            <LoadingButton
              isLoading={submitting}
              onClick={handleSubmit}
              variant={'primary'}
              disabled={code.join('').length !== 6 || submitting}
            >
              {submitting ? 'Verifying...' : 'Verify'}
            </LoadingButton>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

const ConfirmationModal = ({ isOpen, onClose, onConfirm, locationName }) => {
  const [confirmationText, setConfirmationText] = useState('');

  return (
    <Modal show={isOpen} onClose={onClose} size="md">
      <Modal.Header className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">Are you sure?</h3>
      </Modal.Header>
      <Modal.Body>
        <div className="mb-4 border-l-4 border-orange-500 bg-orange-100 p-4 text-orange-700">
          <p>Unexpected bad things will happen if you don't read this!</p>
        </div>
        <p className="mb-4 text-center">
          All transaction processing will be suspended immediately for all payment plans and
          terminals. Batches will not be effected. Are you sure you want to disable payments for{' '}
          {locationName}?
        </p>
        <p className="mb-2 text-center">Please type in the location name to confirm.</p>
        <TextInput
          type="text"
          value={confirmationText}
          onChange={(e) => setConfirmationText(e.target.value)}
          className="mb-4"
        />
        <Button
          color="failure"
          className="w-full"
          onClick={onConfirm}
          disabled={confirmationText !== locationName}
        >
          I understand, disable payments
        </Button>
      </Modal.Body>
    </Modal>
  );
};

const DetailItem = ({ label, value }) => (
  <div className="flex justify-between border-b py-1 last:border-b-0">
    <span className="font-semibold text-gray-700">{label}</span>
    <span className="text-gray-600">{value}</span>
  </div>
);

const PaymentDetail = ({
  label,
  value,
  className,
}: {
  className?: string;
  label: string;
  value: React.ReactNode;
}) => (
  <div className={`flex py-1${className}`}>
    <span className="font-semibold text-gray-700">{label}</span>
    <span className="text-gray-600">
      {': '}
      {value}
    </span>
  </div>
);
