'use client';

import { useState, useEffect, useMemo } from 'react';
import { useMutation } from '@apollo/client';
import { Search } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import useDebounce from '@/components/hooks/useDebounce';
import { Propay_TablesDocument, Propay_QueryDocument } from '@/graphql/generated/graphql';
import { Column } from '@/components/globals';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import DataGridView from '@/components/globals/sortable-table/data-grid-view';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

export default function PropayDataPage() {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [isSortAsc, setIsSortAsc] = useState(true);
  const [sortKey, setSortKey] = useState('');
  const { toast } = useToast();
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  // For storing selected row data for modal
  const [selectedRowData, setSelectedRowData] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // For table selection
  const [fetchTables, { data: tablesData, loading: tablesLoading }] =
    useMutation(Propay_TablesDocument);
  const [selectedTable, setSelectedTable] = useState<string | null>(null);

  // Load available tables
  useEffect(() => {
    fetchTables();
  }, [fetchTables]);

  // Set default table when tables are loaded
  useEffect(() => {
    if (tablesData?.propay_tables?.tables?.length && !selectedTable) {
      setSelectedTable(tablesData.propay_tables.tables[0] as string);
    }
  }, [tablesData, selectedTable]);

  // Fetch table data
  const [fetchTableData, { data: propayData, loading, error }] = useMutation(Propay_QueryDocument);

  // Refetch data when params change
  useEffect(() => {
    if (!selectedTable) return;

    const searchObj = debouncedSearchQuery
      ? {
          $or: getSearchableFields().map((field) => ({
            [field]: { $regex: debouncedSearchQuery, $options: 'i' },
          })),
        }
      : undefined;

    const sortObj = sortKey ? { [sortKey]: isSortAsc ? 1 : -1 } : undefined;

    fetchTableData({
      variables: {
        input: {
          table: selectedTable,
          page: currentPage,
          pageSize: pageSize,
          queries: searchObj ? JSON.stringify(searchObj) : undefined,
          sorts: sortObj ? JSON.stringify(sortObj) : undefined,
        },
      },
    });
  }, [
    selectedTable,
    currentPage,
    pageSize,
    sortKey,
    isSortAsc,
    debouncedSearchQuery,
    fetchTableData,
  ]);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Failed to load data',
        description: error.message,
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  // Get field configurations for the selected table
  const getTableConfig = () => {
    const tableConfigurations: Record<string, any[]> = {
      tierreport: [
        { key: 'TierID', displayName: 'TierID' },
        { key: 'TierName', displayName: 'Tier Name' },
        { key: 'SponsorBank', displayName: 'Sponsor Bank' },
        { key: 'IsEnabled', displayName: 'Is Enabled' },
        { key: 'CCPerTranLimit', displayName: 'CC Per Transaction Limit' },
        { key: 'CCHoldDays', displayName: 'CC Hold Days' },
      ],
      'affiliate signup detail': [
        { key: 'AccountNumber', displayName: 'Account Number' },
        { key: 'DoingBusinessAs', displayName: 'Doing Business As' },
        { key: 'WithdrawalRoutingNumber', displayName: 'Withdrawal Routing Number' },
        { key: 'Tier', displayName: 'Tier' },
        { key: 'TaxId', displayName: 'Tax ID' },
        { key: 'Email', displayName: 'Email' },
        { key: 'AffiliateName', displayName: 'Affiliate Name' },
      ],
      comprehensivetransactiondetail: [
        { key: 'TransDescription', displayName: 'Transaction Description' },
        { key: 'AuthAmount', displayName: 'Auth Amount' },
        { key: 'TransactionInfoId', displayName: 'Transaction Info ID' },
        { key: 'AccountNum', displayName: 'Account Number' },
        { key: 'BatchId', displayName: 'Batch ID' },
        { key: 'BusinessLegalName', displayName: 'Business Legal Name' },
        { key: 'InvNum', displayName: 'Invoice Number' },
        { key: 'FundDate', displayName: 'Fund Date' },
        { key: 'ResponseCodeDescription', displayName: 'Response Code Description' },
      ],
      submittedinterchangequalificationreport: [
        { key: 'Affiliation', displayName: 'Affiliation' },
        { key: 'TransactionInfoId', displayName: 'Transaction Info ID' },
        { key: 'AccountNum', displayName: 'Account Number' },
        { key: 'Amount', displayName: 'Amount' },
        { key: 'DoingBusinessAs', displayName: 'Doing Business As' },
        { key: 'InvoiceNumber', displayName: 'Invoice Number' },
        { key: 'SettlementDate', displayName: 'Settlement Date' },
        { key: 'TranNumber', displayName: 'Transaction Number' },
      ],
    };

    if (tableConfigurations?.[selectedTable as string]) {
      return tableConfigurations[selectedTable as string];
    }
    let columns = Object.keys(propayData?.propay_query?.data[0] ?? {});
    columns = columns.filter((col) => !['_id', 'createdAt', 'updatedAt'].includes(col));
    return columns.map((col) => ({ displayName: col, key: col }));
  };

  // Get searchable fields for the selected table
  const getSearchableFields = () => {
    const config = getTableConfig();
    return config.map((field) => field.key);
  };

  // Create dynamic columns based on selected table
  const columns: Column[] = useMemo(() => {
    if (!selectedTable) return [];

    const tableConfig = getTableConfig();
    return tableConfig.map((field) => ({
      key: field.key,
      header: field.displayName,
      sortable: true,
      onServerSort: (key) => {
        setSortKey(key);
        setIsSortAsc((prev) => (sortKey === key ? !prev : true));
      },
      onClick: (row) => {
        setSelectedRowData(row);
        setIsModalOpen(true);
      },
    }));
  }, [selectedTable, sortKey, propayData]);

  // Prepare rows data
  const rowsData = useMemo(() => {
    return propayData?.propay_query?.data || [];
  }, [propayData]);

  const totalRecords = propayData?.propay_query?.pagination?.total || 0;

  // Handle table change
  const handleTableChange = (value: string) => {
    setSelectedTable(value);
    setCurrentPage(1);
    setSortKey('');
    setIsSortAsc(true);
    setSearchValue('');
  };

  // Format key-value pairs for display in modal
  const formatKeyValuePairs = (data: any) => {
    if (!data) return [];

    return Object.entries(data).map(([key, value]) => {
      // Format the key for display
      const formattedKey = key
        .replace(/([A-Z])/g, ' $1') // Add space before capital letters
        .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter

      return { key: formattedKey, value: value as string };
    });
  };

  if (tablesLoading && !selectedTable) {
    return <div>Loading...</div>;
  }

  return (
    <div className="mx-5">
      <div className="my-4 flex items-center justify-between">
        <Select value={selectedTable || ''} onValueChange={handleTableChange}>
          <SelectTrigger className="w-[250px]">
            <SelectValue placeholder="Select a table" />
          </SelectTrigger>
          <SelectContent>
            {tablesData?.propay_tables?.tables
              ?.filter((table) => !!table)
              .map((table) => (
                <SelectItem key={table as string} value={table as string}>
                  {(table as string).charAt(0).toUpperCase() + (table as string).slice(1)}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>

        <form
          className="flex w-full max-w-sm items-center justify-end space-x-2"
          onSubmit={(e) => e.preventDefault()}
        >
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              placeholder="Search"
              className="pl-8"
            />
          </div>
          <Button type="submit">Search</Button>
        </form>
      </div>

      <DataGridView
        columns={columns}
        rows={rowsData}
        pageSize={pageSize}
        currentPage={currentPage}
        isLoading={loading}
        mode="server"
        onPageChange={setCurrentPage}
        onPageSizeChange={setPageSize}
        totalRecords={totalRecords}
      />

      {/* Detail Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto p-8">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              {selectedTable && selectedTable.charAt(0).toUpperCase() + selectedTable.slice(1)}{' '}
              Details
            </DialogTitle>
          </DialogHeader>

          <div className="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
            {formatKeyValuePairs(selectedRowData).map((item, index) => (
              <div key={index} className="border-b pb-2">
                <p className="text-sm font-medium text-gray-500">{item.key}</p>
                <p className="mt-1">{item.value || 'N/A'}</p>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
