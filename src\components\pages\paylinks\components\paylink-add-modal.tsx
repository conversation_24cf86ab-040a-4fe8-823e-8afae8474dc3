import { <PERSON><PERSON>, But<PERSON> } from 'flowbite-react';
import { useForm, FormProvider } from 'react-hook-form';
import { message } from '@/components/shared/utils';
import {
  Gateway_CreatePayLinkInputDataFormPricingTaxType,
  Paylink_CreateDocument,
} from '@/graphql/generated/graphql';
import { useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { useLocationSelector } from '@/components/hooks';
import { SpinnerLoading } from '@/components/globals/spinner-loading';

import { PaylinkForm } from './paylink-form';

export type PayLinkForm = {
  allowExtraDiscount: boolean;
  allowTip: boolean;
  allowEdit: boolean;
  onSuccessURL?: string;
  onFailureURL?: string;
  disableCustomAddress?: boolean;
  disableCustomPayment?: boolean;
  disablePreselectCard?: boolean;
  disableACH?: boolean;
  disableCC?: boolean;
  validUntil?: string;
  prefilledAddress?: {
    email?: string;
    phone?: string;
    country?: string;
    state?: string;
    city?: string;
    zip?: string;
    address?: string;
    nameOnCard?: string;
  };
  pricing: {
    taxType: 'fixed' | 'percentage';
    tax: number;
    lineItems: {
      productId: string;
      amount: number;
    }[];
  };
};

type PaylinkAddModalProps = {
  isOpen: boolean;
  onClose: () => void;
  refetchPaylinkList: () => void;
};

export const PaylinkAddModal = ({ isOpen, onClose, refetchPaylinkList }: PaylinkAddModalProps) => {
  const { locationFilter } = useLocationSelector({ readonly: true });

  const methods = useForm<PayLinkForm>({});

  const { reset } = methods;

  const [createLinkMutation, { loading: createProductLoading }] = useMutation(
    Paylink_CreateDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Payment link'));
        reset();
        onClose();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Payment link', error.message));
      },
    },
  );

  const onSubmitForm = async (data: PayLinkForm) => {
    try {
      let taxVal =
        typeof data.pricing.tax === 'string' ? parseFloat(data.pricing.tax) : data.pricing.tax;
      if (!taxVal) {
        taxVal = 0;
      }

      await createLinkMutation({
        variables: {
          input: {
            groupID: locationFilter?.id ?? '',
            data: {
              autoToken: true,
              save: true,
              form: {
                allowExtraDiscount: data.allowExtraDiscount,
                allowTip: data.allowTip,
                allowEdit: data.allowEdit,
                onSuccessURL: data.onSuccessURL,
                onFailureURL: data.onFailureURL,
                disableCustomAddress: data.disableCustomAddress,
                disableCustomPayment: data.disableCustomPayment,
                disablePreselectCard: data.disablePreselectCard,
                disableACH: data.disableACH,
                disableCC: data.disableCC,
                validUntil: data.validUntil,
                prefilledAddress: data.prefilledAddress,
                pricing: {
                  taxType: data.pricing.taxType as Gateway_CreatePayLinkInputDataFormPricingTaxType,
                  tax: taxVal,
                  lineItems: data.pricing.lineItems.map((item) => {
                    return {
                      productId: item.productId,
                      amount: parseInt(item.amount as any),
                    };
                  }),
                },
              },
            },
          },
        },
      });
      refetchPaylinkList();
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="4xl">
      <Modal.Header>
        <div className="text-xl font-semibold">Add PayLink</div>
      </Modal.Header>
      <Modal.Body>
        <SpinnerLoading isLoading={createProductLoading} />
        <FormProvider {...methods}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              // prevent passthrough of the event to the form
              e.stopPropagation();
              methods.handleSubmit(onSubmitForm)(e);
            }}
            className="space-y-4"
          >
            <PaylinkForm />
            <div className="flex justify-end gap-5">
              <Button type="submit" color="blue">
                Create Payment Link
              </Button>
            </div>
          </form>
        </FormProvider>
      </Modal.Body>
    </Modal>
  );
};
