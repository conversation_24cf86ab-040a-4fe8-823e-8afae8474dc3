import { ApolloLink, Observable } from '@apollo/client';
import { ApolloClient, InMemoryCache } from '@apollo/experimental-nextjs-app-support';
import { mockGraphQLData } from '@/lib/mock/graphql-mock-data';
import { env } from 'next-runtime-env';

// Mock resolvers for GraphQL operations
const mockResolvers = {
  Query: {
    // User queries
    authenticatedItem: () => ({
      __typename: 'User',
      ...mockGraphQLData.user,
    }),

    // Groups queries
    groups: (_: any, variables: any) => {
      const { take = 10, skip = 0 } = variables;
      const groups = mockGraphQLData.groups.slice(skip, skip + take);
      return groups;
    },

    groupsCount: () => mockGraphQLData.groups.length,

    // Transactions queries
    gateway_transactions: (_: any, variables: any) => {
      const { input } = variables;
      const { pageSize = 10, page = 1 } = input;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      return {
        data: mockGraphQLData.transactions.slice(startIndex, endIndex),
        page: {
          total: mockGraphQLData.transactions.length,
          range: {
            from: startIndex + 1,
            to: Math.min(endIndex, mockGraphQLData.transactions.length),
          },
          page,
          pageSize,
        },
      };
    },

    gateway_transaction: (_: any, variables: any) => {
      const { input } = variables;
      const {
        data: { transactionID },
      } = input;
      return (
        mockGraphQLData.transactions.find((t) => t.transactionID === transactionID) ||
        mockGraphQLData.transactions[0]
      );
    },

    // Products queries
    gateway_products: (_: any, variables: any) => {
      const { input } = variables;
      const { pageSize = 10, page = 1 } = input;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      return {
        data: mockGraphQLData.products.slice(startIndex, endIndex),
        page: {
          total: mockGraphQLData.products.length,
          range: {
            from: startIndex + 1,
            to: Math.min(endIndex, mockGraphQLData.products.length),
          },
          page,
          pageSize,
        },
      };
    },

    gateway_product: (_: any, variables: any) => {
      const { input } = variables;
      const { productId } = input;
      return (
        mockGraphQLData.products.find((p) => p.id === productId) || mockGraphQLData.products[0]
      );
    },

    // Categories queries
    gateway_categories: (_: any, variables: any) => {
      const { input } = variables;
      const { pageSize = 10, page = 1 } = input;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      return {
        data: mockGraphQLData.categories.slice(startIndex, endIndex),
        page: {
          total: mockGraphQLData.categories.length,
          range: {
            from: startIndex + 1,
            to: Math.min(endIndex, mockGraphQLData.categories.length),
          },
          page,
          pageSize,
        },
      };
    },

    gateway_category: (_: any, variables: any) => {
      const { input } = variables;
      const { categoryId } = input;
      return (
        mockGraphQLData.categories.find((c) => c.id === categoryId) || mockGraphQLData.categories[0]
      );
    },

    // Disputes queries
    gateway_disputes: (_: any, variables: any) => {
      const { input } = variables;
      const { pageSize = 10, page = 1 } = input;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;

      return {
        data: mockGraphQLData.disputes.slice(startIndex, endIndex),
        page: {
          total: mockGraphQLData.disputes.length,
          range: {
            from: startIndex + 1,
            to: Math.min(endIndex, mockGraphQLData.disputes.length),
          },
          page,
          pageSize,
        },
      };
    },

    gateway_dispute: (_: any, variables: any) => {
      const { input } = variables;
      const {
        data: { caseID },
      } = input;
      return (
        mockGraphQLData.disputes.find((d) => d.caseID === caseID) || mockGraphQLData.disputes[0]
      );
    },

    // Dashboard queries
    dashboard_get_summary: () => mockGraphQLData.dashboardSummary,
    dashboard_location_summary: () => mockGraphQLData.locationSummary,

    // Affiliation queries
    affiliation_get: () => mockGraphQLData.affiliation,

    // Form utilities
    processor_aur_zip_search: (_: any, variables: any) => {
      const { input } = variables;
      const { zip } = input;
      return {
        item: {
          zip,
          latitude: 40.7128,
          longitude: -74.006,
          city: 'New York',
          state: 'NY',
          country: 'US',
        },
      };
    },

    processor_aur_bank_routing: (_: any, variables: any) => {
      const { input } = variables;
      const { routing } = input;
      return {
        items: [
          {
            routing,
            bank: 'Chase Bank',
          },
        ],
        count: 1,
      };
    },

    processor_aur_mcc: (_: any, variables: any) => {
      return {
        items: [
          { id: '1', mccCode: '5999', description: 'Miscellaneous Retail' },
          { id: '2', mccCode: '7299', description: 'Miscellaneous Services' },
        ],
        count: 2,
      };
    },

    // GoHighLevel integration
    ghl_auth_getIntegrationDetails: (_: any, variables: any) => {
      return {
        locationName: 'Mock GHL Location',
        locationId: 'mock-location-id',
        accessToken: 'mock-access-token',
      };
    },
  },

  Mutation: {
    // Auth mutations
    authclient_authenticate: (_: any, variables: any) => {
      return 'mock-session-token';
    },

    authclient_register: (_: any, variables: any) => {
      return 'mock-session-token';
    },

    authclient_changePassword: () => true,
    authclient_requestPasswordReset: () => true,

    // User mutations
    updateUser: (_: any, variables: any) => {
      return { id: mockGraphQLData.user.id };
    },

    // Group mutations
    deleteGroup: (_: any, variables: any) => {
      const { where } = variables;
      return { id: where.id };
    },

    // OTP mutations
    otp_generate: (_: any, variables: any) => {
      return {
        sid: 'mock-otp-sid',
        to: variables.input.to,
      };
    },

    otp_verify: (_: any, variables: any) => {
      return {
        sid: variables.input.sid,
        status: 'verified',
      };
    },

    // Payment mutations
    gateway_computeCheckout: (_: any, variables: any) => {
      return {
        breakdown: {
          discount: 0,
          directDiscount: 0,
          actualDiscount: 0,
          tax: 1000,
          shipping: 500,
          shippingDiscount: 0,
          shippingDirectDiscount: 0,
          shippingActualDiscount: 0,
          fees: 300,
          actualFees: 300,
          tip: 0,
          subtotal: 10000,
          subscriptionTotal: 0,
          rawTotal: 11800,
          total: 11800,
          expectedTotal: 11800,
        },
        lineItems: [],
        discountBreakdown: [],
      };
    },

    // GoHighLevel mutations
    ghl_auth_setupCommit: (_: any, variables: any) => {
      return {
        success: true,
        message: 'Integration setup completed successfully',
      };
    },
  },
};

// Create a custom mock link that handles GraphQL operations
const createMockLink = () => {
  return new ApolloLink((operation) => {
    return new Observable((observer) => {
      const { operationName, variables } = operation;

      // Simulate network delay
      setTimeout(() => {
        try {
          let result;

          // Handle queries
          if (operation.query.definitions[0]?.kind === 'OperationDefinition') {
            const operationType = operation.query.definitions[0].operation;

            if (operationType === 'query') {
              result = handleQuery(operationName, variables);
            } else if (operationType === 'mutation') {
              result = handleMutation(operationName, variables);
            }
          }

          observer.next({ data: result });
          observer.complete();
        } catch (error) {
          observer.error(error);
        }
      }, 100); // 100ms delay to simulate network
    });
  });
};

// Handle GraphQL queries
const handleQuery = (operationName: string | undefined, variables: any) => {
  switch (operationName) {
    case 'Me':
      return { authenticatedItem: { __typename: 'User', ...mockGraphQLData.user } };

    case 'Groups':
    case 'GetGroupList':
    case 'GroupList':
      const { take = 10, skip = 0, where } = variables || {};
      let filteredGroups = mockGraphQLData.groups;

      // Apply where filters if provided
      if (where) {
        if (where.processorStatus?.equals) {
          filteredGroups = filteredGroups.filter(
            (group) => group.processorStatus === where.processorStatus.equals,
          );
        }
        if (where.AND) {
          // Handle AND conditions
          where.AND.forEach((condition: any) => {
            if (condition.processorStatus?.equals) {
              filteredGroups = filteredGroups.filter(
                (group) => group.processorStatus === condition.processorStatus.equals,
              );
            }
          });
        }
      }

      const groups = filteredGroups.slice(skip, skip + take);
      return { groups, groupsCount: filteredGroups.length };

    case 'Notifications':
      const { take: notifTake = 10, skip: notifSkip = 0 } = variables || {};
      const notifications = mockGraphQLData.notifications.slice(notifSkip, notifSkip + notifTake);
      return { notifications };

    case 'Dashboard_get_summary':
      return { dashboard_get_summary: mockGraphQLData.dashboardSummary };

    case 'Dashboard_location_summary':
      return { dashboard_location_summary: mockGraphQLData.locationSummary };

    case 'Gateway_transactions':
      const { input: transInput } = variables || {};
      const { pageSize = 10, page = 1 } = transInput || {};
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return {
        gateway_transactions: {
          data: mockGraphQLData.transactions.slice(startIndex, endIndex),
          page: {
            total: mockGraphQLData.transactions.length,
            range: {
              from: startIndex + 1,
              to: Math.min(endIndex, mockGraphQLData.transactions.length),
            },
            page,
            pageSize,
          },
        },
      };

    case 'Gateway_transaction':
      const { input: singleTransInput } = variables || {};
      const { data: { transactionID } = {} } = singleTransInput || {};
      return {
        gateway_transaction:
          mockGraphQLData.transactions.find((t) => t.transactionID === transactionID) ||
          mockGraphQLData.transactions[0],
      };

    case 'Gateway_disputes':
      const { input: disputeInput } = variables || {};
      const { pageSize: disputePageSize = 10, page: disputePage = 1 } = disputeInput || {};
      const disputeStartIndex = (disputePage - 1) * disputePageSize;
      const disputeEndIndex = disputeStartIndex + disputePageSize;
      return {
        gateway_disputes: {
          data: mockGraphQLData.disputes.slice(disputeStartIndex, disputeEndIndex),
          page: {
            total: mockGraphQLData.disputes.length,
            range: {
              from: disputeStartIndex + 1,
              to: Math.min(disputeEndIndex, mockGraphQLData.disputes.length),
            },
            page: disputePage,
            pageSize: disputePageSize,
          },
        },
      };

    case 'Gateway_dispute':
      const { input: singleDisputeInput } = variables || {};
      const { data: { caseID } = {} } = singleDisputeInput || {};
      return {
        gateway_dispute:
          mockGraphQLData.disputes.find((d) => d.caseID === caseID) || mockGraphQLData.disputes[0],
      };

    case 'Gateway_batches':
      const { input: batchesInput } = variables || {};
      const { data: batchesData } = batchesInput || {};
      const { page: batchesPageData } = batchesData || {};
      const { pageSize: batchesPageSize = 10, page: batchesPage = 1 } = batchesPageData || {};
      const batchesStartIndex = (batchesPage - 1) * batchesPageSize;
      const batchesEndIndex = batchesStartIndex + batchesPageSize;

      return {
        gateway_batches: {
          data: mockGraphQLData.batches.slice(batchesStartIndex, batchesEndIndex),
          page: {
            total: mockGraphQLData.batches.length,
            range: {
              from: batchesStartIndex + 1,
              to: Math.min(batchesEndIndex, mockGraphQLData.batches.length),
            },
            page: batchesPage,
            pageSize: batchesPageSize,
          },
        },
      };

    case 'Gateway_batch':
      const { input: singleBatchInput } = variables || {};
      const { data: { batchID } = {} } = singleBatchInput || {};
      return {
        gateway_batch:
          mockGraphQLData.batches.find((b) => b.batchID === batchID) || mockGraphQLData.batches[0],
      };

    case 'Gateway_deposits':
      const { input: depositsInput } = variables || {};
      const { data: depositsData } = depositsInput || {};
      const { page: depositsPageData } = depositsData || {};
      const { pageSize: depositsPageSize = 10, page: depositsPage = 1 } = depositsPageData || {};
      const depositsStartIndex = (depositsPage - 1) * depositsPageSize;
      const depositsEndIndex = depositsStartIndex + depositsPageSize;

      return {
        gateway_deposits: {
          data: mockGraphQLData.deposits.slice(depositsStartIndex, depositsEndIndex),
          page: {
            total: mockGraphQLData.deposits.length,
            range: {
              from: depositsStartIndex + 1,
              to: Math.min(depositsEndIndex, mockGraphQLData.deposits.length),
            },
            page: depositsPage,
            pageSize: depositsPageSize,
          },
        },
      };

    case 'Gateway_deposit':
      const { input: singleDepositInput } = variables || {};
      const { data: { depositID } = {} } = singleDepositInput || {};
      return {
        gateway_deposit:
          mockGraphQLData.deposits.find((d) => d.depositID === depositID) ||
          mockGraphQLData.deposits[0],
      };

    case 'Dashboard_Get_Summary':
      return { dashboard_get_summary: mockGraphQLData.dashboardSummary };

    case 'Dashboard_Location_Summary':
      return { dashboard_location_summary: mockGraphQLData.locationSummary };

    case 'Affiliation_get':
      return { affiliation_get: mockGraphQLData.affiliation };

    default:
      console.warn(`Unhandled GraphQL query: ${operationName}`);
      return {};
  }
};

// Handle GraphQL mutations
const handleMutation = (operationName: string | undefined, variables: any) => {
  switch (operationName) {
    case 'Authclient_authenticate':
      return { authclient_authenticate: 'mock-session-token' };

    case 'Authclient_login':
      // Mock successful login response
      return {
        authclient_login: {
          __typename: 'ClientItemAuthenticationWithPasswordSuccess',
          sessionToken: 'mock-session-token-' + Date.now(),
        },
      };

    case 'Authclient_register':
      // Update mock user data with registration info
      if (variables?.firstName && variables?.lastName && variables?.email) {
        mockGraphQLData.user.name = variables.firstName;
        mockGraphQLData.user.lastName = variables.lastName;
        mockGraphQLData.user.displayName = `${variables.firstName} ${variables.lastName}`;
        mockGraphQLData.user.email = variables.email;
        if (variables.phoneNumber) {
          mockGraphQLData.user.phone = variables.phoneNumber;
        }
        if (variables.title) {
          mockGraphQLData.user.title = variables.title;
        }
      }
      // Mock successful registration
      return { authclient_register: true };

    case 'LeadsLogin':
      // Mock leads login for demo page
      return {
        leads_login: {
          sessionToken: 'mock-leads-session-token-' + Date.now(),
          email: variables?.input?.email || '<EMAIL>',
          password: 'mock-password',
        },
      };

    case 'Mutation':
      // Handle leads_initialize
      return { leads_initialize: 'mock-initialization-token' };

    case 'UpdateUser':
      return { updateUser: { id: mockGraphQLData.user.id } };

    case 'Otp_generate':
      return { otp_generate: { sid: 'mock-otp-sid', to: variables?.input?.to } };

    case 'Otp_verify':
      return { otp_verify: { sid: variables?.input?.sid, status: 'verified' } };

    case 'Authclient_changePassword':
      return { authclient_changePassword: true };

    case 'Authclient_requestPasswordReset':
      return { authclient_requestPasswordReset: true };

    case 'Authclient_logout':
    case 'Logout':
      // Mock successful logout
      return { authclient_logout: true };

    default:
      console.warn(`Unhandled GraphQL mutation: ${operationName}`);
      return {};
  }
};

// Create mock Apollo client
export const createMockApolloClient = () => {
  return new ApolloClient({
    link: createMockLink(),
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        errorPolicy: 'all',
      },
      query: {
        errorPolicy: 'all',
      },
    },
  });
};

// Check if we should use mock mode
export const shouldUseMockMode = () => {
  return env('NEXT_PUBLIC_MOCK_MODE') === 'true' || env('NEXT_PUBLIC_DEMO') === 'true';
};
