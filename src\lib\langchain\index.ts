// import { base64ToBlob, getFileTypeFromBase64 } from '@/lib/utils/helpers';
// import { CSVLoader } from '@langchain/community/document_loaders/fs/csv';
// import { DocxLoader } from '@langchain/community/document_loaders/fs/docx';
// import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
// import { Document } from '@langchain/core/documents';
// import { ChatPromptTemplate } from '@langchain/core/prompts';
// import { ChatOpenAI } from '@langchain/openai';
// import { z } from 'zod';

// // Usage in your loadDocuments function
// export async function loadDocuments(base64Files: string[]) {
//   try {
//     const documents = await Promise.all(
//       base64Files.map(async (base64File) => {
//         const blob = base64ToBlob(base64File);

//         // Get file type from base64 string
//         const fileType = getFileTypeFromBase64(base64File) || 'application/octet-stream';

//         console.log('blob', blob);
//         console.log('fileType', fileType);

//         let loader;
//         switch (fileType) {
//           case 'pdf':
//             loader = new PDFLoader(blob);
//             break;
//           case 'csv':
//             loader = new CSVLoader(blob);
//             break;
//           case 'docx':
//             loader = new DocxLoader(blob);
//             break;
//           case 'doc':
//             loader = new DocxLoader(blob, {
//               type: 'doc',
//             });
//             break;
//           default:
//             throw new Error(`Unsupported file type: ${fileType}`);
//         }

//         return await loader.load();
//       }),
//     );

//     return documents as Document[];
//   } catch (error) {
//     return {
//       error: error instanceof Error ? error.message : 'Unknown error',
//     };
//   }
// }

// export async function validateBankStatements(base64Files: string[]) {
//   try {
//     const documents = await loadDocuments(base64Files);

//     console.log('documents', documents);

//     if ('error' in documents) {
//       return {
//         error: documents.error,
//       };
//     }

//     const bankStatementsContent = documents.map((document) => document.pageContent).join('\n\n');

//     console.log('bankStatementsContent', bankStatementsContent);

//     const formattingModel = new ChatOpenAI({
//       model: 'gpt-4-turbo-preview',
//       temperature: 0.2,
//       apiKey: process.env.OPENAI_API_KEY,
//     }).withStructuredOutput(
//       z.object({
//         formattedTable: z.string(),
//       }),
//     );

//     const validationModel = new ChatOpenAI({
//       model: 'gpt-4-turbo-preview',
//       temperature: 0.2,
//       apiKey: process.env.OPENAI_API_KEY,
//     }).withStructuredOutput(
//       z.object({
//         isValid: z.boolean(),
//         explanation: z.string(),
//       }),
//     );

//     // First format the data
//     const formattingPrompt = ChatPromptTemplate.fromTemplate(`
//       You are a data formatting expert. Format the following content into a clear table in markdown format with these columns:
//       Date, Description, Credit, Debit, Balance

//       Some important rules:
//       1. If the text has no spaces between fields (like "DateDetailsCreditsDebitsBalance"), identify and separate them properly
//       2. Ensure dates are in a consistent format
//       3. Align numbers properly in Credit/Debit/Balance columns
//       4. Remove any irrelevant headers or footers
//       5. If amounts have no separators (like "1000"), format them with proper separators (like "1,000.00")

//       Content to format:
//       {bankStatementsContent}

//       Return only the formatted table in markdown format, nothing else
//     `);

//     // Then validate the formatted data
//     const validationPrompt = ChatPromptTemplate.fromTemplate(`
//       You are a financial document validator specialized in identifying legitimate bank statements.

//       Analyze the following formatted bank statement data and determine if it is valid. A valid bank statement typically includes:

//       1. Bank name or financial institution information
//       2. Account holder's name and account information
//       3. Statement period or date
//       4. Transaction history with:
//          - Dates
//          - Descriptions
//          - Deposits/credits
//          - Withdrawals/debits
//          - Running balance
//       5. Opening and closing balances

//       Formatted bank statement content:
//       {formattedContent}

//       Determine if this is a valid bank statement and provide an explanation for your decision.

//       If the document is missing multiple key components listed above or appears to be a different type of document:
//       - Respond with isValid: false
//       - Explain which key components are missing and why it doesn't appear to be a legitimate bank statement

//       If the document contains most or all of these components and appears to be a legitimate bank statement:
//       - Respond with isValid: true
//       - List the key components found that confirm it is a legitimate bank statement
//     `);

//     // Chain the prompts
//     const formattingResult = await formattingPrompt.pipe(formattingModel).invoke({
//       bankStatementsContent,
//     });

//     const validationResult = await validationPrompt.pipe(validationModel).invoke({
//       formattedContent: formattingResult.formattedTable,
//     });

//     return {
//       isValid: validationResult.isValid,
//       explanation: validationResult.explanation,
//       formattedTable: formattingResult.formattedTable,
//     };
//   } catch (error) {
//     return {
//       error: error instanceof Error ? error.message : 'Unknown error',
//     };
//   }
// }
