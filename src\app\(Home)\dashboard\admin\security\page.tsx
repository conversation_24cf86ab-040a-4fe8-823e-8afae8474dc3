'use client';
import { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DollarSign } from 'lucide-react';
import { useLocationSelector } from '@/components/hooks';
import { <PERSON><PERSON>, Card } from 'flowbite-react';

export default function SecuritySettings() {
  const [transactionLimits, setTransactionLimits] = useState(false);
  const [requireCVV, setRequireCVV] = useState(false);
  const [requireAddressVerify, setRequireAddressVerify] = useState(false);
  const [maxAmount, setMaxAmount] = useState('');
  const { locationSelectorElement } = useLocationSelector({
    onlyActive: true,
  });

  return (
    <div className="mx-auto space-y-6 py-0">
      <div className="w-1/4">{locationSelectorElement}</div>

      <Card className="space-y-4">
        <div>
          <div className="flex items-center">
            <div className="flex items-center space-x-2">
              <Switch
                id="transaction-limits"
                checked={transactionLimits}
                onCheckedChange={setTransactionLimits}
              />
              <Label htmlFor="transaction-limits">Enable</Label>
            </div>
            <Label htmlFor="transaction-limits" className="text-base font-medium">
              Transaction Amount Limits
            </Label>

            <div className="ml-8 flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-gray-500" />
              <div className="relative flex-grow">
                <Input
                  type="number"
                  value={maxAmount}
                  onChange={(e) => setMaxAmount(e.target.value)}
                  placeholder="Max Transaction/Refund Ticket Amount"
                  className="w-72"
                  disabled={!transactionLimits}
                />
              </div>
              <p className="pl-2 text-sm text-gray-500">
                Recommended: below 20% of monthly transaction amount
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center">
          <div className="flex items-center space-x-2">
            <Switch id="require-cvv" checked={requireCVV} onCheckedChange={setRequireCVV} />
            <Label htmlFor="require-cvv">Enable</Label>
          </div>
          <Label htmlFor="require-cvv" className="text-base font-medium">
            Require CVV
          </Label>
        </div>

        <div className="flex items-center">
          <div className="flex items-center space-x-2">
            <Switch
              id="require-address-verify"
              checked={requireAddressVerify}
              onCheckedChange={setRequireAddressVerify}
            />
            <Label htmlFor="require-address-verify">Enable</Label>
          </div>
          <Label htmlFor="require-address-verify" className="text-base font-medium">
            Require Address Verify
          </Label>
        </div>
      </Card>

      <Button className="w-full sm:w-auto" color="blue">
        Save changes
      </Button>
    </div>
  );
}
