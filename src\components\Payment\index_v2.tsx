'use client';

import { useForm, FormProvider } from 'react-hook-form';
import { emailPatternRegex, message } from '@/components/shared/utils';
import { useEffect, useState } from 'react';
import styles from './index.module.css';
import Image from 'next/image';
import { cn, int2DecToFloat, moneyFormat, moneyFormatString } from '@/lib/utils';
import { TSEPHostedTokenizerComponent } from '@/components/payments/tsep/tokenizer';
import { axiosClient } from '@/lib/axios';
import type { ProductInput } from './restloader';
import { PaymentSuccess } from './PaymentSuccess';
import { PaymentLoading } from './PaymentLoading';
import { PriceBreakdown } from './PriceBreakdown';
import { OrderItems } from './OrderItems';
import StateCountryForms from '../shared/components/stateCountryFormSelect';
import { ACHHostedComponent } from '@/components/payments/ach-local';
import { CardGLPHostedComponent } from '../payments/glpv2';
import { SubscriptionItems } from './SubscriptionItems';
import { useQuery } from '@apollo/client';
import { GET_BANK_INFO_BY_ROUTING } from '@/graphql/declarations/processor-test';
import { Button, Modal } from 'flowbite-react';
import { FormInput } from '../globals';
export interface PaymentDiscount {
  code: string;
  amount: number;
}

export interface PaymentLineItem {
  id: string;
  name: string;
  description: string;
  price: number;
  amount: number;
  total: number;

  isRecurring: boolean;
  recurringMode: string;
  recurringFrequency: number;
  recurringInterval: number;
  recurringSetupFee: number;
  recurringTotalCycles: number;
  recurringTrialDays: number;
}

export interface PaymentCalculations {
  'Original Price': number;
  'Processing Surcharge': number;
  Shipping: number;
  Tax: number;
  Tip: number;
  Discount: number;
  subscription: number;
}

export interface PaymentFunctionArgs {
  fullName: string;
  companyName: string;
  email: string;
  phoneNumber: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  cardToken: string;
  cvc: string;
  expiryDate: string;
  ip?: string;
  accountNumber?: string;
  routingNumber?: string;
  accountType?: string;
  holderType?: string;
  gpEcomm?: string;
  paymentMethod: 'card' | 'ach' | 'gpecomm' | '';
}

export interface PaymentFunctionReturn {
  status: string;
  transactionID: string;
}
export interface PaymentFormProps {
  paymentData: ProductInput.ProductInput;
  initialValues?: Partial<PaymentFunctionArgs>;
  initialLineItems?: PaymentLineItem[];
  initialDiscounts?: PaymentDiscount[];
  initialCalculations?: Partial<PaymentCalculations>;
  initialTotal?: number;
  initialSubscriptionTotal?: number;
  onBeforePayment?: () => Promise<boolean>;
  onAfterPayment?: (args: {
    success: boolean;
    transactionID: string;
    onSuccessUrl?: string;
    onFailureUrl?: string;
  }) => void;
  onSetDiscounts?: (discounts: PaymentDiscount[]) => void;
  customPaymentFunction?: (formData: PaymentFunctionArgs) => Promise<PaymentFunctionReturn>;
  onTipChange?: (tip: number) => void;
  customAmounts?: { [key: string]: number };
  setCustomAmounts?: (customAmounts: { [key: string]: number }) => void;
  discountCodes?: string[];
  tipAmount?: number;
  paymentType?: string;
  setPaymentType?: (paymentType: string) => void;
  options?: {
    allowEdit?: boolean;
    allowExtraDiscount?: boolean;
    allowTip?: boolean;
    disabledCC?: boolean;
    disabledACH?: boolean;
    onSuccessUrl?: string;
    onFailureUrl?: string;
  };
}

function ConfirmTransactionModal(args: {
  showModal: boolean;
  handleCancel: () => void;
  handleConfirm: () => void;
  isProcessEntry: boolean;
  total?: number;
  calculations?: Partial<PaymentCalculations>;
  watch: any;
}) {
  // console.log(args.calculations);
  const { showModal, handleCancel, handleConfirm, isProcessEntry, watch, calculations } = args;

  const { data: bankData } = useQuery(GET_BANK_INFO_BY_ROUTING, {
    variables: {
      input: {
        code: watch['routingNumber'],
      },
    },
    skip: !watch['routingNumber'],
  });

  const getBankInfo = () => {
    if (watch.routingNumber) {
      return bankData?.processor_tst_bank_routing?.data.name || 'Unknown Bank';
    }
    return watch.brand || 'Unknown Card';
  };

  const formatAmount = (amount: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(int2DecToFloat(parseFloat(amount)) || 0);
  };

  const getPaymentMethod = () => {
    if (watch.routingNumber) {
      return `ACH Payment - ${getBankInfo()}`;
    } else if (watch.cardToken) {
      return `Card Payment - ${watch.brand}`;
    } else if (watch.gpEcomm) {
      return `Card Payment`;
    }
  };

  return (
    <Modal show={showModal} onClose={handleCancel}>
      <Modal.Header>Confirm Transaction</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          <div className="text-lg font-semibold">Transaction Details:</div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Payment Method:</span>
              <span>{getPaymentMethod()}</span>
            </div>

            {/* Add breakdown section */}
            {calculations && (
              <div className="space-y-1 border-t border-gray-200 pt-2">
                {calculations['Original Price'] !== undefined &&
                  calculations['Original Price'] > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>Original Price:</span>
                      <span>{formatAmount(calculations['Original Price']?.toString() || '0')}</span>
                    </div>
                  )}
                {calculations['Processing Surcharge'] !== undefined &&
                  calculations['Processing Surcharge'] > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>Processing Fee:</span>
                      <span>
                        {formatAmount(calculations['Processing Surcharge']?.toString() || '0')}
                      </span>
                    </div>
                  )}
                {calculations['Shipping'] !== undefined && calculations['Shipping'] > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Shipping:</span>
                    <span>{formatAmount(calculations['Shipping']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Tax'] !== undefined && calculations['Tax'] > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Tax:</span>
                    <span>{formatAmount(calculations['Tax']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Tip'] !== undefined && calculations['Tip'] > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Tip:</span>
                    <span>{formatAmount(calculations['Tip']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Discount'] !== undefined && calculations['Discount'] > 0 && (
                  <div className="flex justify-between text-sm text-red-600">
                    <span>Discount:</span>
                    <span>-{formatAmount(calculations['Discount']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['Discount'] !== undefined && calculations['Discount'] > 0 && (
                  <div className="flex justify-between text-sm text-red-600">
                    <span>Discount:</span>
                    <span>-{formatAmount(calculations['Discount']?.toString() || '0')}</span>
                  </div>
                )}
                {calculations['subscription'] !== undefined && calculations['subscription'] > 0 && (
                  <div className="flex justify-between text-sm text-blue-600">
                    <span>Total Subscriptions:</span>
                    <span>{formatAmount(calculations['subscription']?.toString() || '0')}</span>
                  </div>
                )}
              </div>
            )}

            <div className="border-t pt-2">
              <div className="flex justify-between font-bold">
                <span>Total:</span>
                <span>{formatAmount((args.total ?? 0).toString())}</span>
              </div>
            </div>
          </div>
          <p className="mt-4 text-sm text-gray-600">
            Are you sure you want to proceed with this transaction?
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button color="gray" onClick={handleCancel}>
          Cancel
        </Button>
        <Button
          color={isProcessEntry ? 'blue' : 'warning'}
          className="w-full"
          onClick={handleConfirm}
        >
          Proceed
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

const PaymentForm = ({
  paymentData,
  initialValues = {},
  initialLineItems = [],
  initialDiscounts = [],
  initialCalculations = {},
  initialTotal = 0,
  initialSubscriptionTotal = 0,
  onSetDiscounts,
  onBeforePayment,
  onAfterPayment,
  customPaymentFunction,
  options,
  onTipChange,
  customAmounts,
  setCustomAmounts,
  discountCodes: di,
  tipAmount: ta,
  setPaymentType,
}: PaymentFormProps) => {
  const methods = useForm<PaymentFunctionArgs>({
    defaultValues: {
      fullName: initialValues.fullName || '',
      cardToken: '',
      cvc: '',
      expiryDate: '',
      email: initialValues.email || '',
      companyName: initialValues.companyName || '',
      street: initialValues.street || '',
      city: initialValues.city || '',
      state: initialValues.state || 'AZ',
      country: initialValues.country || 'US',
      zipCode: initialValues.zipCode || '',
      paymentMethod: 'gpecomm',
      accountNumber: '',
      routingNumber: '',
      accountType: '',
      holderType: '',
    },
  });

  const [discountInput, setDiscountInput] = useState('');
  const [discounts, setDiscounts] = useState<PaymentDiscount[]>([]);

  useEffect(() => {
    setDiscounts(initialDiscounts);
  }, [initialDiscounts]);

  const calculations = initialCalculations;

  const total = initialTotal;
  // const [lineItems, setLineItems] = useState<PaymentLineItem[]>(initialLineItems);

  const lineItems = initialLineItems;

  const handleAddDiscount = (e) => {
    e.preventDefault();
    // Placeholder function - you can implement API call here
    const mockDiscount = { code: discountInput, amount: 0 }; // Mock amount
    if (discountInput && !discounts.some((d) => d.code === discountInput)) {
      setDiscounts([...discounts, mockDiscount]);
      setDiscountInput('');
      onSetDiscounts?.([...discounts, mockDiscount]);
    }
  };

  const getIP = async () => {
    const resp = await fetch('https://api.ipify.org?format=json');
    const data = await resp.json();
    return data.ip;
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const [paymentMethod, setPaymentMethod] = useState<'card' | 'ach' | 'gpecomm' | ''>('');

  useEffect(() => {
    let availabelOptions: string[] = [];
    if (!options?.disabledCC) {
      availabelOptions.push('gpecomm');
    }
    if (!options?.disabledACH) {
      availabelOptions.push('ach');
    }

    setPaymentMethod(availabelOptions[0] || ('' as any));
  }, []);

  useEffect(() => {
    setPaymentType?.(paymentMethod);
    methods.setValue('paymentMethod', paymentMethod);
  }, [paymentMethod]);

  const standardPayment = async (formData: PaymentFunctionArgs): Promise<PaymentFunctionReturn> => {
    const data = await axiosClient.post('/api/gateway/createTokenizedManualEntry', {
      groupID: paymentData.groupID,
      token: paymentData.token,
      data: {
        paymentDataID: paymentData.data.paymentDataID,
        paymentData: paymentData.data.paymentData,
        customerInfo: {
          nameOnCard: formData.fullName,
          email: formData.email,
          phoneNumber: formData.phoneNumber,
          billingAddress: formData.street,
          billingCity: formData.city,
          billingState: formData.state,
          billingZip: formData.zipCode,
          billingCountry: formData.country,
          paymentType: paymentMethod,
          gpecomm:
            paymentMethod === 'gpecomm'
              ? {
                  id: formData.gpEcomm,
                }
              : undefined,
          card:
            paymentMethod === 'card'
              ? {
                  cardToken: formData.cardToken,
                  cvc: formData.cvc,
                  expiryDate: formData.expiryDate,
                }
              : undefined,
          ach:
            paymentMethod === 'ach'
              ? {
                  accountNumber: formData.accountNumber,
                  routingNumber: formData.routingNumber,
                  accountType: formData.accountType,
                  holderType: formData.holderType,
                }
              : undefined,
        },
        dynamicData: {
          discountCodes: di,
          tip: {
            amount: ta,
            type: 'percentage',
          },
          quantityAmounts: Object.keys(customAmounts ?? {}).map((key) => ({
            id: key,
            quantity: (customAmounts ?? {})[key],
          })),
        },
      },
    });

    return {
      status: data.data.status,
      transactionID: data.data.transaction,
    };
  };

  const submitPayment = async (formData) => {
    try {
      setIsSubmitting(true);
      setPaymentStatus('idle');
      setErrorMessage('');

      if (onBeforePayment) {
        const shouldContinue = await onBeforePayment();
        if (!shouldContinue) {
          setIsSubmitting(false);
          onAfterPayment?.({
            success: false,
            transactionID: '',
            onSuccessUrl: options?.onSuccessUrl,
            onFailureUrl: options?.onFailureUrl,
          });
          return;
        }
      }

      // Your existing payment processing logic here
      const ip = await getIP();
      // ... implement your payment processing ...

      let data: PaymentFunctionReturn | undefined;
      formData.ip = ip;
      if (customPaymentFunction) {
        data = await customPaymentFunction(formData);
      } else {
        data = await standardPayment(formData);
      }

      if (data.status !== 'CAPTURED') {
        throw new Error('Payment failed. Please try again.');
      }

      setPaymentStatus('success');
      onAfterPayment?.({
        success: true,
        transactionID: data.transactionID,
        onSuccessUrl: options?.onSuccessUrl,
        onFailureUrl: options?.onFailureUrl,
      });
    } catch (error) {
      console.error('Payment failed:', error);
      setPaymentStatus('error');
      // @ts-ignore
      setErrorMessage(error?.response?.data?.message || 'Payment failed. Please try again.');
      onAfterPayment?.({
        success: false,
        transactionID: '',
        onSuccessUrl: options?.onSuccessUrl,
        onFailureUrl: options?.onFailureUrl,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const watchedValues = methods.watch();
  const cardToken = watchedValues.cardToken;
  const cardTokenReady = cardToken !== '' && cardToken !== undefined;
  const [achTokenReady, setAchTokenReady] = useState(false);
  const canPay =
    paymentMethod === 'card'
      ? cardTokenReady
      : paymentMethod === 'ach'
        ? achTokenReady
        : paymentMethod === 'gpecomm'
          ? Boolean(watchedValues.gpEcomm)
          : false;

  const [selectedTip, setSelectedTip] = useState<number>(0);
  const [customTip, setCustomTip] = useState<string>('');

  useEffect(() => {
    if (selectedTip > 0) {
      onTipChange?.(selectedTip);
    } else if (customTip) {
      const tipAmount = parseFloat(customTip);
      onTipChange?.(tipAmount);
    } else {
      onTipChange?.(0);
    }
  }, [selectedTip, customTip, onTipChange]);

  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const handlePayButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowConfirmModal(true);
  };

  const handleConfirmPayment = () => {
    setShowConfirmModal(false);
    methods.handleSubmit(submitPayment)();
  };

  const handleCancelPayment = () => {
    setShowConfirmModal(false);
  };

  if (paymentStatus === 'success') {
    return <PaymentSuccess total={total} />;
  }

  if (!lineItems?.length) {
    return <PaymentLoading />;
  }

  const oneTimePayments = lineItems.filter((item) => !item.isRecurring);
  const subscriptionPayments = lineItems.filter((item) => item.isRecurring);

  return (
    <FormProvider {...methods}>
      <form onSubmit={(e) => e.preventDefault()}>
        <div
          className="flex h-screen flex-col bg-gray-50 lg:flex-row"
          style={{
            fontFamily: 'Inter',
          }}
        >
          {/* Sidebar - Payment Summary */}
          <div className="noScrollbar flex w-full flex-col gap-4 overflow-y-auto p-6 lg:w-1/2 lg:p-24">
            {/* Price Breakdown */}
            <PriceBreakdown
              subscriptionTotal={initialSubscriptionTotal}
              calculations={calculations}
              total={total}
            />

            {/* Order Items */}
            <OrderItems
              lineItems={oneTimePayments}
              edit={options?.allowEdit}
              setCustomAmount={
                options?.allowEdit
                  ? (amounts) => {
                      setCustomAmounts?.(amounts);
                    }
                  : undefined
              }
              currentAmount={customAmounts}
            />

            <SubscriptionItems lineItems={subscriptionPayments} />

            {/* Discounts Section */}

            {options?.allowTip && (
              <div className={cn(styles.orderDetails, 'px-4 pb-6 pt-4')}>
                <div className={cn(styles.headerSm, 'p-4 py-2')}>
                  <b className={cn(styles.b, 'text-sm text-gray-500')}>Tips</b>
                  <img className={styles.infoIcon} alt="" src="info.svg" />
                </div>
                <div className="w-full px-4">
                  <div className="flex flex-col gap-4">
                    <div className="flex gap-2">
                      {[3, 5, 10].map((percentage) => (
                        <button
                          key={percentage}
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            setSelectedTip(percentage);
                            setCustomTip('');
                          }}
                          className={cn(
                            'flex-1 rounded px-4 py-2 text-sm font-medium',
                            selectedTip === percentage
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
                          )}
                        >
                          {percentage}%
                        </button>
                      ))}
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={customTip}
                        onChange={(e) => {
                          setCustomTip(e.target.value);
                          setSelectedTip(0);
                        }}
                        placeholder="Custom tip amount"
                        className="flex-1 rounded border border-gray-300 px-3 py-2 text-sm"
                      />
                      <span className="text-sm text-gray-500">$</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {options?.allowExtraDiscount && (
              <div className={cn(styles.orderDetails, 'px-4 pb-6 pt-4')}>
                <div className={cn(styles.headerSm, 'p-4 py-2')}>
                  <b className={cn(styles.b, 'text-sm text-gray-500')}>Discounts</b>
                  <img className={styles.infoIcon} alt="" src="info.svg" />
                </div>
                <div className="w-full px-4">
                  <div className="mb-4 flex gap-2">
                    <input
                      type="text"
                      value={discountInput}
                      onChange={(e) => setDiscountInput(e.target.value.toUpperCase())}
                      placeholder="Enter discount code"
                      className="flex-1 rounded border border-gray-300 px-3 py-2 text-sm uppercase"
                    />
                    <button
                      type="button"
                      onClick={handleAddDiscount}
                      className="rounded bg-blue-500 px-4 py-2 text-sm text-white hover:bg-blue-600"
                    >
                      Apply
                    </button>
                  </div>
                  {discounts.length > 0 && (
                    <>
                      <p className="text-sm font-semibold text-gray-500">Applied Discounts:</p>
                      <div className="mt-2 space-y-2">
                        {discounts.map((discount, index) => (
                          <div key={index} className="flex justify-between">
                            <span className="font-mono text-sm">{discount.code}</span>
                            <span className="text-sm text-green-600">
                              -{moneyFormat(int2DecToFloat(discount.amount))}
                            </span>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Main content - Payment Form */}
          <div className="noScrollbar relative flex w-full flex-col overflow-y-auto pt-6 lg:w-1/2 lg:pt-24">
            <div className="flex flex-grow flex-col gap-4 pb-36 pr-16">
              {/* Billing Details */}
              <div
                className={cn(
                  styles.billingDetails,
                  'mt-0 h-fit w-full border p-4 pt-0 lg:-mt-8 lg:p-8',
                )}
              >
                <div className={styles.headerSm}>
                  <b className={styles.b}>Billing details</b>
                  <img className={styles.infoIcon} alt="" src="info.svg" />
                </div>
                <div className={styles.inputs1}>
                  <div className={styles.listItems}>
                    <FormInput
                      id="email"
                      name="email"
                      label="Email"
                      type="email"
                      rules={{
                        required: message.requiredField,
                        pattern: {
                          value: emailPatternRegex,
                          message: message.emailPattern,
                        },
                      }}
                    />
                  </div>
                  <div className={styles.listItems}>
                    <FormInput id="phoneNumber" name="phoneNumber" label="Phone number" />
                  </div>

                  <div className={'flex w-full gap-4'}>
                    <StateCountryForms
                      countryKey="country"
                      stateKey="state"
                      message={message}
                      methods={methods}
                    />
                  </div>
                  <div className="flex w-full gap-4">
                    <div className={cn(styles.listItems, 'flex-1')}>
                      <FormInput
                        id="city"
                        name="city"
                        label="City"
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                    <div className={cn(styles.listItems, 'flex-1')}>
                      <FormInput
                        id="street"
                        name="street"
                        label="Street"
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                  </div>

                  <div className={styles.listItems}>
                    <FormInput
                      id="zipCode"
                      name="zipCode"
                      label="Zip/Postal code"
                      type="number"
                      rules={{
                        required: message.requiredField,
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Card Details */}
              <div className={cn(styles.cardDetails, 'h-fit w-full p-4 pt-0 lg:p-8')}>
                <div className="mb-4 flex w-full gap-4">
                  {/* <button
                      type="button"
                      onClick={() => setPaymentMethod('card')}
                      className={cn(
                        'flex-1 rounded px-4 py-2',
                        paymentMethod === 'card'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700',
                      )}
                    >
                      Credit Card
                    </button> */}
                  {!options?.disabledCC && (
                    <button
                      type="button"
                      onClick={() => setPaymentMethod('gpecomm')}
                      className={cn(
                        'flex-1 rounded px-4 py-2',
                        ['card', 'gpecomm'].includes(paymentMethod)
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700',
                      )}
                    >
                      Credit/Debit Card
                    </button>
                  )}
                  {!options?.disabledACH && (
                    <button
                      type="button"
                      onClick={() => setPaymentMethod('ach')}
                      className={cn(
                        'flex-1 rounded px-4 py-2',
                        paymentMethod === 'ach'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700',
                      )}
                    >
                      ACH / Bank Transfer
                    </button>
                  )}
                </div>

                {paymentMethod === 'card' && (
                  <>
                    <div className={cn(styles.inputs, 'mt-2')}>
                      <div className={styles.row}>
                        <div className={styles.inputField}>
                          <FormInput
                            id="fullName"
                            name="fullName"
                            label="Full name (as displayed on card)"
                            rules={{ required: message.requiredField }}
                          />
                        </div>
                      </div>
                    </div>
                    <div className={cn(styles.inputs, '!hidden')}>
                      <div className={styles.row}>
                        <div className={styles.inputField}>
                          {/* <FormFormattedInput
                              id="cardNumber"
                              name="cardNumber"
                              label="Card number"
                              mask="9999 9999 9999 9999"
                              rules={{ required: message.requiredField }}
                            /> */}
                          {/* <FormInput
                              id="cardToken"
                              name="cardToken"
                              label="Card token"
                              rules={{ required: message.requiredField }}
                            /> */}
                        </div>
                      </div>
                      {/* <div className={styles.row}>
                          <div className={styles.inputField}>
                            <FormFormattedInput
                              id="cvc"
                              name="cvc"
                              label="CVC"
                              mask="999"
                              rules={{ required: message.requiredField }}
                            />
                          </div>
                          <div className={styles.inputField}>
                            <FormFormattedInput
                              id="expiryDate"
                              name="expiryDate"
                              label="Expiry date"
                              mask="99/99"
                              rules={{ required: message.requiredField }}
                            />
                          </div>
                        </div> */}
                    </div>
                    <br />
                    <TSEPHostedTokenizerComponent
                      // fieldErrors={fieldErrors}
                      onEvent={(eventType, event) => {
                        console.log(eventType, event);
                      }}
                      labels={{
                        cardNumber: 'Card Number',
                        expiryDate: 'Expiry Date',
                        cvv: 'CVC',
                        // cardHolderName: 'Card Holder Name',
                        // zipCode: 'Zip Code',
                      }}
                      allowOptionals={{
                        cvv: true,
                        // zipCode: true,
                        // cardHolderName: true,
                      }}
                      onToken={(token) => {
                        // methods.setValue('fullName', token.cardHolderName);
                        methods.setValue('cardToken', token.tsepToken);
                        methods.setValue('cvc', token.cvv2);
                        methods.setValue('expiryDate', token.expirationDate);
                        // methods.setValue('zipCode', token.zipCode);
                        // methods.setValue('brand', token.cardType);
                        // console.log('token', token);
                      }}
                      onTokenError={() => {
                        // console.error('error', error);
                        // methods.setValue('fullName', '');
                        methods.setValue('cardToken', '');
                        methods.setValue('cvc', '');
                        methods.setValue('expiryDate', '');
                        // methods.setValue('zipCode', '');
                        // methods.setValue('brand', '');
                        // setVerifyResult(undefined);
                      }}
                      parentComponentAttributes={{
                        style: { width: '100%', height: 'fit-content', 'margin-top': '-10px' },
                      }}
                      iframeComponentAttributes={{
                        height: '225px',
                      }}
                    />
                  </>
                )}

                {paymentMethod === 'gpecomm' && (
                  <>
                    <div className={cn(styles.inputs, 'mt-2')}>
                      <div className={styles.row}>
                        <div className={styles.inputField}>
                          <FormInput
                            id="fullName"
                            name="fullName"
                            label="Full name (as displayed on card)"
                            rules={{ required: message.requiredField }}
                          />
                        </div>
                      </div>
                    </div>
                    <div className={cn(styles.inputs, '!hidden')}>
                      <div className={styles.row}>
                        <div className={styles.inputField}>
                          {/* <FormFormattedInput
                              id="cardNumber"
                              name="cardNumber"
                              label="Card number"
                              mask="9999 9999 9999 9999"
                              rules={{ required: message.requiredField }}
                            /> */}
                          {/* <FormInput
                              id="cardToken"
                              name="cardToken"
                              label="Card token"
                              rules={{ required: message.requiredField }}
                            /> */}
                        </div>
                      </div>
                      {/* <div className={styles.row}>
                          <div className={styles.inputField}>
                            <FormFormattedInput
                              id="cvc"
                              name="cvc"
                              label="CVC"
                              mask="999"
                              rules={{ required: message.requiredField }}
                            />
                          </div>
                          <div className={styles.inputField}>
                            <FormFormattedInput
                              id="expiryDate"
                              name="expiryDate"
                              label="Expiry date"
                              mask="99/99"
                              rules={{ required: message.requiredField }}
                            />
                          </div>
                        </div> */}
                    </div>
                    <br />
                    <CardGLPHostedComponent
                      // fieldErrors={fieldErrors}
                      merchantID={paymentData.groupID}
                      onEvent={(eventType, event) => {
                        console.log(eventType, event);
                      }}
                      onToken={(token) => {
                        // methods.setValue('fullName', token.cardHolderName);
                        methods.setValue('gpEcomm', token.paymentID);
                        methods.setValue('paymentMethod', 'gpecomm');
                      }}
                      onError={() => {
                        // console.error('error', error);
                        // methods.setValue('fullName', '');
                        methods.setValue('gpEcomm', '');
                        // setVerifyResult(undefined);
                      }}
                      parentComponentAttributes={{
                        style: { width: '100%', height: 'fit-content', 'margin-top': '-10px' },
                      }}
                      iframeComponentAttributes={{
                        height: '225px',
                      }}
                    />
                  </>
                )}
                {paymentMethod === 'ach' && (
                  <>
                    <div className={cn(styles.inputs, 'mt-2')}>
                      <div className={styles.row}>
                        <div className={styles.inputField}>
                          <FormInput
                            id="fullName"
                            name="fullName"
                            label="Account Holder Name"
                            rules={{ required: message.requiredField }}
                          />
                        </div>
                      </div>
                    </div>
                    <ACHHostedComponent
                      onToken={(token) => {
                        methods.setValue('accountNumber', token.accountNumber);
                        methods.setValue('routingNumber', token.routingNumber);
                        methods.setValue('accountType', token.accountType);
                        methods.setValue('holderType', token.holderType);
                        methods.setValue('paymentMethod', 'ach');
                        setAchTokenReady(true);
                      }}
                      onError={() => {
                        methods.setValue('accountNumber', '');
                        methods.setValue('routingNumber', '');
                        methods.setValue('accountType', '');
                        methods.setValue('holderType', '');
                        methods.setValue('paymentMethod', 'ach');
                        setAchTokenReady(false);
                      }}
                      parentComponentAttributes={{
                        style: { width: '100%' },
                      }}
                      iframeComponentAttributes={{
                        height: '250px',
                      }}
                    />
                  </>
                )}

                <br />
                <button
                  type="button"
                  className={cn(styles.button, 'p-6 text-xl')}
                  disabled={isSubmitting || !canPay}
                  onClick={handlePayButtonClick}
                >
                  <p className={styles.text1}>
                    {isSubmitting
                      ? 'Processing...'
                      : canPay
                        ? total
                          ? `Pay ${moneyFormatString(int2DecToFloat(total))}`
                          : 'Confirm Payment'
                        : '--'}{' '}
                  </p>
                </button>

                <ConfirmTransactionModal
                  showModal={showConfirmModal}
                  handleCancel={handleCancelPayment}
                  handleConfirm={handleConfirmPayment}
                  isProcessEntry={true}
                  watch={watchedValues}
                  total={total + initialSubscriptionTotal}
                  calculations={{ ...calculations, subscription: initialSubscriptionTotal }}
                />

                {/* Add error message here */}
                {paymentStatus === 'error' && (
                  <div className="mt-4 rounded-md bg-red-50 p-3">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg
                          className="h-5 w-5 text-red-400"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">{errorMessage}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Payment Logos */}
                <div
                  className={cn(
                    'flex w-full flex-col items-center justify-center px-4 sm:flex-row',
                  )}
                >
                  <div className="relative aspect-[4/1] h-20">
                    <Image
                      className={'w-full'}
                      layout="fill"
                      alt=""
                      src="/icons/payments/cc-logo.png"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button - Fixed at bottom */}
            <div className="fixed bottom-0 left-1/2 right-16 border-t border-gray-200 bg-gray-100 p-6">
              <button
                type="button"
                className="w-full rounded-md bg-blue-600 py-3 font-medium text-white hover:bg-blue-700"
                onClick={handlePayButtonClick}
              >
                Pay ${(total / 100).toFixed(2)}
              </button>
            </div>
          </div>
        </div>
      </form>

      <ConfirmTransactionModal
        showModal={showConfirmModal}
        handleCancel={handleCancelPayment}
        handleConfirm={handleConfirmPayment}
        isProcessEntry={methods.watch('paymentMethod') === 'ach'}
        total={total}
        calculations={calculations}
        watch={methods.watch}
      />
    </FormProvider>
  );
};

export default PaymentForm;
