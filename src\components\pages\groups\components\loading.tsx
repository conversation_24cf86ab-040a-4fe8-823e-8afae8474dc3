'use client';

import DefaultTable from '@/components/globals/Tables';
import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  const SkeletonColumns = [
    {
      title: 'Group Name',
      cell: () => <Skeleton className="h-5 w-10" />,
    },
    {
      title: 'Merchant Counts',
      cell: () => <Skeleton className="w-27 h-5" />,
    },
    {
      title: 'Role',
      cell: () => <Skeleton className="w-27 h-5" />,
    },
  ];

  return (
    <div>
      <DefaultTable head={SkeletonColumns} body={Array(10).fill({})} skeleton={true} />
    </div>
  );
}
