import { useState } from 'react';
import { <PERSON><PERSON>, Button } from 'flowbite-react';
import { useForm } from 'react-hook-form';
import { useLocationSelector } from '@/components/hooks';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import { useMutation } from '@apollo/client';
import { Gateway_CreatePaymentPlanDocument } from '@/graphql/generated/graphql';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { PaymentFormData, paymentFormDataDefault } from '../utils';
import { PaymentPlanForm } from './payment-plan-form';
import { FaPlus } from 'react-icons/fa';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { floatToInt2Dec } from '@/lib/utils';

type PaymentPlanAddProps = {
  refetchListPage: () => void;
};

const PaymentPlanAdd = ({ refetchListPage }: PaymentPlanAddProps) => {
  const { locationFilter } = useLocationSelector({ readonly: true, onlyActive: true });
  const [addPaymentModal, setPaymentPlanAdd] = useState(false);

  const methods = useForm<PaymentFormData>({
    defaultValues: { ...paymentFormDataDefault },
  });

  const { reset } = methods;

  const [createPaymentPlanMutation, { loading: createPaymentPlanLoading }] = useMutation(
    Gateway_CreatePaymentPlanDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Payment plan'));
        reset();
        setPaymentPlanAdd(false);
        refetchListPage();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Payment plan', error.message));
      },
    },
  );

  const onSubmitForm = async (data: PaymentFormData) => {
    try {
      // console.log('data', {
      //   groupID: locationFilter?.id ?? '',
      //   data: {
      //     form: {
      //       customerID: data.customer?.id ?? '',
      //       paymentID: data.card?.id ?? '',
      //       planName: data.planName,
      //       amount: parseFloat(`${data.amount ?? ''}`),
      //       startDate: data.startDate,
      //       paymentEvery: data.paymentEvery,
      //       paymentInterval: data.paymentInterval,
      //       endDate: data.endDate,
      //       lineItems: data.lineItems ?? [],
      //     },
      //   },
      // });
      await createPaymentPlanMutation({
        variables: {
          input: {
            groupID: locationFilter?.id ?? '',
            data: {
              form: {
                customerID: data.customerID ?? '',
                paymentID: data.paymentID ?? '',
                planName: data.planName,
                amount: floatToInt2Dec(parseFloat(`${data.amount ?? ''}`)),
                startDate: data.startDate,
                paymentEvery: parseInt(`${data.paymentEvery}`),
                paymentInterval: parseInt(`${data.paymentInterval}`),
                endDate: data.endDate,
                lineItems: data.lineItems ?? [],
                payNow: true,
              },
            },
          },
        },
      });

      await apolloClient.refetchQueries({
        include: ['Gateway_paymentPlans', 'Gateway_paymentPlan'],
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  return (
    <>
      <Button
        className="mt-[20px] h-[38px] p-0"
        color="blue"
        onClick={() => setPaymentPlanAdd(true)}
      >
        <div className="flex items-center gap-x-3">
          <FaPlus className="text-xl" />
          <span>Add Plan</span>
        </div>
      </Button>
      <Modal show={addPaymentModal} onClose={() => setPaymentPlanAdd(false)} size="5xl">
        <Modal.Header className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-blue-600">Create Payment Plan</h3>
        </Modal.Header>
        <Modal.Body>
          <SpinnerLoading isLoading={createPaymentPlanLoading} />
          <PaymentPlanForm methods={methods} onSubmitForm={onSubmitForm} />
        </Modal.Body>
      </Modal>
    </>
  );
};

export default PaymentPlanAdd;
