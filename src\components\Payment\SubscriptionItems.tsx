import { cn } from '@/lib/utils';
import { int2DecToFloat, moneyFormat } from '@/lib/utils';
import styles from './index.module.css';
import { PaymentLineItem } from './index';

interface SubscriptionItemsProps {
  lineItems: PaymentLineItem[];
}

export const SubscriptionItems = ({ lineItems }: SubscriptionItemsProps) => {
  if (!lineItems?.length) return null;

  const getRecurringText = (item: PaymentLineItem) => {
    // Handle special recurring modes
    if (item.recurringMode === 'MONTHLY') {
      const cycles = !item.recurringTotalCycles
        ? 'until canceled'
        : `for ${item.recurringTotalCycles} months`;
      return `Charged monthly ${cycles}`;
    }

    if (item.recurringMode === 'YEARLY') {
      const cycles = !item.recurringTotalCycles
        ? 'until canceled'
        : `for ${item.recurringTotalCycles} years`;
      return `Charged annually ${cycles}`;
    }

    // Default behavior for other recurring modes
    // interval is in days
    const daysText = item.recurringInterval === 1 ? 'day' : `${item.recurringInterval} days`;

    // frequency is how many instances to skip (1 = every time, 2 = every other time, etc)
    const frequencyText =
      item.recurringFrequency === 1
        ? 'every'
        : `every ${item.recurringFrequency}${item.recurringFrequency === 2 ? 'nd' : 'th'}`;

    const cycles = !item.recurringTotalCycles
      ? 'until canceled'
      : `for ${item.recurringTotalCycles} payments`;

    return `Charged ${frequencyText} ${daysText} ${cycles}`;
  };

  return (
    <div className={cn(styles.orderDetails, 'px-4 pb-6 pt-4')}>
      <div className={cn(styles.headerSm, 'p-4 py-2 pb-1')}>
        <b className={cn(styles.b, 'text-sm text-gray-500')}>Recurring Items</b>
        <img className={styles.infoIcon} alt="" src="info.svg" />
      </div>
      <div className={styles.table}>
        <div className={cn(styles.tableColumns, '')}>
          <div className={cn(styles.column, 'gap-4')}>
            {lineItems.map((item) => (
              <div key={item.id} className="flex w-full justify-between gap-4">
                <div className={cn('flex flex-[2] flex-col pl-4')}>
                  <p className="text-lg font-bold">{item.name}</p>
                  <p className="text-xs text-gray-500">ID: {item.id}</p>
                  <p className="py-0.5 text-xs text-gray-500">{item.description}</p>
                  <div className="mt-2 flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                      <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-800">
                        {getRecurringText(item)}
                      </span>
                      {item.recurringMode === 'MONTHLY' && (
                        <span className="rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-800">
                          Monthly Plan
                        </span>
                      )}
                      {item.recurringMode === 'YEARLY' && (
                        <span className="rounded-full bg-purple-100 px-2 py-0.5 text-xs text-purple-800">
                          Annual Plan
                        </span>
                      )}
                    </div>
                    <div className="h-1 w-2" />
                    {item.recurringTrialDays > 0 && (
                      <span className="ml-4 text-xs text-gray-500">
                        Includes {item.recurringTrialDays} day trial period
                      </span>
                    )}
                    {item.recurringSetupFee > 0 && (
                      <span className="ml-4 text-xs text-gray-500">
                        One-time setup fee: {moneyFormat(int2DecToFloat(item.recurringSetupFee))}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className={cn('flex flex-1 flex-col pr-4')}>
                    <p className="text-right text-lg font-bold">
                      {moneyFormat(int2DecToFloat(item.total))}
                    </p>
                    <div className="flex items-center justify-end gap-2">
                      <p className="text-right text-gray-500">
                        {moneyFormat(int2DecToFloat(item.price))}
                        {item.recurringMode === 'MONTHLY'
                          ? ' per month'
                          : item.recurringMode === 'YEARLY'
                            ? ' per year'
                            : ' per billing cycle'}
                      </p>
                      <span className="text-gray-500">(x{item.amount})</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
