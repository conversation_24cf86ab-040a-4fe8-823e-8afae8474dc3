import { Calendar, AlertTriangle } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { mockAccountData } from '@/mock/account-status-data';
import { BaseAccountModal } from './BaseAccountModal';
import { InfoItem } from './InfoItem';

interface InactiveStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const InactiveStatusModal = ({ isOpen, onClose }: InactiveStatusModalProps) => {
  const data = mockAccountData.inactive;

  return (
    <BaseAccountModal
      isOpen={isOpen}
      onClose={onClose}
      title="Inactive Account"
      accountData={data}
      statusIcon={AlertTriangle}
      statusColor="text-red-500"
    >
      <Card className="border-red-200 bg-red-50 p-4">
        <div className="mb-4 flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-red-500" />
          <p className="text-red-700">
            This account is currently inactive. Contact support for reactivation.
          </p>
        </div>
        <div className="grid gap-4">
          <InfoItem icon={Calendar} label="Deactivation Date" value={data.deactivationDate} />
          <InfoItem icon={Calendar} label="Last Active Date" value={data.lastActiveDate} />
          <InfoItem icon={AlertTriangle} label="Reason" value={data.reason} />
        </div>
      </Card>
    </BaseAccountModal>
  );
};
