import { Fragment, useMemo, useState } from 'react';
import { Radio, Table } from 'flowbite-react';

type DataType = {
  [key: string]: string; // Dynamic key-value pairs
};

enum Direction {
  desc = 'desc',
  asc = 'asc',
}

type SortableTableV2Props = {
  rows: DataType[];
};

export const SortableTableV2 = ({ rows }: SortableTableV2Props) => {
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: Direction;
  }>({ key: 'transaction', direction: Direction.asc });
  const [statusFilter, setStatusFilter] = useState('All');

  // Update columnKeys whenever rows change
  const columnKeys = useMemo(() => Object.keys(rows[0]), []);

  const handleSort = (column: string) => {
    const direction =
      sortConfig.key === column && sortConfig.direction === Direction.desc
        ? Direction.asc
        : Direction.desc;
    setSortConfig({ key: column, direction });
  };

  const sortData = (data: DataType[], column: string, direction: Direction) => {
    return [...data].sort((a, b) => {
      if (a[column] < b[column]) return direction === 'asc' ? -1 : 1;
      if (a[column] > b[column]) return direction === 'asc' ? 1 : -1;
      return 0;
    });
  };

  const getStatusColor = (status: string) => {
    if (status === 'Pending' || status === 'Inactive') return 'gray';
    if (status === 'Failed') return 'red';
    return 'green';
  };

  const rowData = useMemo(() => {
    if (!rows?.length) return [];
    const sortedData = sortData(rows, sortConfig.key, sortConfig.direction);
    if (statusFilter !== 'All') {
      return sortedData.filter((row) => row.status === statusFilter);
    }
    return sortedData;
  }, [sortConfig, statusFilter, rows]);

  const headerSortIcon = useMemo(() => {
    return (
      <svg
        className="-mt-0.5 ml-1 inline-block h-4 w-4"
        fill="currentColor"
        viewBox="0 0 20 20"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <path
          clipRule="evenodd"
          fillRule="evenodd"
          d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z"
        />
      </svg>
    );
  }, []);

  return (
    <Fragment>
      <div className="mb-6 flex justify-start space-x-4">
        <label className="flex items-center space-x-2">
          <span>Show only: </span>
        </label>
        <label className="flex items-center space-x-2">
          <Radio
            id="all"
            name="status"
            value="All"
            checked={statusFilter === 'All'}
            onChange={() => setStatusFilter('All')}
          />
          <span>All</span>
        </label>

        <label className="flex items-center space-x-2">
          <Radio
            id="completed"
            name="status"
            value="Completed"
            checked={statusFilter === 'Completed'}
            onChange={() => setStatusFilter('Completed')}
          />
          <span>Completed</span>
        </label>

        <label className="flex items-center space-x-2">
          <Radio
            id="pending"
            name="status"
            value="Pending"
            checked={statusFilter === 'Pending'}
            onChange={() => setStatusFilter('Pending')}
          />
          <span>Pending</span>
        </label>

        <label className="flex items-center space-x-2">
          <Radio
            id="failed"
            name="status"
            value="Failed"
            checked={statusFilter === 'Failed'}
            onChange={() => setStatusFilter('Failed')}
          />
          <span>Failed</span>
        </label>
      </div>

      <Table className="w-full text-left text-sm text-gray-500 dark:text-gray-400">
        <Table.Head className="bg-gray-50 text-xs uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400">
          {columnKeys.map((key) => (
            <Table.HeadCell key={key} className="px-4 py-3" onClick={() => handleSort(key)}>
              {key.charAt(0).toUpperCase() + key.slice(1)}
              {headerSortIcon}
            </Table.HeadCell>
          ))}
        </Table.Head>
        <Table.Body>
          {rowData.map((item, index) => (
            <Table.Row
              key={index}
              className="border-b hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
            >
              {columnKeys.map((key) => (
                <Table.Cell
                  key={key}
                  className="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white"
                >
                  {key === 'status' ? (
                    <span
                      className={`bg-${getStatusColor(item[key])}-100 text-${getStatusColor(
                        item[key],
                      )}-800 mr-2 rounded px-2.5 py-0.5 text-xs font-medium dark:bg-${getStatusColor(
                        item[key],
                      )}-900 dark:text-${getStatusColor(item[key])}-300`}
                    >
                      {item[key]}
                    </span>
                  ) : (
                    item[key]
                  )}
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </Fragment>
  );
};
