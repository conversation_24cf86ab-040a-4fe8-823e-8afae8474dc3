import React from 'react';

export type FormControlProps = {
  /** Boolean to toggle visibility */
  visible?: boolean;
  /** Boolean to toggle if read-only or editable */
  readOnly?: boolean;
  /** Flex value */
  flex?: number;
  /** Any additional props for the FormControl */
  children?: React.ReactNode;
  className?: string;
};

export const FormControl = ({
  visible = true,
  readOnly = false,
  flex,
  children,
  className = '',
  ...props
}: FormControlProps): JSX.Element => {
  return (
    <div
      aria-hidden={visible ? undefined : 'true'}
      aria-disabled={readOnly ? 'true' : undefined}
      className={`${visible ? 'inline-flex' : 'hidden'} ${readOnly ? 'pointer-events-none opacity-70' : ''} ${flex ? `flex-${flex}` : ''} ${className} my-2`}
      {...props}
    >
      {children}
    </div>
  );
};

export default FormControl;
