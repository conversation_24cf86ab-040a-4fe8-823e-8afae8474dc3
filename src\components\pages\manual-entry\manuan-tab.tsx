import { Button, Modal } from 'flowbite-react';

import { useEffect, useMemo, useState } from 'react';
import { useCustomerSelector, useLocationSelector } from '@/components/hooks';
import { message, requiredAmountIsValid } from '@/components/shared/utils';
import { EntryType, ManualEntryFormData, manualFormDataDefaults, ProcessType } from './utils';
import { FieldErrors, FormProvider, useForm, useWatch } from 'react-hook-form';
import { FormInput, FormSelect } from '@/components/globals';
import { useMutation, useQuery } from '@apollo/client';
import {
  Gateway_ComputeCheckoutInputDataFormFormPaymentType,
  Gateway_CreateManualEntryDocument,
  Gateway_CreateManualEntryInputDataFormMethodVerifyOrProcess,
  Gateway_CreateManualEntryInputDataFormPaymentType,
  Gateway_CustomerDocument,
} from '@/graphql/generated/graphql';
import { toast } from 'react-toastify';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  RootTsepCardFieldIDs,
  TSEPHostedTokenizerComponent,
} from '@/components/payments/tsep/tokenizer';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
import { useUpdateCustomerModalHook } from '../hooks/updateCustomerModal';
import { PriceBreakdown, PriceBreakdownProps } from '@/components/Payment/PriceBreakdown';
import { ComputeCheckoutUNI } from '@/graphql/declarations/payments';
import StateCountryForms from '@/components/shared/components/stateCountryFormSelect';
import { TsepVerify } from '@/graphql/declarations/tsep';
import FormPhoneNumber from '@/components/globals/form-phone-number/form-phonenumber.index';
import { ACHHostedComponent } from '@/components/payments/ach-local';
import { CardGLPHostedComponent } from '@/components/payments/glpv2';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { GET_BANK_INFO_BY_ROUTING } from '@/graphql/declarations/processor-test';

function floatToMoney(value: number) {
  // convert 12.345 to 1234
  return Math.round(value * 100);
}

function ConfirmTransactionModal(args: {
  showModal: boolean;
  handleCancel: () => void;
  handleConfirm: () => void;
  isProcessEntry: boolean;
  watch: any;
}) {
  const { showModal, handleCancel, handleConfirm, isProcessEntry, watch } = args;

  const { data: bankData } = useQuery(GET_BANK_INFO_BY_ROUTING, {
    variables: {
      input: {
        code: watch['routingNumber'],
      },
    },
    skip: !watch['routingNumber'],
  });

  const getBankInfo = () => {
    if (watch.processType === ProcessType.ach) {
      return bankData?.processor_tst_bank_routing?.data.name || 'Unknown Bank';
    }
    return watch.brand || 'Unknown Card';
  };

  const formatAmount = (amount: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(parseFloat(amount) || 0);
  };

  const getPaymentMethod = () => {
    switch (watch.processType) {
      case ProcessType.ach:
        return `ACH Payment - ${getBankInfo()}`;
      case ProcessType.card:
      case ProcessType.gpecomm:
        return `Card Payment`;
      default:
        return 'Unknown Payment Method';
    }
  };

  return (
    <Modal show={showModal} onClose={handleCancel}>
      <Modal.Header>Confirm Transaction</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          <div className="text-lg font-semibold">Transaction Details:</div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Payment Method:</span>
              <span>{getPaymentMethod()}</span>
            </div>
            <div className="flex justify-between">
              <span>Amount:</span>
              <span>{formatAmount(watch.amount)}</span>
            </div>
            {watch.tip && parseFloat(watch.tip) > 0 && (
              <div className="flex justify-between">
                <span>Tip:</span>
                <span>{formatAmount(watch.tip)}</span>
              </div>
            )}
            {watch.tax && parseFloat(watch.tax) > 0 && (
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>{formatAmount(watch.tax)}</span>
              </div>
            )}
            <div className="border-t pt-2">
              <div className="flex justify-between font-bold">
                <span>Total:</span>
                <span>
                  {formatAmount(
                    (
                      parseFloat(watch.amount || '0') +
                      parseFloat(watch.tip || '0') +
                      parseFloat(watch.tax || '0')
                    ).toString(),
                  )}
                </span>
              </div>
            </div>
          </div>
          <p className="mt-4 text-sm text-gray-600">
            Are you sure you want to proceed with this transaction?
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button color="gray" onClick={handleCancel}>
          Cancel
        </Button>
        <Button
          color={isProcessEntry ? 'blue' : 'warning'}
          className="w-full"
          onClick={handleConfirm}
        >
          Proceed
        </Button>
      </Modal.Footer>
    </Modal>
  );
}

export const ManualTab = () => {
  const router = useRouter();
  const { locationFilter, locationSelectorElement } = useLocationSelector({
    onlyActive: true,
  });

  const searchParams = useSearchParams();

  const { customerSelectorElement, customer } = useCustomerSelector({
    groupdId: locationFilter?.id || '',
    preselectedID: searchParams?.get('customerID') || '',
    canPay: true,
  });

  const { data: customerData, refetch: refetchCustomerData } = useQuery(Gateway_CustomerDocument, {
    variables: {
      input: {
        data: {
          customerID: customer?.id ?? '',
        },
        groupID: locationFilter?.id ?? '',
      },
    },
    skip: !customer?.id || !locationFilter?.id,
  });

  const { setCostumerIdToUpdate, viewModal } = useUpdateCustomerModalHook(
    locationFilter,
    refetchCustomerData,
  );

  const { data: groupData } = useQuery(GET_GROUPS_LIST, {
    variables: {
      where: {
        id: { equals: locationFilter?.id ?? '' },
      },
    },
    skip: !locationFilter?.id,
  });

  const groupInfo = useMemo(() => {
    if (!groupData) return null;
    return groupData.groups?.[0];
  }, [groupData]);

  const [manualEntryMutation, { loading }] = useMutation(Gateway_CreateManualEntryDocument, {
    onCompleted: (data) => {
      toast.success(message.api.successCreate('Transaction'));
      router.push(`/dashboard/reporting?id=${data.gateway_createManualEntry?.transactionID}`);
    },
    onError: (error) => {
      toast.error(message.api.errorCreate('Transaction', error.message));
    },
  });

  const methods = useForm<ManualEntryFormData>({
    defaultValues: { ...manualFormDataDefaults },
  });

  const customerIdWatcher = useWatch({
    control: methods.control,
    name: 'customerID',
  });

  const _methodVerifyOrProcessWatcher = useWatch({
    control: methods.control,
    name: 'methodVerifyOrProcess',
  });
  const watch = useWatch({
    control: methods.control,
  });

  useEffect(() => {
    if (customerData) {
      const customer = customerData.gateway_customer;
      const defaultCard =
        customer?.paymentCards?.find((card) => card?.isDefault) ?? customer?.paymentCards?.[0];

      methods.reset({
        nameOnCard: customer?.nameOnCard || '',
        email: customer?.email || '',
        phoneNumber: customer?.phone || '',
        address: customer?.billingAddress || '',
        city: customer?.billingCity || '',
        state: customer?.billingState || '',
        zip: customer?.billingZip || '',
        country: customer?.billingCountry || '',
        // cardNumber: defaultCard?.last4 || '',
        // cvc: '•••',
        // expiryDate: defaultCard?.expires || '',
        customerID: customer?.id || '',

        cardNumber: '',
        cvc: '',
        expiryDate: '',
        cardToken: '',
        brand: '',

        accountNumber: '',
        routingNumber: '',
        accountType: '',
        holderType: '',
      });

      handleCardSelection(defaultCard?.cardID || '');
    }
  }, [customerData]);

  const [, setFieldErrors] = useState<FieldErrors>({});

  const [verifyResult, setVerifyResult] = useState<{
    status?: string;
    message?: string;
    cardType?: string;
  }>();

  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState<ManualEntryFormData | null>(null);

  const [transactionError, setTransactionError] = useState<string | null>(null);

  const handleConfirm = () => {
    if (formData) {
      onSubmitForm(formData);
    }
    setShowModal(false);
  };

  const handleCancel = () => {
    setShowModal(false);
    setFormData(null);
  };

  const handleFormSubmit = (data: ManualEntryFormData) => {
    setFormData(data);
    setShowModal(true);
  };

  const onSubmitForm = async (data: ManualEntryFormData) => {
    if (!data.zip) {
      setFieldErrors({
        [RootTsepCardFieldIDs.zipCode]: {
          type: 'required',
          message: 'Incomplete Card Information Details',
        },
      });
      return;
    }

    if (!data.nameOnCard) {
      setFieldErrors({
        nameOnCard: {
          type: 'required',
          message: 'Name on Card is required',
        },
      });
      return;
    }

    if (watch.processType === ProcessType.card) {
      if (!data.cardToken && !data.cardNumber) {
        // alert('Card number is required');
        setFieldErrors({
          [RootTsepCardFieldIDs.cardNumber]: {
            type: 'required',
            message: 'Incomplete Card Information Details',
          },
        });
        return;
      }

      if (!data.cvc) {
        setFieldErrors({
          [RootTsepCardFieldIDs.cvv]: {
            type: 'required',
            message: 'Incomplete Card Information Details',
          },
        });
        return;
      }

      if (!data.expiryDate) {
        setFieldErrors({
          [RootTsepCardFieldIDs.expiryDate]: {
            type: 'required',
            message: 'Incomplete Card Information Details',
          },
        });
        return;
      }

      if (data.methodVerifyOrProcess === EntryType.verify) {
        // console.log('triggering submit');
        // tokenizerRef.current?.click?.();
        // // tokenizerRef.current?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        // return;
        console.log('data', data);
        const resp = await apolloClient.mutate({
          mutation: TsepVerify,
          variables: {
            input: {
              card: {
                token: data.cardToken.replace(/\s/g, ''),
                cvv: data.cvc.trim(),
                expiry: data.expiryDate,
                type: data.brand ?? 'U',
              },
              avs: {
                firstName: data.nameOnCard,
                lastName: data.nameOnCard,
                addressLine1: data.address,
                zipCode: data.zip,
              },
            },
          },
        });
        setVerifyResult({
          cardType: resp.data?.tsep_verify?.cardType || '',
          status: resp.data?.tsep_verify?.status || '',
          message: resp.data?.tsep_verify?.message || '',
        });

        console.log('resp', resp);

        return;
      }
    }

    try {
      // console.log('data', data);
      let amount = data?.amount ? floatToMoney(parseFloat(data.amount)) : 0;
      let tip = data?.tip ? floatToMoney(parseFloat(data.tip)) : 0;
      let tax = data?.tax ? floatToMoney(parseFloat(data.tax)) : 0;
      let total = amount;

      let paymentType = Gateway_CreateManualEntryInputDataFormPaymentType.Card;

      if (data.processType === ProcessType.ach) {
        paymentType = Gateway_CreateManualEntryInputDataFormPaymentType.Ach;
      } else if (data.processType === ProcessType.gpecomm) {
        paymentType = Gateway_CreateManualEntryInputDataFormPaymentType.Gpecomm;
      }

      const d = await manualEntryMutation({
        variables: {
          input: {
            data: {
              form: {
                // TODO: fix this tax
                tax: tax,
                amount: total,
                tip: tip,
                methodVerifyOrProcess:
                  data.methodVerifyOrProcess as Gateway_CreateManualEntryInputDataFormMethodVerifyOrProcess,
                customerID: data.customerID,
                nameOnCard: data.nameOnCard,
                email: data.email,
                phoneNumber: data.phoneNumber,
                billingAddress: data.address,
                billingCity: data.city,
                billingState: data.state,
                customerCardID: data.savedCardID,
                paymentType: paymentType,
                card:
                  watch.processType === ProcessType.card
                    ? {
                        expiryDate: data.expiryDate,
                        cvc: data.cvc,
                        // cardNumber: data.cardNumber,
                        cardToken: data.cardToken.replace(/\s/g, ''),
                      }
                    : undefined,
                ach:
                  watch.processType === ProcessType.ach
                    ? {
                        accountNumber: data.accountNumber,
                        routingNumber: data.routingNumber,
                        accountType: data.accountType,
                        accountHolderType: data.holderType,
                      }
                    : undefined,
                gpecomm:
                  watch.processType === ProcessType.gpecomm
                    ? {
                        id: data.gpEcomm,
                        cvv: data.cvc,
                      }
                    : undefined,
                billingCountry: data.country,
                billingZip: data.zip,
              },
              // meta: {
              //   level2: true,
              //   // includeSurcharge: true,
              // },
            },
            groupID: locationFilter?.id ?? '',
          },
        },
      });

      if (d.errors) {
        // console.log(d.errors);
        // @ts-ignore
        throw new Error(d.errors);
      }
      setTransactionError(null); // Clear any previous errors on success
    } catch (e) {
      // @ts-ignore
      let message = e.message;
      // console.log('Add Customer Mutation error: ', e);
      toast.error('Error: ' + message);
      setTransactionError(message); // Set the error message
    }
  };

  const methodVerifyOrProcessWatcher = _methodVerifyOrProcessWatcher || 'process';
  const isProcessEntry = methodVerifyOrProcessWatcher === EntryType.process;
  const disableFields = !!customerIdWatcher;
  const canPay =
    watch.processType === ProcessType.card
      ? Boolean(watch.cardToken)
      : watch.processType === ProcessType.ach
        ? Boolean(watch.accountNumber)
        : watch.processType === ProcessType.gpecomm
          ? Boolean(watch.gpEcomm)
          : false;

  const [priceCalculation, setPriceCalculation] = useState<PriceBreakdownProps>({
    total: 0,
    calculations: {},
    subscriptionTotal: 0,
  });

  const memoizedPrices = useMemo(() => {
    return {
      amount: watch.amount,
      tip: watch.tip,
      tax: watch.tax,
    };
  }, [watch.amount, watch.tip, watch.tax]);

  const calculateAPI = async () => {
    if (!isProcessEntry) {
      return setPriceCalculation({
        total: 0,
        calculations: {},
        subscriptionTotal: 0,
      });
    }

    let amount = floatToMoney(parseFloat(memoizedPrices.amount ?? '0') || 0);
    if (!amount) {
      return setPriceCalculation({
        total: 0,
        calculations: {},
        subscriptionTotal: 0,
      });
    }

    let paymentType = Gateway_ComputeCheckoutInputDataFormFormPaymentType.Card;

    if (watch.processType === ProcessType.ach) {
      paymentType = Gateway_ComputeCheckoutInputDataFormFormPaymentType.Ach;
    } else if (watch.processType === ProcessType.gpecomm) {
      paymentType = Gateway_ComputeCheckoutInputDataFormFormPaymentType.Gpecomm;
    }

    const d = await apolloClient.mutate({
      mutation: ComputeCheckoutUNI,
      variables: {
        input: {
          data: {
            form: {
              form: {
                paymentType,
                amount: amount,
                tip: floatToMoney(parseFloat(memoizedPrices.tip ?? '0') || 0),
                tax: floatToMoney(parseFloat(memoizedPrices.tax ?? '0') || 0),
              },
            },
          },
          groupID: locationFilter?.id ?? '',
        },
      },
    });

    if (d.data?.gateway_computeCheckout) {
      const { breakdown: data } = d.data.gateway_computeCheckout;
      setPriceCalculation({
        total: data?.total ?? 0,
        subscriptionTotal: data?.subscriptionTotal ?? 0,
        calculations: {
          'Original Price': data?.subtotal ?? 0,
          Discount: data?.discount ?? 0,
          Tax: data?.tax ?? 0,
          Shipping: data?.shipping ?? 0,
          'Processing Surcharge': data?.fees ?? 0,
          Tip: data?.tip ?? 0,
        },
      });
    }
  };

  useEffect(() => {
    if (isProcessEntry) {
      calculateAPI();
    }
  }, [memoizedPrices, isProcessEntry]);

  const handleCardSelection = (selectedCardId: string) => {
    const selectedCard = customerData?.gateway_customer?.paymentCards?.find(
      (card) => card?.cardID === selectedCardId,
    );

    let type = selectedCard?.type || 'card';

    if (selectedCard) {
      methods.setValue('savedCardID', selectedCardId);
      if (type === 'card') {
        methods.setValue('processType', ProcessType.card);
        methods.setValue('cardToken', '*********' + (selectedCard.last4 || ''));
        methods.setValue('expiryDate', selectedCard.expires || '');
        methods.setValue('brand', selectedCard.brand || '');
        methods.setValue('cvc', ''); // Reset CVC as it needs to be entered for each transaction
      } else if (type === 'ach') {
        methods.setValue('processType', ProcessType.ach);
        methods.setValue('accountNumber', selectedCard.accountNumber || '');
        methods.setValue('routingNumber', selectedCard.routingNumber || '');
        methods.setValue('accountType', selectedCard.accountType || '');
        methods.setValue('holderType', selectedCard.accountHolderType || '');
      } else if (type === 'gpecomm') {
        methods.setValue('processType', ProcessType.gpecomm);
        methods.setValue('gpEcomm', selectedCard.gpEcommID || '');
      }
    }
  };

  useEffect(() => {
    let cardID = searchParams?.get('cardID');
    if (cardID && customerData) {
      handleCardSelection(cardID);
    }
  }, [searchParams, customerData]);

  // const stateList = useMemo(() => {
  //   const stateList = getStateList(methods.watch('country'));
  //   return stateList;
  // }, [methods.watch('country')]);

  // const countryList = useMemo(() => {
  //   return CountryList();
  // }, []);

  return (
    <>
      <StaticInfoBox />
      <div className="flex justify-between">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="w-1/4">{customerSelectorElement}</div>
      </div>
      <SpinnerLoading isLoading={loading} />

      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleFormSubmit)}>
          <div className="mt-10 grid grid-cols-3 gap-6">
            <div className="col-span-1 md:col-span-1">
              <FormSelect
                id="processType"
                name="processType"
                label="Transaction Method"
                rules={{ required: message.requiredField }}
                // @ts-ignore
                options={[
                  { value: '', label: 'Select Transaction Method:' },
                  // { value: ProcessType.authorize, label: ProcessType.authorize },
                  // {
                  //   value: ProcessType.card,
                  //   label: ProcessType.card,
                  // },
                  !groupInfo?.default_globalDisableCC && {
                    value: ProcessType.gpecomm,
                    label: ProcessType.card,
                  },
                  !groupInfo?.default_globalDisableACH && {
                    value: ProcessType.ach,
                    label: ProcessType.ach,
                    // disabled: true,
                  },
                  // {
                  //   value: ProcessType.bank,
                  //   label: ProcessType.bank,
                  //   disabled: true,
                  // },
                ].filter(Boolean)}
              />
              {watch.processType === ProcessType.card && (
                <>
                  <FormInput
                    id="nameOnCard"
                    name="nameOnCard"
                    label="Name on Card"
                    disabled={disableFields}
                    rules={{ required: message.requiredField }}
                  />

                  {!watch.customerID && (
                    <TSEPHostedTokenizerComponent
                      // fieldErrors={fieldErrors}
                      // onEvent={(eventType, event) => {
                      //   console.log(eventType, event);
                      // }}
                      labels={{
                        cardNumber: 'Card Number',
                        expiryDate: 'Expiry Date',
                        cvv: 'CVC',
                        // cardHolderName: 'Card Holder Name',
                        // zipCode: 'Zip Code',
                      }}
                      allowOptionals={{
                        cvv: true,
                        // cardHolderName: true,
                        // zipCode: true,
                      }}
                      iframeComponentAttributes={{
                        height: '230px',
                      }}
                      onToken={(token) => {
                        // methods.setValue('nameOnCard', token.cardHolderName);
                        methods.setValue('cardToken', token.tsepToken);
                        methods.setValue('cvc', token.cvv2);
                        methods.setValue('expiryDate', token.expirationDate);
                        methods.setValue('brand', token.cardType);
                        // methods.setValue('zip', token.zipCode);
                        // console.log('token', token);
                      }}
                      onTokenError={() => {
                        // console.error('error', error);
                        // methods.setValue('nameOnCard', '');
                        methods.setValue('cardToken', '');
                        methods.setValue('cvc', '');
                        methods.setValue('expiryDate', '');
                        methods.setValue('brand', '');
                        // methods.setValue('zip', '');
                        setVerifyResult(undefined);
                      }}
                    />
                  )}

                  {watch.customerID && (
                    <div>
                      <FormSelect
                        id="savedCardID"
                        name="savedCardID"
                        label="Payment Cards"
                        onChangeCallback={(e) => handleCardSelection(e.target.value)}
                        options={[
                          { value: '', label: 'Select a card' },
                          ...(customerData?.gateway_customer?.paymentCards?.map((card) => ({
                            value: card?.cardID || '',
                            label: `${card?.brand} ending in ${card?.last4} (Expires: ${card?.expires})`,
                          })) || []),
                        ]}
                      />
                      <FormInput
                        id="cardToken"
                        name="cardToken"
                        label="Card Number"
                        disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                      <FormInput
                        id="expiryDate"
                        name="expiryDate"
                        label="Expiry Date (MM/YY)"
                        disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                      <FormInput
                        id="cvc"
                        name="cvc"
                        label="CVV/CVC"
                        // disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                  )}
                </>
              )}
              {watch.processType === ProcessType.ach && (
                <>
                  <FormInput
                    id="nameOnCard"
                    name="nameOnCard"
                    label="Account Name"
                    disabled={disableFields}
                    rules={{ required: message.requiredField }}
                  />

                  {!watch.customerID && (
                    <ACHHostedComponent
                      onToken={(token) => {
                        // methods.setValue('nameOnCard', token.cardHolderName);
                        methods.setValue('accountNumber', token.accountNumber);
                        methods.setValue('routingNumber', token.routingNumber);
                        methods.setValue('accountType', token.accountType);
                        methods.setValue('holderType', token.holderType);
                        // methods.setValue('zip', token.zipCode);
                        // console.log('token', token);
                      }}
                      onError={() => {
                        methods.setValue('accountNumber', '');
                        methods.setValue('routingNumber', '');
                        methods.setValue('accountType', '');
                        methods.setValue('holderType', '');
                      }}
                    />
                  )}

                  {watch.customerID && (
                    <div>
                      <FormSelect
                        id="savedCardID"
                        name="savedCardID"
                        label="Bank Accounts"
                        onChangeCallback={(e) => handleCardSelection(e.target.value)}
                        options={[
                          { value: '', label: 'Select a bank account' },
                          ...(customerData?.gateway_customer?.paymentCards?.map((card) => ({
                            value: card?.cardID || '',
                            label: `${card?.brand} ending in ${card?.last4} (RN: ${card?.routingNumber})`,
                          })) || []),
                        ]}
                      />
                      <FormInput
                        id="accountNumber"
                        name="accountNumber"
                        label="Account Number"
                        disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                      <FormInput
                        id="routingNumber"
                        name="routingNumber"
                        label="Routing Number"
                        disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                      <FormInput
                        id="accountType"
                        name="accountType"
                        label="Account Type"
                        disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                      <FormInput
                        id="holderType"
                        name="holderType"
                        label="Account Holder Type"
                        disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                  )}
                </>
              )}
              {watch.processType === ProcessType.gpecomm && (
                <>
                  <FormInput
                    id="nameOnCard"
                    name="nameOnCard"
                    label="Account Name"
                    disabled={disableFields}
                    rules={{ required: message.requiredField }}
                  />

                  {!watch.customerID && (
                    <CardGLPHostedComponent
                      merchantID={locationFilter?.id || ''}
                      onToken={(token) => {
                        // methods.setValue('nameOnCard', token.cardHolderName);
                        methods.setValue('gpEcomm', token.paymentID);
                        // methods.setValue('zip', token.zipCode);
                        // console.log('token', token);
                      }}
                      onError={() => {
                        methods.setValue('gpEcomm', '');
                      }}
                    />
                  )}

                  {watch.customerID && (
                    <div>
                      <FormSelect
                        id="savedCardID"
                        name="savedCardID"
                        label="Bank Accounts"
                        onChangeCallback={(e) => handleCardSelection(e.target.value)}
                        options={[
                          { value: '', label: 'Select a bank account' },
                          ...(customerData?.gateway_customer?.paymentCards?.map((card) => ({
                            value: card?.cardID || '',
                            label: `${card?.brand} ending in ${card?.last4} (RN: ${card?.routingNumber})`,
                          })) || []),
                        ]}
                      />
                      <FormInput
                        id="gpEcomm"
                        name="gpEcomm"
                        label="Payment Method ID"
                        disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />

                      <FormInput
                        id="cvc"
                        name="cvc"
                        label="CVV/CVC"
                        // disabled={disableFields}
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                  )}
                </>
              )}

              {/* <FormInput
                id="email"
                name="email"
                label="Email"
                disabled={disableFields}
                rules={{  }}
              /> */}
            </div>
            <div className="col-span-1 md:col-span-1">
              <FormInput
                id="email"
                name="email"
                label="Email"
                disabled={disableFields}
                rules={{ required: message.requiredField }}
              />
              <FormPhoneNumber
                id="phoneNumber"
                name="phoneNumber"
                label="Business Phone"
                disabled={disableFields}
                country="us"
                preferredCountries={['us', 'ca']}
                rules={{ required: message.requiredField }}
              />
              <FormInput
                id="address"
                name="address"
                label="Billing Address"
                disabled={disableFields}
                rules={{ required: message.requiredField }}
              />

              {/* <FormInput
                id="state"
                name="state"
                label="State"
                disabled={disableFields}
                rules={{ required: message.requiredField }}
              /> */}

              {/* 
              <FormInput
                id="country"
                name="country"
                label="Country"
                disabled={disableFields}
                rules={{ required: message.requiredField }}
              /> */}

              <StateCountryForms
                countryKey="country"
                stateKey="state"
                message={message}
                disableFields={disableFields}
                methods={methods}
              />
              <FormInput
                id="city"
                name="city"
                label="City"
                disabled={disableFields}
                rules={{ required: message.requiredField }}
              />

              <FormInput
                id="zip"
                name="zip"
                label="Zip Code"
                // disabled={disableFields}
                rules={{ required: message.requiredField }}
              />

              {customerIdWatcher && (
                <button
                  className="text-sm text-primary-800 underline"
                  type="button"
                  onClick={() => {
                    setCostumerIdToUpdate(customerIdWatcher);
                  }}
                >
                  Something wrong with the information? Edit here
                </button>
              )}

              {/* HIDE atm Nov. 3 */}
              {/* <FormSelect
                id="country"
                name="country"
                label="Country"
                disabled={disableFields}
                rules={{ required: message.requiredField }}
                options={COUNTRIES}
              /> */}
            </div>
            <div className="col-span-1">
              <FormSelect
                id="methodVerifyOrProcess"
                name="methodVerifyOrProcess"
                label="Transaction Type"
                rules={{ required: message.requiredField }}
                options={[
                  { value: '', label: 'Select entry type' },
                  // { value: EntryType.verify, label: 'Verify' },
                  { value: EntryType.process, label: 'Process' },
                ]}
              />

              {isProcessEntry && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <FormInput
                      id="amount"
                      name="amount"
                      label=" Amount"
                      type="number"
                      placeholder="0.00"
                      className="col-span-2"
                      rules={requiredAmountIsValid}
                    />
                    <FormInput
                      id="tip"
                      name="tip"
                      type="number"
                      label=" Tip"
                      placeholder="0.00"
                      // rules={isProcessEntry && {  }}
                    />
                    <FormInput
                      id="tax"
                      name="tax"
                      type="number"
                      label=" Tax"
                      placeholder="0.00"
                      // rules={isProcessEntry && {  }}
                    />
                  </div>
                  <div className="flex justify-between border-b py-1 pb-4 last:border-b-0">
                    {/* <span className="font-semibold text-gray-700">Amount</span>
                    <span className="text-gray-600">{moneyFormat(Number(total) ?? 0)}</span> */}
                  </div>
                </>
              )}

              {isProcessEntry && (
                <div
                  className=""
                  style={{
                    fontFamily: `Inter`,
                  }}
                >
                  <PriceBreakdown {...priceCalculation} />
                </div>
              )}
              <br />
              <Button
                type="submit"
                color={isProcessEntry ? 'blue' : 'warning'}
                className="w-full"
                disabled={!methodVerifyOrProcessWatcher || !canPay}
              >
                {isProcessEntry ? 'Checkout' : 'Verify'}
              </Button>
              {transactionError && (
                <div className="mt-2 text-red-500">Transaction failed: {transactionError}</div>
              )}
              {verifyResult && (
                <div>
                  <div>Card Type: {verifyResult.cardType}</div>
                  <div>Status: {verifyResult.status}</div>
                  <div>Message: {verifyResult.message}</div>
                </div>
              )}
            </div>
          </div>
        </form>
      </FormProvider>

      <ConfirmTransactionModal
        showModal={showModal}
        handleCancel={handleCancel}
        handleConfirm={handleConfirm}
        isProcessEntry={isProcessEntry}
        watch={watch}
      />
      {viewModal}
    </>
  );
};
