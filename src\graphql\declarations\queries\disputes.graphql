query Gateway_disputes($input: Gateway_disputesInput!) {
  gateway_disputes(input: $input) {
    data {
      caseID
      location
      date
      reason
      amount
      brand
      transactionID
      hasChallenged
      daysToRepresent
      customer
      status
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}

query Gateway_dispute($input: Gateway_disputeInput!) {
  gateway_dispute(input: $input) {
    disputeID
    caseID
    location
    date
    reason
    amount
    brand
    transactionID
    paymentType
    createdBy
    method
    batchID
    paymentPlan
    source
    authCode
    entryMethod
    daysToRepresent
    tokenSource
    gsa
    emv
    last4
    customerID
    name
    email
    phone
    country
    billingAddress
    products {
      productName
      productID
      quantity
      price
      discount
      description
    }
    history {
      action
      body
      actor
      createdAt
    }
    files {
      id
      fileUrl
      purpose
      fileFormat
      createdAt
      submittedAt
    }
    status
  }
}

mutation Gateway_uploadDisputeDocument($input: Gateway_uploadDisputeDocumentInput!) {
  gateway_uploadDisputeDocument(input: $input) {
    itemID
  }
}

mutation Gateway_submitDisputeDocument($input: Gateway_submitDisputeDocumentInput!) {
  gateway_submitDisputeDocument(input: $input) {
    disputeID
  }
}
