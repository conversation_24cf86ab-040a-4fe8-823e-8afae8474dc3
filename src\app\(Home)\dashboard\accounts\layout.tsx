'use client';

import { TabLinks } from '@/components/globals';
import { AccountsPages } from '@/components/globals/Headerv3';
import { usePathname } from 'next/navigation';
import React from 'react';

export default function Page({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  return (
    <>
      {/* <TabLinks items={AccountsPages} /> */}
      <div className="mx-4">{children}</div>
    </>
  );
}
