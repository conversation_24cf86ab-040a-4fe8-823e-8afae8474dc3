import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const hashCustomerData = {
  name: (name: string | null | undefined) => {
    if (!name) return '';
    const parts = name.split(' ');
    if (parts.length < 2) return name;
    return `${parts[0][0]}. ${parts[parts.length - 1]}`;
  },

  email: (email: string | null | undefined) => {
    if (!email) return '';
    const [username, domain] = email.split('@');
    if (!domain) return email;
    const hashedUsername = username[0] + '*'.repeat(username.length - 1);
    const [domainName, tld] = domain.split('.');
    const hashedDomain = domainName[0] + '*'.repeat(domainName.length - 1);
    return `${hashedUsername}@${hashedDomain}.${tld}`;
  },

  phone: (phone: string | null | undefined) => {
    if (!phone) return '';
    return '*'.repeat(phone.length - 4) + phone.slice(-4);
  },

  address: (address: string | null | undefined) => {
    if (!address) return '';
    const parts = address.split(',');
    return parts[0] || address; // Only return the street address part
  },
};

export const hashCustomerName = (name: string): string => {
  if (!name) return '';
  const parts = name.split(' ');
  if (parts.length < 2) return name;
  return `${parts[0][0]}. ${parts[parts.length - 1]}`;
};

export const hashEmail = (email: string): string => {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (!domain) return email;
  return `${username[0]}${'*'.repeat(username.length - 1)}@${domain[0]}${'*'.repeat(domain.length - 1)}`;
};

export const hashPhone = (phone: string): string => {
  if (!phone) return '';
  return '*'.repeat(phone.length - 4) + phone.slice(-4);
};

export const hashAddress = (address: string): string => {
  if (!address) return '';
  const parts = address.split(',');
  return parts[0] || address;
};

export const moneyFormat = (amount: number | null | undefined): string => {
  if (amount === null || amount === undefined) return '--';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

export const moneyFormatString = (amount: string | number | null | undefined): string => {
  if (!amount) return '--';
  if (typeof amount === 'number') {
    return moneyFormat(amount);
  }
  return moneyFormat(parseFloat(amount));
};

export const int2DecToFloat = (amount: number | null | undefined): number => {
  if (amount === null || amount === undefined) return 0;
  return amount / 100;
};

export function int2DecToFloatFromString(amount: string | number | null | undefined): number {
  if (amount === null || amount === undefined) return 0;
  if (typeof amount === 'string') {
    return parseInt(amount, 10) / 100;
  }
  return amount / 100;
}

export function formatEIN(ein: string | null | undefined): string {
  if (!ein) return '';
  // Format EIN as XX-XXXXXXX
  const cleaned = ein.replace(/\D/g, '');
  if (cleaned.length < 2) return cleaned;
  return `${cleaned.slice(0, 2)}-${cleaned.slice(2)}`;
}

export const formatPhoneNumber = (phoneNumber: string | null | undefined): string => {
  if (!phoneNumber) return '';

  // Strip all non-numeric characters
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Format as (XXX) XXX-XXXX
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }

  return phoneNumber;
};

export function formatSSN(ssn: string | null | undefined): string {
  if (!ssn) return '';
  // Format SSN as XXX-XX-XXXX
  const cleaned = ssn.replace(/\D/g, '');
  if (cleaned.length < 3) return cleaned;
  if (cleaned.length < 5) return `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
  return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 5)}-${cleaned.slice(5)}`;
}

// This function handles both string and number inputs
export function moneyFormatNumber(amount: string | number | null | undefined): string {
  if (amount === null || amount === undefined) return '--';
  if (typeof amount === 'string') {
    return moneyFormat(parseFloat(amount));
  }
  return moneyFormat(amount);
}

// This function converts a float to an integer with 2 decimal places
export function floatToInt2Dec(amount: number | null | undefined): number {
  if (amount === null || amount === undefined) return 0;
  return Math.round(amount * 100);
}
