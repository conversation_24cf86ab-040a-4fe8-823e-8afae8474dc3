export const hashCustomerData = {
  name: (name: string | null | undefined) => {
    if (!name) return '';
    const parts = name.split(' ');
    if (parts.length < 2) return name;
    return `${parts[0][0]}. ${parts[parts.length - 1]}`;
  },

  email: (email: string | null | undefined) => {
    if (!email) return '';
    const [username, domain] = email.split('@');
    if (!domain) return email;
    const hashedUsername = username[0] + '*'.repeat(username.length - 1);
    const [domainName, tld] = domain.split('.');
    const hashedDomain = domainName[0] + '*'.repeat(domainName.length - 1);
    return `${hashedUsername}@${hashedDomain}.${tld}`;
  },

  phone: (phone: string | null | undefined) => {
    if (!phone) return '';
    return '*'.repeat(phone.length - 4) + phone.slice(-4);
  },

  address: (address: string | null | undefined) => {
    if (!address) return '';
    const parts = address.split(',');
    return parts[0] || address; // Only return the street address part
  },
};
