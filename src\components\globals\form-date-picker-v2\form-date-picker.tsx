import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label, HelperText } from 'flowbite-react';
import 'react-datepicker/dist/react-datepicker.css';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
import moment from 'moment';

export type FormDatepickerv2Props = {
  name: string;
  helperText?: string;
  id: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  visible?: boolean;
  readOnly?: boolean;
  minDate?: Date;
  maxDate?: Date;
  flex?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (date: Date | null) => void;
  label?: string;
  disabled?: boolean;
  defaultValue?: Date | null;
  tabIndex?: number;
  endAdornment?: React.ReactNode;
};

export const FormDatepickerv2 = ({
  id,
  name,
  label,
  rules,
  disabled,
  helperText = '',
  defaultValue,
  visible = true,
  readOnly = false,
  minDate,
  maxDate,
  flex,
  tabIndex,
  tooltip,
  endAdornment,
  onChangeCallback = (date: Date | null) => {
    date;
  },
  ...props
}: FormDatepickerv2Props): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  const formatDateForInput = (date: Date | null): string => {
    if (!date) return '';
    return moment(date).format('YYYY-MM-DD');
  };

  const parseInputDate = (value: string): Date | null => {
    if (!value) return null;
    const parsed = moment(value, 'YYYY-MM-DD');
    return parsed.isValid() ? parsed.toDate() : null;
  };

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={`relative w-full ${visible ? '' : 'hidden'} ${flex ? `flex-${flex}` : ''}`}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div className="relative w-full">
            <input
              type="date"
              className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500"
              value={formatDateForInput(value)}
              onChange={(e) => {
                const parsedDate = parseInputDate(e.target.value);
                onChange(parsedDate);
                onChangeCallback(parsedDate);
              }}
              id={id}
              ref={ref}
              onBlur={onBlur}
              readOnly={readOnly}
              disabled={isDisabled()}
              tabIndex={tabIndex}
              min={minDate ? formatDateForInput(minDate) : undefined}
              max={maxDate ? formatDateForInput(maxDate) : undefined}
              {...props}
            />

            {endAdornment && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                {endAdornment}
              </div>
            )}
          </div>
          <HelperText color={invalid ? 'failure' : 'default'}>
            {invalid ? error?.message : helperText}
          </HelperText>
        </div>
      )}
    />
  );
};

export default FormDatepickerv2;
