'use client';

import { FormInput, PageHeader } from '@/components/globals';
import { useLocationSelector } from '@/components/hooks';
import { message } from '@/components/shared/utils';
import { Button } from '@/components/ui/button';
import { HelperText } from 'flowbite-react';
import { FormProvider, useForm } from 'react-hook-form';

const Integrations = () => {
  const { locationSelectorElement } = useLocationSelector({});
  const methods = useForm({
    defaultValues: {
      stripePublicKey: '',
      stripeSecretKey: '',
    },
  });

  const { isSubmitting } = methods.formState;

  const onSync = (data) => {
    // TODO: Implement submit logic
    console.log('Form submitted:', data);
  };

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Integration" />
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3"></div>
      </div>

      <div className="py-10">
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSync)}>
            <div className="flex items-center justify-between space-x-4">
              <div className="max-w-2xl flex-1 space-y-4">
                <FormInput
                  id="stripePublicKey"
                  name="stripePublicKey"
                  label="Stripe public key"
                  type="text"
                  helperText=""
                  rules={{
                    required: message.requiredField,
                    pattern: {
                      value: /^(pk_live_|pk_test_)[A-Za-z0-9]{24,}$/,
                      message: 'Enter a valid Stripe public key (e.g., pk_live_xxx or pk_test_xxx)',
                    },
                  }}
                />
                <FormInput
                  id="stripeSecretKey"
                  name="stripeSecretKey"
                  label="Stripe secret key"
                  type="text"
                  helperText=""
                  rules={{
                    required: message.requiredField,
                    pattern: {
                      value: /^(sk_live_|sk_test_)[A-Za-z0-9]{24,}$/,
                      message: 'Enter a valid Stripe secret key (e.g., sk_live_xxx or sk_test_xxx)',
                    },
                  }}
                />
                <HelperText className="mt-5 py-2" color={'gray'}>
                  Enter your stripe credentials to sync your transactions, customers, and products
                  with the system. Please note that we do not store or save your API keys.
                </HelperText>
              </div>
              <div className="mx-5">
                <Button type="submit" disabled={isSubmitting} variant={'primary'}>
                  Sync
                </Button>
              </div>
            </div>
          </form>
        </FormProvider>
      </div>
    </>
  );
};

export default Integrations;
