// import { graphql } from '../generated';

// export const updateAuroraDraft = graphql(`
//   mutation Processor_aur_draft_update($input: Processor_aur_draft_updateInput!) {
//     processor_aur_draft_update(input: $input)
//   }
// `);

// export const createAuroraDraft = graphql(`
//   mutation Processor_aur_draft_create($input: Processor_aur_draft_createInput!) {
//     processor_aur_draft_create(input: $input)
//   }
// `);

// export const submitAuroraDraft = graphql(`
//   mutation Processor_aur_draft_submit($input: Processor_aur_draft_submitInput!) {
//     processor_aur_draft_submit(input: $input) {
//       urlForSigning
//       applicationId
//       applicationNumber
//     }
//   }
// `);
