import { useEffect, useMemo, useState } from 'react';
import { PageHeader, TopComponent, useDataGridView } from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import {
  Gateway_CategoriesDocument,
  GatewayUniCategoriesOutputData,
} from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import { CategoryAdd } from './components/category-add';
import { useSearchParams } from 'next/navigation';
import { CategoryUpdate } from './components/category-update';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export const CategoriesTab = () => {
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);
  const queryParams = useSearchParams();
  const categoryId = queryParams?.get('id');
  const [selectedCategoryId, setSelectedCategoryId] = useState(categoryId ?? null);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({});

  const {
    data: categoriesListData,
    loading: categoriesListLoading,
    refetch: refetchcategoriesListData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    maxVariables,
  } = useDataGridView({
    query: Gateway_CategoriesDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refetchcategoriesListData();
    }
  }, [locationFilter]);

  const rows = useMemo(() => {
    const data = categoriesListData?.gateway_categories?.data ?? [];
    return data.filter(
      (category): category is NonNullable<typeof category> & GatewayUniCategoriesOutputData =>
        category !== null,
    );
  }, [categoriesListData]);

  const columns: Column<GatewayUniCategoriesOutputData>[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      onClick: (row) => setSelectedCategoryId(row?.id ?? ''),
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'description',
      header: 'Description',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      // renderCell: (row) => (
      //   <>
      //     {(row.description?.slice(0, 100) ?? '') +
      //       ((row.description?.length ?? 0) > 100 ? '...' : '')}
      //   </>
      // ),
    },
    {
      key: 'subCategory',
      header: 'Sub categories',
      valueGetter: (row) => row.subCategory?.join(', '),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      // renderCell: (row) => (
      //   <>
      //     {(row.subCategory?.join(',').slice(0, 50) ?? '') +
      //       ((row.subCategory?.length ?? 0) > 50 ? '...' : '')}
      //   </>
      // ),
    },
  ];

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_CategoriesDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_categories?.data ?? [];
  };

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Categories" />
        <CategoryAdd
          refetchListPage={refetchcategoriesListData}
          groupID={locationFilter?.id ?? ''}
        />
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
          <TopComponent value={searchValue} setValue={setSearchValue}>
            <ExportCSV4 dataFetcher={fetchExportData} reportName="categories" columns={columns} />
          </TopComponent>
        </div>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={rows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={categoriesListLoading || loadingGroupList}
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={categoriesListData?.gateway_categories?.page?.total ?? 0}
        />
      </div>
      <CategoryUpdate
        isOpen={selectedCategoryId !== null}
        onClose={() => setSelectedCategoryId(null)}
        refetchListPage={refetchcategoriesListData}
        queryData={{
          categoryID: selectedCategoryId ?? '',
          groupID: locationFilter?.id ?? '',
        }}
      />
    </>
  );
};
