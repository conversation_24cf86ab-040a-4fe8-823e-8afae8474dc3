'use client';

import { useEffect, useState } from 'react';
import { NextPage } from 'next';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { GetPaymentPageDataUNI } from '@/graphql/declarations/payments';
import { PaymentFormProps } from '@/components/Payment';

interface PaymentInitiateProps {
  type: 'payment_initiate_props';
  publishableKey: string;
  amount: number;
  currency: string;
  mode: string;
  productDetails: { productId: string; priceId: string };
  contact?: {
    id: string;
    name: string;
    email: string;
    contact: string;
  };
  orderId: string;
  transactionId: string;
  subscriptionId: string;
  locationId: string;
}

const Payment: NextPage = () => {
  const [paymentData, setPaymentData] = useState<PaymentInitiateProps | null>(null);

  async function loadInvoiceData() {
    if (!paymentData) return;

    const d = await apolloClient.query({
      query: GetPaymentPageDataUNI,
      variables: {
        input: {
          apiKey: paymentData?.publishableKey,
          transactionID: paymentData?.transactionId,
        },
      },
    });

    let data = d.data.ghl_api_getPaymentPageDataUNI?.data;
    let payData = d.data.ghl_api_getPaymentPageDataUNI?.paymentLink;
    if (!payData) return;

    // redict to /pay-ghl with search params
    const searchParams = new URLSearchParams();
    searchParams.set('paymentData', payData.paymentData);
    searchParams.set('token', payData.token);
    searchParams.set('groupID', payData.groupID);
    searchParams.set('publishableKey', paymentData?.publishableKey);
    searchParams.set('transactionId', paymentData?.transactionId);
    searchParams.set(
      'initialValues',
      JSON.stringify({
        fullName: data?.customerData?.name,
        email: data?.customerData?.email,
        city: data?.customerData?.city,
        companyName: data?.customerData?.company,
        country: data?.customerData?.country,
        state: data?.customerData?.state,
        zipCode: data?.customerData?.postal_code,
        street: data?.customerData?.address_line_1,
        phoneNumber: data?.customerData?.phone,
      } as PaymentFormProps['initialValues']),
    );
    window.location.href = `/pay-ghl?${searchParams.toString()}`;
  }

  useEffect(() => {
    loadInvoiceData();
    setTimeout(() => {}, 5000);
  }, [paymentData]);

  const isReady = paymentData !== null;

  async function providerReadyFlag() {
    const key = await new Promise((resolve) => {
      window.parent.postMessage(
        JSON.stringify({
          type: 'custom_provider_ready',
          loaded: true,
        }),
        '*',
      );
    });
  }

  function processEvents(rawdata: any) {
    try {
      const data = JSON.parse(rawdata);
      const dType = data.type;
      if (!dType) {
        console.log('No type found in data');
        return;
      }

      switch (dType) {
        case 'payment_initiate_props': {
          let d = data as PaymentInitiateProps;
          setPaymentData(d);
          break;
        }
      }
    } catch (e) {
      console.error('Error in parsing data', e);
    }
  }

  useEffect(() => {
    providerReadyFlag();
    window.addEventListener('message', ({ data }) => {
      processEvents(data);
    });
  }, []);

  return <div></div>;
};

export default Payment;
