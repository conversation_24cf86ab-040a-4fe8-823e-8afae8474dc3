import * as Icons from 'lucide-react';
import { Card } from '@/components/ui/card';
import { mockAccountData } from '@/mock/account-status-data';
import { BaseAccountModal } from './BaseAccountModal';
import { InfoItem } from './InfoItem';

interface ActiveStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ActiveStatusModal = ({ isOpen, onClose }: ActiveStatusModalProps) => {
  const data = mockAccountData.active;

  return (
    <BaseAccountModal
      isOpen={isOpen}
      onClose={onClose}
      title="Active Account"
      accountData={data}
      statusIcon={Icons.CheckCircle2}
      statusColor="text-green-500"
    >
      {/* Account Performance */}
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Account Performance</h3>
        <div className="grid gap-4 md:grid-cols-2">
          {' '}
          <InfoItem
            icon={Icons.CircleDollarSign}
            label="Monthly Volume"
            value={data.performance.monthlyVolume}
          />
          <InfoItem
            icon={Icons.Calendar}
            label="Last Transaction"
            value={data.performance.lastTransaction}
          />
          <InfoItem
            icon={Icons.CircleDollarSign}
            label="Average Ticket"
            value={data.performance.averageTicket}
          />
          <InfoItem
            icon={Icons.CircleDollarSign}
            label="Month to Date"
            value={data.performance.monthToDateVolume}
          />
        </div>
      </Card>

      {/* Transaction Limits */}
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Transaction Limits</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <InfoItem
            icon={Icons.CircleDollarSign}
            label="Card Per Transaction"
            value={`$${(5000).toLocaleString()}`}
          />
          <InfoItem
            icon={Icons.CircleDollarSign}
            label="Card Monthly"
            value={`$${(50000).toLocaleString()}`}
          />
          <InfoItem
            icon={Icons.CircleDollarSign}
            label="Bank Transfer Per Transaction"
            value={`$${(10000).toLocaleString()}`}
          />
          <InfoItem
            icon={Icons.CircleDollarSign}
            label="Bank Transfer Monthly"
            value={`$${(100000).toLocaleString()}`}
          />
        </div>
        <div className="mt-4 grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Card Monthly Usage</div>
            <div className="h-2 w-full rounded-full bg-gray-200">
              <div className="h-full w-[45%] rounded-full bg-green-500" />
            </div>
            <div className="text-sm text-gray-600">45% of limit used</div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">
              Bank Transfer Monthly Usage
            </div>
            <div className="h-2 w-full rounded-full bg-gray-200">
              <div className="h-full w-[25%] rounded-full bg-green-500" />
            </div>
            <div className="text-sm text-gray-600">25% of limit used</div>
          </div>
        </div>
      </Card>

      {/* Bank Account Information */}
      <Card className="p-4">
        <h3 className="mb-4 text-lg font-semibold">Bank Account Information</h3>
        <div className="grid gap-4 md:grid-cols-2">
          {' '}
          <InfoItem icon={Icons.Building2} label="Bank Name" value="JPMORGAN CHASE BANK, NA" />
          <InfoItem icon={Icons.Building2} label="Account Number" value="**** **** 4321" />
          <InfoItem icon={Icons.Building2} label="Routing Number" value="*********" />
        </div>
      </Card>
    </BaseAccountModal>
  );
};
