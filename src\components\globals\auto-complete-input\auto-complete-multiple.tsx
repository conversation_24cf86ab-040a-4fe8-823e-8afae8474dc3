import { Checkbox, Label } from 'flowbite-react';
import React, { ChangeEvent, useEffect, useRef, useState } from 'react';
import { TextInput } from 'flowbite-react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { FaTimes } from 'react-icons/fa'; // For the remove button
import { AutoCompleteOption } from './auto-complete';

type DataType = {
  [key: string]: string; // Dynamic key-value pairs
};

type AutoCompleteInputProp = {
  options: AutoCompleteOption[];
  onChange?: (value: AutoCompleteOption[]) => void;
  value: AutoCompleteOption[];
  setValue: (value: AutoCompleteOption[]) => void;
};

export const AutoCompleteInputV2 = ({
  options,
  onChange,
  value = [],
  setValue,
}: AutoCompleteInputProp) => {
  const [query, setQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [optionList, setOptionList] = useState(options);
  // const [value, setValue] = useState<AutoCompleteOption[]>([]);
  const divRef = useRef<HTMLDivElement>(null);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setShowDropdown(true);
    setQuery(value);
    setOptionList(
      options.filter((option) => option.label.toLowerCase().includes(value.toLowerCase())),
    );
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (divRef.current && !divRef.current.contains(event.target as Node)) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside, true);
    return () => {
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, []);

  useEffect(() => {
    if (optionList.length === 0 && options.length > 0) {
      setOptionList(options);
    }
  }, [options]);

  const handleOptionClick = (option: AutoCompleteOption) => {
    if (!value.some((selected) => selected.id === option.id)) {
      const updatedvalue = [...value, option];
      setQuery('');
      setValue(updatedvalue);
      if (onChange) {
        onChange(updatedvalue);
      }
    }
  };

  const handleRemoveOption = (optionId: string) => {
    const updatedvalue = value.filter((option) => option.id !== optionId);
    setValue(updatedvalue);
    if (onChange) {
      onChange(updatedvalue);
    }
  };

  return (
    <div className="relative" tabIndex={0} onFocus={() => setShowDropdown(true)} ref={divRef}>
      <div className="flex flex-wrap gap-2 rounded border bg-gray-50">
        {value.map((option) => (
          <div key={option.id} className="flex items-center rounded bg-gray-200 p-1">
            <span>{option.label}</span>
            <FaTimes
              className="ml-2 cursor-pointer text-gray-600"
              onClick={() => handleRemoveOption(option.id)}
            />
          </div>
        ))}
        <input
          type="text"
          value={query}
          onChange={handleChange}
          placeholder={value.length > 0 ? '' : 'Search for location'}
          className="border-none! flex-grow bg-none p-1"
        />
        <div className="absolute right-6 top-5">
          {showDropdown ? <FaChevronUp /> : <FaChevronDown />}
        </div>
      </div>

      {showDropdown && (
        <ul className="absolute z-50 mt-1 w-full rounded border border-gray-300 bg-white">
          {optionList.length === 0 ? (
            <li className="p-2">No results found</li>
          ) : (
            optionList.map((option) => (
              <li
                key={option.label}
                className="cursor-pointer p-2 hover:bg-gray-200"
                onClick={() => handleOptionClick(option)}
              >
                <div className="flex items-center">
                  <Checkbox
                    id={option.id}
                    value={option.id}
                    checked={value.some((selected) => selected.id === option.id)}
                    readOnly
                  />
                  <Label htmlFor={option.id} className="ml-2">
                    {option.label}
                  </Label>
                </div>
              </li>
            ))
          )}
        </ul>
      )}
    </div>
  );
};
