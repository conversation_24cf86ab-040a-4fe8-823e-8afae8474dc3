import { FormInput } from '@/components/globals';
import FormTextDisplay from '@/components/globals/form-text/form-text';
import { message } from '@/components/shared/utils';
import { GET_BANK_INFO_BY_ROUTING } from '@/graphql/declarations/processor-test';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { cn } from '@/lib/utils';
import { Promisable } from '@/types/types';
import clsx from 'clsx';
import type { NextPage } from 'next';
import { useSearchParams } from 'next/navigation';
import { MutableRefObject, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { updateData } from '../updateData';
import styles from './index.module.css';

const BankPage: NextPage<{
  triggerSubmit?: MutableRefObject<() => Promisable<boolean>>;
  initialData?: any;
}> = (args) => {
  const searchParams = useSearchParams();

  const methods = useForm({
    defaultValues: args.initialData ?? {
      routingNumber: '',
      bankName: '',
      accountNumber: '',
      confirmAccountNumber: '',
      nameOnAccount: '',
    },
    mode: 'onChange',
  });

  const { watch, setError, clearErrors, trigger } = methods;

  useEffect(() => {
    const subscription = watch((values, { name }) => {
      if (name === 'accountNumber' || name === 'confirmAccountNumber') {
        const { accountNumber, confirmAccountNumber } = values;

        if (accountNumber && confirmAccountNumber && accountNumber !== confirmAccountNumber) {
          setError('confirmAccountNumber', {
            type: 'validate',
            message: 'Account numbers must match.',
          });
        } else {
          clearErrors('confirmAccountNumber');
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, setError, clearErrors]);

  useEffect(() => {
    if (args.triggerSubmit) {
      args.triggerSubmit.current = async () => {
        const isValid = await trigger();
        if (isValid) {
          const val = methods.getValues();
          try {
            return await updateData({
              submitType: 'bank',
              data: val,
              groupID: searchParams?.get('groupID') ?? undefined,
              form: methods,
            });
          } catch (error) {
            console.log(error);
            return false;
          }
        }
        return false;
      };
    }
  }, [args.triggerSubmit]);

  const routineNumber = methods.watch('routingNumber');

  const fetchBankName = async (_routingNumber: string) => {
    if (_routingNumber.length === 9) {
      try {
        const resp = await apolloClient.query({
          query: GET_BANK_INFO_BY_ROUTING,
          variables: {
            input: {
              code: _routingNumber,
            },
          },
        });

        if (resp.data.processor_tst_bank_routing?.data) {
          methods.setValue('bankName', resp.data.processor_tst_bank_routing.data.name);
          clearErrors('routingNumber');
        } else {
          methods.setValue('bankName', '');
          setError('routingNumber', { type: 'manual', message: 'Invalid routing number.' });
        }
      } catch (error) {
        console.log(error);
        methods.setValue('bankName', '');
        setError('routingNumber', { type: 'manual', message: 'Bank name cannot be found.' });
      }
    } else {
      methods.setValue('bankName', '');
      setError('routingNumber', {
        type: 'manual',
        message: 'Please input the 9 routing numbers correctly.',
      });
    }
  };

  useEffect(() => {
    fetchBankName(routineNumber);
  }, [routineNumber]);

  return (
    <FormProvider {...methods}>
      <form className="space-y-4">
        <div className={styles.bank}>
          <div className={clsx('min-h-96', styles.inputWidgetLg)}>
            <div className={styles.inputWidgetLgInner}>
              <div className={styles.inputFieldParent}>
                <FormInput
                  id="routingNumber"
                  name="routingNumber"
                  label="Routing #"
                  rules={{
                    required: message.requiredField,
                    validate: async (value) => {
                      if (value.length !== 9) {
                        return 'Please input the 9 routing numbers correctly.';
                      }
                      const currentError = methods.getFieldState(
                        'routingNumber',
                        methods.formState,
                      )?.error;
                      if (currentError?.message) {
                        return currentError.message;
                      }
                      return true;
                    },
                  }}
                />
                <FormTextDisplay id="bankName" name="bankName" label="Bank Name" />
              </div>
            </div>
            <div className={cn(styles.frameParent, 'max-w-[49%]')}>
              <div className={styles.inputFieldGroup}>
                <FormInput
                  id="accountNumber"
                  name="accountNumber"
                  label="Account Number"
                  rules={{ required: message.requiredField }}
                />
                <FormInput
                  id="confirmAccountNumber"
                  name="confirmAccountNumber"
                  label="Re-enter Account Number"
                  rules={{
                    required: message.requiredField,
                    validate: (value) =>
                      value === methods.getValues('accountNumber') || 'Account numbers must match.',
                  }}
                />
                <FormInput
                  id="nameOnAccount"
                  name="nameOnAccount"
                  label="Name on Account"
                  rules={{ required: message.requiredField }}
                />
              </div>
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default BankPage;
