'use client';

import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import { MerchantForm } from '../types/merchart-schema';
import { FormControl, FormField, FormItem } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { documentTypes } from '@/consts/DocumentTypes';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useSearchParams } from 'next/navigation';
import { useQuery } from '@apollo/client';
import { deleteApplicationFile, getGroups } from '@/graphql/declarations/group';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { LoaderSquares } from '@/components/globals/Loaders/Square';
import axios from 'axios';
import { AUTHSTORE } from '@/lib/auth-storage';

const Documents = ({ formState }: { formState: MerchantForm }) => {
  // const [categories, setCategories] = useState<
  //   {
  //     label: string;
  //     value: string;
  //   }[]
  // >([]);

  // const searchParams = useSearchParams();

  // const groupID = searchParams?.get('modal');

  // const {
  //   data: groupData,
  //   refetch: groupRefetch,
  //   loading: groupLoading,
  // } = useQuery(getGroups, {
  //   variables: {
  //     where: {
  //       id: {
  //         equals: groupID,
  //       },
  //     },
  //     skip: 0,
  //     take: 1,
  //   },
  //   skip: !groupID,
  // });

  // function formatFileSize(bytes: number) {
  //   if (!bytes) return '';
  //   const units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  //   let index = 0;

  //   while (bytes >= 1024 && index < units.length - 1) {
  //     bytes /= 1024;
  //     index++;
  //   }

  //   return `${bytes.toFixed(2)} ${units[index]}`;
  // }

  // const AddAnotherDocument = () => {
  //   let filteredDocumentTypes = documentTypes.filter(
  //     (type) => !categories.find((category) => category.value === type.value),
  //   );
  //   if (filteredDocumentTypes.length === 0) return;
  //   setCategories((prev) => [...prev, filteredDocumentTypes[0]]);
  // };

  // async function uploadFile(args: { groupID: string; file: File; type: string; label: string }) {
  //   let targetString = `upload-${args.type}-string`;
  //   let targetBar = `upload-${args.type}-bar`;
  //   console.log({
  //     args,
  //     targetType: targetString,
  //   });
  //   const formData = new FormData();
  //   formData.append('File', args.file);
  //   formData.append('Name', args.file.name);
  //   formData.append('Type', args.type);
  //   formData.append('groupID', args.groupID);

  //   try {
  //     const string = document.getElementById(targetString);
  //     if (string) {
  //       string.innerHTML = args.label;
  //     }
  //     const response = await axios.post(`/api/provider/aur/draft/upload`, formData, {
  //       baseURL: process.env.NEXT_PUBLIC_API_URL,
  //       headers: {
  //         'Content-Type': 'multipart/form-data',
  //         Authorization: `Bearer ${AUTHSTORE.get()}`,
  //       },
  //       onUploadProgress: (progressEvent) => {
  //         const percentCompleted = Math.round(
  //           (progressEvent.loaded * 100) / (progressEvent.total ?? 1),
  //         );
  //         // console.log(percentCompleted);
  //         const bar = document.getElementById(targetBar);
  //         if (bar) {
  //           bar.style.width = `${percentCompleted}%`;
  //         }
  //       },
  //     });

  //     const bar = document.getElementById(targetBar);
  //     if (bar) {
  //       bar.style.width = `0%`;
  //     }

  //     if (string) {
  //       string.innerHTML = '';
  //     }
  //     // get the progress of the upload
  //     // console.log(response);
  //     groupRefetch();
  //   } catch (error) {
  //     console.error('Error uploading file', error);
  //   }
  // }

  // // let fileList = groupData?.groups?.[0]?.processorAUR?.[0]?.processor_aur_uploads?.files ?? [];

  // // get the unique types of files
  // // const uniqueTypes = Array.from(new Set(fileList.map((file: any) => file?.type)));

  // let lastType = useRef<string | null>(null);
  // useEffect(() => {
  //   if (!uniqueTypes) return;
  //   if (uniqueTypes.length === 0) return;

  //   const newCategories = uniqueTypes
  //     .map((type) => {
  //       const exDocType = documentTypes.find((docType) => docType.value === type);
  //       return exDocType;
  //     })
  //     .filter((type) => type);

  //   if (lastType.current) {
  //     if (lastType.current === JSON.stringify(newCategories)) return;
  //   }
  //   lastType.current = JSON.stringify(newCategories);

  //   setCategories(newCategories as any);
  // }, [uniqueTypes]);

  // // const selectedFileType = useRef<string | null>(null);

  // async function deleteFileUpload(args: { id?: string | null; groupID?: string | null }) {
  //   if (!args.id || !args.groupID) return;
  //   const resp = await apolloClient.mutate({
  //     mutation: deleteApplicationFile,
  //     variables: {
  //       input: {
  //         fileID: args.id,
  //         groupId: args.groupID,
  //       },
  //     },
  //   });
  //   await groupRefetch();
  //   console.log(resp);
  // }

  // const fileUploadBar = useRef<string | null>(null);
  // const fileUploadString = useRef<string | null>(null);
  // const fileUploadType = useRef<string | null>(null);

  // return (
  //   <div className="flex flex-col gap-6">
  //     <div className="flex w-full flex-col gap-4">
  //       {groupLoading && (
  //         <div className="flex h-32 items-center justify-center">
  //           <LoaderSquares />
  //         </div>
  //       )}
  //       {categories.map((obj, index) => (
  //         <div
  //           key={obj.value}
  //           className="flex w-full flex-col gap-8 rounded-lg border border-[#dbeaf7] p-6"
  //         >
  //           <FormField
  //             control={formState.control}
  //             name={`documents[${index}].document_type` as any}
  //             render={() => (
  //               <FormItem className="flex flex-1 flex-col">
  //                 <Label className="text-base font-normal text-zinc-700">
  //                   Document Type<span className="text-red-400"> *</span>
  //                 </Label>
  //                 <FormControl className="mt-2">
  //                   <Select
  //                     onValueChange={(item: string) => {
  //                       const newCat = [...categories];
  //                       const newItem = documentTypes.find((type) => type.value === item);
  //                       // @ts-ignore
  //                       newCat[index] = { ...newItem };
  //                       setCategories(newCat);
  //                     }}
  //                     value={obj.value}
  //                   >
  //                     <SelectTrigger
  //                       className={cn(
  //                         'border border-zinc-400 bg-background text-zinc-600 hover:bg-accent hover:text-zinc-600',
  //                       )}
  //                     >
  //                       <SelectValue placeholder="Select Document Type" />
  //                     </SelectTrigger>
  //                     <SelectContent>
  //                       {documentTypes.map((obj, i) => (
  //                         <SelectItem key={obj.value} value={obj.value} className="text-zinc-600">
  //                           {obj.label}
  //                         </SelectItem>
  //                       ))}
  //                     </SelectContent>
  //                   </Select>
  //                 </FormControl>
  //               </FormItem>
  //             )}
  //           />
  //           <div className="group relative flex-1">
  //             <div className="flex flex-col gap-y-2 text-[#495057]">
  //               <Label className="text-base font-normal text-zinc-600">
  //                 {obj.label} Files
  //                 <span className="text-red-400"> </span>
  //               </Label>
  //               {fileList
  //                 .filter((file: any) => {
  //                   return file?.type === obj?.value;
  //                 })
  //                 .filter((file: any) => !file?.deletedAt)
  //                 .map((file: any) => (
  //                   <div key={file?.id} className="flex w-full items-center gap-4">
  //                     <div className="flex flex-1 flex-col gap-2">
  //                       <span className="text-sm text-zinc-600">{file?.name}</span>
  //                       <span className="text-xs text-zinc-400">
  //                         {moment(file?.createdAt).format('MMM DD, YYYY')}
  //                       </span>
  //                     </div>
  //                     <Button
  //                       variant="destructive"
  //                       onClick={() => {
  //                         deleteFileUpload({
  //                           id: file?.id,
  //                           groupID: groupID,
  //                         });
  //                       }}
  //                     >
  //                       Delete
  //                     </Button>
  //                   </div>
  //                 ))}
  //               <p id={`upload-${obj.value}-string`} className="text-xs text-zinc-400"></p>
  //               <div className="mt-4 h-1 w-full rounded-lg bg-accent">
  //                 <div
  //                   className="h-full rounded-lg bg-blue-600"
  //                   id={`upload-${obj.value}-bar`}
  //                   style={{
  //                     width: '0%',
  //                   }}
  //                 ></div>
  //               </div>
  //               <Input
  //                 type="file"
  //                 id={`file-${obj.value}-select`}
  //                 accept="/*"
  //                 multiple
  //                 className="absolute left-0 h-full w-[50%] cursor-pointer opacity-0"
  //                 onChange={(e) => {
  //                   (async function () {
  //                     const files = e.target.files;
  //                     if (!files || files?.length == 0) return;

  //                     if (!groupID) return;

  //                     // console.log({
  //                     //   groupID: groupID,
  //                     //   file: files[0],
  //                     //   type: selectedFileType.current,
  //                     //   obj,
  //                     // });
  //                     for (let i = 0; i < files.length; i++) {
  //                       await uploadFile({
  //                         groupID: groupID,
  //                         file: files[i],
  //                         type: obj.value,
  //                         label: `Uploading file ${i + 1} of ${files.length}`,
  //                       });
  //                     }
  //                   })();
  //                 }}
  //               />
  //               <Button
  //                 variant="primary"
  //                 className="mt-1"
  //                 disabled={!obj?.value}
  //                 onClick={() => {
  //                   console.log(obj.label, obj.value);

  //                   document.getElementById(`file-${obj.value}-select`)?.click();
  //                 }}
  //               >
  //                 Add {obj?.label} File
  //               </Button>
  //               {/* <Button
  //                 variant="outline"
  //                 disabled={!obj?.value}
  //                 onClick={(e) => {
  //                   e.preventDefault();
  //                   setCategories((prev) => {
  //                     const newCategories = [...prev];
  //                     newCategories.splice(index, 1);
  //                     return newCategories;
  //                   });
  //                 }}
  //               >
  //                 Remove All Documents In This Type
  //               </Button> */}
  //             </div>
  //           </div>
  //         </div>
  //       ))}
  //     </div>
  //     <Button variant="primary" className="ml-auto mr-4 w-fit" onClick={AddAnotherDocument}>
  //       Add New Document
  //     </Button>
  //   </div>
  // );

  return <></>;
};

export default Documents;
