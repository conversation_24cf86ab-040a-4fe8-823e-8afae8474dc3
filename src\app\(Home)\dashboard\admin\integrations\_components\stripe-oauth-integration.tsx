'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, AlertCircle } from 'lucide-react';
import LoadingButton from '@/components/globals/loading-button';
import { toast } from 'react-toastify';
import { axiosClient } from '@/lib/axios';

interface StripeOAuthIntegrationProps {
  groupId: string;
}

export default function StripeOAuthIntegration({ groupId }: StripeOAuthIntegrationProps) {
  const [loading, setLoading] = useState(false);

  const handleConnectWithStripe = async () => {
    if (!groupId) {
      toast.error('Please select a group first');
      return;
    }

    setLoading(true);
    try {
      // Generate a random state value for CSRF protection
      const state = Math.random().toString(36).substring(2, 15);

      // The redirect URL will be back to the current page
      const redirectTo = window.location.href;

      const response = await axiosClient.post('/api/stripe/authorize', {
        groupID: groupId,
        redirectTo,
        state,
      });

      if (response.data && response.data.authUrl) {
        // Store the state in localStorage for verification when redirected back
        localStorage.setItem('stripe_oauth_state', state);

        // Redirect to Stripe's OAuth page
        window.location.href = response.data.authUrl;
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Failed to initiate Stripe OAuth flow:', error);
      toast.error('Failed to connect with Stripe. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <CreditCard className="text-primary h-6 w-6" />
          Stripe Connect
        </CardTitle>
        <CardDescription>
          Connect your Stripe account to enable direct payment processing
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <Alert variant="default" className="bg-[#F4F4F5]">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              By connecting your Stripe account, you authorize us to access your Stripe data for
              payment processing. You can disconnect at any time.
            </AlertDescription>
          </Alert>

          <div className="flex items-center justify-center">
            <LoadingButton
              variant="primary"
              type="button"
              disabled={loading || !groupId}
              isLoading={loading}
              loadingText="Connecting..."
              className="bg-[#635BFF] hover:bg-[#635BFF]/80"
              onClick={handleConnectWithStripe}
            >
              Connect with Stripe
            </LoadingButton>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
