import { ChangeEvent } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label, Select } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';

export type FormSelectProps = {
  name: string;
  helperText?: string;
  id: string;
  options: {
    label: string;
    value: string;
    disabled?: boolean;
  }[];
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  visible?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  flex?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLSelectElement>) => void;
  variant?: 'primary' | 'secondary';
  label?: string;
  disabled?: boolean;
  defaultValue?: string;
  tabIndex?: number;
  endAdornment?: React.ReactNode;
  type?: string; // Add this line
};

export const FormSelect = ({
  id,
  name,
  label,
  rules,
  options,
  disabled,
  helperText = '',
  defaultValue,
  visible = true,
  readOnly = false,
  maxLength,
  flex,
  tabIndex,
  tooltip,
  endAdornment,
  variant = 'primary',
  onChangeCallback = (e: ChangeEvent<HTMLSelectElement>) => {
    e;
  },
  type = 'text', // Add this line
  ...props
}: FormSelectProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || ''}
      render={({
        field: { onChange, onBlur, value, ref },
        fieldState,
        fieldState: { invalid, error },
      }) => (
        <div className={`relative w-full ${visible ? '' : 'hidden'} ${flex ? `flex-${flex}` : ''}`}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div className="relative">
            <Select
              onChange={(e) => {
                onChange(e);
                onChangeCallback(e);
              }}
              ref={ref}
              value={value}
              onBlur={(e) => {
                onChange(e);
                onBlur();
              }}
              disabled={isDisabled()}
              {...props}
            >
              {options.map((option, index) => (
                <option key={index} value={option.value} disabled={option.disabled}>
                  {option.label}
                </option>
              ))}
            </Select>
            {endAdornment && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                {endAdornment}
              </div>
            )}
            {/*{tooltip && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <FormToolTip tooltip={tooltip} mode="input" />
                </div>
              )} */}
          </div>
          <HelperText color={invalid ? 'failure' : 'default'}>
            {invalid ? error?.message : helperText}{' '}
          </HelperText>
        </div>
      )}
    />
  );
};

export default FormSelect;
