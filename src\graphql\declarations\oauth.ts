import { graphql } from '../generated';

export const OauthClientCreate = graphql(`
  mutation OauthClientCreate($input: Oauthclient_createInput!) {
    oauthclient_create(input: $input) {
      id
      clientId
      clientSecret
    }
  }
`);

export const OauthClientUpdate = graphql(`
  mutation OauthClientUpdate($input: Oauthclient_updateInput!) {
    oauthclient_update(input: $input) {
      id
      clientId
      clientSecret
    }
  }
`);

export const OauthClientDelete = graphql(`
  mutation OauthClientDelete($where: [OAuth_ClientWhereUniqueInput!]!) {
    deleteOAuthClients(where: $where) {
      id
    }
  }
`);

export const OAuthClients = graphql(`
  query OAuthClients($where: OAuth_ClientWhereInput!, $take: Int, $skip: Int!) {
    oAuthClients(where: $where, take: $take, skip: $skip) {
      id
      clientId
      redirectUri
      grantTypes
      scope
      createdAt
      authCodesCount
      tokensCount
      group {
        id
        name
        actualName
        labelName
      }
    }
  }
`);

export const OAuthTokenDelete = graphql(`
  mutation OAuthTokenDelete($where: [OAuth_TokenWhereUniqueInput!]!) {
    deleteOAuthTokens(where: $where) {
      id
    }
  }
`);

export const OAuthTokens = graphql(`
  query OAuthTokens($where: OAuth_TokenWhereInput!, $take: Int, $skip: Int!) {
    oAuthTokens(where: $where, take: $take, skip: $skip) {
      id
      accessToken
      refreshToken
      expiresAt
      refreshTokenExpiresAt
      scope
      createdAt
      group {
        id
        name
        actualName
        labelName
      }
      client {
        id
        group {
          actualName
          name
          labelName
          id
        }
      }
    }
  }
`);
