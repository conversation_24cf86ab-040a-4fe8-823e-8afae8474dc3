// import { graphql } from "../generated";

import { graphql } from '../generated';

// export const CreateGroup = graphql(`
//   mutation CreateGroup($data: GroupCreateInput!) {
//     createGroup(data: $data) {
//       name
//     }
//   }
// `);

// export const GetGroup = graphql(`
//   query GetGroup($where: GroupWhereUniqueInput!) {
//     group(where: $where) {
//       id
//       name
//       members {
//         user {
//           id
//           email
//           name
//           lastName
//         }
//       }
//       membersCount
//       merchants {
//         id
//         name
//         mainProcessor
//         processorStatus
//       }
//       merchantsCount
//       processorAURCount
//     }
//   }
// `);

// export const ListGroup = graphql(`
//   query ListGroup {
//     groups {
//       id
//       name
//       members {
//         user {
//           name
//           email
//         }
//         access
//       }
//       merchants {
//         name
//         id
//       }
//     }
//   }
// `);

export const getGroups = graphql(`
  query Groups($where: GroupWhereInput!, $take: Int, $skip: Int!) {
    groups(where: $where, skip: $skip, take: $take) {
      id
      name
      membersCount
      processorStatus
      mainProcessor
      mainGateway
      # processorAURCount
    }
  }
`);

export const deleteGroup = graphql(`
  mutation DeleteGroup($where: GroupWhereUniqueInput!) {
    deleteGroup(where: $where) {
      id
    }
  }
`);

export const GET_GROUPS_LIST = graphql(`
  query GroupList(
    $where: GroupWhereInput!
    $orderBy: [GroupOrderByInput!]
    $take: Int
    $skip: Int = 0
  ) {
    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      name
      labelName
      actualName
      membersCount
      processorStatus
      signingURL
      mainProcessor
      mainGateway
      einNumber
      createdAt
      updatedAt
      displayMerchantID
      pciStatus
      firstName
      lastName
      fullName
      addressLine1
      addressLine2
      city
      state
      country
      zip
      mccCode
      phoneNumber
      email
      default_includeSurcharge
      default_globalDisableCC
      default_globalDisableACH
      ghlAccessesCount
      hubspotAccessesCount
      ghlPayTransactionMapsCount
      supportTicketsCount
      serviceAccountsCount
      flag_disableTokens
      flag_disableAutoToken
    }
    groupsCount(where: $where)
  }
`);

export const GetGroupList = graphql(`
  query GetGroupList(
    $where: GroupWhereInput!
    $orderBy: [GroupOrderByInput!]
    $take: Int
    $skip: Int = 0
  ) {
    groups(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      name
      labelName
      actualName
      membersCount
      processorStatus
      signingURL
      mainProcessor
      mainGateway
      einNumber
      createdAt
      updatedAt
      displayMerchantID
      pciStatus
      firstName
      lastName
      fullName
      addressLine1
      addressLine2
      city
      state
      country
      zip
      mccCode
      phoneNumber
      email
      default_includeSurcharge
      default_globalDisableCC
      default_globalDisableACH
      ghlAccessesCount
      hubspotAccessesCount
      ghlPayTransactionMapsCount
      supportTicketsCount
      serviceAccountsCount
      flag_disableTokens
      flag_disableAutoToken
    }
    groupsCount(where: $where)
  }
`);

export const deleteApplicationFile = graphql(`
  mutation Processor_aur_draft_file_delete($input: Processor_aur_draft_file_deleteInput!) {
    processor_aur_draft_file_delete(input: $input)
  }
`);

export const signGroup = graphql(`
  mutation SignGroup($input: Group_signGroupInput!) {
    group_signGroup(input: $input)
  }
`);
