import { apolloClient } from '../lib/graphql/ApolloClient';
import { UserMembersList, UpdateGroupMemberData } from '../graphql/declarations/user-members';

async function updateUserRoles() {
  try {
    // Get all users
    const { data } = await apolloClient.query({
      query: UserMembersList,
      variables: {
        where: {},
        take: 100,
        skip: 0,
      },
    });

    if (!data?.groupMembers?.length) {
      console.log('No members found');
      return;
    }

    // Sort users by last login time (most recent first)
    const sortedMembers = [...data.groupMembers].sort((a, b) => {
      const dateA = new Date(a.user?.lastLogin || 0);
      const dateB = new Date(b.user?.lastLogin || 0);
      return dateB.getTime() - dateA.getTime();
    });

    // Update roles: keep first 2 as admin, rest as agent
    for (let i = 0; i < sortedMembers.length; i++) {
      const member = sortedMembers[i];
      const newAccess = i < 2 ? 3 : 2;

      if (member.access === newAccess) {
        continue; // Skip if role is already correct
      }

      try {
        await apolloClient.mutate({
          mutation: UpdateGroupMemberData,
          variables: {
            where: {
              id: member.id,
            },
            updateBaseMemberData: {
              access: newAccess,
            },
            createMemberFlags: [],
            deleteMemberFlags: [],
          },
        });
        console.log(`Updated user ${member.user?.email}: ${newAccess === 3 ? 'Admin' : 'Agent'}`);
      } catch (err) {
        console.error(`Failed to update user ${member.user?.email}:`, err);
      }
    }

    console.log('Role update completed');
  } catch (error) {
    console.error('Script failed:', error);
  }
}

updateUserRoles();
