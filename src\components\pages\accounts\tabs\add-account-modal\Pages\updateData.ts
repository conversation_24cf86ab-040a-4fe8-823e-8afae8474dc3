import {
  Processor_draft_create,
  Processor_draft_documentUpload,
  Processor_draft_update,
} from '@/graphql/declarations/processor';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { searchParamsProc } from '@/lib/search-params-proc';
import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

// export interface MerchantFormInput {
//   businessInfo?: {
//     legalBusinessName?: string;
//     typeOfBusiness?: string;
//     dbaName?: string;
//     ein?: string;
//     dateBusinessEstablished?: string;
//     businessEmail?: string;
//     businessPhone?: string;
//     website?: string;
//     customerServicePhone?: string;
//     street?: string;
//     zipCode?: string;
//     city?: string;
//     state?: string;
//     country?: string;
//     differentLegalAddress?: boolean;
//     legalMailingStreet?: string;
//     legalMailingZipCode?: string;
//     legalMailingCity?: string;
//     legalMailingState?: string;
//     legalMailingCountry?: string;
//   };
//   transactionInfo?: {
//     businessCategory?: string;
//     description?: string;
//     swipe?: number;
//     keyed?: number;
//     ecommerce?: number;
//     avgTransactionAmount?: number;
//     highestTransactionAmount?: number;
//     grossMonthlySalesVolume?: number;
//     amexAvgTransactionAmount?: number;
//     amexHighestTransactionAmount?: number;
//     amexGrossMonthlySalesVolume?: number;
//   };
//   owners?: {
//     isControlOwner?: boolean;
//     firstName?: string;
//     lastName?: string;
//     title?: string;
//     ownershipPercentage?: number;
//     phoneNumber?: string;
//     homeAddress?: string;
//     country?: string;
//     state?: string;
//     city?: string;
//     zipCode?: string;
//     dateOfBirth?: string;
//     ssn?: string;
//     email?: string;
//   }[];
//   files?: {
//     name: string;
//     category: string;
//     mimetype: string;
//     b64: string;
//   }[];
//   bankInfo?: {
//     routingNumber?: string;
//     accountNumber?: string;
//     bankName?: string;
//     nameOnAccount?: string;
//   };
// }

function arrayBufferToBase64(buffer: ArrayBuffer) {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

const groupOnboardingFormSchema = z.object({
  businessInfo: z
    .object({
      legalBusinessName: z.string().optional(),
      typeOfBusiness: z.string().optional(),
      dbaName: z.string().optional(),
      ein: z.string().optional(),
      dateBusinessEstablished: z.string().optional(),
      businessEmail: z.string().optional(),
      businessPhone: z.string().optional(),
      businessPhoneCountryCode: z.string().optional(),
      website: z.string().optional(),
      customerServicePhone: z.string().optional(),
      customerServicePhoneCountryCode: z.string().optional(),
      street: z.string().optional(),
      zipCode: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().optional(),
      differentLegalAddress: z.boolean().optional(),
      legalMailingStreet: z.string().optional(),
      legalMailingZipCode: z.string().optional(),
      legalMailingCity: z.string().optional(),
      legalMailingState: z.string().optional(),
      legalMailingCountry: z.string().optional(),
    })
    .optional(),
  transactionInfo: z
    .object({
      businessCategory: z.string().optional(),
      description: z.string().optional(),
      swipe: z.number().optional(),
      keyed: z.number().optional(),
      ecommerce: z.number().optional(),
      avgTransactionAmount: z.number().optional(),
      highestTransactionAmount: z.number().optional(),
      grossMonthlySalesVolume: z.number().optional(),
      amexAvgTransactionAmount: z.number().optional(),
      amexHighestTransactionAmount: z.number().optional(),
      amexGrossMonthlySalesVolume: z.number().optional(),
    })
    .optional(),
  owners: z
    .array(
      z.object({
        isControlOwner: z.boolean().optional(),
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        title: z.string().optional(),
        ownershipPercentage: z.number().optional(),
        phoneNumber: z.string().optional(),
        phoneNumberCountryCode: z.string().optional(),
        homeAddress: z.string().optional(),
        country: z.string().optional(),
        state: z.string().optional(),
        city: z.string().optional(),
        zipCode: z.string().optional(),
        dateOfBirth: z.string().optional(),
        ssn: z.string().optional(),
        email: z.string().optional(),
      }),
    )
    .optional(),
  files: z
    .array(
      z.object({
        name: z.string(),
        category: z.string(),
        purpose: z.string(),
        mimetype: z.string(),
        b64: z.string(),
        submittedOn: z.string().optional(),
      }),
    )
    .optional(),
  bankInfo: z
    .object({
      routingNumber: z.string().optional(),
      accountNumber: z.string().optional(),
      bankName: z.string().optional(),
      nameOnAccount: z.string().optional(),
    })
    .optional(),
  attestations: z
    .array(
      z.object({
        name: z
          .union([
            z.literal('propay_sub_merchant_terms_and_conditions'),
            z.literal('beneficial_owner'),
          ])
          .optional(),
        ip_address: z.string().optional(),
        time_of_attestation: z.string().optional(),
        url: z.string().optional(),
        signature: z.string().optional(),
      }),
    )
    .optional(),
});

export type MerchantFormInput = z.infer<typeof groupOnboardingFormSchema>;

export const updateData = async (args: {
  submitType: string;
  data: any;
  groupID?: string;
  form?: UseFormReturn<any>;
}): Promise<boolean> => {
  const { submitType, data, groupID } = args;
  // submit type can either be: 'business', 'transaction', 'bank', 'ownership', "document"

  const tableMapping = {
    business: 'businessInfo',
    transaction: 'transactionInfo',
    bank: 'bankInfo',
    ownership: 'owners',
    document: 'files',
    attestations: 'attestations',
  };

  const tableName = tableMapping[submitType];

  if (!tableName) {
    console.log('Invalid submit type');
    return false;
  }

  // remove null fields
  Object.keys(data).forEach((key) => data[key] === null && delete data[key]);

  // if toupdate is file type
  if (tableName === 'files') {
    console.log('data', data);
    let o = data.old;

    await apolloClient.mutate({
      mutation: Processor_draft_update,
      variables: {
        input: {
          groupId: groupID!,
          data: {
            files: o
              ? o.map((file) => {
                  return {
                    name: file.name,
                    b64: file.b64,
                    category: file.category ?? '',
                    purpose: file.purpose ?? '',
                    id: file.id,
                    mimetype: file.mimetype,
                  };
                })
              : [],
          },
        },
      },
    });

    let f = (data?.new ?? []) as {
      file: File;
      category: string;
    }[];

    if (f.length) {
      let fileProcess = await Promise.all(
        f.map(async (file) => {
          return {
            name: file.file.name,
            mimeType: file.file.type,
            category: file.category,
            purpose: 'IDENTITY_VERIFICATION',
            fileData:
              file.file.type + ';base64,' + arrayBufferToBase64(await file.file.arrayBuffer()),
          };
        }),
      );

      await apolloClient.mutate({
        mutation: Processor_draft_documentUpload,
        variables: {
          input: {
            groupId: groupID!,
            files: fileProcess,
          },
        },
      });
    }

    await apolloClient.resetStore();
    return true;
  } else {
    // get the specific tableName from the zod schema and validate the data
    const validatedData = groupOnboardingFormSchema.safeParse({
      [tableName]: data,
    });

    if (!validatedData.success) {
      let issues = validatedData.error.issues;
      if (args.form) {
        for (let issue of issues) {
          if (issue.path[0] === tableName) {
            // @ts-ignore
            args.form.setError(issue.path[1], { message: issue.message });
          }
        }
      }
      console.log('Invalid data', validatedData.error, data);
      return false;
    }

    let processedData = validatedData.data[tableName];

    if (groupID) {
      const resp = await apolloClient.mutate({
        mutation: Processor_draft_update,
        variables: {
          input: {
            groupId: groupID,
            data: {
              [tableName]: processedData,
            },
          },
        },
      });

      await apolloClient.resetStore();
      return true;
    } else {
      const resp = await apolloClient.mutate({
        mutation: Processor_draft_create,
        variables: {
          input: {
            data: {
              [tableName]: data,
            },
          },
        },
      });

      searchParamsProc({
        values: [{ key: 'groupID', value: resp.data?.processor_draft_create?.groupID ?? '' }],
      });

      await apolloClient.resetStore();

      return true;
    }
  }
};
