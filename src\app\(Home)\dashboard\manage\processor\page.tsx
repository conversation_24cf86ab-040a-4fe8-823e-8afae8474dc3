'use client';

import { PAYMENT_PROCESSORS } from '@/consts/processors';
import { ProcessorCard } from '@/components/pages/manage/processor/processor-card';
import { useState } from 'react';
import { Processor } from '@/types/processors';

export default function ProcessorPage() {
  const [paymentProcessors, setPaymentProcessors] = useState(PAYMENT_PROCESSORS);

  const handleToggle = (processor: Processor, enabled: boolean) => {
    const updateProcessor = (p: Processor) => ({
      ...p,
      enabled: p.id === processor.id ? enabled : p.enabled,
    });

    setPaymentProcessors(paymentProcessors.map(updateProcessor));
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Payment Processors</h1>
        <p className="text-muted-foreground">Enable and configure your payment processors.</p>
      </div>
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-6">
          {paymentProcessors.map((processor) => (
            <ProcessorCard
              key={processor.id}
              processor={processor}
              onToggle={(enabled) => handleToggle(processor, enabled)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
