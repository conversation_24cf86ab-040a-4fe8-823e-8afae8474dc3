import { ReactNode } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { HelperText, Label } from 'flowbite-react';
// import { AutoCompleteInput } from './AutoCompleteInput';
import { Tooltip } from 'flowbite-react';
import { AutoCompleteInput, AutoCompleteOption } from '../auto-complete-input';
import { cn } from '@/lib/utils';
import { useFormFieldHelpers } from '@/components/hooks';

type FormAutoCompleteProps<TObj> = {
  id: string;
  name: string;
  label: string;
  helperText?: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  className?: string;
  readOnly?: boolean;
  visible?: boolean;
  disabled?: boolean;
  options: AutoCompleteOption[];
  defaultValue?: Record<string, unknown>;
  multiple?: boolean;
  fullWidth?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  endAdornment?: ReactNode;
  autofocus?: boolean;
  freeSolo?: boolean;
  tooltip?: string;
  disableChip?: boolean;
  onChangeStrategy?: 'all' | 'id' | 'label';
  initialText?: string;
  onTextChange?: (value: string) => void;
  optionsLoading?: boolean;
};

export const FormAutoComplete = <TObj extends Record<string, any>>({
  id,
  name,
  rules,
  label,
  helperText,
  options,
  multiple = false,
  className,
  endAdornment,
  tooltip,
  onChangeStrategy,
  initialText,
  onTextChange,
  optionsLoading,
  disabled,
  ...props
}: FormAutoCompleteProps<TObj>) => {
  const { control } = useFormContext();

  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={multiple ? [] : { id: '', label: '' }}
      render={({ field: { onChange, ...fieldRest }, fieldState: { error, invalid } }) => (
        <div className={cn('relative w-full', className)}>
          <Label htmlFor={id} className="mb-2">
            {label} {isRequired() && '*'} {tooltip && <Tooltip content={tooltip} />}
          </Label>
          <div>
            <AutoCompleteInput
              {...fieldRest}
              label={label}
              options={options}
              optionsLoading={optionsLoading}
              value={
                onChangeStrategy === 'id'
                  ? options.find((opt) => opt.id === fieldRest.value)
                  : onChangeStrategy === 'label'
                    ? options.find((opt) => opt.label === fieldRest.value)
                    : fieldRest.value
              }
              initialText={initialText}
              onTextChange={onTextChange}
              onChange={(value) => {
                let _fetchStrat = onChangeStrategy || 'all';
                switch (_fetchStrat) {
                  case 'id':
                    onChange(value.id);
                    break;
                  case 'label':
                    onChange(value.label);
                    break;
                  default:
                    onChange(value);
                    break;
                }
              }}
            />
            {endAdornment && <div className="absolute right-2 top-2">{endAdornment}</div>}
            <HelperText color={invalid ? 'failure' : 'default'}>
              {invalid ? error?.message : helperText}{' '}
            </HelperText>
          </div>
        </div>
      )}
    />
  );
};
