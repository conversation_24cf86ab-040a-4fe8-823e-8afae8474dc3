query Gateway_paymentPlans($input: Gateway_paymentPlansInput!) {
  gateway_paymentPlans(input: $input) {
    data {
      planID
      planName
      startDate
      customerName
      customerID
      amount
      last4
      expires
      duration
      status
    }
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
  }
}

query Gateway_paymentPlan($input: Gateway_paymentPlanInput!) {
  gateway_paymentPlan(input: $input) {
    planID
    planName
    createdAt
    amount
    startDate
    endDate
    locationName
    locationID
    status
    last4
    expires
    creator
    history {
      transactionID
      date
      amount
      status
    }
    customerName
    customerID
    customerEmail
    customerPhone
    customerCountry
    customerBillingAddress
    paymentBrand
    paymentEvery
    paymentInterval
    paymentExpires
    mode
    paymentID
    nextPaymentDate
    cancelReason
    recurringRefundable
    subscriptionCredit
  }
}
