import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { HiTrash, HiPencil } from 'react-icons/hi';
import Image from 'next/image';
import {
  Gateway_DeleteProductDocument,
  Gateway_ProductDocument,
} from '@/graphql/generated/graphql';
import { useMutation, useQuery } from '@apollo/client';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import { ProductUpdateModal } from './product-update-modal';

type ProductViewModalProps = {
  isOpen: boolean;
  onClose: () => void;
  refetchproductListData: () => void;
  queryData: {
    productID: string;
    groupID: string;
  };
};

const ProductViewModal = ({
  isOpen,
  onClose,
  refetchproductListData,
  queryData,
}: ProductViewModalProps) => {
  const { productID, groupID } = queryData;
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  const [deleteProductMutation] = useMutation(Gateway_DeleteProductDocument, {
    onCompleted: (_) => {
      setShowDeleteConfirmation(false);
      toast.success(message.api.successDelete('Product'));
      refetchproductListData();
      onClose();
    },
    onError: (error) => {
      toast.error(message.api.errorDelete('Product', error.message));
    },
  });

  const { data: productData, refetch } = useQuery(Gateway_ProductDocument, {
    variables: {
      input: {
        data: {
          productID,
        },
        groupID,
      },
    },
    skip: !productID || !groupID,
  });

  const product = productData?.gateway_product;

  const handleDeleteProduct = async () => {
    try {
      await deleteProductMutation({
        variables: {
          input: {
            groupID,
            data: {
              productID,
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  const handleUpdateClose = () => {
    setShowUpdateModal(false);
    refetch();
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="5xl">
        <Modal.Header className="border-b-0 p-6">
          <div className="flex w-full items-center justify-between">
            <h3 className="text-2xl font-bold">{product?.name}</h3>
          </div>
        </Modal.Header>
        <Modal.Body className="p-6 pt-0">
          <p className="mb-4 text-3xl font-bold">${product?.price}</p>
          <div className="mb-6 grid grid-cols-4 gap-4">
            {product?.productImages?.map((image, index) => (
              <div key={index} className="w-full bg-gray-100">
                {/* <img
                  src={image}
                  alt={`Product ${index + 1}`}
                  className="h-full w-full object-cover"
                /> */}
                <Image
                  src={image?.url ?? ''} //
                  alt={`Product ${index + 1}`}
                  layout="responsive"
                  width={500}
                  height={500}
                  className="object-cover"
                />
              </div>
            ))}
          </div>
          <h4 className="mb-2 text-lg font-semibold">Details</h4>
          <p className="mb-4 text-gray-600">{product?.description}</p>
          {/* <h4 className="mb-2 text-lg font-semibold">Colors</h4> */}
          {/* <div className="mb-6 flex space-x-2">
            {['blue', 'gray', 'back', 'green'].map((color, index) => (
              <div
                key={index}
                className="h-8 w-8 rounded-full"
                style={{ backgroundColor: color }}
              />
            ))}
          </div> */}
          <div className="mb-6 grid grid-cols-3 gap-4">
            <InfoBox title="SKU" value={product?.sku ?? ''} />
            <InfoBox title="Category" value={product?.category ?? ''} />
            <InfoBox title="Sub Category" value={product?.subCategory ?? ''} />
            <InfoBox title="Brand" value={product?.brand ?? ''} />
          </div>

          {product?.isRecurring && (
            <>
              <h4 className="mb-2 text-lg font-semibold">Recurring Details</h4>
              <div className="mb-6 grid grid-cols-3 gap-4">
                <InfoBox title="Recurring Mode" value={product?.recurringMode ?? ''} />
                {product?.recurringMode !== 'MONTHLY' && product?.recurringMode !== 'YEARLY' && (
                  <InfoBox title="Recurring Interval" value={product?.recurringInterval ?? ''} />
                )}
                <InfoBox title="Recurring Frequency" value={product?.recurringFrequency ?? ''} />
                <InfoBox title="Total Cycles" value={product?.recurringTotalCycles ?? ''} />
                <InfoBox title="Trial Days" value={product?.recurringTrialDays ?? ''} />
                <InfoBox title="Setup Fee" value={`$${product?.recurringSetupFee ?? '0'}`} />
              </div>

              {(product?.recurringMode === 'MONTHLY' || product?.recurringMode === 'YEARLY') && (
                <div className="mb-6 rounded-lg border border-blue-100 bg-blue-50 p-3 text-sm text-blue-800">
                  <p>
                    <strong>Note:</strong> With{' '}
                    {product?.recurringMode === 'MONTHLY' ? 'Monthly' : 'Yearly'} mode, the customer
                    will be charged on the same day of the{' '}
                    {product?.recurringMode === 'MONTHLY' ? 'month' : 'year'} as the initial
                    payment.
                  </p>
                  <p className="mt-1">
                    For payments starting on the 29th-31st, the payment will adjust to the last day
                    of shorter months to accommodate different month lengths and leap years.
                  </p>
                  {product?.recurringMode === 'YEARLY' && (
                    <p className="mt-1">
                      <strong>For yearly payments:</strong> If a payment was started on February
                      29th (leap year), it will be automatically adjusted to February 28th in
                      non-leap years.
                    </p>
                  )}
                </div>
              )}
            </>
          )}

          <div className="flex justify-between">
            <div className="flex gap-2">
              <Button color="blue" className="mr-2" onClick={() => setShowUpdateModal(true)}>
                <HiPencil className="mr-2 h-5 w-5" /> Edit
              </Button>
              {/* <Button color="light">Preview</Button> */}
            </div>
            <Button color="failure" onClick={() => setShowDeleteConfirmation(true)}>
              <HiTrash className="mr-2 h-5 w-5" /> Delete
            </Button>
          </div>
        </Modal.Body>
      </Modal>

      <DeleteConfirmationModal
        isOpen={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        onConfirm={handleDeleteProduct}
      />

      <ProductUpdateModal
        isOpen={showUpdateModal}
        onClose={handleUpdateClose}
        queryData={{
          productID,
          groupID,
        }}
      />
    </>
  );
};

const InfoBox: React.FC<{
  title: string;
  value: string | number;
  isNew?: boolean;
  icon?: React.ReactNode;
}> = ({ title, value, isNew, icon }) => (
  <div className="rounded-lg bg-gray-50 p-3">
    <p className="text-sm font-medium">{title}</p>
    <p className="items-centertext-gray-500 flex">
      {icon}
      {value}
      {isNew && (
        <span className="ml-2 rounded bg-blue-100 px-2 py-1 text-xs font-semibold text-blue-800">
          New
        </span>
      )}
    </p>
  </div>
);

const DeleteConfirmationModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}> = ({ isOpen, onClose, onConfirm }) => (
  <Modal show={isOpen} onClose={onClose} size="md">
    <Modal.Body>
      <div className="text-center">
        <HiTrash className="mx-auto mb-4 h-14 w-14 text-gray-400" />
        <h3 className="mb-5 text-lg font-normal text-gray-500">
          Are you sure you want to delete this product?
        </h3>
        <div className="flex justify-center space-x-4">
          <Button color="gray" onClick={onClose}>
            No, cancel
          </Button>
          <Button color="failure" onClick={onConfirm}>
            Yes, I'm sure
          </Button>
        </div>
      </div>
    </Modal.Body>
  </Modal>
);

export { ProductViewModal, DeleteConfirmationModal };
