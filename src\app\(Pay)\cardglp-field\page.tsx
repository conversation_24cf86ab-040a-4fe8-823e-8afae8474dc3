'use client';

import { CardGLPFields } from '@/components/payments/glpv2';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function CardFieldPage() {
  const query = useSearchParams();
  const querySettings = query?.get('settings') ? JSON.parse(query.get('settings') ?? '{}') : {};
  const cssString = query?.get('css') ?? '';
  // merchant id is actually groupID, but meh
  const merchantID = query?.get('merchantID') ?? '';

  const settings = {
    labels: {
      cardNumber: querySettings?.labels?.cardNumber ?? 'Card Number',
      expiration: querySettings?.labels?.expiration ?? 'Expiry Date',
      submitText: querySettings?.labels?.submitText ?? 'Confirm Card',
      cvv: querySettings?.labels?.cvv ?? 'CVV',
    },
    styles: {
      input: {
        borderRadius: querySettings?.styles?.input?.borderRadius ?? '10px',
        borderColor: querySettings?.styles?.input?.borderColor ?? '#ccc',
        focusBorderColor: querySettings?.styles?.input?.focusBorderColor ?? '#71C5E8',
        padding: querySettings?.styles?.input?.padding ?? '12px',
        marginBottom: querySettings?.styles?.input?.marginBottom ?? '20px',
        backgroundColor: querySettings?.styles?.input?.backgroundColor ?? '#ffffff',
        color: querySettings?.styles?.input?.color ?? '#000000',
      },
      button: {
        borderRadius: querySettings?.styles?.button?.borderRadius ?? '10px',
        borderColor: querySettings?.styles?.button?.borderColor ?? '#1a56db',
        textColor: querySettings?.styles?.button?.textColor ?? '#1a56db',
        hoverBgColor: querySettings?.styles?.button?.hoverBgColor ?? '#1a56db',
        hoverTextColor: querySettings?.styles?.button?.hoverTextColor ?? 'white',
        padding: querySettings?.styles?.button?.padding ?? '12px',
        margin: querySettings?.styles?.button?.margin ?? '10px',
        fontSize: querySettings?.styles?.button?.fontSize ?? '17px',
      },
      label: {
        fontSize: querySettings?.styles?.label?.fontSize ?? '14px',
        fontWeight: querySettings?.styles?.label?.fontWeight ?? '500',
        color: querySettings?.styles?.label?.color ?? '#374151',
        marginBottom: querySettings?.styles?.label?.marginBottom ?? '8px',
      },
      font: {
        family: querySettings?.styles?.font?.family ?? "'Roboto Mono'",
        style: querySettings?.styles?.font?.style ?? 'normal',
        weight: querySettings?.styles?.font?.weight ?? '200',
        display: querySettings?.styles?.font?.display ?? 'swap',
        src:
          querySettings?.styles?.font?.src ??
          "url(https://fonts.gstatic.com/s/robotomono/v22/L0xuDF4xlVMF-BfR8bXMIhJHg45mwgGEFl0_Xvq_ROW4.woff2) format('woff2')",
        unicodeRange:
          querySettings?.styles?.font?.unicodeRange ??
          'U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD',
      },
    },
  };

  const sendMessage = (message: any) => {
    window.parent.postMessage(message, '*');
    // console.log('message', message);
  };

  useEffect(() => {
    if (cssString) {
      let style = document.createElement('style');
      document.head.appendChild(style);
      style.textContent = cssString;
    }
  }, []);

  return (
    <div className="flex h-full w-full flex-col items-center justify-center" id="card-parent">
      <CardGLPFields
        merchantID={merchantID}
        labels={settings.labels}
        styles={settings.styles}
        onToken={(token) => {
          sendMessage({ type: 'token', data: token });
        }}
        onEvent={(eventType, event) => {
          sendMessage({ type: eventType, data: event });
        }}
        onError={(error) => {
          sendMessage({ type: 'error', data: error });
        }}
      />
    </div>
  );
}
