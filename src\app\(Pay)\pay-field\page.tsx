'use client';

import TSEPTokenizerComponent from '@/components/payments/tsep/tokenizer';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function PayFieldPage() {
  // send card info into parent component, this page is meant to be a child component iframe

  // const [isReady, setIsReady] = useState(false);

  const query = useSearchParams();
  const querySettings = query?.get('settings') ? JSON.parse(query.get('settings') ?? '{}') : {};
  const cssString = query?.get('css') ?? '';

  // console.log('querySettings', querySettings);

  const onToken = (token) => {
    // send token to parent component
    window.parent.postMessage(token, '*');
  };

  // const parentMessageListener = () => {
  //   console.log('listening for message from parent');
  //   // listen for token from parent component
  //   window.addEventListener('message', function (event) {
  //     parentMessageParser(event);
  //   });
  // };

  // console.log('querySettings', querySettings);

  const settings = {
    labels: {
      cardNumber: querySettings?.labels?.cardNumber ?? 'Card Number',
      expiryDate: querySettings?.labels?.expiryDate ?? 'Expiry Date',
      cvv: querySettings?.labels?.cvv ?? 'CVV',
      cardHolderName: querySettings?.labels?.cardHolderName ?? 'Card Holder Name',
      zipCode: querySettings?.labels?.zipCode ?? 'Zip Code',
    },
    allowOptionals: {
      cardHolderName: querySettings?.allowOptionals?.cardHolderName,
      zipCode: querySettings?.allowOptionals?.zipCode,
      cvv: querySettings?.allowOptionals?.cvv,
    },
  };

  useEffect(() => {
    // parentMessageListener();
    // console.log('cssString', cssString);
    if (cssString) {
      // Create a new <style> element
      let style = document.createElement('style');

      // Add the <style> element to the document's <head> element
      document.head.appendChild(style);

      // Append a CSS string to the <style> element
      style.textContent = cssString;
    }
  }, []);

  return (
    <div
      className="flex h-full min-h-[100px] w-full flex-col items-center justify-center"
      id="tsep-parent"
    >
      <TSEPTokenizerComponent
        labels={{
          cardNumber: settings.labels.cardNumber,
          expiryDate: settings.labels.expiryDate,
          cvv: settings.labels.cvv,
          cardHolderName: settings.labels.cardHolderName,
          zipCode: settings.labels.zipCode,
        }}
        allowOptionals={{
          cardHolderName: settings.allowOptionals.cardHolderName,
          zipCode: settings.allowOptionals.zipCode,
          cvv: settings.allowOptionals.cvv,
        }}
        onEvent={(event, data) => {
          // console.log(event, data);
          onToken({ type: 'event', data: { event, data } });
        }}
        onTokenError={(error) => {
          // console.error('error', error);
          onToken({ type: 'error', data: error });
        }}
        onToken={(token) => {
          onToken({ type: 'token', data: token });
          // methods.setValue('nameOnCard', token.cardHolderName);
          // methods.setValue('cardToken', token.tsepToken);
          // methods.setValue('cvc', token.cvv2);
          // methods.setValue('expiryDate', token.expirationDate);
          // methods.setValue('zip', token.zipCode);
          // methods.setValue('brand', token.cardType);
          // setData({
          //   cardToken: token.tsepToken,
          //   cvc: token.cvv2,
          //   expiryDate: token.expirationDate,
          //   nameOnCard: token.cardHolderName,
          //   address: '3000',
          //   zip: token.zipCode,
          //   brand: token.cardType,
          // });
          // console.log('token', token);
        }}
      />
    </div>
  );
}
