'use client';

import { CardFields } from '@/components/payments/glp-local';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export default function CardFieldPage() {
  const query = useSearchParams();
  const querySettings = query?.get('settings') ? JSON.parse(query.get('settings') ?? '{}') : {};
  const cssString = query?.get('css') ?? '';

  const settings = {
    labels: {
      cardNumber: querySettings?.labels?.cardNumber ?? 'Card Number',
      expiryMonth: querySettings?.labels?.expiryMonth ?? 'Month',
      expiryYear: querySettings?.labels?.expiryYear ?? 'Year',
      cvv: querySettings?.labels?.cvv ?? 'CVV',
    },
  };

  const sendMessage = (message: any) => {
    window.parent.postMessage(message, '*');
    // console.log('message', message);
  };

  useEffect(() => {
    if (cssString) {
      let style = document.createElement('style');
      document.head.appendChild(style);
      style.textContent = cssString;
    }
  }, []);

  return (
    <div className="flex h-full w-full flex-col items-center justify-center" id="card-parent">
      <CardFields
        labels={settings.labels}
        onError={(error) => {
          sendMessage({ type: 'error', data: error });
        }}
        onToken={(token) => {
          sendMessage({ type: 'token', data: token });
        }}
        onEvent={(eventType, event) => {
          sendMessage({ type: 'event', data: { event: eventType, data: event } });
        }}
      />
    </div>
  );
}
