# This file is automatically generated by Keystone, do not modify it manually.
# Modify your Keystone config when you want to change this.

type ServerLog {
  id: ID!
  method: String
  url: String
  graphql: String
  status: String
  elapsed: String
  userID: String
  errorMessage: String
  ip: String
  body: JSON
  createdAt: DateTime
}

scalar DateTime @specifiedBy(url: "https://datatracker.ietf.org/doc/html/rfc3339#section-5.6")

input ServerLogWhereUniqueInput {
  id: ID
}

input ServerLogWhereInput {
  AND: [ServerLogWhereInput!]
  OR: [ServerLogWhereInput!]
  NOT: [ServerLogWhereInput!]
  id: IDFilter
  method: StringFilter
  url: StringFilter
  graphql: StringFilter
  status: StringFilter
  elapsed: StringFilter
  userID: StringFilter
  errorMessage: StringFilter
  ip: StringFilter
  createdAt: DateTimeNullableFilter
}

input IDFilter {
  equals: ID
  in: [ID!]
  notIn: [ID!]
  lt: ID
  lte: ID
  gt: ID
  gte: ID
  not: IDFilter
}

input StringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode
  not: NestedStringFilter
}

enum QueryMode {
  default
  insensitive
}

input NestedStringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input DateTimeNullableFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeNullableFilter
}

input ServerLogOrderByInput {
  id: OrderDirection
  method: OrderDirection
  url: OrderDirection
  graphql: OrderDirection
  status: OrderDirection
  elapsed: OrderDirection
  userID: OrderDirection
  errorMessage: OrderDirection
  ip: OrderDirection
  createdAt: OrderDirection
}

enum OrderDirection {
  asc
  desc
}

input ServerLogUpdateInput {
  method: String
  url: String
  graphql: String
  status: String
  elapsed: String
  userID: String
  errorMessage: String
  ip: String
  body: JSON
  createdAt: DateTime
}

input ServerLogUpdateArgs {
  where: ServerLogWhereUniqueInput!
  data: ServerLogUpdateInput!
}

input ServerLogCreateInput {
  method: String
  url: String
  graphql: String
  status: String
  elapsed: String
  userID: String
  errorMessage: String
  ip: String
  body: JSON
  createdAt: DateTime
}

type ServerError {
  id: ID!
  errorMessage: String
  url: String
  graphql: String
  createdAt: DateTime
  status: String
  method: String
  userID: String
}

input ServerErrorWhereUniqueInput {
  id: ID
}

input ServerErrorWhereInput {
  AND: [ServerErrorWhereInput!]
  OR: [ServerErrorWhereInput!]
  NOT: [ServerErrorWhereInput!]
  id: IDFilter
  errorMessage: StringFilter
  url: StringFilter
  graphql: StringFilter
  createdAt: DateTimeNullableFilter
  status: StringFilter
  method: StringFilter
  userID: StringFilter
}

input ServerErrorOrderByInput {
  id: OrderDirection
  errorMessage: OrderDirection
  url: OrderDirection
  graphql: OrderDirection
  createdAt: OrderDirection
  status: OrderDirection
  method: OrderDirection
  userID: OrderDirection
}

input ServerErrorUpdateInput {
  errorMessage: String
  url: String
  graphql: String
  createdAt: DateTime
  status: String
  method: String
  userID: String
}

input ServerErrorUpdateArgs {
  where: ServerErrorWhereUniqueInput!
  data: ServerErrorUpdateInput!
}

input ServerErrorCreateInput {
  errorMessage: String
  url: String
  graphql: String
  createdAt: DateTime
  status: String
  method: String
  userID: String
}

type GatewayLog {
  id: ID!
  domain: String
  action: String
  message: String
  proccesor: String
  gateway: String
  metadata: JSON
  decrypted: String
  createdAt: DateTime
}

input GatewayLogWhereUniqueInput {
  id: ID
}

input GatewayLogWhereInput {
  AND: [GatewayLogWhereInput!]
  OR: [GatewayLogWhereInput!]
  NOT: [GatewayLogWhereInput!]
  id: IDFilter
  domain: StringFilter
  action: StringFilter
  message: StringFilter
  proccesor: StringFilter
  gateway: StringFilter
  createdAt: DateTimeNullableFilter
}

input GatewayLogOrderByInput {
  id: OrderDirection
  domain: OrderDirection
  action: OrderDirection
  message: OrderDirection
  proccesor: OrderDirection
  gateway: OrderDirection
  createdAt: OrderDirection
}

input GatewayLogUpdateInput {
  domain: String
  action: String
  message: String
  proccesor: String
  gateway: String
  metadata: JSON
  createdAt: DateTime
}

input GatewayLogUpdateArgs {
  where: GatewayLogWhereUniqueInput!
  data: GatewayLogUpdateInput!
}

input GatewayLogCreateInput {
  domain: String
  action: String
  message: String
  proccesor: String
  gateway: String
  metadata: JSON
  createdAt: DateTime
}

type User {
  id: ID!
  name: String
  lastName: String
  displayName: String
  email: String
  phone: String
  title: String
  adminPassword: PasswordState
  avatar: ImageFieldOutput
  role: UserRoleType
  createdAt: DateTime
  type: String
  groups(
    where: GroupMemberWhereInput! = {}
    orderBy: [GroupMemberOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupMemberWhereUniqueInput
  ): [GroupMember!]
  groupsCount(where: GroupMemberWhereInput! = {}): Int
  affiliationProfile: AffiliationProfile
  affiliatedTo: AffiliationProfile
  caches(
    where: UserCacheWhereInput! = {}
    orderBy: [UserCacheOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: UserCacheWhereUniqueInput
  ): [UserCache!]
  cachesCount(where: UserCacheWhereInput! = {}): Int
  ghlSSoBindings(
    where: GHLSSOBindingWhereInput! = {}
    orderBy: [GHLSSOBindingOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GHLSSOBindingWhereUniqueInput
  ): [GHLSSOBinding!]
  ghlSSoBindingsCount(where: GHLSSOBindingWhereInput! = {}): Int
  lastLogin: DateTime
  notifications(
    where: NotificationWhereInput! = {}
    orderBy: [NotificationOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: NotificationWhereUniqueInput
  ): [Notification!]
  notificationsCount(where: NotificationWhereInput! = {}): Int
  flag_canAffiliate: Boolean
  createdGroups(
    where: GroupWhereInput! = {}
    orderBy: [GroupOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupWhereUniqueInput
  ): [Group!]
  createdGroupsCount(where: GroupWhereInput! = {}): Int
  supportTickets(
    where: GroupSupportTicketWhereInput! = {}
    orderBy: [GroupSupportTicketOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupSupportTicketWhereUniqueInput
  ): [GroupSupportTicket!]
  supportTicketsCount(where: GroupSupportTicketWhereInput! = {}): Int
}

type PasswordState {
  isSet: Boolean!
}

type ImageFieldOutput {
  id: ID!
  filesize: Int!
  width: Int!
  height: Int!
  extension: ImageExtension!
  url: String!
}

enum ImageExtension {
  jpg
  png
  webp
  gif
}

enum UserRoleType {
  dev
  admin
  user
}

input UserWhereUniqueInput {
  id: ID
  email: String
}

input UserWhereInput {
  AND: [UserWhereInput!]
  OR: [UserWhereInput!]
  NOT: [UserWhereInput!]
  id: IDFilter
  name: StringFilter
  lastName: StringFilter
  email: StringFilter
  phone: StringFilter
  title: StringFilter
  adminPassword: PasswordFilter
  localAuth: UserLocalAuthWhereInput
  role: UserRoleTypeNullableFilter
  createdAt: DateTimeNullableFilter
  type: StringFilter
  groups: GroupMemberManyRelationFilter
  affiliationProfile: AffiliationProfileWhereInput
  affiliatedTo: AffiliationProfileWhereInput
  caches: UserCacheManyRelationFilter
  ghlSSoBindings: GHLSSOBindingManyRelationFilter
  lastLogin: DateTimeNullableFilter
  notifications: NotificationManyRelationFilter
  flag_canAffiliate: BooleanFilter
  createdGroups: GroupManyRelationFilter
  supportTickets: GroupSupportTicketManyRelationFilter
}

input PasswordFilter {
  isSet: Boolean!
}

input UserLocalAuthWhereInput {
  AND: [UserLocalAuthWhereInput!]
  OR: [UserLocalAuthWhereInput!]
  NOT: [UserLocalAuthWhereInput!]
  id: IDFilter
}

input UserRoleTypeNullableFilter {
  equals: UserRoleType
  in: [UserRoleType!]
  notIn: [UserRoleType!]
  not: UserRoleTypeNullableFilter
}

input GroupMemberManyRelationFilter {
  every: GroupMemberWhereInput
  some: GroupMemberWhereInput
  none: GroupMemberWhereInput
}

input UserCacheManyRelationFilter {
  every: UserCacheWhereInput
  some: UserCacheWhereInput
  none: UserCacheWhereInput
}

input GHLSSOBindingManyRelationFilter {
  every: GHLSSOBindingWhereInput
  some: GHLSSOBindingWhereInput
  none: GHLSSOBindingWhereInput
}

input NotificationManyRelationFilter {
  every: NotificationWhereInput
  some: NotificationWhereInput
  none: NotificationWhereInput
}

input BooleanFilter {
  equals: Boolean
  not: BooleanFilter
}

input GroupManyRelationFilter {
  every: GroupWhereInput
  some: GroupWhereInput
  none: GroupWhereInput
}

input GroupSupportTicketManyRelationFilter {
  every: GroupSupportTicketWhereInput
  some: GroupSupportTicketWhereInput
  none: GroupSupportTicketWhereInput
}

input UserOrderByInput {
  id: OrderDirection
  name: OrderDirection
  lastName: OrderDirection
  email: OrderDirection
  phone: OrderDirection
  title: OrderDirection
  role: OrderDirection
  createdAt: OrderDirection
  type: OrderDirection
  lastLogin: OrderDirection
  flag_canAffiliate: OrderDirection
}

input UserUpdateInput {
  name: String
  lastName: String
  email: String
  phone: String
  title: String
  adminPassword: String
  avatar: ImageFieldInput
  role: UserRoleType
  createdAt: DateTime
  type: String
  groups: GroupMemberRelateToManyForUpdateInput
  affiliationProfile: AffiliationProfileRelateToOneForUpdateInput
  affiliatedTo: AffiliationProfileRelateToOneForUpdateInput
  caches: UserCacheRelateToManyForUpdateInput
  ghlSSoBindings: GHLSSOBindingRelateToManyForUpdateInput
  lastLogin: DateTime
  notifications: NotificationRelateToManyForUpdateInput
  flag_canAffiliate: Boolean
  createdGroups: GroupRelateToManyForUpdateInput
  supportTickets: GroupSupportTicketRelateToManyForUpdateInput
}

input ImageFieldInput {
  upload: Upload!
}

"""
The `Upload` scalar type represents a file upload.
"""
scalar Upload

input GroupMemberRelateToManyForUpdateInput {
  disconnect: [GroupMemberWhereUniqueInput!]
  set: [GroupMemberWhereUniqueInput!]
  create: [GroupMemberCreateInput!]
  connect: [GroupMemberWhereUniqueInput!]
}

input AffiliationProfileRelateToOneForUpdateInput {
  create: AffiliationProfileCreateInput
  connect: AffiliationProfileWhereUniqueInput
  disconnect: Boolean
}

input UserCacheRelateToManyForUpdateInput {
  disconnect: [UserCacheWhereUniqueInput!]
  set: [UserCacheWhereUniqueInput!]
  create: [UserCacheCreateInput!]
  connect: [UserCacheWhereUniqueInput!]
}

input GHLSSOBindingRelateToManyForUpdateInput {
  disconnect: [GHLSSOBindingWhereUniqueInput!]
  set: [GHLSSOBindingWhereUniqueInput!]
  create: [GHLSSOBindingCreateInput!]
  connect: [GHLSSOBindingWhereUniqueInput!]
}

input NotificationRelateToManyForUpdateInput {
  disconnect: [NotificationWhereUniqueInput!]
  set: [NotificationWhereUniqueInput!]
  create: [NotificationCreateInput!]
  connect: [NotificationWhereUniqueInput!]
}

input GroupRelateToManyForUpdateInput {
  disconnect: [GroupWhereUniqueInput!]
  set: [GroupWhereUniqueInput!]
  create: [GroupCreateInput!]
  connect: [GroupWhereUniqueInput!]
}

input GroupSupportTicketRelateToManyForUpdateInput {
  disconnect: [GroupSupportTicketWhereUniqueInput!]
  set: [GroupSupportTicketWhereUniqueInput!]
  create: [GroupSupportTicketCreateInput!]
  connect: [GroupSupportTicketWhereUniqueInput!]
}

input UserUpdateArgs {
  where: UserWhereUniqueInput!
  data: UserUpdateInput!
}

input UserCreateInput {
  name: String
  lastName: String
  email: String
  phone: String
  title: String
  adminPassword: String
  avatar: ImageFieldInput
  role: UserRoleType
  createdAt: DateTime
  type: String
  groups: GroupMemberRelateToManyForCreateInput
  affiliationProfile: AffiliationProfileRelateToOneForCreateInput
  affiliatedTo: AffiliationProfileRelateToOneForCreateInput
  caches: UserCacheRelateToManyForCreateInput
  ghlSSoBindings: GHLSSOBindingRelateToManyForCreateInput
  lastLogin: DateTime
  notifications: NotificationRelateToManyForCreateInput
  flag_canAffiliate: Boolean
  createdGroups: GroupRelateToManyForCreateInput
  supportTickets: GroupSupportTicketRelateToManyForCreateInput
}

input GroupMemberRelateToManyForCreateInput {
  create: [GroupMemberCreateInput!]
  connect: [GroupMemberWhereUniqueInput!]
}

input AffiliationProfileRelateToOneForCreateInput {
  create: AffiliationProfileCreateInput
  connect: AffiliationProfileWhereUniqueInput
}

input UserCacheRelateToManyForCreateInput {
  create: [UserCacheCreateInput!]
  connect: [UserCacheWhereUniqueInput!]
}

input GHLSSOBindingRelateToManyForCreateInput {
  create: [GHLSSOBindingCreateInput!]
  connect: [GHLSSOBindingWhereUniqueInput!]
}

input NotificationRelateToManyForCreateInput {
  create: [NotificationCreateInput!]
  connect: [NotificationWhereUniqueInput!]
}

input GroupRelateToManyForCreateInput {
  create: [GroupCreateInput!]
  connect: [GroupWhereUniqueInput!]
}

input GroupSupportTicketRelateToManyForCreateInput {
  create: [GroupSupportTicketCreateInput!]
  connect: [GroupSupportTicketWhereUniqueInput!]
}

type UserCache {
  id: ID!
  user: User
  keyword: String
  data: JSON
}

input UserCacheWhereUniqueInput {
  id: ID
}

input UserCacheWhereInput {
  AND: [UserCacheWhereInput!]
  OR: [UserCacheWhereInput!]
  NOT: [UserCacheWhereInput!]
  id: IDFilter
  user: UserWhereInput
  keyword: StringFilter
}

input UserCacheOrderByInput {
  id: OrderDirection
  keyword: OrderDirection
}

input UserCacheUpdateInput {
  user: UserRelateToOneForUpdateInput
  keyword: String
  data: JSON
}

input UserRelateToOneForUpdateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
  disconnect: Boolean
}

input UserCacheUpdateArgs {
  where: UserCacheWhereUniqueInput!
  data: UserCacheUpdateInput!
}

input UserCacheCreateInput {
  user: UserRelateToOneForCreateInput
  keyword: String
  data: JSON
}

input UserRelateToOneForCreateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
}

type Group {
  id: ID!
  name: String
  labelName: String
  actualName: String
  members(
    where: GroupMemberWhereInput! = {}
    orderBy: [GroupMemberOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupMemberWhereUniqueInput
  ): [GroupMember!]
  membersCount(where: GroupMemberWhereInput! = {}): Int
  processorStatus: String
  signingURL: String
  mainProcessor: String
  mainGateway: String
  einNumber: String
  createdBy: User
  processorAUR: AURApplication
  processorGLP: GLPApplication
  processorTST: TSTApplication
  gatewayFLPay: FLPayGateway
  createdAt: DateTime
  updatedAt: DateTime
  displayMerchantID: String
  pciStatus: String
  firstName: String
  lastName: String
  fullName: String
  addressLine1: String
  addressLine2: String
  city: String
  state: String
  country: String
  zip: String
  mccCode: String
  phoneNumber: String
  email: String
  default_includeSurcharge: Boolean
  default_globalDisableCC: Boolean
  default_globalDisableACH: Boolean
  ghlAccesses(
    where: GHLAccessWhereInput! = {}
    orderBy: [GHLAccessOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GHLAccessWhereUniqueInput
  ): [GHLAccess!]
  ghlAccessesCount(where: GHLAccessWhereInput! = {}): Int
  hubspotAccesses(
    where: HubspotAccessWhereInput! = {}
    orderBy: [HubspotAccessOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: HubspotAccessWhereUniqueInput
  ): [HubspotAccess!]
  hubspotAccessesCount(where: HubspotAccessWhereInput! = {}): Int
  ghlPayTransactionMaps(
    where: GHLPayTransactionMapWhereInput! = {}
    orderBy: [GHLPayTransactionMapOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GHLPayTransactionMapWhereUniqueInput
  ): [GHLPayTransactionMap!]
  ghlPayTransactionMapsCount(where: GHLPayTransactionMapWhereInput! = {}): Int
  supportTickets(
    where: GroupSupportTicketWhereInput! = {}
    orderBy: [GroupSupportTicketOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupSupportTicketWhereUniqueInput
  ): [GroupSupportTicket!]
  supportTicketsCount(where: GroupSupportTicketWhereInput! = {}): Int
  affiliatedTo: AffiliationProfile
  serviceAccounts(
    where: ServiceAPIAccountWhereInput! = {}
    orderBy: [ServiceAPIAccountOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: ServiceAPIAccountWhereUniqueInput
  ): [ServiceAPIAccount!]
  serviceAccountsCount(where: ServiceAPIAccountWhereInput! = {}): Int
  stripeDataApplication: StripeDataApplication
  flag_disableTokens: Boolean
  flag_disableAutoToken: Boolean
  webhooks(
    where: GroupWebhookWhereInput! = {}
    orderBy: [GroupWebhookOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupWebhookWhereUniqueInput
  ): [GroupWebhook!]
  webhooksCount(where: GroupWebhookWhereInput! = {}): Int
  webhookEvents(
    where: EventWebhookWhereInput! = {}
    orderBy: [EventWebhookOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: EventWebhookWhereUniqueInput
  ): [EventWebhook!]
  webhookEventsCount(where: EventWebhookWhereInput! = {}): Int
  earns(
    where: SweepEarnHistoryWhereInput! = {}
    orderBy: [SweepEarnHistoryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: SweepEarnHistoryWhereUniqueInput
  ): [SweepEarnHistory!]
  earnsCount(where: SweepEarnHistoryWhereInput! = {}): Int
  custom_staticCNPSurcharge: Float
  custom_dynamicCNPSurcharge: Float
  custom_staticCPSurcharge: Float
  custom_dynamicCPSurcharge: Float
}

input GroupWhereUniqueInput {
  id: ID
}

input GroupWhereInput {
  AND: [GroupWhereInput!]
  OR: [GroupWhereInput!]
  NOT: [GroupWhereInput!]
  id: IDFilter
  labelName: StringFilter
  actualName: StringFilter
  members: GroupMemberManyRelationFilter
  processorStatus: StringFilter
  signingURL: StringFilter
  mainProcessor: StringFilter
  mainGateway: StringFilter
  einNumber: StringFilter
  createdBy: UserWhereInput
  processorAUR: AURApplicationWhereInput
  processorGLP: GLPApplicationWhereInput
  processorTST: TSTApplicationWhereInput
  gatewayFLPay: FLPayGatewayWhereInput
  createdAt: DateTimeNullableFilter
  updatedAt: DateTimeNullableFilter
  pciStatus: StringFilter
  firstName: StringFilter
  lastName: StringFilter
  addressLine1: StringFilter
  addressLine2: StringFilter
  city: StringFilter
  state: StringFilter
  country: StringFilter
  zip: StringFilter
  mccCode: StringFilter
  phoneNumber: StringFilter
  email: StringFilter
  default_includeSurcharge: BooleanFilter
  default_globalDisableCC: BooleanFilter
  default_globalDisableACH: BooleanFilter
  ghlAccesses: GHLAccessManyRelationFilter
  hubspotAccesses: HubspotAccessManyRelationFilter
  ghlPayTransactionMaps: GHLPayTransactionMapManyRelationFilter
  supportTickets: GroupSupportTicketManyRelationFilter
  affiliatedTo: AffiliationProfileWhereInput
  serviceAccounts: ServiceAPIAccountManyRelationFilter
  stripeDataApplication: StripeDataApplicationWhereInput
  flag_disableTokens: BooleanFilter
  flag_disableAutoToken: BooleanFilter
  webhooks: GroupWebhookManyRelationFilter
  webhookEvents: EventWebhookManyRelationFilter
  earns: SweepEarnHistoryManyRelationFilter
  custom_staticCNPSurcharge: FloatNullableFilter
  custom_dynamicCNPSurcharge: FloatNullableFilter
  custom_staticCPSurcharge: FloatNullableFilter
  custom_dynamicCPSurcharge: FloatNullableFilter
}

input GHLAccessManyRelationFilter {
  every: GHLAccessWhereInput
  some: GHLAccessWhereInput
  none: GHLAccessWhereInput
}

input HubspotAccessManyRelationFilter {
  every: HubspotAccessWhereInput
  some: HubspotAccessWhereInput
  none: HubspotAccessWhereInput
}

input GHLPayTransactionMapManyRelationFilter {
  every: GHLPayTransactionMapWhereInput
  some: GHLPayTransactionMapWhereInput
  none: GHLPayTransactionMapWhereInput
}

input ServiceAPIAccountManyRelationFilter {
  every: ServiceAPIAccountWhereInput
  some: ServiceAPIAccountWhereInput
  none: ServiceAPIAccountWhereInput
}

input GroupWebhookManyRelationFilter {
  every: GroupWebhookWhereInput
  some: GroupWebhookWhereInput
  none: GroupWebhookWhereInput
}

input EventWebhookManyRelationFilter {
  every: EventWebhookWhereInput
  some: EventWebhookWhereInput
  none: EventWebhookWhereInput
}

input SweepEarnHistoryManyRelationFilter {
  every: SweepEarnHistoryWhereInput
  some: SweepEarnHistoryWhereInput
  none: SweepEarnHistoryWhereInput
}

input FloatNullableFilter {
  equals: Float
  in: [Float!]
  notIn: [Float!]
  lt: Float
  lte: Float
  gt: Float
  gte: Float
  not: FloatNullableFilter
}

input GroupOrderByInput {
  id: OrderDirection
  labelName: OrderDirection
  actualName: OrderDirection
  processorStatus: OrderDirection
  signingURL: OrderDirection
  mainProcessor: OrderDirection
  mainGateway: OrderDirection
  einNumber: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
  pciStatus: OrderDirection
  firstName: OrderDirection
  lastName: OrderDirection
  addressLine1: OrderDirection
  addressLine2: OrderDirection
  city: OrderDirection
  state: OrderDirection
  country: OrderDirection
  zip: OrderDirection
  mccCode: OrderDirection
  phoneNumber: OrderDirection
  email: OrderDirection
  default_includeSurcharge: OrderDirection
  default_globalDisableCC: OrderDirection
  default_globalDisableACH: OrderDirection
  flag_disableTokens: OrderDirection
  flag_disableAutoToken: OrderDirection
  custom_staticCNPSurcharge: OrderDirection
  custom_dynamicCNPSurcharge: OrderDirection
  custom_staticCPSurcharge: OrderDirection
  custom_dynamicCPSurcharge: OrderDirection
}

input GroupUpdateInput {
  labelName: String
  actualName: String
  members: GroupMemberRelateToManyForUpdateInput
  processorStatus: String
  signingURL: String
  mainProcessor: String
  mainGateway: String
  einNumber: String
  createdBy: UserRelateToOneForUpdateInput
  processorAUR: AURApplicationRelateToOneForUpdateInput
  processorGLP: GLPApplicationRelateToOneForUpdateInput
  processorTST: TSTApplicationRelateToOneForUpdateInput
  gatewayFLPay: FLPayGatewayRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
  pciStatus: String
  firstName: String
  lastName: String
  addressLine1: String
  addressLine2: String
  city: String
  state: String
  country: String
  zip: String
  mccCode: String
  phoneNumber: String
  email: String
  default_includeSurcharge: Boolean
  default_globalDisableCC: Boolean
  default_globalDisableACH: Boolean
  ghlAccesses: GHLAccessRelateToManyForUpdateInput
  hubspotAccesses: HubspotAccessRelateToManyForUpdateInput
  ghlPayTransactionMaps: GHLPayTransactionMapRelateToManyForUpdateInput
  supportTickets: GroupSupportTicketRelateToManyForUpdateInput
  affiliatedTo: AffiliationProfileRelateToOneForUpdateInput
  serviceAccounts: ServiceAPIAccountRelateToManyForUpdateInput
  stripeDataApplication: StripeDataApplicationRelateToOneForUpdateInput
  flag_disableTokens: Boolean
  flag_disableAutoToken: Boolean
  webhooks: GroupWebhookRelateToManyForUpdateInput
  webhookEvents: EventWebhookRelateToManyForUpdateInput
  earns: SweepEarnHistoryRelateToManyForUpdateInput
  custom_staticCNPSurcharge: Float
  custom_dynamicCNPSurcharge: Float
  custom_staticCPSurcharge: Float
  custom_dynamicCPSurcharge: Float
}

input AURApplicationRelateToOneForUpdateInput {
  create: AURApplicationCreateInput
  connect: AURApplicationWhereUniqueInput
  disconnect: Boolean
}

input GLPApplicationRelateToOneForUpdateInput {
  create: GLPApplicationCreateInput
  connect: GLPApplicationWhereUniqueInput
  disconnect: Boolean
}

input TSTApplicationRelateToOneForUpdateInput {
  create: TSTApplicationCreateInput
  connect: TSTApplicationWhereUniqueInput
  disconnect: Boolean
}

input FLPayGatewayRelateToOneForUpdateInput {
  create: FLPayGatewayCreateInput
  connect: FLPayGatewayWhereUniqueInput
  disconnect: Boolean
}

input GHLAccessRelateToManyForUpdateInput {
  disconnect: [GHLAccessWhereUniqueInput!]
  set: [GHLAccessWhereUniqueInput!]
  create: [GHLAccessCreateInput!]
  connect: [GHLAccessWhereUniqueInput!]
}

input HubspotAccessRelateToManyForUpdateInput {
  disconnect: [HubspotAccessWhereUniqueInput!]
  set: [HubspotAccessWhereUniqueInput!]
  create: [HubspotAccessCreateInput!]
  connect: [HubspotAccessWhereUniqueInput!]
}

input GHLPayTransactionMapRelateToManyForUpdateInput {
  disconnect: [GHLPayTransactionMapWhereUniqueInput!]
  set: [GHLPayTransactionMapWhereUniqueInput!]
  create: [GHLPayTransactionMapCreateInput!]
  connect: [GHLPayTransactionMapWhereUniqueInput!]
}

input ServiceAPIAccountRelateToManyForUpdateInput {
  disconnect: [ServiceAPIAccountWhereUniqueInput!]
  set: [ServiceAPIAccountWhereUniqueInput!]
  create: [ServiceAPIAccountCreateInput!]
  connect: [ServiceAPIAccountWhereUniqueInput!]
}

input StripeDataApplicationRelateToOneForUpdateInput {
  create: StripeDataApplicationCreateInput
  connect: StripeDataApplicationWhereUniqueInput
  disconnect: Boolean
}

input GroupWebhookRelateToManyForUpdateInput {
  disconnect: [GroupWebhookWhereUniqueInput!]
  set: [GroupWebhookWhereUniqueInput!]
  create: [GroupWebhookCreateInput!]
  connect: [GroupWebhookWhereUniqueInput!]
}

input EventWebhookRelateToManyForUpdateInput {
  disconnect: [EventWebhookWhereUniqueInput!]
  set: [EventWebhookWhereUniqueInput!]
  create: [EventWebhookCreateInput!]
  connect: [EventWebhookWhereUniqueInput!]
}

input SweepEarnHistoryRelateToManyForUpdateInput {
  disconnect: [SweepEarnHistoryWhereUniqueInput!]
  set: [SweepEarnHistoryWhereUniqueInput!]
  create: [SweepEarnHistoryCreateInput!]
  connect: [SweepEarnHistoryWhereUniqueInput!]
}

input GroupUpdateArgs {
  where: GroupWhereUniqueInput!
  data: GroupUpdateInput!
}

input GroupCreateInput {
  labelName: String
  actualName: String
  members: GroupMemberRelateToManyForCreateInput
  processorStatus: String
  signingURL: String
  mainProcessor: String
  mainGateway: String
  einNumber: String
  createdBy: UserRelateToOneForCreateInput
  processorAUR: AURApplicationRelateToOneForCreateInput
  processorGLP: GLPApplicationRelateToOneForCreateInput
  processorTST: TSTApplicationRelateToOneForCreateInput
  gatewayFLPay: FLPayGatewayRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
  pciStatus: String
  firstName: String
  lastName: String
  addressLine1: String
  addressLine2: String
  city: String
  state: String
  country: String
  zip: String
  mccCode: String
  phoneNumber: String
  email: String
  default_includeSurcharge: Boolean
  default_globalDisableCC: Boolean
  default_globalDisableACH: Boolean
  ghlAccesses: GHLAccessRelateToManyForCreateInput
  hubspotAccesses: HubspotAccessRelateToManyForCreateInput
  ghlPayTransactionMaps: GHLPayTransactionMapRelateToManyForCreateInput
  supportTickets: GroupSupportTicketRelateToManyForCreateInput
  affiliatedTo: AffiliationProfileRelateToOneForCreateInput
  serviceAccounts: ServiceAPIAccountRelateToManyForCreateInput
  stripeDataApplication: StripeDataApplicationRelateToOneForCreateInput
  flag_disableTokens: Boolean
  flag_disableAutoToken: Boolean
  webhooks: GroupWebhookRelateToManyForCreateInput
  webhookEvents: EventWebhookRelateToManyForCreateInput
  earns: SweepEarnHistoryRelateToManyForCreateInput
  custom_staticCNPSurcharge: Float
  custom_dynamicCNPSurcharge: Float
  custom_staticCPSurcharge: Float
  custom_dynamicCPSurcharge: Float
}

input AURApplicationRelateToOneForCreateInput {
  create: AURApplicationCreateInput
  connect: AURApplicationWhereUniqueInput
}

input GLPApplicationRelateToOneForCreateInput {
  create: GLPApplicationCreateInput
  connect: GLPApplicationWhereUniqueInput
}

input TSTApplicationRelateToOneForCreateInput {
  create: TSTApplicationCreateInput
  connect: TSTApplicationWhereUniqueInput
}

input FLPayGatewayRelateToOneForCreateInput {
  create: FLPayGatewayCreateInput
  connect: FLPayGatewayWhereUniqueInput
}

input GHLAccessRelateToManyForCreateInput {
  create: [GHLAccessCreateInput!]
  connect: [GHLAccessWhereUniqueInput!]
}

input HubspotAccessRelateToManyForCreateInput {
  create: [HubspotAccessCreateInput!]
  connect: [HubspotAccessWhereUniqueInput!]
}

input GHLPayTransactionMapRelateToManyForCreateInput {
  create: [GHLPayTransactionMapCreateInput!]
  connect: [GHLPayTransactionMapWhereUniqueInput!]
}

input ServiceAPIAccountRelateToManyForCreateInput {
  create: [ServiceAPIAccountCreateInput!]
  connect: [ServiceAPIAccountWhereUniqueInput!]
}

input StripeDataApplicationRelateToOneForCreateInput {
  create: StripeDataApplicationCreateInput
  connect: StripeDataApplicationWhereUniqueInput
}

input GroupWebhookRelateToManyForCreateInput {
  create: [GroupWebhookCreateInput!]
  connect: [GroupWebhookWhereUniqueInput!]
}

input EventWebhookRelateToManyForCreateInput {
  create: [EventWebhookCreateInput!]
  connect: [EventWebhookWhereUniqueInput!]
}

input SweepEarnHistoryRelateToManyForCreateInput {
  create: [SweepEarnHistoryCreateInput!]
  connect: [SweepEarnHistoryWhereUniqueInput!]
}

type GroupWebhook {
  id: ID!
  group: Group
  webhookURL: String
  scope: JSON
}

input GroupWebhookWhereUniqueInput {
  id: ID
}

input GroupWebhookWhereInput {
  AND: [GroupWebhookWhereInput!]
  OR: [GroupWebhookWhereInput!]
  NOT: [GroupWebhookWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  webhookURL: StringFilter
}

input GroupWebhookOrderByInput {
  id: OrderDirection
  webhookURL: OrderDirection
}

input GroupWebhookUpdateInput {
  group: GroupRelateToOneForUpdateInput
  webhookURL: String
  scope: JSON
}

input GroupRelateToOneForUpdateInput {
  create: GroupCreateInput
  connect: GroupWhereUniqueInput
  disconnect: Boolean
}

input GroupWebhookUpdateArgs {
  where: GroupWebhookWhereUniqueInput!
  data: GroupWebhookUpdateInput!
}

input GroupWebhookCreateInput {
  group: GroupRelateToOneForCreateInput
  webhookURL: String
  scope: JSON
}

input GroupRelateToOneForCreateInput {
  create: GroupCreateInput
  connect: GroupWhereUniqueInput
}

type GroupMember {
  id: ID!
  group: Group
  user: User
  access: Int
  flags(
    where: GroupMemberFlagWhereInput! = {}
    orderBy: [GroupMemberFlagOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupMemberFlagWhereUniqueInput
  ): [GroupMemberFlag!]
  flagsCount(where: GroupMemberFlagWhereInput! = {}): Int
  invite: GroupMemberInvite
}

input GroupMemberWhereUniqueInput {
  id: ID
}

input GroupMemberWhereInput {
  AND: [GroupMemberWhereInput!]
  OR: [GroupMemberWhereInput!]
  NOT: [GroupMemberWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  user: UserWhereInput
  access: IntNullableFilter
  flags: GroupMemberFlagManyRelationFilter
  invite: GroupMemberInviteWhereInput
}

input IntNullableFilter {
  equals: Int
  in: [Int!]
  notIn: [Int!]
  lt: Int
  lte: Int
  gt: Int
  gte: Int
  not: IntNullableFilter
}

input GroupMemberFlagManyRelationFilter {
  every: GroupMemberFlagWhereInput
  some: GroupMemberFlagWhereInput
  none: GroupMemberFlagWhereInput
}

input GroupMemberOrderByInput {
  id: OrderDirection
  access: OrderDirection
}

input GroupMemberUpdateInput {
  group: GroupRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  access: Int
  flags: GroupMemberFlagRelateToManyForUpdateInput
  invite: GroupMemberInviteRelateToOneForUpdateInput
}

input GroupMemberFlagRelateToManyForUpdateInput {
  disconnect: [GroupMemberFlagWhereUniqueInput!]
  set: [GroupMemberFlagWhereUniqueInput!]
  create: [GroupMemberFlagCreateInput!]
  connect: [GroupMemberFlagWhereUniqueInput!]
}

input GroupMemberInviteRelateToOneForUpdateInput {
  create: GroupMemberInviteCreateInput
  connect: GroupMemberInviteWhereUniqueInput
  disconnect: Boolean
}

input GroupMemberUpdateArgs {
  where: GroupMemberWhereUniqueInput!
  data: GroupMemberUpdateInput!
}

input GroupMemberCreateInput {
  group: GroupRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  access: Int
  flags: GroupMemberFlagRelateToManyForCreateInput
  invite: GroupMemberInviteRelateToOneForCreateInput
}

input GroupMemberFlagRelateToManyForCreateInput {
  create: [GroupMemberFlagCreateInput!]
  connect: [GroupMemberFlagWhereUniqueInput!]
}

input GroupMemberInviteRelateToOneForCreateInput {
  create: GroupMemberInviteCreateInput
  connect: GroupMemberInviteWhereUniqueInput
}

type GroupMemberInvite {
  id: ID!
  group: Group
  groupMembership: GroupMember
  email: String
}

input GroupMemberInviteWhereUniqueInput {
  id: ID
}

input GroupMemberInviteWhereInput {
  AND: [GroupMemberInviteWhereInput!]
  OR: [GroupMemberInviteWhereInput!]
  NOT: [GroupMemberInviteWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  groupMembership: GroupMemberWhereInput
  email: StringFilter
}

input GroupMemberInviteOrderByInput {
  id: OrderDirection
  email: OrderDirection
}

input GroupMemberInviteUpdateInput {
  group: GroupRelateToOneForUpdateInput
  groupMembership: GroupMemberRelateToOneForUpdateInput
  email: String
}

input GroupMemberRelateToOneForUpdateInput {
  create: GroupMemberCreateInput
  connect: GroupMemberWhereUniqueInput
  disconnect: Boolean
}

input GroupMemberInviteUpdateArgs {
  where: GroupMemberInviteWhereUniqueInput!
  data: GroupMemberInviteUpdateInput!
}

input GroupMemberInviteCreateInput {
  group: GroupRelateToOneForCreateInput
  groupMembership: GroupMemberRelateToOneForCreateInput
  email: String
}

input GroupMemberRelateToOneForCreateInput {
  create: GroupMemberCreateInput
  connect: GroupMemberWhereUniqueInput
}

type GroupMemberFlag {
  id: ID!
  groupMember: GroupMember
  flag: String
}

input GroupMemberFlagWhereUniqueInput {
  id: ID
}

input GroupMemberFlagWhereInput {
  AND: [GroupMemberFlagWhereInput!]
  OR: [GroupMemberFlagWhereInput!]
  NOT: [GroupMemberFlagWhereInput!]
  id: IDFilter
  groupMember: GroupMemberWhereInput
  flag: StringFilter
}

input GroupMemberFlagOrderByInput {
  id: OrderDirection
  flag: OrderDirection
}

input GroupMemberFlagUpdateInput {
  groupMember: GroupMemberRelateToOneForUpdateInput
  flag: String
}

input GroupMemberFlagUpdateArgs {
  where: GroupMemberFlagWhereUniqueInput!
  data: GroupMemberFlagUpdateInput!
}

input GroupMemberFlagCreateInput {
  groupMember: GroupMemberRelateToOneForCreateInput
  flag: String
}

type TSTApplication {
  id: ID!
  group: Group
  applicationID: String
  applicationNumber: String
  processor_tst_signingUrl: String
  processor_tst_applicationStatus: ApplicationStatusObject
}

input TSTApplicationWhereUniqueInput {
  id: ID
}

input TSTApplicationWhereInput {
  AND: [TSTApplicationWhereInput!]
  OR: [TSTApplicationWhereInput!]
  NOT: [TSTApplicationWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  applicationID: StringFilter
  applicationNumber: StringFilter
}

input TSTApplicationOrderByInput {
  id: OrderDirection
  applicationID: OrderDirection
  applicationNumber: OrderDirection
}

input TSTApplicationUpdateInput {
  group: GroupRelateToOneForUpdateInput
  applicationID: String
  applicationNumber: String
}

input TSTApplicationUpdateArgs {
  where: TSTApplicationWhereUniqueInput!
  data: TSTApplicationUpdateInput!
}

input TSTApplicationCreateInput {
  group: GroupRelateToOneForCreateInput
  applicationID: String
  applicationNumber: String
}

type GLPApplication {
  id: ID!
  group: Group
  applicationID: String
  applicationNumber: String
  draftID: String
  cardProcessorID: String
  cardProcessorName: String
  achProcessorID: String
  achProcessorName: String
  tokenizerID: String
  tokenizerName: String
  fundsAccountID: String
  fundsAccountName: String
  onboardingError: String
  glpStatus: String
  mock: Boolean
  enableSweep: Boolean
  sweepOverhead: Int
}

input GLPApplicationWhereUniqueInput {
  id: ID
}

input GLPApplicationWhereInput {
  AND: [GLPApplicationWhereInput!]
  OR: [GLPApplicationWhereInput!]
  NOT: [GLPApplicationWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  applicationID: StringFilter
  applicationNumber: StringFilter
  draftID: StringFilter
  cardProcessorID: StringFilter
  cardProcessorName: StringFilter
  achProcessorID: StringFilter
  achProcessorName: StringFilter
  tokenizerID: StringFilter
  tokenizerName: StringFilter
  fundsAccountID: StringFilter
  fundsAccountName: StringFilter
  onboardingError: StringFilter
  glpStatus: StringFilter
  mock: BooleanFilter
  enableSweep: BooleanFilter
  sweepOverhead: IntNullableFilter
}

input GLPApplicationOrderByInput {
  id: OrderDirection
  applicationID: OrderDirection
  applicationNumber: OrderDirection
  draftID: OrderDirection
  cardProcessorID: OrderDirection
  cardProcessorName: OrderDirection
  achProcessorID: OrderDirection
  achProcessorName: OrderDirection
  tokenizerID: OrderDirection
  tokenizerName: OrderDirection
  fundsAccountID: OrderDirection
  fundsAccountName: OrderDirection
  onboardingError: OrderDirection
  glpStatus: OrderDirection
  mock: OrderDirection
  enableSweep: OrderDirection
  sweepOverhead: OrderDirection
}

input GLPApplicationUpdateInput {
  group: GroupRelateToOneForUpdateInput
  applicationID: String
  applicationNumber: String
  draftID: String
  cardProcessorID: String
  cardProcessorName: String
  achProcessorID: String
  achProcessorName: String
  tokenizerID: String
  tokenizerName: String
  fundsAccountID: String
  fundsAccountName: String
  onboardingError: String
  glpStatus: String
  mock: Boolean
  enableSweep: Boolean
  sweepOverhead: Int
}

input GLPApplicationUpdateArgs {
  where: GLPApplicationWhereUniqueInput!
  data: GLPApplicationUpdateInput!
}

input GLPApplicationCreateInput {
  group: GroupRelateToOneForCreateInput
  applicationID: String
  applicationNumber: String
  draftID: String
  cardProcessorID: String
  cardProcessorName: String
  achProcessorID: String
  achProcessorName: String
  tokenizerID: String
  tokenizerName: String
  fundsAccountID: String
  fundsAccountName: String
  onboardingError: String
  glpStatus: String
  mock: Boolean
  enableSweep: Boolean
  sweepOverhead: Int
}

type GLPPayWebhook {
  id: ID!
  data: JSON
  data_text: String
  data_id: String
  data_mid: String
  data_status: String
  data_action_type: String
  createdAt: DateTime
}

input GLPPayWebhookWhereUniqueInput {
  id: ID
}

input GLPPayWebhookWhereInput {
  AND: [GLPPayWebhookWhereInput!]
  OR: [GLPPayWebhookWhereInput!]
  NOT: [GLPPayWebhookWhereInput!]
  id: IDFilter
  createdAt: DateTimeNullableFilter
}

input GLPPayWebhookOrderByInput {
  id: OrderDirection
  createdAt: OrderDirection
}

input GLPPayWebhookUpdateInput {
  data: JSON
  createdAt: DateTime
}

input GLPPayWebhookUpdateArgs {
  where: GLPPayWebhookWhereUniqueInput!
  data: GLPPayWebhookUpdateInput!
}

input GLPPayWebhookCreateInput {
  data: JSON
  createdAt: DateTime
}

type GLPPayDraft {
  id: ID!
  data: JSON
  encryptedView: String
  signingURL: String
  fileUploads: JSON
}

input GLPPayDraftWhereUniqueInput {
  id: ID
}

input GLPPayDraftWhereInput {
  AND: [GLPPayDraftWhereInput!]
  OR: [GLPPayDraftWhereInput!]
  NOT: [GLPPayDraftWhereInput!]
  id: IDFilter
  signingURL: StringFilter
}

input GLPPayDraftOrderByInput {
  id: OrderDirection
  signingURL: OrderDirection
}

input GLPPayDraftUpdateInput {
  data: JSON
  signingURL: String
  fileUploads: JSON
}

input GLPPayDraftUpdateArgs {
  where: GLPPayDraftWhereUniqueInput!
  data: GLPPayDraftUpdateInput!
}

input GLPPayDraftCreateInput {
  data: JSON
  signingURL: String
  fileUploads: JSON
}

type AURApplication {
  id: ID!
  group: Group
  applicationID: String
  applicationNumber: String
  processor_aur_signingUrl: String
  processor_aur_applicationStatus: ApplicationStatusObject
  processor_aur_uploads: AURFileUploadDataList
}

input AURApplicationWhereUniqueInput {
  id: ID
}

input AURApplicationWhereInput {
  AND: [AURApplicationWhereInput!]
  OR: [AURApplicationWhereInput!]
  NOT: [AURApplicationWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  applicationID: StringFilter
  applicationNumber: StringFilter
}

input AURApplicationOrderByInput {
  id: OrderDirection
  applicationID: OrderDirection
  applicationNumber: OrderDirection
}

input AURApplicationUpdateInput {
  group: GroupRelateToOneForUpdateInput
  applicationID: String
  applicationNumber: String
}

input AURApplicationUpdateArgs {
  where: AURApplicationWhereUniqueInput!
  data: AURApplicationUpdateInput!
}

input AURApplicationCreateInput {
  group: GroupRelateToOneForCreateInput
  applicationID: String
  applicationNumber: String
}

type FLPayGateway {
  id: ID!
  group: Group
  mainCardProcessor: String
  publicKey: String
}

input FLPayGatewayWhereUniqueInput {
  id: ID
}

input FLPayGatewayWhereInput {
  AND: [FLPayGatewayWhereInput!]
  OR: [FLPayGatewayWhereInput!]
  NOT: [FLPayGatewayWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  mainCardProcessor: StringFilter
  publicKey: StringFilter
  processors: FLPayGatewayProcessorWhereInput
}

input FLPayGatewayProcessorWhereInput {
  AND: [FLPayGatewayProcessorWhereInput!]
  OR: [FLPayGatewayProcessorWhereInput!]
  NOT: [FLPayGatewayProcessorWhereInput!]
  id: IDFilter
}

input FLPayGatewayOrderByInput {
  id: OrderDirection
  mainCardProcessor: OrderDirection
  publicKey: OrderDirection
}

input FLPayGatewayUpdateInput {
  group: GroupRelateToOneForUpdateInput
  mainCardProcessor: String
  publicKey: String
}

input FLPayGatewayUpdateArgs {
  where: FLPayGatewayWhereUniqueInput!
  data: FLPayGatewayUpdateInput!
}

input FLPayGatewayCreateInput {
  group: GroupRelateToOneForCreateInput
  mainCardProcessor: String
  publicKey: String
}

type GUni_Transactions {
  id: ID!
  tx_id: String
  type: String
  status: String
  channel: String
  capture_mode: String
  amount: Int
  currency: String
  country: String
  merchant_id: String
  group_id: String
  batch_id: String
  result: String
  message: String
  entry_mode: String
  pmt_id: String
  masked_number_last4: String
  brand: String
  exp_month: String
  exp_year: String
  authcode: String
  brand_reference: String
  avs_address_result: String
  avs_postal_code_result: String
  avs_action: String
  cvv_result: String
  gsa: String
  emv: String
  commercial_level: String
  payment_plan_id: String
  created_by: String
  customer_id: String
  customer_name: String
  customer_email: String
  customer_phone: String
  customer_country: String
  customer_address: String
  parentTransaction: GUni_Transactions
  childrenTransactions(
    where: GUni_TransactionsWhereInput! = {}
    orderBy: [GUni_TransactionsOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_TransactionsWhereUniqueInput
  ): [GUni_Transactions!]
  childrenTransactionsCount(where: GUni_TransactionsWhereInput! = {}): Int
  created_at: DateTime
  orderData: GUni_Transaction_OrderData
  referenceID: String
}

input GUni_TransactionsWhereUniqueInput {
  id: ID
}

input GUni_TransactionsWhereInput {
  AND: [GUni_TransactionsWhereInput!]
  OR: [GUni_TransactionsWhereInput!]
  NOT: [GUni_TransactionsWhereInput!]
  id: IDFilter
  tx_id: StringFilter
  type: StringFilter
  status: StringFilter
  channel: StringFilter
  capture_mode: StringFilter
  amount: IntNullableFilter
  currency: StringFilter
  country: StringFilter
  merchant_id: StringFilter
  group_id: StringFilter
  batch_id: StringFilter
  result: StringFilter
  message: StringFilter
  entry_mode: StringFilter
  pmt_id: StringFilter
  masked_number_last4: StringFilter
  brand: StringFilter
  exp_month: StringFilter
  exp_year: StringFilter
  authcode: StringFilter
  brand_reference: StringFilter
  avs_address_result: StringFilter
  avs_postal_code_result: StringFilter
  avs_action: StringFilter
  cvv_result: StringFilter
  gsa: StringFilter
  emv: StringFilter
  commercial_level: StringFilter
  payment_plan_id: StringFilter
  created_by: StringFilter
  customer_id: StringFilter
  customer_name: StringFilter
  customer_email: StringFilter
  customer_phone: StringFilter
  customer_country: StringFilter
  customer_address: StringFilter
  parentTransaction: GUni_TransactionsWhereInput
  childrenTransactions: GUni_TransactionsManyRelationFilter
  created_at: DateTimeNullableFilter
  orderData: GUni_Transaction_OrderDataWhereInput
  referenceID: StringFilter
}

input GUni_TransactionsManyRelationFilter {
  every: GUni_TransactionsWhereInput
  some: GUni_TransactionsWhereInput
  none: GUni_TransactionsWhereInput
}

input GUni_TransactionsOrderByInput {
  id: OrderDirection
  tx_id: OrderDirection
  type: OrderDirection
  status: OrderDirection
  channel: OrderDirection
  capture_mode: OrderDirection
  amount: OrderDirection
  currency: OrderDirection
  country: OrderDirection
  merchant_id: OrderDirection
  group_id: OrderDirection
  batch_id: OrderDirection
  result: OrderDirection
  message: OrderDirection
  entry_mode: OrderDirection
  pmt_id: OrderDirection
  masked_number_last4: OrderDirection
  brand: OrderDirection
  exp_month: OrderDirection
  exp_year: OrderDirection
  authcode: OrderDirection
  brand_reference: OrderDirection
  avs_address_result: OrderDirection
  avs_postal_code_result: OrderDirection
  avs_action: OrderDirection
  cvv_result: OrderDirection
  gsa: OrderDirection
  emv: OrderDirection
  commercial_level: OrderDirection
  payment_plan_id: OrderDirection
  created_by: OrderDirection
  customer_id: OrderDirection
  customer_name: OrderDirection
  customer_email: OrderDirection
  customer_phone: OrderDirection
  customer_country: OrderDirection
  customer_address: OrderDirection
  created_at: OrderDirection
  referenceID: OrderDirection
}

input GUni_TransactionsUpdateInput {
  tx_id: String
  type: String
  status: String
  channel: String
  capture_mode: String
  amount: Int
  currency: String
  country: String
  merchant_id: String
  group_id: String
  batch_id: String
  result: String
  message: String
  entry_mode: String
  pmt_id: String
  masked_number_last4: String
  brand: String
  exp_month: String
  exp_year: String
  authcode: String
  brand_reference: String
  avs_address_result: String
  avs_postal_code_result: String
  avs_action: String
  cvv_result: String
  gsa: String
  emv: String
  commercial_level: String
  payment_plan_id: String
  created_by: String
  customer_id: String
  customer_name: String
  customer_email: String
  customer_phone: String
  customer_country: String
  customer_address: String
  parentTransaction: GUni_TransactionsRelateToOneForUpdateInput
  childrenTransactions: GUni_TransactionsRelateToManyForUpdateInput
  created_at: DateTime
  orderData: GUni_Transaction_OrderDataRelateToOneForUpdateInput
  referenceID: String
}

input GUni_TransactionsRelateToOneForUpdateInput {
  create: GUni_TransactionsCreateInput
  connect: GUni_TransactionsWhereUniqueInput
  disconnect: Boolean
}

input GUni_TransactionsRelateToManyForUpdateInput {
  disconnect: [GUni_TransactionsWhereUniqueInput!]
  set: [GUni_TransactionsWhereUniqueInput!]
  create: [GUni_TransactionsCreateInput!]
  connect: [GUni_TransactionsWhereUniqueInput!]
}

input GUni_Transaction_OrderDataRelateToOneForUpdateInput {
  create: GUni_Transaction_OrderDataCreateInput
  connect: GUni_Transaction_OrderDataWhereUniqueInput
  disconnect: Boolean
}

input GUni_TransactionsUpdateArgs {
  where: GUni_TransactionsWhereUniqueInput!
  data: GUni_TransactionsUpdateInput!
}

input GUni_TransactionsCreateInput {
  tx_id: String
  type: String
  status: String
  channel: String
  capture_mode: String
  amount: Int
  currency: String
  country: String
  merchant_id: String
  group_id: String
  batch_id: String
  result: String
  message: String
  entry_mode: String
  pmt_id: String
  masked_number_last4: String
  brand: String
  exp_month: String
  exp_year: String
  authcode: String
  brand_reference: String
  avs_address_result: String
  avs_postal_code_result: String
  avs_action: String
  cvv_result: String
  gsa: String
  emv: String
  commercial_level: String
  payment_plan_id: String
  created_by: String
  customer_id: String
  customer_name: String
  customer_email: String
  customer_phone: String
  customer_country: String
  customer_address: String
  parentTransaction: GUni_TransactionsRelateToOneForCreateInput
  childrenTransactions: GUni_TransactionsRelateToManyForCreateInput
  created_at: DateTime
  orderData: GUni_Transaction_OrderDataRelateToOneForCreateInput
  referenceID: String
}

input GUni_TransactionsRelateToOneForCreateInput {
  create: GUni_TransactionsCreateInput
  connect: GUni_TransactionsWhereUniqueInput
}

input GUni_TransactionsRelateToManyForCreateInput {
  create: [GUni_TransactionsCreateInput!]
  connect: [GUni_TransactionsWhereUniqueInput!]
}

input GUni_Transaction_OrderDataRelateToOneForCreateInput {
  create: GUni_Transaction_OrderDataCreateInput
  connect: GUni_Transaction_OrderDataWhereUniqueInput
}

type GUni_Transaction_OrderData {
  id: ID!
  transaction: GUni_Transactions
  lineItems: JSON
  tip: Int
  subTotal: Int
  tax: Int
  shipping: Int
  discount: Int
  surcharge: Int
  total: Int
  rawTotal: Int
  shippingAddress: JSON
  narrative: String
  metadata: JSON
  customer: GUni_Customer
  created_at: DateTime
  referenceID: String
  referenceSource: String
}

input GUni_Transaction_OrderDataWhereUniqueInput {
  id: ID
}

input GUni_Transaction_OrderDataWhereInput {
  AND: [GUni_Transaction_OrderDataWhereInput!]
  OR: [GUni_Transaction_OrderDataWhereInput!]
  NOT: [GUni_Transaction_OrderDataWhereInput!]
  id: IDFilter
  transaction: GUni_TransactionsWhereInput
  tip: IntNullableFilter
  subTotal: IntNullableFilter
  tax: IntNullableFilter
  shipping: IntNullableFilter
  discount: IntNullableFilter
  surcharge: IntNullableFilter
  total: IntNullableFilter
  rawTotal: IntNullableFilter
  narrative: StringFilter
  customer: GUni_CustomerWhereInput
  created_at: DateTimeNullableFilter
  referenceID: StringFilter
  referenceSource: StringFilter
}

input GUni_Transaction_OrderDataOrderByInput {
  id: OrderDirection
  tip: OrderDirection
  subTotal: OrderDirection
  tax: OrderDirection
  shipping: OrderDirection
  discount: OrderDirection
  surcharge: OrderDirection
  total: OrderDirection
  rawTotal: OrderDirection
  narrative: OrderDirection
  created_at: OrderDirection
  referenceID: OrderDirection
  referenceSource: OrderDirection
}

input GUni_Transaction_OrderDataUpdateInput {
  transaction: GUni_TransactionsRelateToOneForUpdateInput
  lineItems: JSON
  tip: Int
  subTotal: Int
  tax: Int
  shipping: Int
  discount: Int
  surcharge: Int
  total: Int
  rawTotal: Int
  shippingAddress: JSON
  narrative: String
  metadata: JSON
  customer: GUni_CustomerRelateToOneForUpdateInput
  created_at: DateTime
  referenceID: String
  referenceSource: String
}

input GUni_CustomerRelateToOneForUpdateInput {
  create: GUni_CustomerCreateInput
  connect: GUni_CustomerWhereUniqueInput
  disconnect: Boolean
}

input GUni_Transaction_OrderDataUpdateArgs {
  where: GUni_Transaction_OrderDataWhereUniqueInput!
  data: GUni_Transaction_OrderDataUpdateInput!
}

input GUni_Transaction_OrderDataCreateInput {
  transaction: GUni_TransactionsRelateToOneForCreateInput
  lineItems: JSON
  tip: Int
  subTotal: Int
  tax: Int
  shipping: Int
  discount: Int
  surcharge: Int
  total: Int
  rawTotal: Int
  shippingAddress: JSON
  narrative: String
  metadata: JSON
  customer: GUni_CustomerRelateToOneForCreateInput
  created_at: DateTime
  referenceID: String
  referenceSource: String
}

input GUni_CustomerRelateToOneForCreateInput {
  create: GUni_CustomerCreateInput
  connect: GUni_CustomerWhereUniqueInput
}

type GUni_BatchedTransaction {
  id: ID!
  status: String
  batch_id: String
  transaction_id: String
  merchant_id: String
  group_id: String
  amount: Int
  created_at: DateTime
}

input GUni_BatchedTransactionWhereUniqueInput {
  id: ID
}

input GUni_BatchedTransactionWhereInput {
  AND: [GUni_BatchedTransactionWhereInput!]
  OR: [GUni_BatchedTransactionWhereInput!]
  NOT: [GUni_BatchedTransactionWhereInput!]
  id: IDFilter
  status: StringFilter
  batch_id: StringFilter
  transaction_id: StringFilter
  merchant_id: StringFilter
  group_id: StringFilter
  amount: IntNullableFilter
  created_at: DateTimeNullableFilter
}

input GUni_BatchedTransactionOrderByInput {
  id: OrderDirection
  status: OrderDirection
  batch_id: OrderDirection
  transaction_id: OrderDirection
  merchant_id: OrderDirection
  group_id: OrderDirection
  amount: OrderDirection
  created_at: OrderDirection
}

input GUni_BatchedTransactionUpdateInput {
  status: String
  batch_id: String
  transaction_id: String
  merchant_id: String
  group_id: String
  amount: Int
  created_at: DateTime
}

input GUni_BatchedTransactionUpdateArgs {
  where: GUni_BatchedTransactionWhereUniqueInput!
  data: GUni_BatchedTransactionUpdateInput!
}

input GUni_BatchedTransactionCreateInput {
  status: String
  batch_id: String
  transaction_id: String
  merchant_id: String
  group_id: String
  amount: Int
  created_at: DateTime
}

type GUni_Customer {
  id: ID!
  nameOnCard: String
  email: String
  phone: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  paymentCards(
    where: GUni_Customer_PaymentCardWhereInput! = {}
    orderBy: [GUni_Customer_PaymentCardOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Customer_PaymentCardWhereUniqueInput
  ): [GUni_Customer_PaymentCard!]
  paymentCardsCount(where: GUni_Customer_PaymentCardWhereInput! = {}): Int
  createdAt: DateTime
  paymentPlans(
    where: GUni_PaymentPlanWhereInput! = {}
    orderBy: [GUni_PaymentPlanOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_PaymentPlanWhereUniqueInput
  ): [GUni_PaymentPlan!]
  paymentPlansCount(where: GUni_PaymentPlanWhereInput! = {}): Int
  orderData(
    where: GUni_Transaction_OrderDataWhereInput! = {}
    orderBy: [GUni_Transaction_OrderDataOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Transaction_OrderDataWhereUniqueInput
  ): [GUni_Transaction_OrderData!]
  orderDataCount(where: GUni_Transaction_OrderDataWhereInput! = {}): Int
  groupID: String
}

input GUni_CustomerWhereUniqueInput {
  id: ID
}

input GUni_CustomerWhereInput {
  AND: [GUni_CustomerWhereInput!]
  OR: [GUni_CustomerWhereInput!]
  NOT: [GUni_CustomerWhereInput!]
  id: IDFilter
  nameOnCard: StringFilter
  email: StringFilter
  phone: StringFilter
  billingAddress: StringFilter
  billingCity: StringFilter
  billingState: StringFilter
  billingZip: StringFilter
  billingCountry: StringFilter
  paymentCards: GUni_Customer_PaymentCardManyRelationFilter
  createdAt: DateTimeNullableFilter
  paymentPlans: GUni_PaymentPlanManyRelationFilter
  orderData: GUni_Transaction_OrderDataManyRelationFilter
  groupID: StringFilter
}

input GUni_Customer_PaymentCardManyRelationFilter {
  every: GUni_Customer_PaymentCardWhereInput
  some: GUni_Customer_PaymentCardWhereInput
  none: GUni_Customer_PaymentCardWhereInput
}

input GUni_PaymentPlanManyRelationFilter {
  every: GUni_PaymentPlanWhereInput
  some: GUni_PaymentPlanWhereInput
  none: GUni_PaymentPlanWhereInput
}

input GUni_Transaction_OrderDataManyRelationFilter {
  every: GUni_Transaction_OrderDataWhereInput
  some: GUni_Transaction_OrderDataWhereInput
  none: GUni_Transaction_OrderDataWhereInput
}

input GUni_CustomerOrderByInput {
  id: OrderDirection
  nameOnCard: OrderDirection
  email: OrderDirection
  phone: OrderDirection
  billingAddress: OrderDirection
  billingCity: OrderDirection
  billingState: OrderDirection
  billingZip: OrderDirection
  billingCountry: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
}

input GUni_CustomerUpdateInput {
  nameOnCard: String
  email: String
  phone: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  paymentCards: GUni_Customer_PaymentCardRelateToManyForUpdateInput
  createdAt: DateTime
  paymentPlans: GUni_PaymentPlanRelateToManyForUpdateInput
  orderData: GUni_Transaction_OrderDataRelateToManyForUpdateInput
  groupID: String
}

input GUni_Customer_PaymentCardRelateToManyForUpdateInput {
  disconnect: [GUni_Customer_PaymentCardWhereUniqueInput!]
  set: [GUni_Customer_PaymentCardWhereUniqueInput!]
  create: [GUni_Customer_PaymentCardCreateInput!]
  connect: [GUni_Customer_PaymentCardWhereUniqueInput!]
}

input GUni_PaymentPlanRelateToManyForUpdateInput {
  disconnect: [GUni_PaymentPlanWhereUniqueInput!]
  set: [GUni_PaymentPlanWhereUniqueInput!]
  create: [GUni_PaymentPlanCreateInput!]
  connect: [GUni_PaymentPlanWhereUniqueInput!]
}

input GUni_Transaction_OrderDataRelateToManyForUpdateInput {
  disconnect: [GUni_Transaction_OrderDataWhereUniqueInput!]
  set: [GUni_Transaction_OrderDataWhereUniqueInput!]
  create: [GUni_Transaction_OrderDataCreateInput!]
  connect: [GUni_Transaction_OrderDataWhereUniqueInput!]
}

input GUni_CustomerUpdateArgs {
  where: GUni_CustomerWhereUniqueInput!
  data: GUni_CustomerUpdateInput!
}

input GUni_CustomerCreateInput {
  nameOnCard: String
  email: String
  phone: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  paymentCards: GUni_Customer_PaymentCardRelateToManyForCreateInput
  createdAt: DateTime
  paymentPlans: GUni_PaymentPlanRelateToManyForCreateInput
  orderData: GUni_Transaction_OrderDataRelateToManyForCreateInput
  groupID: String
}

input GUni_Customer_PaymentCardRelateToManyForCreateInput {
  create: [GUni_Customer_PaymentCardCreateInput!]
  connect: [GUni_Customer_PaymentCardWhereUniqueInput!]
}

input GUni_PaymentPlanRelateToManyForCreateInput {
  create: [GUni_PaymentPlanCreateInput!]
  connect: [GUni_PaymentPlanWhereUniqueInput!]
}

input GUni_Transaction_OrderDataRelateToManyForCreateInput {
  create: [GUni_Transaction_OrderDataCreateInput!]
  connect: [GUni_Transaction_OrderDataWhereUniqueInput!]
}

type GUni_Customer_PaymentCard {
  id: ID!
  customer: GUni_Customer
  type: String
  isDefault: Boolean
  cardToken: String
  address: String
  zipCode: String
  last4: String
  brand: String
  expires: String
  cvc: String
  transactionIdentifier: String
  routingNumber: String
  bankName: String
  accountNumber: String
  accountType: String
  accountHolderType: String
  gpEcommID: String
  paymentPlans(
    where: GUni_PaymentPlanWhereInput! = {}
    orderBy: [GUni_PaymentPlanOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_PaymentPlanWhereUniqueInput
  ): [GUni_PaymentPlan!]
  paymentPlansCount(where: GUni_PaymentPlanWhereInput! = {}): Int
  createdAt: DateTime
  groupID: String
}

input GUni_Customer_PaymentCardWhereUniqueInput {
  id: ID
}

input GUni_Customer_PaymentCardWhereInput {
  AND: [GUni_Customer_PaymentCardWhereInput!]
  OR: [GUni_Customer_PaymentCardWhereInput!]
  NOT: [GUni_Customer_PaymentCardWhereInput!]
  id: IDFilter
  customer: GUni_CustomerWhereInput
  type: StringFilter
  isDefault: BooleanFilter
  cardToken: StringFilter
  address: StringFilter
  zipCode: StringFilter
  last4: StringFilter
  brand: StringFilter
  expires: StringFilter
  cvc: StringFilter
  transactionIdentifier: StringFilter
  routingNumber: StringFilter
  bankName: StringFilter
  accountNumber: StringFilter
  accountType: StringFilter
  accountHolderType: StringFilter
  gpEcommID: StringFilter
  paymentPlans: GUni_PaymentPlanManyRelationFilter
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
}

input GUni_Customer_PaymentCardOrderByInput {
  id: OrderDirection
  type: OrderDirection
  isDefault: OrderDirection
  cardToken: OrderDirection
  address: OrderDirection
  zipCode: OrderDirection
  last4: OrderDirection
  brand: OrderDirection
  expires: OrderDirection
  cvc: OrderDirection
  transactionIdentifier: OrderDirection
  routingNumber: OrderDirection
  bankName: OrderDirection
  accountNumber: OrderDirection
  accountType: OrderDirection
  accountHolderType: OrderDirection
  gpEcommID: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
}

input GUni_Customer_PaymentCardUpdateInput {
  customer: GUni_CustomerRelateToOneForUpdateInput
  type: String
  isDefault: Boolean
  cardToken: String
  address: String
  zipCode: String
  last4: String
  brand: String
  expires: String
  cvc: String
  transactionIdentifier: String
  routingNumber: String
  bankName: String
  accountNumber: String
  accountType: String
  accountHolderType: String
  gpEcommID: String
  paymentPlans: GUni_PaymentPlanRelateToManyForUpdateInput
  createdAt: DateTime
  groupID: String
}

input GUni_Customer_PaymentCardUpdateArgs {
  where: GUni_Customer_PaymentCardWhereUniqueInput!
  data: GUni_Customer_PaymentCardUpdateInput!
}

input GUni_Customer_PaymentCardCreateInput {
  customer: GUni_CustomerRelateToOneForCreateInput
  type: String
  isDefault: Boolean
  cardToken: String
  address: String
  zipCode: String
  last4: String
  brand: String
  expires: String
  cvc: String
  transactionIdentifier: String
  routingNumber: String
  bankName: String
  accountNumber: String
  accountType: String
  accountHolderType: String
  gpEcommID: String
  paymentPlans: GUni_PaymentPlanRelateToManyForCreateInput
  createdAt: DateTime
  groupID: String
}

type GUni_Category {
  id: ID!
  name: String
  status: String
  color: String
  description: String
  subCategory: JSON
  colors: JSON
  createdAt: DateTime
  groupID: String
}

input GUni_CategoryWhereUniqueInput {
  id: ID
}

input GUni_CategoryWhereInput {
  AND: [GUni_CategoryWhereInput!]
  OR: [GUni_CategoryWhereInput!]
  NOT: [GUni_CategoryWhereInput!]
  id: IDFilter
  name: StringFilter
  status: StringFilter
  color: StringFilter
  description: StringFilter
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
}

input GUni_CategoryOrderByInput {
  id: OrderDirection
  name: OrderDirection
  status: OrderDirection
  color: OrderDirection
  description: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
}

input GUni_CategoryUpdateInput {
  name: String
  status: String
  color: String
  description: String
  subCategory: JSON
  colors: JSON
  createdAt: DateTime
  groupID: String
}

input GUni_CategoryUpdateArgs {
  where: GUni_CategoryWhereUniqueInput!
  data: GUni_CategoryUpdateInput!
}

input GUni_CategoryCreateInput {
  name: String
  status: String
  color: String
  description: String
  subCategory: JSON
  colors: JSON
  createdAt: DateTime
  groupID: String
}

type GUni_PaymentPlan {
  id: ID!
  planName: String
  amount: Int
  setupFee: Int
  startDate: DateTime
  interval: Int
  frequency: Int
  endDate: DateTime
  creator: String
  status: String
  customer: GUni_Customer
  paymentCard: GUni_Customer_PaymentCard
  lineItems: JSON
  createdAt: DateTime
  groupID: String
  payments(
    where: GUni_PaymentPlan_PaymentWhereInput! = {}
    orderBy: [GUni_PaymentPlan_PaymentOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_PaymentPlan_PaymentWhereUniqueInput
  ): [GUni_PaymentPlan_Payment!]
  paymentsCount(where: GUni_PaymentPlan_PaymentWhereInput! = {}): Int
  skipChargeUntil: DateTime
  paymentSeriesNumber: Int
}

input GUni_PaymentPlanWhereUniqueInput {
  id: ID
}

input GUni_PaymentPlanWhereInput {
  AND: [GUni_PaymentPlanWhereInput!]
  OR: [GUni_PaymentPlanWhereInput!]
  NOT: [GUni_PaymentPlanWhereInput!]
  id: IDFilter
  planName: StringFilter
  amount: IntNullableFilter
  setupFee: IntNullableFilter
  startDate: DateTimeNullableFilter
  interval: IntNullableFilter
  frequency: IntNullableFilter
  endDate: DateTimeNullableFilter
  creator: StringFilter
  status: StringFilter
  customer: GUni_CustomerWhereInput
  paymentCard: GUni_Customer_PaymentCardWhereInput
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
  payments: GUni_PaymentPlan_PaymentManyRelationFilter
  skipChargeUntil: DateTimeNullableFilter
  paymentSeriesNumber: IntNullableFilter
}

input GUni_PaymentPlan_PaymentManyRelationFilter {
  every: GUni_PaymentPlan_PaymentWhereInput
  some: GUni_PaymentPlan_PaymentWhereInput
  none: GUni_PaymentPlan_PaymentWhereInput
}

input GUni_PaymentPlanOrderByInput {
  id: OrderDirection
  planName: OrderDirection
  amount: OrderDirection
  setupFee: OrderDirection
  startDate: OrderDirection
  interval: OrderDirection
  frequency: OrderDirection
  endDate: OrderDirection
  creator: OrderDirection
  status: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
  skipChargeUntil: OrderDirection
  paymentSeriesNumber: OrderDirection
}

input GUni_PaymentPlanUpdateInput {
  planName: String
  amount: Int
  setupFee: Int
  startDate: DateTime
  interval: Int
  frequency: Int
  endDate: DateTime
  creator: String
  status: String
  customer: GUni_CustomerRelateToOneForUpdateInput
  paymentCard: GUni_Customer_PaymentCardRelateToOneForUpdateInput
  lineItems: JSON
  createdAt: DateTime
  groupID: String
  payments: GUni_PaymentPlan_PaymentRelateToManyForUpdateInput
  skipChargeUntil: DateTime
  paymentSeriesNumber: Int
}

input GUni_Customer_PaymentCardRelateToOneForUpdateInput {
  create: GUni_Customer_PaymentCardCreateInput
  connect: GUni_Customer_PaymentCardWhereUniqueInput
  disconnect: Boolean
}

input GUni_PaymentPlan_PaymentRelateToManyForUpdateInput {
  disconnect: [GUni_PaymentPlan_PaymentWhereUniqueInput!]
  set: [GUni_PaymentPlan_PaymentWhereUniqueInput!]
  create: [GUni_PaymentPlan_PaymentCreateInput!]
  connect: [GUni_PaymentPlan_PaymentWhereUniqueInput!]
}

input GUni_PaymentPlanUpdateArgs {
  where: GUni_PaymentPlanWhereUniqueInput!
  data: GUni_PaymentPlanUpdateInput!
}

input GUni_PaymentPlanCreateInput {
  planName: String
  amount: Int
  setupFee: Int
  startDate: DateTime
  interval: Int
  frequency: Int
  endDate: DateTime
  creator: String
  status: String
  customer: GUni_CustomerRelateToOneForCreateInput
  paymentCard: GUni_Customer_PaymentCardRelateToOneForCreateInput
  lineItems: JSON
  createdAt: DateTime
  groupID: String
  payments: GUni_PaymentPlan_PaymentRelateToManyForCreateInput
  skipChargeUntil: DateTime
  paymentSeriesNumber: Int
}

input GUni_Customer_PaymentCardRelateToOneForCreateInput {
  create: GUni_Customer_PaymentCardCreateInput
  connect: GUni_Customer_PaymentCardWhereUniqueInput
}

input GUni_PaymentPlan_PaymentRelateToManyForCreateInput {
  create: [GUni_PaymentPlan_PaymentCreateInput!]
  connect: [GUni_PaymentPlan_PaymentWhereUniqueInput!]
}

type GUni_PaymentPlan_Payment {
  id: ID!
  paymentPlan: GUni_PaymentPlan
  paymentPlanCycleIndexID: String
  transactionID: String
  gateway: String
  processor: String
  payload: JSON
  amount: Int
  status: String
  createdAt: DateTime
  groupID: String
}

input GUni_PaymentPlan_PaymentWhereUniqueInput {
  id: ID
}

input GUni_PaymentPlan_PaymentWhereInput {
  AND: [GUni_PaymentPlan_PaymentWhereInput!]
  OR: [GUni_PaymentPlan_PaymentWhereInput!]
  NOT: [GUni_PaymentPlan_PaymentWhereInput!]
  id: IDFilter
  paymentPlan: GUni_PaymentPlanWhereInput
  paymentPlanCycleIndexID: StringFilter
  transactionID: StringFilter
  gateway: StringFilter
  processor: StringFilter
  amount: IntNullableFilter
  status: StringFilter
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
}

input GUni_PaymentPlan_PaymentOrderByInput {
  id: OrderDirection
  paymentPlanCycleIndexID: OrderDirection
  transactionID: OrderDirection
  gateway: OrderDirection
  processor: OrderDirection
  amount: OrderDirection
  status: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
}

input GUni_PaymentPlan_PaymentUpdateInput {
  paymentPlan: GUni_PaymentPlanRelateToOneForUpdateInput
  paymentPlanCycleIndexID: String
  transactionID: String
  gateway: String
  processor: String
  payload: JSON
  amount: Int
  status: String
  createdAt: DateTime
  groupID: String
}

input GUni_PaymentPlanRelateToOneForUpdateInput {
  create: GUni_PaymentPlanCreateInput
  connect: GUni_PaymentPlanWhereUniqueInput
  disconnect: Boolean
}

input GUni_PaymentPlan_PaymentUpdateArgs {
  where: GUni_PaymentPlan_PaymentWhereUniqueInput!
  data: GUni_PaymentPlan_PaymentUpdateInput!
}

input GUni_PaymentPlan_PaymentCreateInput {
  paymentPlan: GUni_PaymentPlanRelateToOneForCreateInput
  paymentPlanCycleIndexID: String
  transactionID: String
  gateway: String
  processor: String
  payload: JSON
  amount: Int
  status: String
  createdAt: DateTime
  groupID: String
}

input GUni_PaymentPlanRelateToOneForCreateInput {
  create: GUni_PaymentPlanCreateInput
  connect: GUni_PaymentPlanWhereUniqueInput
}

type GUni_Product {
  id: ID!
  name: String
  price: Int
  discount: Int
  productStatus: String
  taxExempt: Boolean
  kitchenItem: String
  sku: String
  category: String
  subCategory: String
  brand: String
  itemWeight: String
  length: String
  breadth: String
  width: String
  description: String
  isInStore: Boolean
  isOnline: Boolean
  productImages: JSON
  createdAt: DateTime
  groupID: String
  referenceID: String
  referenceSource: String
  isRecurring: Boolean
  recurringInterval: Int
  recurringFrequency: Int
  recurringTotalCycles: Int
  recurringTrialDays: Int
  recurringSetupFee: Int
}

input GUni_ProductWhereUniqueInput {
  id: ID
}

input GUni_ProductWhereInput {
  AND: [GUni_ProductWhereInput!]
  OR: [GUni_ProductWhereInput!]
  NOT: [GUni_ProductWhereInput!]
  id: IDFilter
  name: StringFilter
  price: IntNullableFilter
  discount: IntNullableFilter
  productStatus: StringFilter
  taxExempt: BooleanFilter
  kitchenItem: StringFilter
  sku: StringFilter
  category: StringFilter
  subCategory: StringFilter
  brand: StringFilter
  itemWeight: StringFilter
  length: StringFilter
  breadth: StringFilter
  width: StringFilter
  description: StringFilter
  isInStore: BooleanFilter
  isOnline: BooleanFilter
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
  referenceID: StringFilter
  referenceSource: StringFilter
  isRecurring: BooleanFilter
  recurringInterval: IntNullableFilter
  recurringFrequency: IntNullableFilter
  recurringTotalCycles: IntNullableFilter
  recurringTrialDays: IntNullableFilter
  recurringSetupFee: IntNullableFilter
}

input GUni_ProductOrderByInput {
  id: OrderDirection
  name: OrderDirection
  price: OrderDirection
  discount: OrderDirection
  productStatus: OrderDirection
  taxExempt: OrderDirection
  kitchenItem: OrderDirection
  sku: OrderDirection
  category: OrderDirection
  subCategory: OrderDirection
  brand: OrderDirection
  itemWeight: OrderDirection
  length: OrderDirection
  breadth: OrderDirection
  width: OrderDirection
  description: OrderDirection
  isInStore: OrderDirection
  isOnline: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
  referenceID: OrderDirection
  referenceSource: OrderDirection
  isRecurring: OrderDirection
  recurringInterval: OrderDirection
  recurringFrequency: OrderDirection
  recurringTotalCycles: OrderDirection
  recurringTrialDays: OrderDirection
  recurringSetupFee: OrderDirection
}

input GUni_ProductUpdateInput {
  name: String
  price: Int
  discount: Int
  productStatus: String
  taxExempt: Boolean
  kitchenItem: String
  sku: String
  category: String
  subCategory: String
  brand: String
  itemWeight: String
  length: String
  breadth: String
  width: String
  description: String
  isInStore: Boolean
  isOnline: Boolean
  productImages: JSON
  createdAt: DateTime
  groupID: String
  referenceID: String
  referenceSource: String
  isRecurring: Boolean
  recurringInterval: Int
  recurringFrequency: Int
  recurringTotalCycles: Int
  recurringTrialDays: Int
  recurringSetupFee: Int
}

input GUni_ProductUpdateArgs {
  where: GUni_ProductWhereUniqueInput!
  data: GUni_ProductUpdateInput!
}

input GUni_ProductCreateInput {
  name: String
  price: Int
  discount: Int
  productStatus: String
  taxExempt: Boolean
  kitchenItem: String
  sku: String
  category: String
  subCategory: String
  brand: String
  itemWeight: String
  length: String
  breadth: String
  width: String
  description: String
  isInStore: Boolean
  isOnline: Boolean
  productImages: JSON
  createdAt: DateTime
  groupID: String
  referenceID: String
  referenceSource: String
  isRecurring: Boolean
  recurringInterval: Int
  recurringFrequency: Int
  recurringTotalCycles: Int
  recurringTrialDays: Int
  recurringSetupFee: Int
}

type GUni_Discount {
  id: ID!
  name: String
  type: String
  discount: Int
  maxDiscount: Int
  validFrom: DateTime
  validTo: DateTime
  maxClaims: Int
  claims: Int
  validSKU: JSON
  status: String
  scope: String
  createdAt: DateTime
  groupID: String
}

input GUni_DiscountWhereUniqueInput {
  id: ID
}

input GUni_DiscountWhereInput {
  AND: [GUni_DiscountWhereInput!]
  OR: [GUni_DiscountWhereInput!]
  NOT: [GUni_DiscountWhereInput!]
  id: IDFilter
  name: StringFilter
  type: StringFilter
  discount: IntNullableFilter
  maxDiscount: IntNullableFilter
  validFrom: DateTimeNullableFilter
  validTo: DateTimeNullableFilter
  maxClaims: IntNullableFilter
  claims: IntNullableFilter
  status: StringFilter
  scope: StringFilter
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
}

input GUni_DiscountOrderByInput {
  id: OrderDirection
  name: OrderDirection
  type: OrderDirection
  discount: OrderDirection
  maxDiscount: OrderDirection
  validFrom: OrderDirection
  validTo: OrderDirection
  maxClaims: OrderDirection
  claims: OrderDirection
  status: OrderDirection
  scope: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
}

input GUni_DiscountUpdateInput {
  name: String
  type: String
  discount: Int
  maxDiscount: Int
  validFrom: DateTime
  validTo: DateTime
  maxClaims: Int
  claims: Int
  validSKU: JSON
  status: String
  scope: String
  createdAt: DateTime
  groupID: String
}

input GUni_DiscountUpdateArgs {
  where: GUni_DiscountWhereUniqueInput!
  data: GUni_DiscountUpdateInput!
}

input GUni_DiscountCreateInput {
  name: String
  type: String
  discount: Int
  maxDiscount: Int
  validFrom: DateTime
  validTo: DateTime
  maxClaims: Int
  claims: Int
  validSKU: JSON
  status: String
  scope: String
  createdAt: DateTime
  groupID: String
}

type GUni_PayLink {
  id: ID!
  paymentData: String
  createdAt: DateTime
  paymentRaw: JSON
  pricingCheck: JSON
  groupID: String
}

input GUni_PayLinkWhereUniqueInput {
  id: ID
}

input GUni_PayLinkWhereInput {
  AND: [GUni_PayLinkWhereInput!]
  OR: [GUni_PayLinkWhereInput!]
  NOT: [GUni_PayLinkWhereInput!]
  id: IDFilter
  paymentData: StringFilter
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
}

input GUni_PayLinkOrderByInput {
  id: OrderDirection
  paymentData: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
}

input GUni_PayLinkUpdateInput {
  paymentData: String
  createdAt: DateTime
  paymentRaw: JSON
  pricingCheck: JSON
  groupID: String
}

input GUni_PayLinkUpdateArgs {
  where: GUni_PayLinkWhereUniqueInput!
  data: GUni_PayLinkUpdateInput!
}

input GUni_PayLinkCreateInput {
  paymentData: String
  createdAt: DateTime
  paymentRaw: JSON
  pricingCheck: JSON
  groupID: String
}

type GUni_Dispute {
  id: ID!
  caseID: String
  locationID: String
  transactionID: String
  date: DateTime
  reason: String
  reasonCode: String
  accountNumber: String
  brand: String
  amount: Int
  transactionAmount: Int
  status: String
  merchantChargebackDate: DateTime
  merchantRepresentmentDate: DateTime
  representmentDate: DateTime
  representmentStatus: String
  result: String
  createdAt: DateTime
  groupID: String
  files(
    where: GUni_Dispute_FilesWhereInput! = {}
    orderBy: [GUni_Dispute_FilesOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Dispute_FilesWhereUniqueInput
  ): [GUni_Dispute_Files!]
  filesCount(where: GUni_Dispute_FilesWhereInput! = {}): Int
  history(
    where: GUni_Dispute_HistoryWhereInput! = {}
    orderBy: [GUni_Dispute_HistoryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Dispute_HistoryWhereUniqueInput
  ): [GUni_Dispute_History!]
  historyCount(where: GUni_Dispute_HistoryWhereInput! = {}): Int
}

input GUni_DisputeWhereUniqueInput {
  id: ID
  caseID: String
}

input GUni_DisputeWhereInput {
  AND: [GUni_DisputeWhereInput!]
  OR: [GUni_DisputeWhereInput!]
  NOT: [GUni_DisputeWhereInput!]
  id: IDFilter
  caseID: StringFilter
  locationID: StringFilter
  transactionID: StringFilter
  date: DateTimeNullableFilter
  reason: StringFilter
  reasonCode: StringFilter
  accountNumber: StringFilter
  brand: StringFilter
  amount: IntNullableFilter
  transactionAmount: IntNullableFilter
  status: StringFilter
  merchantChargebackDate: DateTimeNullableFilter
  merchantRepresentmentDate: DateTimeNullableFilter
  representmentDate: DateTimeNullableFilter
  representmentStatus: StringFilter
  result: StringFilter
  createdAt: DateTimeNullableFilter
  groupID: StringFilter
  files: GUni_Dispute_FilesManyRelationFilter
  history: GUni_Dispute_HistoryManyRelationFilter
}

input GUni_Dispute_FilesManyRelationFilter {
  every: GUni_Dispute_FilesWhereInput
  some: GUni_Dispute_FilesWhereInput
  none: GUni_Dispute_FilesWhereInput
}

input GUni_Dispute_HistoryManyRelationFilter {
  every: GUni_Dispute_HistoryWhereInput
  some: GUni_Dispute_HistoryWhereInput
  none: GUni_Dispute_HistoryWhereInput
}

input GUni_DisputeOrderByInput {
  id: OrderDirection
  caseID: OrderDirection
  locationID: OrderDirection
  transactionID: OrderDirection
  date: OrderDirection
  reason: OrderDirection
  reasonCode: OrderDirection
  accountNumber: OrderDirection
  brand: OrderDirection
  amount: OrderDirection
  transactionAmount: OrderDirection
  status: OrderDirection
  merchantChargebackDate: OrderDirection
  merchantRepresentmentDate: OrderDirection
  representmentDate: OrderDirection
  representmentStatus: OrderDirection
  result: OrderDirection
  createdAt: OrderDirection
  groupID: OrderDirection
}

input GUni_DisputeUpdateInput {
  caseID: String
  locationID: String
  transactionID: String
  date: DateTime
  reason: String
  reasonCode: String
  accountNumber: String
  brand: String
  amount: Int
  transactionAmount: Int
  status: String
  merchantChargebackDate: DateTime
  merchantRepresentmentDate: DateTime
  representmentDate: DateTime
  representmentStatus: String
  result: String
  createdAt: DateTime
  groupID: String
  files: GUni_Dispute_FilesRelateToManyForUpdateInput
  history: GUni_Dispute_HistoryRelateToManyForUpdateInput
}

input GUni_Dispute_FilesRelateToManyForUpdateInput {
  disconnect: [GUni_Dispute_FilesWhereUniqueInput!]
  set: [GUni_Dispute_FilesWhereUniqueInput!]
  create: [GUni_Dispute_FilesCreateInput!]
  connect: [GUni_Dispute_FilesWhereUniqueInput!]
}

input GUni_Dispute_HistoryRelateToManyForUpdateInput {
  disconnect: [GUni_Dispute_HistoryWhereUniqueInput!]
  set: [GUni_Dispute_HistoryWhereUniqueInput!]
  create: [GUni_Dispute_HistoryCreateInput!]
  connect: [GUni_Dispute_HistoryWhereUniqueInput!]
}

input GUni_DisputeUpdateArgs {
  where: GUni_DisputeWhereUniqueInput!
  data: GUni_DisputeUpdateInput!
}

input GUni_DisputeCreateInput {
  caseID: String
  locationID: String
  transactionID: String
  date: DateTime
  reason: String
  reasonCode: String
  accountNumber: String
  brand: String
  amount: Int
  transactionAmount: Int
  status: String
  merchantChargebackDate: DateTime
  merchantRepresentmentDate: DateTime
  representmentDate: DateTime
  representmentStatus: String
  result: String
  createdAt: DateTime
  groupID: String
  files: GUni_Dispute_FilesRelateToManyForCreateInput
  history: GUni_Dispute_HistoryRelateToManyForCreateInput
}

input GUni_Dispute_FilesRelateToManyForCreateInput {
  create: [GUni_Dispute_FilesCreateInput!]
  connect: [GUni_Dispute_FilesWhereUniqueInput!]
}

input GUni_Dispute_HistoryRelateToManyForCreateInput {
  create: [GUni_Dispute_HistoryCreateInput!]
  connect: [GUni_Dispute_HistoryWhereUniqueInput!]
}

type GUni_Dispute_Files {
  id: ID!
  dispute: GUni_Dispute
  fileUrl: String
  purpose: String
  fileFormat: String
  sumittedAt: DateTime
  createdAt: DateTime
}

input GUni_Dispute_FilesWhereUniqueInput {
  id: ID
}

input GUni_Dispute_FilesWhereInput {
  AND: [GUni_Dispute_FilesWhereInput!]
  OR: [GUni_Dispute_FilesWhereInput!]
  NOT: [GUni_Dispute_FilesWhereInput!]
  id: IDFilter
  dispute: GUni_DisputeWhereInput
  fileUrl: StringFilter
  purpose: StringFilter
  fileFormat: StringFilter
  sumittedAt: DateTimeNullableFilter
  createdAt: DateTimeNullableFilter
}

input GUni_Dispute_FilesOrderByInput {
  id: OrderDirection
  fileUrl: OrderDirection
  purpose: OrderDirection
  fileFormat: OrderDirection
  sumittedAt: OrderDirection
  createdAt: OrderDirection
}

input GUni_Dispute_FilesUpdateInput {
  dispute: GUni_DisputeRelateToOneForUpdateInput
  fileUrl: String
  purpose: String
  fileFormat: String
  sumittedAt: DateTime
  createdAt: DateTime
}

input GUni_DisputeRelateToOneForUpdateInput {
  create: GUni_DisputeCreateInput
  connect: GUni_DisputeWhereUniqueInput
  disconnect: Boolean
}

input GUni_Dispute_FilesUpdateArgs {
  where: GUni_Dispute_FilesWhereUniqueInput!
  data: GUni_Dispute_FilesUpdateInput!
}

input GUni_Dispute_FilesCreateInput {
  dispute: GUni_DisputeRelateToOneForCreateInput
  fileUrl: String
  purpose: String
  fileFormat: String
  sumittedAt: DateTime
  createdAt: DateTime
}

input GUni_DisputeRelateToOneForCreateInput {
  create: GUni_DisputeCreateInput
  connect: GUni_DisputeWhereUniqueInput
}

type GUni_Dispute_History {
  id: ID!
  dispute: GUni_Dispute
  action: String
  body: String
  actor: String
  createdAt: DateTime
}

input GUni_Dispute_HistoryWhereUniqueInput {
  id: ID
}

input GUni_Dispute_HistoryWhereInput {
  AND: [GUni_Dispute_HistoryWhereInput!]
  OR: [GUni_Dispute_HistoryWhereInput!]
  NOT: [GUni_Dispute_HistoryWhereInput!]
  id: IDFilter
  dispute: GUni_DisputeWhereInput
  action: StringFilter
  body: StringFilter
  actor: StringFilter
  createdAt: DateTimeNullableFilter
}

input GUni_Dispute_HistoryOrderByInput {
  id: OrderDirection
  action: OrderDirection
  body: OrderDirection
  actor: OrderDirection
  createdAt: OrderDirection
}

input GUni_Dispute_HistoryUpdateInput {
  dispute: GUni_DisputeRelateToOneForUpdateInput
  action: String
  body: String
  actor: String
  createdAt: DateTime
}

input GUni_Dispute_HistoryUpdateArgs {
  where: GUni_Dispute_HistoryWhereUniqueInput!
  data: GUni_Dispute_HistoryUpdateInput!
}

input GUni_Dispute_HistoryCreateInput {
  dispute: GUni_DisputeRelateToOneForCreateInput
  action: String
  body: String
  actor: String
  createdAt: DateTime
}

type DashboardHourlyStatistic {
  id: ID!
  ownerID: String
  pointID: String
  type: String
  value: Float
  createdAt: DateTime
  updatedAt: DateTime
}

input DashboardHourlyStatisticWhereUniqueInput {
  id: ID
}

input DashboardHourlyStatisticWhereInput {
  AND: [DashboardHourlyStatisticWhereInput!]
  OR: [DashboardHourlyStatisticWhereInput!]
  NOT: [DashboardHourlyStatisticWhereInput!]
  id: IDFilter
  ownerID: StringFilter
  pointID: StringFilter
  type: StringFilter
  value: FloatNullableFilter
  createdAt: DateTimeNullableFilter
  updatedAt: DateTimeNullableFilter
}

input DashboardHourlyStatisticOrderByInput {
  id: OrderDirection
  ownerID: OrderDirection
  pointID: OrderDirection
  type: OrderDirection
  value: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input DashboardHourlyStatisticUpdateInput {
  ownerID: String
  pointID: String
  type: String
  value: Float
  createdAt: DateTime
  updatedAt: DateTime
}

input DashboardHourlyStatisticUpdateArgs {
  where: DashboardHourlyStatisticWhereUniqueInput!
  data: DashboardHourlyStatisticUpdateInput!
}

input DashboardHourlyStatisticCreateInput {
  ownerID: String
  pointID: String
  type: String
  value: Float
  createdAt: DateTime
  updatedAt: DateTime
}

type OTP {
  id: ID!
  contact: String
  channel: String
  sid: String
  verified: Boolean
  metadata: String
}

input OTPWhereUniqueInput {
  id: ID
}

input OTPWhereInput {
  AND: [OTPWhereInput!]
  OR: [OTPWhereInput!]
  NOT: [OTPWhereInput!]
  id: IDFilter
  contact: StringFilter
  channel: StringFilter
  sid: StringFilter
  verified: BooleanFilter
  metadata: StringFilter
}

input OTPOrderByInput {
  id: OrderDirection
  contact: OrderDirection
  channel: OrderDirection
  sid: OrderDirection
  verified: OrderDirection
  metadata: OrderDirection
}

input OTPUpdateInput {
  contact: String
  channel: String
  sid: String
  verified: Boolean
  metadata: String
}

input OTPUpdateArgs {
  where: OTPWhereUniqueInput!
  data: OTPUpdateInput!
}

input OTPCreateInput {
  contact: String
  channel: String
  sid: String
  verified: Boolean
  metadata: String
}

type MFA {
  id: ID!
  userid: String
  browserid: String
  contact: String
  channel: String
  sid: String
  verified: Boolean
  metadata: String
  isOneTime: Boolean
  isUsed: Boolean
  expiresAt: DateTime
}

input MFAWhereUniqueInput {
  id: ID
}

input MFAWhereInput {
  AND: [MFAWhereInput!]
  OR: [MFAWhereInput!]
  NOT: [MFAWhereInput!]
  id: IDFilter
  userid: StringFilter
  browserid: StringFilter
  contact: StringFilter
  channel: StringFilter
  sid: StringFilter
  verified: BooleanFilter
  metadata: StringFilter
  isOneTime: BooleanFilter
  isUsed: BooleanFilter
  expiresAt: DateTimeNullableFilter
}

input MFAOrderByInput {
  id: OrderDirection
  userid: OrderDirection
  browserid: OrderDirection
  contact: OrderDirection
  channel: OrderDirection
  sid: OrderDirection
  verified: OrderDirection
  metadata: OrderDirection
  isOneTime: OrderDirection
  isUsed: OrderDirection
  expiresAt: OrderDirection
}

input MFAUpdateInput {
  userid: String
  browserid: String
  contact: String
  channel: String
  sid: String
  verified: Boolean
  metadata: String
  isOneTime: Boolean
  isUsed: Boolean
  expiresAt: DateTime
}

input MFAUpdateArgs {
  where: MFAWhereUniqueInput!
  data: MFAUpdateInput!
}

input MFACreateInput {
  userid: String
  browserid: String
  contact: String
  channel: String
  sid: String
  verified: Boolean
  metadata: String
  isOneTime: Boolean
  isUsed: Boolean
  expiresAt: DateTime
}

type Notification {
  id: ID!
  user: User
  content: String
  createdAt: DateTime
  topics: String
  read: Boolean
  metadata: JSON
}

input NotificationWhereUniqueInput {
  id: ID
}

input NotificationWhereInput {
  AND: [NotificationWhereInput!]
  OR: [NotificationWhereInput!]
  NOT: [NotificationWhereInput!]
  id: IDFilter
  user: UserWhereInput
  content: StringFilter
  createdAt: DateTimeNullableFilter
  topics: StringFilter
  read: BooleanFilter
}

input NotificationOrderByInput {
  id: OrderDirection
  content: OrderDirection
  createdAt: OrderDirection
  topics: OrderDirection
  read: OrderDirection
}

input NotificationUpdateInput {
  user: UserRelateToOneForUpdateInput
  content: String
  createdAt: DateTime
  topics: String
  read: Boolean
  metadata: JSON
}

input NotificationUpdateArgs {
  where: NotificationWhereUniqueInput!
  data: NotificationUpdateInput!
}

input NotificationCreateInput {
  user: UserRelateToOneForCreateInput
  content: String
  createdAt: DateTime
  topics: String
  read: Boolean
  metadata: JSON
}

type AffiliationProfile {
  id: ID!
  user: User
  affiliates(
    where: UserWhereInput! = {}
    orderBy: [UserOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: UserWhereUniqueInput
  ): [User!]
  affiliatesCount(where: UserWhereInput! = {}): Int
  groupAffiliates(
    where: GroupWhereInput! = {}
    orderBy: [GroupOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupWhereUniqueInput
  ): [Group!]
  groupAffiliatesCount(where: GroupWhereInput! = {}): Int
  codes(
    where: AffiliationProfileKeyCodeWhereInput! = {}
    orderBy: [AffiliationProfileKeyCodeOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: AffiliationProfileKeyCodeWhereUniqueInput
  ): [AffiliationProfileKeyCode!]
  codesCount(where: AffiliationProfileKeyCodeWhereInput! = {}): Int
  earns(
    where: AffiliateEarnHistoryWhereInput! = {}
    orderBy: [AffiliateEarnHistoryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: AffiliateEarnHistoryWhereUniqueInput
  ): [AffiliateEarnHistory!]
  earnsCount(where: AffiliateEarnHistoryWhereInput! = {}): Int
  bank_routingNumber: String
  bank_accountNumber: String
  bank_accountName: String
}

input AffiliationProfileWhereUniqueInput {
  id: ID
}

input AffiliationProfileWhereInput {
  AND: [AffiliationProfileWhereInput!]
  OR: [AffiliationProfileWhereInput!]
  NOT: [AffiliationProfileWhereInput!]
  id: IDFilter
  user: UserWhereInput
  affiliates: UserManyRelationFilter
  groupAffiliates: GroupManyRelationFilter
  codes: AffiliationProfileKeyCodeManyRelationFilter
  earns: AffiliateEarnHistoryManyRelationFilter
  bank_routingNumber: StringFilter
  bank_accountNumber: StringFilter
  bank_accountName: StringFilter
}

input UserManyRelationFilter {
  every: UserWhereInput
  some: UserWhereInput
  none: UserWhereInput
}

input AffiliationProfileKeyCodeManyRelationFilter {
  every: AffiliationProfileKeyCodeWhereInput
  some: AffiliationProfileKeyCodeWhereInput
  none: AffiliationProfileKeyCodeWhereInput
}

input AffiliateEarnHistoryManyRelationFilter {
  every: AffiliateEarnHistoryWhereInput
  some: AffiliateEarnHistoryWhereInput
  none: AffiliateEarnHistoryWhereInput
}

input AffiliationProfileOrderByInput {
  id: OrderDirection
  bank_routingNumber: OrderDirection
  bank_accountNumber: OrderDirection
  bank_accountName: OrderDirection
}

input AffiliationProfileUpdateInput {
  user: UserRelateToOneForUpdateInput
  affiliates: UserRelateToManyForUpdateInput
  groupAffiliates: GroupRelateToManyForUpdateInput
  codes: AffiliationProfileKeyCodeRelateToManyForUpdateInput
  earns: AffiliateEarnHistoryRelateToManyForUpdateInput
  bank_routingNumber: String
  bank_accountNumber: String
  bank_accountName: String
}

input UserRelateToManyForUpdateInput {
  disconnect: [UserWhereUniqueInput!]
  set: [UserWhereUniqueInput!]
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

input AffiliationProfileKeyCodeRelateToManyForUpdateInput {
  disconnect: [AffiliationProfileKeyCodeWhereUniqueInput!]
  set: [AffiliationProfileKeyCodeWhereUniqueInput!]
  create: [AffiliationProfileKeyCodeCreateInput!]
  connect: [AffiliationProfileKeyCodeWhereUniqueInput!]
}

input AffiliateEarnHistoryRelateToManyForUpdateInput {
  disconnect: [AffiliateEarnHistoryWhereUniqueInput!]
  set: [AffiliateEarnHistoryWhereUniqueInput!]
  create: [AffiliateEarnHistoryCreateInput!]
  connect: [AffiliateEarnHistoryWhereUniqueInput!]
}

input AffiliationProfileUpdateArgs {
  where: AffiliationProfileWhereUniqueInput!
  data: AffiliationProfileUpdateInput!
}

input AffiliationProfileCreateInput {
  user: UserRelateToOneForCreateInput
  affiliates: UserRelateToManyForCreateInput
  groupAffiliates: GroupRelateToManyForCreateInput
  codes: AffiliationProfileKeyCodeRelateToManyForCreateInput
  earns: AffiliateEarnHistoryRelateToManyForCreateInput
  bank_routingNumber: String
  bank_accountNumber: String
  bank_accountName: String
}

input UserRelateToManyForCreateInput {
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

input AffiliationProfileKeyCodeRelateToManyForCreateInput {
  create: [AffiliationProfileKeyCodeCreateInput!]
  connect: [AffiliationProfileKeyCodeWhereUniqueInput!]
}

input AffiliateEarnHistoryRelateToManyForCreateInput {
  create: [AffiliateEarnHistoryCreateInput!]
  connect: [AffiliateEarnHistoryWhereUniqueInput!]
}

type AffiliateEarnHistory {
  id: ID!
  affiliateProfile: AffiliationProfile
  amount: Float
  description: String
  invalid: Boolean
  pending: Boolean
  referenceID: String
  createdAt: DateTime
}

input AffiliateEarnHistoryWhereUniqueInput {
  id: ID
}

input AffiliateEarnHistoryWhereInput {
  AND: [AffiliateEarnHistoryWhereInput!]
  OR: [AffiliateEarnHistoryWhereInput!]
  NOT: [AffiliateEarnHistoryWhereInput!]
  id: IDFilter
  affiliateProfile: AffiliationProfileWhereInput
  amount: FloatNullableFilter
  description: StringFilter
  invalid: BooleanFilter
  pending: BooleanFilter
  referenceID: StringFilter
  createdAt: DateTimeNullableFilter
}

input AffiliateEarnHistoryOrderByInput {
  id: OrderDirection
  amount: OrderDirection
  description: OrderDirection
  invalid: OrderDirection
  pending: OrderDirection
  referenceID: OrderDirection
  createdAt: OrderDirection
}

input AffiliateEarnHistoryUpdateInput {
  affiliateProfile: AffiliationProfileRelateToOneForUpdateInput
  amount: Float
  description: String
  invalid: Boolean
  pending: Boolean
  referenceID: String
  createdAt: DateTime
}

input AffiliateEarnHistoryUpdateArgs {
  where: AffiliateEarnHistoryWhereUniqueInput!
  data: AffiliateEarnHistoryUpdateInput!
}

input AffiliateEarnHistoryCreateInput {
  affiliateProfile: AffiliationProfileRelateToOneForCreateInput
  amount: Float
  description: String
  invalid: Boolean
  pending: Boolean
  referenceID: String
  createdAt: DateTime
}

type AffiliationProfileKeyCode {
  id: ID!
  code: String
  description: String
  affiliateProfile: AffiliationProfile
}

input AffiliationProfileKeyCodeWhereUniqueInput {
  id: ID
  code: String
}

input AffiliationProfileKeyCodeWhereInput {
  AND: [AffiliationProfileKeyCodeWhereInput!]
  OR: [AffiliationProfileKeyCodeWhereInput!]
  NOT: [AffiliationProfileKeyCodeWhereInput!]
  id: IDFilter
  code: StringFilter
  description: StringFilter
  affiliateProfile: AffiliationProfileWhereInput
}

input AffiliationProfileKeyCodeOrderByInput {
  id: OrderDirection
  code: OrderDirection
  description: OrderDirection
}

input AffiliationProfileKeyCodeUpdateInput {
  code: String
  description: String
  affiliateProfile: AffiliationProfileRelateToOneForUpdateInput
}

input AffiliationProfileKeyCodeUpdateArgs {
  where: AffiliationProfileKeyCodeWhereUniqueInput!
  data: AffiliationProfileKeyCodeUpdateInput!
}

input AffiliationProfileKeyCodeCreateInput {
  code: String
  description: String
  affiliateProfile: AffiliationProfileRelateToOneForCreateInput
}

type GHLAccess {
  id: ID!
  group: Group
  ghsUserId: String
  planId: String
  locationId: String
  companyId: String
  view_payment_test_pk: String
  view_payment_test_ak: String
  view_payment_live_pk: String
  view_payment_live_ak: String
  updatedAt: DateTime
}

input GHLAccessWhereUniqueInput {
  id: ID
}

input GHLAccessWhereInput {
  AND: [GHLAccessWhereInput!]
  OR: [GHLAccessWhereInput!]
  NOT: [GHLAccessWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  ghsUserId: StringFilter
  planId: StringFilter
  locationId: StringFilter
  companyId: StringFilter
  updatedAt: DateTimeNullableFilter
}

input GHLAccessOrderByInput {
  id: OrderDirection
  ghsUserId: OrderDirection
  planId: OrderDirection
  locationId: OrderDirection
  companyId: OrderDirection
  updatedAt: OrderDirection
}

input GHLAccessUpdateInput {
  group: GroupRelateToOneForUpdateInput
  ghsUserId: String
  planId: String
  locationId: String
  companyId: String
  updatedAt: DateTime
}

input GHLAccessUpdateArgs {
  where: GHLAccessWhereUniqueInput!
  data: GHLAccessUpdateInput!
}

input GHLAccessCreateInput {
  group: GroupRelateToOneForCreateInput
  ghsUserId: String
  planId: String
  locationId: String
  companyId: String
  updatedAt: DateTime
}

type GHLSSOBinding {
  id: ID!
  localUser: User
  ghlUserID: String
  ghlUserEmail: String
  locationID: String
  locationName: String
  createdAt: DateTime
}

input GHLSSOBindingWhereUniqueInput {
  id: ID
}

input GHLSSOBindingWhereInput {
  AND: [GHLSSOBindingWhereInput!]
  OR: [GHLSSOBindingWhereInput!]
  NOT: [GHLSSOBindingWhereInput!]
  id: IDFilter
  localUser: UserWhereInput
  ghlUserID: StringFilter
  ghlUserEmail: StringFilter
  locationID: StringFilter
  locationName: StringFilter
  createdAt: DateTimeNullableFilter
}

input GHLSSOBindingOrderByInput {
  id: OrderDirection
  ghlUserID: OrderDirection
  ghlUserEmail: OrderDirection
  locationID: OrderDirection
  locationName: OrderDirection
  createdAt: OrderDirection
}

input GHLSSOBindingUpdateInput {
  localUser: UserRelateToOneForUpdateInput
  ghlUserID: String
  ghlUserEmail: String
  locationID: String
  locationName: String
  createdAt: DateTime
}

input GHLSSOBindingUpdateArgs {
  where: GHLSSOBindingWhereUniqueInput!
  data: GHLSSOBindingUpdateInput!
}

input GHLSSOBindingCreateInput {
  localUser: UserRelateToOneForCreateInput
  ghlUserID: String
  ghlUserEmail: String
  locationID: String
  locationName: String
  createdAt: DateTime
}

type GHLPayTransactionMap {
  id: ID!
  ghlAccess: GHLAccess
  ghlRefId: String
  ghlRefType: String
  gatewayRefId: String
  gatewayRefType: String
  gatewayProvider: String
  group: Group
  createdAt: DateTime
}

input GHLPayTransactionMapWhereUniqueInput {
  id: ID
}

input GHLPayTransactionMapWhereInput {
  AND: [GHLPayTransactionMapWhereInput!]
  OR: [GHLPayTransactionMapWhereInput!]
  NOT: [GHLPayTransactionMapWhereInput!]
  id: IDFilter
  ghlAccess: GHLAccessWhereInput
  ghlRefId: StringFilter
  ghlRefType: StringFilter
  gatewayRefId: StringFilter
  gatewayRefType: StringFilter
  gatewayProvider: StringFilter
  group: GroupWhereInput
  createdAt: DateTimeNullableFilter
}

input GHLPayTransactionMapOrderByInput {
  id: OrderDirection
  ghlRefId: OrderDirection
  ghlRefType: OrderDirection
  gatewayRefId: OrderDirection
  gatewayRefType: OrderDirection
  gatewayProvider: OrderDirection
  createdAt: OrderDirection
}

input GHLPayTransactionMapUpdateInput {
  ghlAccess: GHLAccessRelateToOneForUpdateInput
  ghlRefId: String
  ghlRefType: String
  gatewayRefId: String
  gatewayRefType: String
  gatewayProvider: String
  group: GroupRelateToOneForUpdateInput
  createdAt: DateTime
}

input GHLAccessRelateToOneForUpdateInput {
  create: GHLAccessCreateInput
  connect: GHLAccessWhereUniqueInput
  disconnect: Boolean
}

input GHLPayTransactionMapUpdateArgs {
  where: GHLPayTransactionMapWhereUniqueInput!
  data: GHLPayTransactionMapUpdateInput!
}

input GHLPayTransactionMapCreateInput {
  ghlAccess: GHLAccessRelateToOneForCreateInput
  ghlRefId: String
  ghlRefType: String
  gatewayRefId: String
  gatewayRefType: String
  gatewayProvider: String
  group: GroupRelateToOneForCreateInput
  createdAt: DateTime
}

input GHLAccessRelateToOneForCreateInput {
  create: GHLAccessCreateInput
  connect: GHLAccessWhereUniqueInput
}

type GHLCachedCode {
  id: ID!
  code: String
  locationId: String
  userId: String
  data: JSON
  createdAt: DateTime
}

input GHLCachedCodeWhereUniqueInput {
  id: ID
}

input GHLCachedCodeWhereInput {
  AND: [GHLCachedCodeWhereInput!]
  OR: [GHLCachedCodeWhereInput!]
  NOT: [GHLCachedCodeWhereInput!]
  id: IDFilter
  code: StringFilter
  locationId: StringFilter
  userId: StringFilter
  createdAt: DateTimeNullableFilter
}

input GHLCachedCodeOrderByInput {
  id: OrderDirection
  code: OrderDirection
  locationId: OrderDirection
  userId: OrderDirection
  createdAt: OrderDirection
}

input GHLCachedCodeUpdateInput {
  code: String
  locationId: String
  userId: String
  data: JSON
  createdAt: DateTime
}

input GHLCachedCodeUpdateArgs {
  where: GHLCachedCodeWhereUniqueInput!
  data: GHLCachedCodeUpdateInput!
}

input GHLCachedCodeCreateInput {
  code: String
  locationId: String
  userId: String
  data: JSON
  createdAt: DateTime
}

type GroupSupportTicket {
  id: ID!
  group: Group
  createdBy: User
  title: String
  category: String
  description: String
  status: String
  messages(
    where: GroupSupportMessageWhereInput! = {}
    orderBy: [GroupSupportMessageOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupSupportMessageWhereUniqueInput
  ): [GroupSupportMessage!]
  messagesCount(where: GroupSupportMessageWhereInput! = {}): Int
  createdAt: DateTime
  lastMessageCreatedAt: String
}

input GroupSupportTicketWhereUniqueInput {
  id: ID
}

input GroupSupportTicketWhereInput {
  AND: [GroupSupportTicketWhereInput!]
  OR: [GroupSupportTicketWhereInput!]
  NOT: [GroupSupportTicketWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  createdBy: UserWhereInput
  title: StringFilter
  category: StringNullableFilter
  description: StringFilter
  status: StringFilter
  messages: GroupSupportMessageManyRelationFilter
  createdAt: DateTimeNullableFilter
}

input StringNullableFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode
  not: StringNullableFilter
}

input GroupSupportMessageManyRelationFilter {
  every: GroupSupportMessageWhereInput
  some: GroupSupportMessageWhereInput
  none: GroupSupportMessageWhereInput
}

input GroupSupportTicketOrderByInput {
  id: OrderDirection
  title: OrderDirection
  category: OrderDirection
  description: OrderDirection
  status: OrderDirection
  createdAt: OrderDirection
}

input GroupSupportTicketUpdateInput {
  group: GroupRelateToOneForUpdateInput
  createdBy: UserRelateToOneForUpdateInput
  title: String
  category: String
  description: String
  status: String
  messages: GroupSupportMessageRelateToManyForUpdateInput
  createdAt: DateTime
}

input GroupSupportMessageRelateToManyForUpdateInput {
  disconnect: [GroupSupportMessageWhereUniqueInput!]
  set: [GroupSupportMessageWhereUniqueInput!]
  create: [GroupSupportMessageCreateInput!]
  connect: [GroupSupportMessageWhereUniqueInput!]
}

input GroupSupportTicketUpdateArgs {
  where: GroupSupportTicketWhereUniqueInput!
  data: GroupSupportTicketUpdateInput!
}

input GroupSupportTicketCreateInput {
  group: GroupRelateToOneForCreateInput
  createdBy: UserRelateToOneForCreateInput
  title: String
  category: String
  description: String
  status: String
  messages: GroupSupportMessageRelateToManyForCreateInput
  createdAt: DateTime
}

input GroupSupportMessageRelateToManyForCreateInput {
  create: [GroupSupportMessageCreateInput!]
  connect: [GroupSupportMessageWhereUniqueInput!]
}

type GroupSupportMessage {
  id: ID!
  ticket: GroupSupportTicket
  message: String
  files: JSON
  name: String
  createdAt: DateTime
  actualName: String
}

input GroupSupportMessageWhereUniqueInput {
  id: ID
}

input GroupSupportMessageWhereInput {
  AND: [GroupSupportMessageWhereInput!]
  OR: [GroupSupportMessageWhereInput!]
  NOT: [GroupSupportMessageWhereInput!]
  id: IDFilter
  ticket: GroupSupportTicketWhereInput
  message: StringFilter
  name: StringFilter
  createdAt: DateTimeNullableFilter
}

input GroupSupportMessageOrderByInput {
  id: OrderDirection
  message: OrderDirection
  name: OrderDirection
  createdAt: OrderDirection
}

input GroupSupportMessageUpdateInput {
  ticket: GroupSupportTicketRelateToOneForUpdateInput
  message: String
  files: JSON
  name: String
  createdAt: DateTime
}

input GroupSupportTicketRelateToOneForUpdateInput {
  create: GroupSupportTicketCreateInput
  connect: GroupSupportTicketWhereUniqueInput
  disconnect: Boolean
}

input GroupSupportMessageUpdateArgs {
  where: GroupSupportMessageWhereUniqueInput!
  data: GroupSupportMessageUpdateInput!
}

input GroupSupportMessageCreateInput {
  ticket: GroupSupportTicketRelateToOneForCreateInput
  message: String
  files: JSON
  name: String
  createdAt: DateTime
}

input GroupSupportTicketRelateToOneForCreateInput {
  create: GroupSupportTicketCreateInput
  connect: GroupSupportTicketWhereUniqueInput
}

type LeadEntry {
  id: ID!
  createdAt: DateTime
  firstName: String
  lastName: String
  email: String
  phoneNumber: String
  companyName: String
  companyRole: String
  mockAccountEmail: String
  notes: String
}

input LeadEntryWhereUniqueInput {
  id: ID
}

input LeadEntryWhereInput {
  AND: [LeadEntryWhereInput!]
  OR: [LeadEntryWhereInput!]
  NOT: [LeadEntryWhereInput!]
  id: IDFilter
  createdAt: DateTimeNullableFilter
  firstName: StringFilter
  lastName: StringFilter
  email: StringFilter
  phoneNumber: StringFilter
  companyName: StringFilter
  companyRole: StringFilter
  mockAccountEmail: StringFilter
  notes: StringFilter
}

input LeadEntryOrderByInput {
  id: OrderDirection
  createdAt: OrderDirection
  firstName: OrderDirection
  lastName: OrderDirection
  email: OrderDirection
  phoneNumber: OrderDirection
  companyName: OrderDirection
  companyRole: OrderDirection
  mockAccountEmail: OrderDirection
  notes: OrderDirection
}

input LeadEntryUpdateInput {
  createdAt: DateTime
  firstName: String
  lastName: String
  email: String
  phoneNumber: String
  companyName: String
  companyRole: String
  mockAccountEmail: String
  notes: String
}

input LeadEntryUpdateArgs {
  where: LeadEntryWhereUniqueInput!
  data: LeadEntryUpdateInput!
}

input LeadEntryCreateInput {
  createdAt: DateTime
  firstName: String
  lastName: String
  email: String
  phoneNumber: String
  companyName: String
  companyRole: String
  mockAccountEmail: String
  notes: String
}

type SweepGlobalPay {
  id: ID!
  groupID: String
  merchantID: String
  createdAt: DateTime
  amount: Int
  method: String
  note: String
  metadata: JSON
}

input SweepGlobalPayWhereUniqueInput {
  id: ID
}

input SweepGlobalPayWhereInput {
  AND: [SweepGlobalPayWhereInput!]
  OR: [SweepGlobalPayWhereInput!]
  NOT: [SweepGlobalPayWhereInput!]
  id: IDFilter
  groupID: StringFilter
  merchantID: StringFilter
  createdAt: DateTimeNullableFilter
  amount: IntNullableFilter
  method: StringFilter
  note: StringFilter
}

input SweepGlobalPayOrderByInput {
  id: OrderDirection
  groupID: OrderDirection
  merchantID: OrderDirection
  createdAt: OrderDirection
  amount: OrderDirection
  method: OrderDirection
  note: OrderDirection
}

input SweepGlobalPayUpdateInput {
  groupID: String
  merchantID: String
  createdAt: DateTime
  amount: Int
  method: String
  note: String
  metadata: JSON
}

input SweepGlobalPayUpdateArgs {
  where: SweepGlobalPayWhereUniqueInput!
  data: SweepGlobalPayUpdateInput!
}

input SweepGlobalPayCreateInput {
  groupID: String
  merchantID: String
  createdAt: DateTime
  amount: Int
  method: String
  note: String
  metadata: JSON
}

type SweepEarnHistory {
  id: ID!
  group: Group
  amount: Float
  invalid: Boolean
  pending: Boolean
  checkToGPInterchange: Boolean
  description: String
  referenceID: String
  createdAt: DateTime
}

input SweepEarnHistoryWhereUniqueInput {
  id: ID
}

input SweepEarnHistoryWhereInput {
  AND: [SweepEarnHistoryWhereInput!]
  OR: [SweepEarnHistoryWhereInput!]
  NOT: [SweepEarnHistoryWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  amount: FloatNullableFilter
  invalid: BooleanFilter
  pending: BooleanFilter
  checkToGPInterchange: BooleanFilter
  description: StringFilter
  referenceID: StringFilter
  createdAt: DateTimeNullableFilter
}

input SweepEarnHistoryOrderByInput {
  id: OrderDirection
  amount: OrderDirection
  invalid: OrderDirection
  pending: OrderDirection
  checkToGPInterchange: OrderDirection
  description: OrderDirection
  referenceID: OrderDirection
  createdAt: OrderDirection
}

input SweepEarnHistoryUpdateInput {
  group: GroupRelateToOneForUpdateInput
  amount: Float
  invalid: Boolean
  pending: Boolean
  checkToGPInterchange: Boolean
  description: String
  referenceID: String
  createdAt: DateTime
}

input SweepEarnHistoryUpdateArgs {
  where: SweepEarnHistoryWhereUniqueInput!
  data: SweepEarnHistoryUpdateInput!
}

input SweepEarnHistoryCreateInput {
  group: GroupRelateToOneForCreateInput
  amount: Float
  invalid: Boolean
  pending: Boolean
  checkToGPInterchange: Boolean
  description: String
  referenceID: String
  createdAt: DateTime
}

type ServiceAPIAccount {
  id: ID!
  group: Group
  token: String
  secretView: String
  active: Boolean
  ipWhitelist: JSON
  allowedMethods: JSON
  createdAt: DateTime
  updatedAt: DateTime
  expiresAt: DateTime
}

input ServiceAPIAccountWhereUniqueInput {
  id: ID
  token: String
}

input ServiceAPIAccountWhereInput {
  AND: [ServiceAPIAccountWhereInput!]
  OR: [ServiceAPIAccountWhereInput!]
  NOT: [ServiceAPIAccountWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  token: StringFilter
  active: BooleanFilter
  createdAt: DateTimeNullableFilter
  updatedAt: DateTimeNullableFilter
  expiresAt: DateTimeNullableFilter
}

input ServiceAPIAccountOrderByInput {
  id: OrderDirection
  token: OrderDirection
  active: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
  expiresAt: OrderDirection
}

input ServiceAPIAccountUpdateInput {
  group: GroupRelateToOneForUpdateInput
  token: String
  active: Boolean
  ipWhitelist: JSON
  allowedMethods: JSON
  createdAt: DateTime
  updatedAt: DateTime
  expiresAt: DateTime
}

input ServiceAPIAccountUpdateArgs {
  where: ServiceAPIAccountWhereUniqueInput!
  data: ServiceAPIAccountUpdateInput!
}

input ServiceAPIAccountCreateInput {
  group: GroupRelateToOneForCreateInput
  token: String
  active: Boolean
  ipWhitelist: JSON
  allowedMethods: JSON
  createdAt: DateTime
  updatedAt: DateTime
  expiresAt: DateTime
}

type StripeDataApplication {
  id: ID!
  group: Group
  keyType: String
  publishableKey: String
  secretKey: String
  accessKey: String
  refreshKey: String
  userId: String
  tokenType: String
  scope: String
}

input StripeDataApplicationWhereUniqueInput {
  id: ID
}

input StripeDataApplicationWhereInput {
  AND: [StripeDataApplicationWhereInput!]
  OR: [StripeDataApplicationWhereInput!]
  NOT: [StripeDataApplicationWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  keyType: StringFilter
  publishableKey: StringFilter
  secretKey: StringFilter
  accessKey: StringFilter
  refreshKey: StringFilter
  userId: StringFilter
  tokenType: StringFilter
  scope: StringFilter
}

input StripeDataApplicationOrderByInput {
  id: OrderDirection
  keyType: OrderDirection
  publishableKey: OrderDirection
  secretKey: OrderDirection
  accessKey: OrderDirection
  refreshKey: OrderDirection
  userId: OrderDirection
  tokenType: OrderDirection
  scope: OrderDirection
}

input StripeDataApplicationUpdateInput {
  group: GroupRelateToOneForUpdateInput
  keyType: String
  publishableKey: String
  secretKey: String
  accessKey: String
  refreshKey: String
  userId: String
  tokenType: String
  scope: String
}

input StripeDataApplicationUpdateArgs {
  where: StripeDataApplicationWhereUniqueInput!
  data: StripeDataApplicationUpdateInput!
}

input StripeDataApplicationCreateInput {
  group: GroupRelateToOneForCreateInput
  keyType: String
  publishableKey: String
  secretKey: String
  accessKey: String
  refreshKey: String
  userId: String
  tokenType: String
  scope: String
}

type EventWebhook {
  id: ID!
  type: String
  group: Group
  payload: String
  createdAt: DateTime
}

input EventWebhookWhereUniqueInput {
  id: ID
}

input EventWebhookWhereInput {
  AND: [EventWebhookWhereInput!]
  OR: [EventWebhookWhereInput!]
  NOT: [EventWebhookWhereInput!]
  id: IDFilter
  type: StringFilter
  group: GroupWhereInput
  payload: StringFilter
  createdAt: DateTimeNullableFilter
}

input EventWebhookOrderByInput {
  id: OrderDirection
  type: OrderDirection
  payload: OrderDirection
  createdAt: OrderDirection
}

input EventWebhookUpdateInput {
  type: String
  group: GroupRelateToOneForUpdateInput
  payload: String
  createdAt: DateTime
}

input EventWebhookUpdateArgs {
  where: EventWebhookWhereUniqueInput!
  data: EventWebhookUpdateInput!
}

input EventWebhookCreateInput {
  type: String
  group: GroupRelateToOneForCreateInput
  payload: String
  createdAt: DateTime
}

type HubspotAccess {
  id: ID!
  group: Group
  initiatorAccountID: String
  clientID: String
  hubId: String
  hubDomain: String
  scopes: JSON
  hubspotUserID: String
  updatedAt: DateTime
}

input HubspotAccessWhereUniqueInput {
  id: ID
}

input HubspotAccessWhereInput {
  AND: [HubspotAccessWhereInput!]
  OR: [HubspotAccessWhereInput!]
  NOT: [HubspotAccessWhereInput!]
  id: IDFilter
  group: GroupWhereInput
  initiatorAccountID: StringFilter
  clientID: StringFilter
  hubId: StringFilter
  hubDomain: StringFilter
  hubspotUserID: StringFilter
  updatedAt: DateTimeNullableFilter
}

input HubspotAccessOrderByInput {
  id: OrderDirection
  initiatorAccountID: OrderDirection
  clientID: OrderDirection
  hubId: OrderDirection
  hubDomain: OrderDirection
  hubspotUserID: OrderDirection
  updatedAt: OrderDirection
}

input HubspotAccessUpdateInput {
  group: GroupRelateToOneForUpdateInput
  initiatorAccountID: String
  clientID: String
  hubId: String
  hubDomain: String
  scopes: JSON
  hubspotUserID: String
  updatedAt: DateTime
}

input HubspotAccessUpdateArgs {
  where: HubspotAccessWhereUniqueInput!
  data: HubspotAccessUpdateInput!
}

input HubspotAccessCreateInput {
  group: GroupRelateToOneForCreateInput
  initiatorAccountID: String
  clientID: String
  hubId: String
  hubDomain: String
  scopes: JSON
  hubspotUserID: String
  updatedAt: DateTime
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON
  @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type Mutation {
  createServerLog(data: ServerLogCreateInput!): ServerLog
  createServerLogs(data: [ServerLogCreateInput!]!): [ServerLog]
  updateServerLog(where: ServerLogWhereUniqueInput!, data: ServerLogUpdateInput!): ServerLog
  updateServerLogs(data: [ServerLogUpdateArgs!]!): [ServerLog]
  deleteServerLog(where: ServerLogWhereUniqueInput!): ServerLog
  deleteServerLogs(where: [ServerLogWhereUniqueInput!]!): [ServerLog]
  createServerError(data: ServerErrorCreateInput!): ServerError
  createServerErrors(data: [ServerErrorCreateInput!]!): [ServerError]
  updateServerError(where: ServerErrorWhereUniqueInput!, data: ServerErrorUpdateInput!): ServerError
  updateServerErrors(data: [ServerErrorUpdateArgs!]!): [ServerError]
  deleteServerError(where: ServerErrorWhereUniqueInput!): ServerError
  deleteServerErrors(where: [ServerErrorWhereUniqueInput!]!): [ServerError]
  createGatewayLog(data: GatewayLogCreateInput!): GatewayLog
  createGatewayLogs(data: [GatewayLogCreateInput!]!): [GatewayLog]
  updateGatewayLog(where: GatewayLogWhereUniqueInput!, data: GatewayLogUpdateInput!): GatewayLog
  updateGatewayLogs(data: [GatewayLogUpdateArgs!]!): [GatewayLog]
  deleteGatewayLog(where: GatewayLogWhereUniqueInput!): GatewayLog
  deleteGatewayLogs(where: [GatewayLogWhereUniqueInput!]!): [GatewayLog]
  createUser(data: UserCreateInput!): User
  createUsers(data: [UserCreateInput!]!): [User]
  updateUser(where: UserWhereUniqueInput!, data: UserUpdateInput!): User
  updateUsers(data: [UserUpdateArgs!]!): [User]
  deleteUser(where: UserWhereUniqueInput!): User
  deleteUsers(where: [UserWhereUniqueInput!]!): [User]
  createUserCache(data: UserCacheCreateInput!): UserCache
  createUserCaches(data: [UserCacheCreateInput!]!): [UserCache]
  updateUserCache(where: UserCacheWhereUniqueInput!, data: UserCacheUpdateInput!): UserCache
  updateUserCaches(data: [UserCacheUpdateArgs!]!): [UserCache]
  deleteUserCache(where: UserCacheWhereUniqueInput!): UserCache
  deleteUserCaches(where: [UserCacheWhereUniqueInput!]!): [UserCache]
  createGroup(data: GroupCreateInput!): Group
  createGroups(data: [GroupCreateInput!]!): [Group]
  updateGroup(where: GroupWhereUniqueInput!, data: GroupUpdateInput!): Group
  updateGroups(data: [GroupUpdateArgs!]!): [Group]
  deleteGroup(where: GroupWhereUniqueInput!): Group
  deleteGroups(where: [GroupWhereUniqueInput!]!): [Group]
  createGroupWebhook(data: GroupWebhookCreateInput!): GroupWebhook
  createGroupWebhooks(data: [GroupWebhookCreateInput!]!): [GroupWebhook]
  updateGroupWebhook(
    where: GroupWebhookWhereUniqueInput!
    data: GroupWebhookUpdateInput!
  ): GroupWebhook
  updateGroupWebhooks(data: [GroupWebhookUpdateArgs!]!): [GroupWebhook]
  deleteGroupWebhook(where: GroupWebhookWhereUniqueInput!): GroupWebhook
  deleteGroupWebhooks(where: [GroupWebhookWhereUniqueInput!]!): [GroupWebhook]
  createGroupMember(data: GroupMemberCreateInput!): GroupMember
  createGroupMembers(data: [GroupMemberCreateInput!]!): [GroupMember]
  updateGroupMember(where: GroupMemberWhereUniqueInput!, data: GroupMemberUpdateInput!): GroupMember
  updateGroupMembers(data: [GroupMemberUpdateArgs!]!): [GroupMember]
  deleteGroupMember(where: GroupMemberWhereUniqueInput!): GroupMember
  deleteGroupMembers(where: [GroupMemberWhereUniqueInput!]!): [GroupMember]
  createGroupMemberInvite(data: GroupMemberInviteCreateInput!): GroupMemberInvite
  createGroupMemberInvites(data: [GroupMemberInviteCreateInput!]!): [GroupMemberInvite]
  updateGroupMemberInvite(
    where: GroupMemberInviteWhereUniqueInput!
    data: GroupMemberInviteUpdateInput!
  ): GroupMemberInvite
  updateGroupMemberInvites(data: [GroupMemberInviteUpdateArgs!]!): [GroupMemberInvite]
  deleteGroupMemberInvite(where: GroupMemberInviteWhereUniqueInput!): GroupMemberInvite
  deleteGroupMemberInvites(where: [GroupMemberInviteWhereUniqueInput!]!): [GroupMemberInvite]
  createGroupMemberFlag(data: GroupMemberFlagCreateInput!): GroupMemberFlag
  createGroupMemberFlags(data: [GroupMemberFlagCreateInput!]!): [GroupMemberFlag]
  updateGroupMemberFlag(
    where: GroupMemberFlagWhereUniqueInput!
    data: GroupMemberFlagUpdateInput!
  ): GroupMemberFlag
  updateGroupMemberFlags(data: [GroupMemberFlagUpdateArgs!]!): [GroupMemberFlag]
  deleteGroupMemberFlag(where: GroupMemberFlagWhereUniqueInput!): GroupMemberFlag
  deleteGroupMemberFlags(where: [GroupMemberFlagWhereUniqueInput!]!): [GroupMemberFlag]
  createTSTApplication(data: TSTApplicationCreateInput!): TSTApplication
  createTSTApplications(data: [TSTApplicationCreateInput!]!): [TSTApplication]
  updateTSTApplication(
    where: TSTApplicationWhereUniqueInput!
    data: TSTApplicationUpdateInput!
  ): TSTApplication
  updateTSTApplications(data: [TSTApplicationUpdateArgs!]!): [TSTApplication]
  deleteTSTApplication(where: TSTApplicationWhereUniqueInput!): TSTApplication
  deleteTSTApplications(where: [TSTApplicationWhereUniqueInput!]!): [TSTApplication]
  createGLPApplication(data: GLPApplicationCreateInput!): GLPApplication
  createGLPApplications(data: [GLPApplicationCreateInput!]!): [GLPApplication]
  updateGLPApplication(
    where: GLPApplicationWhereUniqueInput!
    data: GLPApplicationUpdateInput!
  ): GLPApplication
  updateGLPApplications(data: [GLPApplicationUpdateArgs!]!): [GLPApplication]
  deleteGLPApplication(where: GLPApplicationWhereUniqueInput!): GLPApplication
  deleteGLPApplications(where: [GLPApplicationWhereUniqueInput!]!): [GLPApplication]
  createGLPPayWebhook(data: GLPPayWebhookCreateInput!): GLPPayWebhook
  createGLPPayWebhooks(data: [GLPPayWebhookCreateInput!]!): [GLPPayWebhook]
  updateGLPPayWebhook(
    where: GLPPayWebhookWhereUniqueInput!
    data: GLPPayWebhookUpdateInput!
  ): GLPPayWebhook
  updateGLPPayWebhooks(data: [GLPPayWebhookUpdateArgs!]!): [GLPPayWebhook]
  deleteGLPPayWebhook(where: GLPPayWebhookWhereUniqueInput!): GLPPayWebhook
  deleteGLPPayWebhooks(where: [GLPPayWebhookWhereUniqueInput!]!): [GLPPayWebhook]
  createGLPPayDraft(data: GLPPayDraftCreateInput!): GLPPayDraft
  createGLPPayDrafts(data: [GLPPayDraftCreateInput!]!): [GLPPayDraft]
  updateGLPPayDraft(where: GLPPayDraftWhereUniqueInput!, data: GLPPayDraftUpdateInput!): GLPPayDraft
  updateGLPPayDrafts(data: [GLPPayDraftUpdateArgs!]!): [GLPPayDraft]
  deleteGLPPayDraft(where: GLPPayDraftWhereUniqueInput!): GLPPayDraft
  deleteGLPPayDrafts(where: [GLPPayDraftWhereUniqueInput!]!): [GLPPayDraft]
  createAURApplication(data: AURApplicationCreateInput!): AURApplication
  createAURApplications(data: [AURApplicationCreateInput!]!): [AURApplication]
  updateAURApplication(
    where: AURApplicationWhereUniqueInput!
    data: AURApplicationUpdateInput!
  ): AURApplication
  updateAURApplications(data: [AURApplicationUpdateArgs!]!): [AURApplication]
  deleteAURApplication(where: AURApplicationWhereUniqueInput!): AURApplication
  deleteAURApplications(where: [AURApplicationWhereUniqueInput!]!): [AURApplication]
  createFLPayGateway(data: FLPayGatewayCreateInput!): FLPayGateway
  createFLPayGateways(data: [FLPayGatewayCreateInput!]!): [FLPayGateway]
  updateFLPayGateway(
    where: FLPayGatewayWhereUniqueInput!
    data: FLPayGatewayUpdateInput!
  ): FLPayGateway
  updateFLPayGateways(data: [FLPayGatewayUpdateArgs!]!): [FLPayGateway]
  deleteFLPayGateway(where: FLPayGatewayWhereUniqueInput!): FLPayGateway
  deleteFLPayGateways(where: [FLPayGatewayWhereUniqueInput!]!): [FLPayGateway]
  createGUni_Transactions(data: GUni_TransactionsCreateInput!): GUni_Transactions
  createGUniTransactions(data: [GUni_TransactionsCreateInput!]!): [GUni_Transactions]
  updateGUni_Transactions(
    where: GUni_TransactionsWhereUniqueInput!
    data: GUni_TransactionsUpdateInput!
  ): GUni_Transactions
  updateGUniTransactions(data: [GUni_TransactionsUpdateArgs!]!): [GUni_Transactions]
  deleteGUni_Transactions(where: GUni_TransactionsWhereUniqueInput!): GUni_Transactions
  deleteGUniTransactions(where: [GUni_TransactionsWhereUniqueInput!]!): [GUni_Transactions]
  createGUni_Transaction_OrderData(
    data: GUni_Transaction_OrderDataCreateInput!
  ): GUni_Transaction_OrderData
  createGUniTransactionOrderData(
    data: [GUni_Transaction_OrderDataCreateInput!]!
  ): [GUni_Transaction_OrderData]
  updateGUni_Transaction_OrderData(
    where: GUni_Transaction_OrderDataWhereUniqueInput!
    data: GUni_Transaction_OrderDataUpdateInput!
  ): GUni_Transaction_OrderData
  updateGUniTransactionOrderData(
    data: [GUni_Transaction_OrderDataUpdateArgs!]!
  ): [GUni_Transaction_OrderData]
  deleteGUni_Transaction_OrderData(
    where: GUni_Transaction_OrderDataWhereUniqueInput!
  ): GUni_Transaction_OrderData
  deleteGUniTransactionOrderData(
    where: [GUni_Transaction_OrderDataWhereUniqueInput!]!
  ): [GUni_Transaction_OrderData]
  createGUni_BatchedTransaction(data: GUni_BatchedTransactionCreateInput!): GUni_BatchedTransaction
  createGUniBatchedTransactions(
    data: [GUni_BatchedTransactionCreateInput!]!
  ): [GUni_BatchedTransaction]
  updateGUni_BatchedTransaction(
    where: GUni_BatchedTransactionWhereUniqueInput!
    data: GUni_BatchedTransactionUpdateInput!
  ): GUni_BatchedTransaction
  updateGUniBatchedTransactions(
    data: [GUni_BatchedTransactionUpdateArgs!]!
  ): [GUni_BatchedTransaction]
  deleteGUni_BatchedTransaction(
    where: GUni_BatchedTransactionWhereUniqueInput!
  ): GUni_BatchedTransaction
  deleteGUniBatchedTransactions(
    where: [GUni_BatchedTransactionWhereUniqueInput!]!
  ): [GUni_BatchedTransaction]
  createGUni_Customer(data: GUni_CustomerCreateInput!): GUni_Customer
  createGUniCustomers(data: [GUni_CustomerCreateInput!]!): [GUni_Customer]
  updateGUni_Customer(
    where: GUni_CustomerWhereUniqueInput!
    data: GUni_CustomerUpdateInput!
  ): GUni_Customer
  updateGUniCustomers(data: [GUni_CustomerUpdateArgs!]!): [GUni_Customer]
  deleteGUni_Customer(where: GUni_CustomerWhereUniqueInput!): GUni_Customer
  deleteGUniCustomers(where: [GUni_CustomerWhereUniqueInput!]!): [GUni_Customer]
  createGUni_Customer_PaymentCard(
    data: GUni_Customer_PaymentCardCreateInput!
  ): GUni_Customer_PaymentCard
  createGUniCustomerPaymentCards(
    data: [GUni_Customer_PaymentCardCreateInput!]!
  ): [GUni_Customer_PaymentCard]
  updateGUni_Customer_PaymentCard(
    where: GUni_Customer_PaymentCardWhereUniqueInput!
    data: GUni_Customer_PaymentCardUpdateInput!
  ): GUni_Customer_PaymentCard
  updateGUniCustomerPaymentCards(
    data: [GUni_Customer_PaymentCardUpdateArgs!]!
  ): [GUni_Customer_PaymentCard]
  deleteGUni_Customer_PaymentCard(
    where: GUni_Customer_PaymentCardWhereUniqueInput!
  ): GUni_Customer_PaymentCard
  deleteGUniCustomerPaymentCards(
    where: [GUni_Customer_PaymentCardWhereUniqueInput!]!
  ): [GUni_Customer_PaymentCard]
  createGUni_Category(data: GUni_CategoryCreateInput!): GUni_Category
  createGUniCategories(data: [GUni_CategoryCreateInput!]!): [GUni_Category]
  updateGUni_Category(
    where: GUni_CategoryWhereUniqueInput!
    data: GUni_CategoryUpdateInput!
  ): GUni_Category
  updateGUniCategories(data: [GUni_CategoryUpdateArgs!]!): [GUni_Category]
  deleteGUni_Category(where: GUni_CategoryWhereUniqueInput!): GUni_Category
  deleteGUniCategories(where: [GUni_CategoryWhereUniqueInput!]!): [GUni_Category]
  createGUni_PaymentPlan(data: GUni_PaymentPlanCreateInput!): GUni_PaymentPlan
  createGUniPaymentPlans(data: [GUni_PaymentPlanCreateInput!]!): [GUni_PaymentPlan]
  updateGUni_PaymentPlan(
    where: GUni_PaymentPlanWhereUniqueInput!
    data: GUni_PaymentPlanUpdateInput!
  ): GUni_PaymentPlan
  updateGUniPaymentPlans(data: [GUni_PaymentPlanUpdateArgs!]!): [GUni_PaymentPlan]
  deleteGUni_PaymentPlan(where: GUni_PaymentPlanWhereUniqueInput!): GUni_PaymentPlan
  deleteGUniPaymentPlans(where: [GUni_PaymentPlanWhereUniqueInput!]!): [GUni_PaymentPlan]
  createGUni_PaymentPlan_Payment(
    data: GUni_PaymentPlan_PaymentCreateInput!
  ): GUni_PaymentPlan_Payment
  createGUniPaymentPlanPayments(
    data: [GUni_PaymentPlan_PaymentCreateInput!]!
  ): [GUni_PaymentPlan_Payment]
  updateGUni_PaymentPlan_Payment(
    where: GUni_PaymentPlan_PaymentWhereUniqueInput!
    data: GUni_PaymentPlan_PaymentUpdateInput!
  ): GUni_PaymentPlan_Payment
  updateGUniPaymentPlanPayments(
    data: [GUni_PaymentPlan_PaymentUpdateArgs!]!
  ): [GUni_PaymentPlan_Payment]
  deleteGUni_PaymentPlan_Payment(
    where: GUni_PaymentPlan_PaymentWhereUniqueInput!
  ): GUni_PaymentPlan_Payment
  deleteGUniPaymentPlanPayments(
    where: [GUni_PaymentPlan_PaymentWhereUniqueInput!]!
  ): [GUni_PaymentPlan_Payment]
  createGUni_Product(data: GUni_ProductCreateInput!): GUni_Product
  createGUniProducts(data: [GUni_ProductCreateInput!]!): [GUni_Product]
  updateGUni_Product(
    where: GUni_ProductWhereUniqueInput!
    data: GUni_ProductUpdateInput!
  ): GUni_Product
  updateGUniProducts(data: [GUni_ProductUpdateArgs!]!): [GUni_Product]
  deleteGUni_Product(where: GUni_ProductWhereUniqueInput!): GUni_Product
  deleteGUniProducts(where: [GUni_ProductWhereUniqueInput!]!): [GUni_Product]
  createGUni_Discount(data: GUni_DiscountCreateInput!): GUni_Discount
  createGUniDiscounts(data: [GUni_DiscountCreateInput!]!): [GUni_Discount]
  updateGUni_Discount(
    where: GUni_DiscountWhereUniqueInput!
    data: GUni_DiscountUpdateInput!
  ): GUni_Discount
  updateGUniDiscounts(data: [GUni_DiscountUpdateArgs!]!): [GUni_Discount]
  deleteGUni_Discount(where: GUni_DiscountWhereUniqueInput!): GUni_Discount
  deleteGUniDiscounts(where: [GUni_DiscountWhereUniqueInput!]!): [GUni_Discount]
  createGUni_PayLink(data: GUni_PayLinkCreateInput!): GUni_PayLink
  createGUniPayLinks(data: [GUni_PayLinkCreateInput!]!): [GUni_PayLink]
  updateGUni_PayLink(
    where: GUni_PayLinkWhereUniqueInput!
    data: GUni_PayLinkUpdateInput!
  ): GUni_PayLink
  updateGUniPayLinks(data: [GUni_PayLinkUpdateArgs!]!): [GUni_PayLink]
  deleteGUni_PayLink(where: GUni_PayLinkWhereUniqueInput!): GUni_PayLink
  deleteGUniPayLinks(where: [GUni_PayLinkWhereUniqueInput!]!): [GUni_PayLink]
  createGUni_Dispute(data: GUni_DisputeCreateInput!): GUni_Dispute
  createGUniDisputes(data: [GUni_DisputeCreateInput!]!): [GUni_Dispute]
  updateGUni_Dispute(
    where: GUni_DisputeWhereUniqueInput!
    data: GUni_DisputeUpdateInput!
  ): GUni_Dispute
  updateGUniDisputes(data: [GUni_DisputeUpdateArgs!]!): [GUni_Dispute]
  deleteGUni_Dispute(where: GUni_DisputeWhereUniqueInput!): GUni_Dispute
  deleteGUniDisputes(where: [GUni_DisputeWhereUniqueInput!]!): [GUni_Dispute]
  createGUni_Dispute_Files(data: GUni_Dispute_FilesCreateInput!): GUni_Dispute_Files
  createGUniDisputeFiles(data: [GUni_Dispute_FilesCreateInput!]!): [GUni_Dispute_Files]
  updateGUni_Dispute_Files(
    where: GUni_Dispute_FilesWhereUniqueInput!
    data: GUni_Dispute_FilesUpdateInput!
  ): GUni_Dispute_Files
  updateGUniDisputeFiles(data: [GUni_Dispute_FilesUpdateArgs!]!): [GUni_Dispute_Files]
  deleteGUni_Dispute_Files(where: GUni_Dispute_FilesWhereUniqueInput!): GUni_Dispute_Files
  deleteGUniDisputeFiles(where: [GUni_Dispute_FilesWhereUniqueInput!]!): [GUni_Dispute_Files]
  createGUni_Dispute_History(data: GUni_Dispute_HistoryCreateInput!): GUni_Dispute_History
  createGUniDisputeHistories(data: [GUni_Dispute_HistoryCreateInput!]!): [GUni_Dispute_History]
  updateGUni_Dispute_History(
    where: GUni_Dispute_HistoryWhereUniqueInput!
    data: GUni_Dispute_HistoryUpdateInput!
  ): GUni_Dispute_History
  updateGUniDisputeHistories(data: [GUni_Dispute_HistoryUpdateArgs!]!): [GUni_Dispute_History]
  deleteGUni_Dispute_History(where: GUni_Dispute_HistoryWhereUniqueInput!): GUni_Dispute_History
  deleteGUniDisputeHistories(
    where: [GUni_Dispute_HistoryWhereUniqueInput!]!
  ): [GUni_Dispute_History]
  createDashboardHourlyStatistic(
    data: DashboardHourlyStatisticCreateInput!
  ): DashboardHourlyStatistic
  createDashboardHourlyStatistics(
    data: [DashboardHourlyStatisticCreateInput!]!
  ): [DashboardHourlyStatistic]
  updateDashboardHourlyStatistic(
    where: DashboardHourlyStatisticWhereUniqueInput!
    data: DashboardHourlyStatisticUpdateInput!
  ): DashboardHourlyStatistic
  updateDashboardHourlyStatistics(
    data: [DashboardHourlyStatisticUpdateArgs!]!
  ): [DashboardHourlyStatistic]
  deleteDashboardHourlyStatistic(
    where: DashboardHourlyStatisticWhereUniqueInput!
  ): DashboardHourlyStatistic
  deleteDashboardHourlyStatistics(
    where: [DashboardHourlyStatisticWhereUniqueInput!]!
  ): [DashboardHourlyStatistic]
  createOTP(data: OTPCreateInput!): OTP
  createOTPS(data: [OTPCreateInput!]!): [OTP]
  updateOTP(where: OTPWhereUniqueInput!, data: OTPUpdateInput!): OTP
  updateOTPS(data: [OTPUpdateArgs!]!): [OTP]
  deleteOTP(where: OTPWhereUniqueInput!): OTP
  deleteOTPS(where: [OTPWhereUniqueInput!]!): [OTP]
  createMFA(data: MFACreateInput!): MFA
  createMFAS(data: [MFACreateInput!]!): [MFA]
  updateMFA(where: MFAWhereUniqueInput!, data: MFAUpdateInput!): MFA
  updateMFAS(data: [MFAUpdateArgs!]!): [MFA]
  deleteMFA(where: MFAWhereUniqueInput!): MFA
  deleteMFAS(where: [MFAWhereUniqueInput!]!): [MFA]
  createNotification(data: NotificationCreateInput!): Notification
  createNotifications(data: [NotificationCreateInput!]!): [Notification]
  updateNotification(
    where: NotificationWhereUniqueInput!
    data: NotificationUpdateInput!
  ): Notification
  updateNotifications(data: [NotificationUpdateArgs!]!): [Notification]
  deleteNotification(where: NotificationWhereUniqueInput!): Notification
  deleteNotifications(where: [NotificationWhereUniqueInput!]!): [Notification]
  createAffiliationProfile(data: AffiliationProfileCreateInput!): AffiliationProfile
  createAffiliationProfiles(data: [AffiliationProfileCreateInput!]!): [AffiliationProfile]
  updateAffiliationProfile(
    where: AffiliationProfileWhereUniqueInput!
    data: AffiliationProfileUpdateInput!
  ): AffiliationProfile
  updateAffiliationProfiles(data: [AffiliationProfileUpdateArgs!]!): [AffiliationProfile]
  deleteAffiliationProfile(where: AffiliationProfileWhereUniqueInput!): AffiliationProfile
  deleteAffiliationProfiles(where: [AffiliationProfileWhereUniqueInput!]!): [AffiliationProfile]
  createAffiliateEarnHistory(data: AffiliateEarnHistoryCreateInput!): AffiliateEarnHistory
  createAffiliateEarnHistories(data: [AffiliateEarnHistoryCreateInput!]!): [AffiliateEarnHistory]
  updateAffiliateEarnHistory(
    where: AffiliateEarnHistoryWhereUniqueInput!
    data: AffiliateEarnHistoryUpdateInput!
  ): AffiliateEarnHistory
  updateAffiliateEarnHistories(data: [AffiliateEarnHistoryUpdateArgs!]!): [AffiliateEarnHistory]
  deleteAffiliateEarnHistory(where: AffiliateEarnHistoryWhereUniqueInput!): AffiliateEarnHistory
  deleteAffiliateEarnHistories(
    where: [AffiliateEarnHistoryWhereUniqueInput!]!
  ): [AffiliateEarnHistory]
  createAffiliationProfileKeyCode(
    data: AffiliationProfileKeyCodeCreateInput!
  ): AffiliationProfileKeyCode
  createAffiliationProfileKeyCodes(
    data: [AffiliationProfileKeyCodeCreateInput!]!
  ): [AffiliationProfileKeyCode]
  updateAffiliationProfileKeyCode(
    where: AffiliationProfileKeyCodeWhereUniqueInput!
    data: AffiliationProfileKeyCodeUpdateInput!
  ): AffiliationProfileKeyCode
  updateAffiliationProfileKeyCodes(
    data: [AffiliationProfileKeyCodeUpdateArgs!]!
  ): [AffiliationProfileKeyCode]
  deleteAffiliationProfileKeyCode(
    where: AffiliationProfileKeyCodeWhereUniqueInput!
  ): AffiliationProfileKeyCode
  deleteAffiliationProfileKeyCodes(
    where: [AffiliationProfileKeyCodeWhereUniqueInput!]!
  ): [AffiliationProfileKeyCode]
  createGHLAccess(data: GHLAccessCreateInput!): GHLAccess
  createGHLAccesses(data: [GHLAccessCreateInput!]!): [GHLAccess]
  updateGHLAccess(where: GHLAccessWhereUniqueInput!, data: GHLAccessUpdateInput!): GHLAccess
  updateGHLAccesses(data: [GHLAccessUpdateArgs!]!): [GHLAccess]
  deleteGHLAccess(where: GHLAccessWhereUniqueInput!): GHLAccess
  deleteGHLAccesses(where: [GHLAccessWhereUniqueInput!]!): [GHLAccess]
  createGHLSSOBinding(data: GHLSSOBindingCreateInput!): GHLSSOBinding
  createGHLSSOBindings(data: [GHLSSOBindingCreateInput!]!): [GHLSSOBinding]
  updateGHLSSOBinding(
    where: GHLSSOBindingWhereUniqueInput!
    data: GHLSSOBindingUpdateInput!
  ): GHLSSOBinding
  updateGHLSSOBindings(data: [GHLSSOBindingUpdateArgs!]!): [GHLSSOBinding]
  deleteGHLSSOBinding(where: GHLSSOBindingWhereUniqueInput!): GHLSSOBinding
  deleteGHLSSOBindings(where: [GHLSSOBindingWhereUniqueInput!]!): [GHLSSOBinding]
  createGHLPayTransactionMap(data: GHLPayTransactionMapCreateInput!): GHLPayTransactionMap
  createGHLPayTransactionMaps(data: [GHLPayTransactionMapCreateInput!]!): [GHLPayTransactionMap]
  updateGHLPayTransactionMap(
    where: GHLPayTransactionMapWhereUniqueInput!
    data: GHLPayTransactionMapUpdateInput!
  ): GHLPayTransactionMap
  updateGHLPayTransactionMaps(data: [GHLPayTransactionMapUpdateArgs!]!): [GHLPayTransactionMap]
  deleteGHLPayTransactionMap(where: GHLPayTransactionMapWhereUniqueInput!): GHLPayTransactionMap
  deleteGHLPayTransactionMaps(
    where: [GHLPayTransactionMapWhereUniqueInput!]!
  ): [GHLPayTransactionMap]
  createGHLCachedCode(data: GHLCachedCodeCreateInput!): GHLCachedCode
  createGHLCachedCodes(data: [GHLCachedCodeCreateInput!]!): [GHLCachedCode]
  updateGHLCachedCode(
    where: GHLCachedCodeWhereUniqueInput!
    data: GHLCachedCodeUpdateInput!
  ): GHLCachedCode
  updateGHLCachedCodes(data: [GHLCachedCodeUpdateArgs!]!): [GHLCachedCode]
  deleteGHLCachedCode(where: GHLCachedCodeWhereUniqueInput!): GHLCachedCode
  deleteGHLCachedCodes(where: [GHLCachedCodeWhereUniqueInput!]!): [GHLCachedCode]
  createGroupSupportTicket(data: GroupSupportTicketCreateInput!): GroupSupportTicket
  createGroupSupportTickets(data: [GroupSupportTicketCreateInput!]!): [GroupSupportTicket]
  updateGroupSupportTicket(
    where: GroupSupportTicketWhereUniqueInput!
    data: GroupSupportTicketUpdateInput!
  ): GroupSupportTicket
  updateGroupSupportTickets(data: [GroupSupportTicketUpdateArgs!]!): [GroupSupportTicket]
  deleteGroupSupportTicket(where: GroupSupportTicketWhereUniqueInput!): GroupSupportTicket
  deleteGroupSupportTickets(where: [GroupSupportTicketWhereUniqueInput!]!): [GroupSupportTicket]
  createGroupSupportMessage(data: GroupSupportMessageCreateInput!): GroupSupportMessage
  createGroupSupportMessages(data: [GroupSupportMessageCreateInput!]!): [GroupSupportMessage]
  updateGroupSupportMessage(
    where: GroupSupportMessageWhereUniqueInput!
    data: GroupSupportMessageUpdateInput!
  ): GroupSupportMessage
  updateGroupSupportMessages(data: [GroupSupportMessageUpdateArgs!]!): [GroupSupportMessage]
  deleteGroupSupportMessage(where: GroupSupportMessageWhereUniqueInput!): GroupSupportMessage
  deleteGroupSupportMessages(where: [GroupSupportMessageWhereUniqueInput!]!): [GroupSupportMessage]
  createLeadEntry(data: LeadEntryCreateInput!): LeadEntry
  createLeadEntries(data: [LeadEntryCreateInput!]!): [LeadEntry]
  updateLeadEntry(where: LeadEntryWhereUniqueInput!, data: LeadEntryUpdateInput!): LeadEntry
  updateLeadEntries(data: [LeadEntryUpdateArgs!]!): [LeadEntry]
  deleteLeadEntry(where: LeadEntryWhereUniqueInput!): LeadEntry
  deleteLeadEntries(where: [LeadEntryWhereUniqueInput!]!): [LeadEntry]
  createSweepGlobalPay(data: SweepGlobalPayCreateInput!): SweepGlobalPay
  createSweepGlobalPays(data: [SweepGlobalPayCreateInput!]!): [SweepGlobalPay]
  updateSweepGlobalPay(
    where: SweepGlobalPayWhereUniqueInput!
    data: SweepGlobalPayUpdateInput!
  ): SweepGlobalPay
  updateSweepGlobalPays(data: [SweepGlobalPayUpdateArgs!]!): [SweepGlobalPay]
  deleteSweepGlobalPay(where: SweepGlobalPayWhereUniqueInput!): SweepGlobalPay
  deleteSweepGlobalPays(where: [SweepGlobalPayWhereUniqueInput!]!): [SweepGlobalPay]
  createSweepEarnHistory(data: SweepEarnHistoryCreateInput!): SweepEarnHistory
  createSweepEarnHistories(data: [SweepEarnHistoryCreateInput!]!): [SweepEarnHistory]
  updateSweepEarnHistory(
    where: SweepEarnHistoryWhereUniqueInput!
    data: SweepEarnHistoryUpdateInput!
  ): SweepEarnHistory
  updateSweepEarnHistories(data: [SweepEarnHistoryUpdateArgs!]!): [SweepEarnHistory]
  deleteSweepEarnHistory(where: SweepEarnHistoryWhereUniqueInput!): SweepEarnHistory
  deleteSweepEarnHistories(where: [SweepEarnHistoryWhereUniqueInput!]!): [SweepEarnHistory]
  createServiceAPIAccount(data: ServiceAPIAccountCreateInput!): ServiceAPIAccount
  createServiceAPIAccounts(data: [ServiceAPIAccountCreateInput!]!): [ServiceAPIAccount]
  updateServiceAPIAccount(
    where: ServiceAPIAccountWhereUniqueInput!
    data: ServiceAPIAccountUpdateInput!
  ): ServiceAPIAccount
  updateServiceAPIAccounts(data: [ServiceAPIAccountUpdateArgs!]!): [ServiceAPIAccount]
  deleteServiceAPIAccount(where: ServiceAPIAccountWhereUniqueInput!): ServiceAPIAccount
  deleteServiceAPIAccounts(where: [ServiceAPIAccountWhereUniqueInput!]!): [ServiceAPIAccount]
  createStripeDataApplication(data: StripeDataApplicationCreateInput!): StripeDataApplication
  createStripeDataApplications(data: [StripeDataApplicationCreateInput!]!): [StripeDataApplication]
  updateStripeDataApplication(
    where: StripeDataApplicationWhereUniqueInput!
    data: StripeDataApplicationUpdateInput!
  ): StripeDataApplication
  updateStripeDataApplications(data: [StripeDataApplicationUpdateArgs!]!): [StripeDataApplication]
  deleteStripeDataApplication(where: StripeDataApplicationWhereUniqueInput!): StripeDataApplication
  deleteStripeDataApplications(
    where: [StripeDataApplicationWhereUniqueInput!]!
  ): [StripeDataApplication]
  createEventWebhook(data: EventWebhookCreateInput!): EventWebhook
  createEventWebhooks(data: [EventWebhookCreateInput!]!): [EventWebhook]
  updateEventWebhook(
    where: EventWebhookWhereUniqueInput!
    data: EventWebhookUpdateInput!
  ): EventWebhook
  updateEventWebhooks(data: [EventWebhookUpdateArgs!]!): [EventWebhook]
  deleteEventWebhook(where: EventWebhookWhereUniqueInput!): EventWebhook
  deleteEventWebhooks(where: [EventWebhookWhereUniqueInput!]!): [EventWebhook]
  createHubspotAccess(data: HubspotAccessCreateInput!): HubspotAccess
  createHubspotAccesses(data: [HubspotAccessCreateInput!]!): [HubspotAccess]
  updateHubspotAccess(
    where: HubspotAccessWhereUniqueInput!
    data: HubspotAccessUpdateInput!
  ): HubspotAccess
  updateHubspotAccesses(data: [HubspotAccessUpdateArgs!]!): [HubspotAccess]
  deleteHubspotAccess(where: HubspotAccessWhereUniqueInput!): HubspotAccess
  deleteHubspotAccesses(where: [HubspotAccessWhereUniqueInput!]!): [HubspotAccess]
  endSession: Boolean!
  authenticateUserWithPassword(
    email: String!
    adminPassword: String!
  ): UserAuthenticationWithPasswordResult
  createInitialUser(data: CreateInitialUserInput!): UserAuthenticationWithPasswordSuccess!
  authclient_login(
    email: String!
    password: String!
    browserId: String
  ): ClientItemAuthenticationWithPasswordResult
  authclient_register(
    email: String!
    phoneNumber: String
    otpSid: String
    browserId: String
    affiliateId: String
    firstName: String
    lastName: String
    title: String
    password: String!
  ): Boolean
  authclient_requestPasswordReset(email: String!): Boolean
  authclient_resetPassword(token: String!, password: String!): Boolean
  authclient_changePassword(oldPassword: String!, newPassword: String!): Boolean
  cache_save(input: Cache_saveInput!): UserCache
  group_signGroup(input: Group_signGroupInput!): Boolean
  group_createInvite(input: Group_createInviteInput!): CreateInviteInfo
  group_claimInvite(input: Group_claimInviteInput!): Boolean
  group_declineInvite(input: Group_declineInviteInput!): Boolean
  group_createWebhook(input: Group_createWebhookInput!): WebhookInfo
  processor_tst_draft_create(input: Processor_tst_draft_createInput!): ProcessorTSTApplicationCreate
  processor_tst_draft_update(input: Processor_tst_draft_updateInput!): ProcessorTSTApplicationUpdate
  processor_tst_draft_submit(input: Processor_tst_draft_submitInput!): TSTSubmission
  processor_globalpay_draft_create(
    input: Processor_globalpay_draft_createInput!
  ): ProcessorGloPayApplicationCreate
  processor_globalpay_draft_update(
    input: Processor_globalpay_draft_updateInput!
  ): ProcessorGloPayApplicationUpdate
  processor_globalpay_draft_submit(
    input: Processor_globalpay_draft_submitInput!
  ): ProcessorGloPaySubmission
  processor_aur_draft_create(input: Processor_aur_draft_createInput!): ProcessorAURApplicationCreate
  processor_aur_draft_update(input: Processor_aur_draft_updateInput!): ProcessorAURApplicationUpdate
  processor_aur_draft_submit(input: Processor_aur_draft_submitInput!): AURSubmission
  processor_aur_draft_file_delete(input: Processor_aur_draft_file_deleteInput!): String
  processor_draft_create(input: Processor_draft_createInput!): ProcessorApplicationCreate
  processor_draft_update(input: Processor_draft_updateInput!): ProcessorApplicationUpdate
  processor_draft_submit(input: Processor_draft_submitInput!): ProcessorSubmission
  processor_draft_documentUpload(input: Processor_draft_documentUploadInput!): DocumentUploadReturn
  processor_draft_documentForward(
    input: Processor_draft_documentForwardInput!
  ): DocumentForwardReturn
  processor_account_payoutUpdate(
    input: Processor_account_payoutUpdateInput!
  ): ProcessorPayoutAccountUpdate
  processor_account_addAccountFeature(
    input: Processor_account_addAccountFeatureInput!
  ): ProcessorAccountFeatureAdd
  gphf_token(input: Gphf_tokenInput!): GPHFTokenResponse
  tsep_manifest: TsepManifestResponse
  tsep_verify(input: Tsep_verifyInput!): TsepVerificationResponse
  propay_links(input: Propay_linksInput!): PropayLinksResponse
  propay_status: PropayStatusResponse
  gateway_test_closeBatch(input: Gateway_test_closeBatchInput!): GatewayTSTCloseBatchOutput
  gateway_test_addCustomer(input: Gateway_test_addCustomerInput!): GatewayTSTAddCustomerOutput
  gateway_test_updateCustomer(
    input: Gateway_test_updateCustomerInput!
  ): GatewayTSTUpdateCustomerOutput
  gateway_test_addPaymentMethod(
    input: Gateway_test_addPaymentMethodInput!
  ): GatewayTSTAddPaymentMethodOutput
  gateway_test_deletePaymentMethod(
    input: Gateway_test_deletePaymentMethodInput!
  ): GatewayTSTDeletePaymentMethodOutput
  gateway_test_deleteCustomer(
    input: Gateway_test_deleteCustomerInput!
  ): GatewayTSTDeleteCustomerOutput
  gateway_test_createPaymentPlan(
    input: Gateway_test_createPaymentPlanInput!
  ): GatewayTSTCreatePaymentPlanOutput
  gateway_test_createManualEntry(
    input: Gateway_test_createManualEntryInput!
  ): GatewayTSTCreateManualEntryOutput
  gateway_test_createCategory(
    input: Gateway_test_createCategoryInput!
  ): GatewayTSTCreateCategoryOutput
  gateway_test_deleteCategory(
    input: Gateway_test_deleteCategoryInput!
  ): GatewayTSTDeleteCategoryOutput
  gateway_test_updateCategory(
    input: Gateway_test_updateCategoryInput!
  ): GatewayTSTUpdateCategoryOutput
  gateway_test_createProduct(input: Gateway_test_createProductInput!): GatewayTSTCreateProductOutput
  gateway_test_updateProduct(input: Gateway_test_updateProductInput!): GatewayTSTUpdateProductOutput
  gateway_test_deleteProduct(input: Gateway_test_deleteProductInput!): GatewayTSTDeleteProductOutput
  gateway_test_createDiscount(
    input: Gateway_test_createDiscountInput!
  ): GatewayTSTCreateDiscountOutput
  gateway_test_deleteDiscount(
    input: Gateway_test_deleteDiscountInput!
  ): GatewayTSTDeleteDiscountOutput
  gateway_test_updateDiscount(
    input: Gateway_test_updateDiscountInput!
  ): GatewayTSTUpdateDiscountOutput
  gateway_flpayset_initialize(input: Gateway_flpayset_initializeInput!): GatewayFLPayInitOutput
  gateway_flpayset_processor_update(
    input: Gateway_flpayset_processor_updateInput!
  ): GatewayFLPayProcessorUpdateOutput
  gateway_closeBatch(input: Gateway_closeBatchInput!): GatewayUNICloseBatchOutput
  gateway_attendDispute(input: Gateway_attendDisputeInput!): GatewayUNIAttendDisputeOutput
  gateway_submitDisputeDocument(
    input: Gateway_submitDisputeDocumentInput!
  ): GatewayUNISubmitDisputeDocumentOutput
  gateway_uploadDisputeDocument(
    input: Gateway_uploadDisputeDocumentInput!
  ): GatewayUNIUploadDisputeDocumentOutput
  gateway_addCustomer(input: Gateway_addCustomerInput!): GatewayUNIAddCustomerOutput
  gateway_updateCustomer(input: Gateway_updateCustomerInput!): GatewayUNIUpdateCustomerOutput
  gateway_addPaymentMethod(input: Gateway_addPaymentMethodInput!): GatewayUNIAddPaymentMethodOutput
  gateway_deletePaymentMethod(
    input: Gateway_deletePaymentMethodInput!
  ): GatewayUNIDeletePaymentMethodOutput
  gateway_setDefaultPaymentMethod(
    input: Gateway_setDefaultPaymentMethodInput!
  ): GatewayUNISetDefaultPaymentMethodOutput
  gateway_deleteCustomer(input: Gateway_deleteCustomerInput!): GatewayUNIDeleteCustomerOutput
  gateway_createPaymentPlan(
    input: Gateway_createPaymentPlanInput!
  ): GatewayUNICreatePaymentPlanOutput
  gateway_updatePaymentPlan(
    input: Gateway_updatePaymentPlanInput!
  ): GatewayUNIUpdatePaymentPlanOutput
  gateway_cancelPaymentPlan(
    input: Gateway_cancelPaymentPlanInput!
  ): GatewayUNICancelPaymentPlanOutput
  gateway_computeCheckout(input: Gateway_computeCheckoutInput!): GatewayUNIComputeCheckoutOutput
  gateway_createManualEntry(
    input: Gateway_createManualEntryInput!
  ): GatewayUNICreateManualEntryOutput
  gateway_revertTransaction(
    input: Gateway_revertTransactionInput!
  ): GatewayUNIRevertTransactionOutput
  gateway_createCategory(input: Gateway_createCategoryInput!): GatewayUNICreateCategoryOutput
  gateway_updateCategory(input: Gateway_updateCategoryInput!): GatewayUNIUpdateCategoryOutput
  gateway_deleteCategory(input: Gateway_deleteCategoryInput!): GatewayUNIDeleteCategoryOutput
  gateway_createProduct(input: Gateway_createProductInput!): GatewayUNICreateProductOutput
  gateway_updateProduct(input: Gateway_updateProductInput!): GatewayUNIUpdateProductOutput
  gateway_deleteProduct(input: Gateway_deleteProductInput!): GatewayUNIDeleteProductOutput
  gateway_createDiscount(input: Gateway_createDiscountInput!): GatewayUNICreateDiscountOutput
  gateway_updateDiscount(input: Gateway_updateDiscountInput!): GatewayUNIUpdateDiscountOutput
  gateway_deleteDiscount(input: Gateway_deleteDiscountInput!): GatewayUNIDeleteDiscountOutput
  gateway_createPayLink(input: Gateway_createPayLinkInput!): GatewayUNICreatePayLinkOutput
  gateway_updatePayLink(input: Gateway_updatePayLinkInput!): GatewayUNIUpdatePayLinkOutput
  gateway_deletePayLink(input: Gateway_deletePayLinkInput!): GatewayUNIDeletePayLinkOutput

  """
  Channel can either be 'sms'
  """
  otp_generate(input: Otp_generateInput!): OTPGenerateOutput
  otp_verify(input: Otp_verifyInput!): OTPVerifyOutput
  authclient_mfa_check(input: Authclient_mfa_checkInput!): Boolean

  """
  Channel can either be 'sms'
  """
  authclient_mfa_generate(input: Authclient_mfa_generateInput!): MFAGenerateOutput
  authclient_mfa_verify(input: Authclient_mfa_verifyInput!): MFAVerifyOutput

  """
  Generate OTP for authenticated user using their saved phone number
  """
  otp_auth_generate(input: Otp_auth_generateInput!): OTPGenerateOutput
  affiliation_initiate: AffiliationProfile
  affiliation_setBankAccount(input: Affiliation_setBankAccountInput!): Boolean
  affiliation_setAffiliateCode(input: Affiliation_setAffiliateCodeInput!): AffiliateCodeOutput
  ghl_api_submitPayment_uni(input: Ghl_api_submitPayment_uniInput!): SubmitPaymentUniReturn
  ghl_auth_bind(input: Ghl_auth_bindInput!): GHLAuthSSOBindReturn
  ghl_auth_sso(input: Ghl_auth_ssoInput!): GHLAuthSSOReturn
  ghl_auth_completeIntegration(input: Ghl_auth_completeIntegrationInput!): Boolean
  ghl_auth_uninstallPaymentIntegration(input: Ghl_auth_uninstallPaymentIntegrationInput!): Boolean
  ghl_auth_testAES(input: Ghl_auth_testAESInput!): Boolean
  ghl_auth_locked(input: Ghl_auth_lockedInput!): Float
  ghl_sync_fromGHL(input: Ghl_sync_fromGHLInput!): GHLSyncFromGHLReturn
  ghl_sync_toGHL(input: Ghl_sync_toGHLInput!): GHLSyncToGHLReturn
  leads_login(input: Leads_loginInput!): LeadLoginOutput
  leads_initialize: String
  file_upload(input: File_uploadInput!): FileUploadOutput
  ai_determineMCCFromSentence(input: Ai_determineMCCFromSentenceInput!): JSON
  serviceacct_create(input: Serviceacct_createInput!): ServiceAPIAccountCreateOutput
  serviceacct_validate(input: Serviceacct_validateInput!): ServiceAPIAccountValidateOutput
  reportdownload_download(input: Reportdownload_downloadInput!): ReportDownloadOutput
  import_stripe(input: Import_stripeInput!): ImportStripeOutput
  stripe_sync(input: Stripe_syncInput!): StripeSyncTransactionsResponse
  stripe_setup(input: Stripe_setupInput!): Boolean
  propay_tables: PropayTables
  propay_query(input: Propay_queryInput!): JSON
}

union ClientItemAuthenticationWithPasswordResult =
  | ClientItemAuthenticationWithPasswordSuccess
  | ClientItemAuthenticationWithPasswordFailure

type ClientItemAuthenticationWithPasswordSuccess {
  sessionToken: String!
  item: User!
}

type ClientItemAuthenticationWithPasswordFailure {
  message: String!
}

union UserAuthenticationWithPasswordResult =
  | UserAuthenticationWithPasswordSuccess
  | UserAuthenticationWithPasswordFailure

type UserAuthenticationWithPasswordSuccess {
  sessionToken: String!
  item: User!
}

type UserAuthenticationWithPasswordFailure {
  message: String!
}

input CreateInitialUserInput {
  name: String
  email: String
  adminPassword: String
}

type Query {
  serverLogs(
    where: ServerLogWhereInput! = {}
    orderBy: [ServerLogOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: ServerLogWhereUniqueInput
  ): [ServerLog!]
  serverLog(where: ServerLogWhereUniqueInput!): ServerLog
  serverLogsCount(where: ServerLogWhereInput! = {}): Int
  serverErrors(
    where: ServerErrorWhereInput! = {}
    orderBy: [ServerErrorOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: ServerErrorWhereUniqueInput
  ): [ServerError!]
  serverError(where: ServerErrorWhereUniqueInput!): ServerError
  serverErrorsCount(where: ServerErrorWhereInput! = {}): Int
  gatewayLogs(
    where: GatewayLogWhereInput! = {}
    orderBy: [GatewayLogOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GatewayLogWhereUniqueInput
  ): [GatewayLog!]
  gatewayLog(where: GatewayLogWhereUniqueInput!): GatewayLog
  gatewayLogsCount(where: GatewayLogWhereInput! = {}): Int
  users(
    where: UserWhereInput! = {}
    orderBy: [UserOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: UserWhereUniqueInput
  ): [User!]
  user(where: UserWhereUniqueInput!): User
  usersCount(where: UserWhereInput! = {}): Int
  userCaches(
    where: UserCacheWhereInput! = {}
    orderBy: [UserCacheOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: UserCacheWhereUniqueInput
  ): [UserCache!]
  userCache(where: UserCacheWhereUniqueInput!): UserCache
  userCachesCount(where: UserCacheWhereInput! = {}): Int
  groups(
    where: GroupWhereInput! = {}
    orderBy: [GroupOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupWhereUniqueInput
  ): [Group!]
  group(where: GroupWhereUniqueInput!): Group
  groupsCount(where: GroupWhereInput! = {}): Int
  groupWebhooks(
    where: GroupWebhookWhereInput! = {}
    orderBy: [GroupWebhookOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupWebhookWhereUniqueInput
  ): [GroupWebhook!]
  groupWebhook(where: GroupWebhookWhereUniqueInput!): GroupWebhook
  groupWebhooksCount(where: GroupWebhookWhereInput! = {}): Int
  groupMembers(
    where: GroupMemberWhereInput! = {}
    orderBy: [GroupMemberOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupMemberWhereUniqueInput
  ): [GroupMember!]
  groupMember(where: GroupMemberWhereUniqueInput!): GroupMember
  groupMembersCount(where: GroupMemberWhereInput! = {}): Int
  groupMemberInvites(
    where: GroupMemberInviteWhereInput! = {}
    orderBy: [GroupMemberInviteOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupMemberInviteWhereUniqueInput
  ): [GroupMemberInvite!]
  groupMemberInvite(where: GroupMemberInviteWhereUniqueInput!): GroupMemberInvite
  groupMemberInvitesCount(where: GroupMemberInviteWhereInput! = {}): Int
  groupMemberFlags(
    where: GroupMemberFlagWhereInput! = {}
    orderBy: [GroupMemberFlagOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupMemberFlagWhereUniqueInput
  ): [GroupMemberFlag!]
  groupMemberFlag(where: GroupMemberFlagWhereUniqueInput!): GroupMemberFlag
  groupMemberFlagsCount(where: GroupMemberFlagWhereInput! = {}): Int
  tSTApplications(
    where: TSTApplicationWhereInput! = {}
    orderBy: [TSTApplicationOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: TSTApplicationWhereUniqueInput
  ): [TSTApplication!]
  tSTApplication(where: TSTApplicationWhereUniqueInput!): TSTApplication
  tSTApplicationsCount(where: TSTApplicationWhereInput! = {}): Int
  gLPApplications(
    where: GLPApplicationWhereInput! = {}
    orderBy: [GLPApplicationOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GLPApplicationWhereUniqueInput
  ): [GLPApplication!]
  gLPApplication(where: GLPApplicationWhereUniqueInput!): GLPApplication
  gLPApplicationsCount(where: GLPApplicationWhereInput! = {}): Int
  gLPPayWebhooks(
    where: GLPPayWebhookWhereInput! = {}
    orderBy: [GLPPayWebhookOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GLPPayWebhookWhereUniqueInput
  ): [GLPPayWebhook!]
  gLPPayWebhook(where: GLPPayWebhookWhereUniqueInput!): GLPPayWebhook
  gLPPayWebhooksCount(where: GLPPayWebhookWhereInput! = {}): Int
  gLPPayDrafts(
    where: GLPPayDraftWhereInput! = {}
    orderBy: [GLPPayDraftOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GLPPayDraftWhereUniqueInput
  ): [GLPPayDraft!]
  gLPPayDraft(where: GLPPayDraftWhereUniqueInput!): GLPPayDraft
  gLPPayDraftsCount(where: GLPPayDraftWhereInput! = {}): Int
  aURApplications(
    where: AURApplicationWhereInput! = {}
    orderBy: [AURApplicationOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: AURApplicationWhereUniqueInput
  ): [AURApplication!]
  aURApplication(where: AURApplicationWhereUniqueInput!): AURApplication
  aURApplicationsCount(where: AURApplicationWhereInput! = {}): Int
  fLPayGateways(
    where: FLPayGatewayWhereInput! = {}
    orderBy: [FLPayGatewayOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: FLPayGatewayWhereUniqueInput
  ): [FLPayGateway!]
  fLPayGateway(where: FLPayGatewayWhereUniqueInput!): FLPayGateway
  fLPayGatewaysCount(where: FLPayGatewayWhereInput! = {}): Int
  gUniTransactions(
    where: GUni_TransactionsWhereInput! = {}
    orderBy: [GUni_TransactionsOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_TransactionsWhereUniqueInput
  ): [GUni_Transactions!]
  gUni_Transactions(where: GUni_TransactionsWhereUniqueInput!): GUni_Transactions
  gUniTransactionsCount(where: GUni_TransactionsWhereInput! = {}): Int
  gUniTransactionOrderData(
    where: GUni_Transaction_OrderDataWhereInput! = {}
    orderBy: [GUni_Transaction_OrderDataOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Transaction_OrderDataWhereUniqueInput
  ): [GUni_Transaction_OrderData!]
  gUni_Transaction_OrderData(
    where: GUni_Transaction_OrderDataWhereUniqueInput!
  ): GUni_Transaction_OrderData
  gUniTransactionOrderDataCount(where: GUni_Transaction_OrderDataWhereInput! = {}): Int
  gUniBatchedTransactions(
    where: GUni_BatchedTransactionWhereInput! = {}
    orderBy: [GUni_BatchedTransactionOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_BatchedTransactionWhereUniqueInput
  ): [GUni_BatchedTransaction!]
  gUni_BatchedTransaction(where: GUni_BatchedTransactionWhereUniqueInput!): GUni_BatchedTransaction
  gUniBatchedTransactionsCount(where: GUni_BatchedTransactionWhereInput! = {}): Int
  gUniCustomers(
    where: GUni_CustomerWhereInput! = {}
    orderBy: [GUni_CustomerOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_CustomerWhereUniqueInput
  ): [GUni_Customer!]
  gUni_Customer(where: GUni_CustomerWhereUniqueInput!): GUni_Customer
  gUniCustomersCount(where: GUni_CustomerWhereInput! = {}): Int
  gUniCustomerPaymentCards(
    where: GUni_Customer_PaymentCardWhereInput! = {}
    orderBy: [GUni_Customer_PaymentCardOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Customer_PaymentCardWhereUniqueInput
  ): [GUni_Customer_PaymentCard!]
  gUni_Customer_PaymentCard(
    where: GUni_Customer_PaymentCardWhereUniqueInput!
  ): GUni_Customer_PaymentCard
  gUniCustomerPaymentCardsCount(where: GUni_Customer_PaymentCardWhereInput! = {}): Int
  gUniCategories(
    where: GUni_CategoryWhereInput! = {}
    orderBy: [GUni_CategoryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_CategoryWhereUniqueInput
  ): [GUni_Category!]
  gUni_Category(where: GUni_CategoryWhereUniqueInput!): GUni_Category
  gUniCategoriesCount(where: GUni_CategoryWhereInput! = {}): Int
  gUniPaymentPlans(
    where: GUni_PaymentPlanWhereInput! = {}
    orderBy: [GUni_PaymentPlanOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_PaymentPlanWhereUniqueInput
  ): [GUni_PaymentPlan!]
  gUni_PaymentPlan(where: GUni_PaymentPlanWhereUniqueInput!): GUni_PaymentPlan
  gUniPaymentPlansCount(where: GUni_PaymentPlanWhereInput! = {}): Int
  gUniPaymentPlanPayments(
    where: GUni_PaymentPlan_PaymentWhereInput! = {}
    orderBy: [GUni_PaymentPlan_PaymentOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_PaymentPlan_PaymentWhereUniqueInput
  ): [GUni_PaymentPlan_Payment!]
  gUni_PaymentPlan_Payment(
    where: GUni_PaymentPlan_PaymentWhereUniqueInput!
  ): GUni_PaymentPlan_Payment
  gUniPaymentPlanPaymentsCount(where: GUni_PaymentPlan_PaymentWhereInput! = {}): Int
  gUniProducts(
    where: GUni_ProductWhereInput! = {}
    orderBy: [GUni_ProductOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_ProductWhereUniqueInput
  ): [GUni_Product!]
  gUni_Product(where: GUni_ProductWhereUniqueInput!): GUni_Product
  gUniProductsCount(where: GUni_ProductWhereInput! = {}): Int
  gUniDiscounts(
    where: GUni_DiscountWhereInput! = {}
    orderBy: [GUni_DiscountOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_DiscountWhereUniqueInput
  ): [GUni_Discount!]
  gUni_Discount(where: GUni_DiscountWhereUniqueInput!): GUni_Discount
  gUniDiscountsCount(where: GUni_DiscountWhereInput! = {}): Int
  gUniPayLinks(
    where: GUni_PayLinkWhereInput! = {}
    orderBy: [GUni_PayLinkOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_PayLinkWhereUniqueInput
  ): [GUni_PayLink!]
  gUni_PayLink(where: GUni_PayLinkWhereUniqueInput!): GUni_PayLink
  gUniPayLinksCount(where: GUni_PayLinkWhereInput! = {}): Int
  gUniDisputes(
    where: GUni_DisputeWhereInput! = {}
    orderBy: [GUni_DisputeOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_DisputeWhereUniqueInput
  ): [GUni_Dispute!]
  gUni_Dispute(where: GUni_DisputeWhereUniqueInput!): GUni_Dispute
  gUniDisputesCount(where: GUni_DisputeWhereInput! = {}): Int
  gUniDisputeFiles(
    where: GUni_Dispute_FilesWhereInput! = {}
    orderBy: [GUni_Dispute_FilesOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Dispute_FilesWhereUniqueInput
  ): [GUni_Dispute_Files!]
  gUni_Dispute_Files(where: GUni_Dispute_FilesWhereUniqueInput!): GUni_Dispute_Files
  gUniDisputeFilesCount(where: GUni_Dispute_FilesWhereInput! = {}): Int
  gUniDisputeHistories(
    where: GUni_Dispute_HistoryWhereInput! = {}
    orderBy: [GUni_Dispute_HistoryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GUni_Dispute_HistoryWhereUniqueInput
  ): [GUni_Dispute_History!]
  gUni_Dispute_History(where: GUni_Dispute_HistoryWhereUniqueInput!): GUni_Dispute_History
  gUniDisputeHistoriesCount(where: GUni_Dispute_HistoryWhereInput! = {}): Int
  dashboardHourlyStatistics(
    where: DashboardHourlyStatisticWhereInput! = {}
    orderBy: [DashboardHourlyStatisticOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: DashboardHourlyStatisticWhereUniqueInput
  ): [DashboardHourlyStatistic!]
  dashboardHourlyStatistic(
    where: DashboardHourlyStatisticWhereUniqueInput!
  ): DashboardHourlyStatistic
  dashboardHourlyStatisticsCount(where: DashboardHourlyStatisticWhereInput! = {}): Int
  oTPS(
    where: OTPWhereInput! = {}
    orderBy: [OTPOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: OTPWhereUniqueInput
  ): [OTP!]
  oTP(where: OTPWhereUniqueInput!): OTP
  oTPSCount(where: OTPWhereInput! = {}): Int
  mFAS(
    where: MFAWhereInput! = {}
    orderBy: [MFAOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: MFAWhereUniqueInput
  ): [MFA!]
  mFA(where: MFAWhereUniqueInput!): MFA
  mFASCount(where: MFAWhereInput! = {}): Int
  notifications(
    where: NotificationWhereInput! = {}
    orderBy: [NotificationOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: NotificationWhereUniqueInput
  ): [Notification!]
  notification(where: NotificationWhereUniqueInput!): Notification
  notificationsCount(where: NotificationWhereInput! = {}): Int
  affiliationProfiles(
    where: AffiliationProfileWhereInput! = {}
    orderBy: [AffiliationProfileOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: AffiliationProfileWhereUniqueInput
  ): [AffiliationProfile!]
  affiliationProfile(where: AffiliationProfileWhereUniqueInput!): AffiliationProfile
  affiliationProfilesCount(where: AffiliationProfileWhereInput! = {}): Int
  affiliateEarnHistories(
    where: AffiliateEarnHistoryWhereInput! = {}
    orderBy: [AffiliateEarnHistoryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: AffiliateEarnHistoryWhereUniqueInput
  ): [AffiliateEarnHistory!]
  affiliateEarnHistory(where: AffiliateEarnHistoryWhereUniqueInput!): AffiliateEarnHistory
  affiliateEarnHistoriesCount(where: AffiliateEarnHistoryWhereInput! = {}): Int
  affiliationProfileKeyCodes(
    where: AffiliationProfileKeyCodeWhereInput! = {}
    orderBy: [AffiliationProfileKeyCodeOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: AffiliationProfileKeyCodeWhereUniqueInput
  ): [AffiliationProfileKeyCode!]
  affiliationProfileKeyCode(
    where: AffiliationProfileKeyCodeWhereUniqueInput!
  ): AffiliationProfileKeyCode
  affiliationProfileKeyCodesCount(where: AffiliationProfileKeyCodeWhereInput! = {}): Int
  gHLAccesses(
    where: GHLAccessWhereInput! = {}
    orderBy: [GHLAccessOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GHLAccessWhereUniqueInput
  ): [GHLAccess!]
  gHLAccess(where: GHLAccessWhereUniqueInput!): GHLAccess
  gHLAccessesCount(where: GHLAccessWhereInput! = {}): Int
  gHLSSOBindings(
    where: GHLSSOBindingWhereInput! = {}
    orderBy: [GHLSSOBindingOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GHLSSOBindingWhereUniqueInput
  ): [GHLSSOBinding!]
  gHLSSOBinding(where: GHLSSOBindingWhereUniqueInput!): GHLSSOBinding
  gHLSSOBindingsCount(where: GHLSSOBindingWhereInput! = {}): Int
  gHLPayTransactionMaps(
    where: GHLPayTransactionMapWhereInput! = {}
    orderBy: [GHLPayTransactionMapOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GHLPayTransactionMapWhereUniqueInput
  ): [GHLPayTransactionMap!]
  gHLPayTransactionMap(where: GHLPayTransactionMapWhereUniqueInput!): GHLPayTransactionMap
  gHLPayTransactionMapsCount(where: GHLPayTransactionMapWhereInput! = {}): Int
  gHLCachedCodes(
    where: GHLCachedCodeWhereInput! = {}
    orderBy: [GHLCachedCodeOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GHLCachedCodeWhereUniqueInput
  ): [GHLCachedCode!]
  gHLCachedCode(where: GHLCachedCodeWhereUniqueInput!): GHLCachedCode
  gHLCachedCodesCount(where: GHLCachedCodeWhereInput! = {}): Int
  groupSupportTickets(
    where: GroupSupportTicketWhereInput! = {}
    orderBy: [GroupSupportTicketOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupSupportTicketWhereUniqueInput
  ): [GroupSupportTicket!]
  groupSupportTicket(where: GroupSupportTicketWhereUniqueInput!): GroupSupportTicket
  groupSupportTicketsCount(where: GroupSupportTicketWhereInput! = {}): Int
  groupSupportMessages(
    where: GroupSupportMessageWhereInput! = {}
    orderBy: [GroupSupportMessageOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: GroupSupportMessageWhereUniqueInput
  ): [GroupSupportMessage!]
  groupSupportMessage(where: GroupSupportMessageWhereUniqueInput!): GroupSupportMessage
  groupSupportMessagesCount(where: GroupSupportMessageWhereInput! = {}): Int
  leadEntries(
    where: LeadEntryWhereInput! = {}
    orderBy: [LeadEntryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: LeadEntryWhereUniqueInput
  ): [LeadEntry!]
  leadEntry(where: LeadEntryWhereUniqueInput!): LeadEntry
  leadEntriesCount(where: LeadEntryWhereInput! = {}): Int
  sweepGlobalPays(
    where: SweepGlobalPayWhereInput! = {}
    orderBy: [SweepGlobalPayOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: SweepGlobalPayWhereUniqueInput
  ): [SweepGlobalPay!]
  sweepGlobalPay(where: SweepGlobalPayWhereUniqueInput!): SweepGlobalPay
  sweepGlobalPaysCount(where: SweepGlobalPayWhereInput! = {}): Int
  sweepEarnHistories(
    where: SweepEarnHistoryWhereInput! = {}
    orderBy: [SweepEarnHistoryOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: SweepEarnHistoryWhereUniqueInput
  ): [SweepEarnHistory!]
  sweepEarnHistory(where: SweepEarnHistoryWhereUniqueInput!): SweepEarnHistory
  sweepEarnHistoriesCount(where: SweepEarnHistoryWhereInput! = {}): Int
  serviceAPIAccounts(
    where: ServiceAPIAccountWhereInput! = {}
    orderBy: [ServiceAPIAccountOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: ServiceAPIAccountWhereUniqueInput
  ): [ServiceAPIAccount!]
  serviceAPIAccount(where: ServiceAPIAccountWhereUniqueInput!): ServiceAPIAccount
  serviceAPIAccountsCount(where: ServiceAPIAccountWhereInput! = {}): Int
  stripeDataApplications(
    where: StripeDataApplicationWhereInput! = {}
    orderBy: [StripeDataApplicationOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: StripeDataApplicationWhereUniqueInput
  ): [StripeDataApplication!]
  stripeDataApplication(where: StripeDataApplicationWhereUniqueInput!): StripeDataApplication
  stripeDataApplicationsCount(where: StripeDataApplicationWhereInput! = {}): Int
  eventWebhooks(
    where: EventWebhookWhereInput! = {}
    orderBy: [EventWebhookOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: EventWebhookWhereUniqueInput
  ): [EventWebhook!]
  eventWebhook(where: EventWebhookWhereUniqueInput!): EventWebhook
  eventWebhooksCount(where: EventWebhookWhereInput! = {}): Int
  hubspotAccesses(
    where: HubspotAccessWhereInput! = {}
    orderBy: [HubspotAccessOrderByInput!]! = []
    take: Int
    skip: Int! = 0
    cursor: HubspotAccessWhereUniqueInput
  ): [HubspotAccess!]
  hubspotAccess(where: HubspotAccessWhereUniqueInput!): HubspotAccess
  hubspotAccessesCount(where: HubspotAccessWhereInput! = {}): Int
  keystone: KeystoneMeta!
  authenticatedItem: AuthenticatedItem
  ErrorTest: String
  cache_get(input: Cache_getInput!): UserCache
  group_flags(input: Group_flagsInput!): GroupFlags
  group_getInviteInfo(input: Group_getInviteInfoInput!): GroupInviteInfo
  processor_tst_mcc(input: Processor_tst_mccInput!): MCCReturnv2
  processor_tst_bank_routing(input: Processor_tst_bank_routingInput!): BankRoutingReturnV2
  processor_tst_zip_search(input: Processor_tst_zip_searchInput!): ZipSearchReturnV2
  processor_tst_draft_signingurl(input: Processor_tst_draft_signingurlInput!): String
  processor_tst_draft_status(input: Processor_tst_draft_statusInput!): ApplicationStatusObject
  processor_globalpay_legalEntityTypes(
    input: Processor_globalpay_legalEntityTypesInput!
  ): LegalEntityTypes
  processor_globalpay_draft_signingurl(input: Processor_globalpay_draft_signingurlInput!): String
  processor_globalpay_draft_status(
    input: Processor_globalpay_draft_statusInput!
  ): ApplicationStatusObject
  processor_aur_mcc(input: Processor_aur_mccInput!): MCCReturn
  processor_aur_bank_routing(input: Processor_aur_bank_routingInput!): BankRoutingReturn
  processor_aur_zip_search(input: Processor_aur_zip_searchInput!): ZipSearchReturn
  processor_aur_draft_signingurl(input: Processor_aur_draft_signingurlInput!): String
  processor_aur_draft_status(input: Processor_aur_draft_statusInput!): ApplicationStatusObject
  processor_aur_draft_uploads(input: Processor_aur_draft_uploadsInput!): AURFileUploadDataList
  processor_draft_signingurl(input: Processor_draft_signingurlInput!): String
  processor_draft_status(input: Processor_draft_statusInput!): ApplicationStatusObject
  processor_draft_documentGet(input: Processor_draft_documentGetInput!): DocumentGetReturn
  processor_draft_documentList(input: Processor_draft_documentListInput!): DocumentListReturn
  processor_account_status(input: Processor_account_statusInput!): ProcessorAccountStatus
  gateway_test_transactions(input: Gateway_test_transactionsInput!): GatewayTSTTransactionsOutput
  gateway_test_transaction(input: Gateway_test_transactionInput!): GatewayTSTTransactionOutput
  gateway_test_batches(input: Gateway_test_batchesInput!): GatewayTSTBatchesOutput
  gateway_test_batch(input: Gateway_test_batchInput!): GatewayTSTBatchOutput
  gateway_test_deposits(input: Gateway_test_depositsInput!): GatewayTSTDepositsOutput
  gateway_test_deposit(input: Gateway_test_depositInput!): GatewayTSTDepositOutput
  gateway_test_disputes(input: Gateway_test_disputesInput!): GatewayTSTDisputesOutput
  gateway_test_dispute(input: Gateway_test_disputeInput!): GatewayTSTDisputeOutput
  gateway_test_customers(input: Gateway_test_customersInput!): GatewayTSTCustomersOutput
  gateway_test_customer(input: Gateway_test_customerInput!): GatewayTSTCustomerOutput
  gateway_test_paymentPlans(input: Gateway_test_paymentPlansInput!): GatewayTSTPaymentPlansOutput
  gateway_test_paymentPlan(input: Gateway_test_paymentPlanInput!): GatewayTSTPaymentPlanOutput
  gateway_test_scheduled(input: Gateway_test_scheduledInput!): GatewayTSTScheduledOutput
  gateway_test_category(input: Gateway_test_categoryInput!): GatewayTSTCategoryOutput
  gateway_test_categories(input: Gateway_test_categoriesInput!): GatewayTSTCategoriesOutput
  gateway_test_products(input: Gateway_test_productsInput!): GatewayTSTProductsOutput
  gateway_test_product(input: Gateway_test_productInput!): GatewayTSTProductOutput
  gateway_test_discount(input: Gateway_test_discountInput!): GatewayTSTDiscountOutput
  gateway_test_discounts(input: Gateway_test_discountsInput!): GatewayTSTDiscountsOutput
  gateway_flpayset_processor_list(
    input: Gateway_flpayset_processor_listInput!
  ): GatewayFLPayProcessorListOutput
  gateway_transactions(input: Gateway_transactionsInput!): GatewayUNITransactionsOutput
  gateway_transaction(input: Gateway_transactionInput!): GatewayUNITransactionOutput
  gateway__transactionBatch(
    input: Gateway__transactionBatchInput!
  ): GatewayUNI_transactionBatchOutput
  gateway_batches(input: Gateway_batchesInput!): GatewayUNIBatchesOutput
  gateway_batch(input: Gateway_batchInput!): GatewayUNIBatchOutput
  gateway__batchBatch(input: Gateway__batchBatchInput!): GatewayUNI_batchBatchOutput
  gateway_deposits(input: Gateway_depositsInput!): GatewayUNIDepositsOutput
  gateway_deposit(input: Gateway_depositInput!): GatewayUNIDepositOutput
  gateway__depositBatch(input: Gateway__depositBatchInput!): GatewayUNI_depositBatchOutput
  gateway_disputes(input: Gateway_disputesInput!): GatewayUNIDisputesOutput
  gateway_dispute(input: Gateway_disputeInput!): GatewayUNIDisputeOutput
  gateway__disputeBatch(input: Gateway__disputeBatchInput!): GatewayUNI_disputeBatchOutput
  gateway_webhookEvents(input: Gateway_webhookEventsInput!): GatewayUNIWebhookEventsOutput
  gateway_customers(input: Gateway_customersInput!): GatewayUNICustomersOutput
  gateway_customer(input: Gateway_customerInput!): GatewayUNICustomerOutput
  gateway__customerBatch(input: Gateway__customerBatchInput!): GatewayUNI_customerBatchOutput
  gateway_paymentPlans(input: Gateway_paymentPlansInput!): GatewayUNIPaymentPlansOutput
  gateway_paymentPlan(input: Gateway_paymentPlanInput!): GatewayUNIPaymentPlanOutput
  gateway__paymentPlanBatch(
    input: Gateway__paymentPlanBatchInput!
  ): GatewayUNI_paymentPlanBatchOutput
  gateway_scheduled(input: Gateway_scheduledInput!): GatewayUNIScheduledOutput
  gateway_scheduledPayment(input: Gateway_scheduledPaymentInput!): GatewayUNIScheduledPaymentOutput
  gateway_accountFunds(input: Gateway_accountFundsInput!): GatewayUNIAccountFundsOutput
  gateway_category(input: Gateway_categoryInput!): GatewayUNICategoryOutput
  gateway_categories(input: Gateway_categoriesInput!): GatewayUNICategoriesOutput
  gateway__categoryBatch(input: Gateway__categoryBatchInput!): GatewayUNI_categoryBatchOutput
  gateway_products(input: Gateway_productsInput!): GatewayUNIProductsOutput
  gateway__productBatch(input: Gateway__productBatchInput!): GatewayUNI_productBatchOutput
  gateway_product(input: Gateway_productInput!): GatewayUNIProductOutput
  gateway_discount(input: Gateway_discountInput!): GatewayUNIDiscountOutput
  gateway_discounts(input: Gateway_discountsInput!): GatewayUNIDiscountsOutput
  gateway__discountBatch(input: Gateway__discountBatchInput!): GatewayUNI_discountBatchOutput
  gateway_payLink(input: Gateway_payLinkInput!): GatewayUNIPayLinkOutput
  gateway_payLinks(input: Gateway_payLinksInput!): GatewayUNIPayLinksOutput
  gateway__payLinkBatch(input: Gateway__payLinkBatchInput!): GatewayUNI_payLinkBatchOutput
  dashboard_get_summary(input: Dashboard_get_summaryInput!): DashboardSummaryOutput
  dashboard_location_summary(input: Dashboard_location_summaryInput!): LocationSummaryOutput
  address_search(input: Address_searchInput!): AddressSearchAutoCompleteResult
  affiliation_get: AffiliationProfile
  affiliation_getAffiliatedUsersWithMerchants(
    input: Affiliation_getAffiliatedUsersWithMerchantsInput!
  ): AffiliatedUsersWithMerchantsOutput
  ghl_api_getMerchantGHL(input: Ghl_api_getMerchantGHLInput!): GHLMerchantUserData
  ghl_api_getOrderDetails(input: Ghl_api_getOrderDetailsInput!): GetOrderDetailsData
  ghl_api_getTransactionDetails(
    input: Ghl_api_getTransactionDetailsInput!
  ): GetTransactionDetailsData
  ghl_api_getSubscriptionDetails(
    input: Ghl_api_getSubscriptionDetailsInput!
  ): GetSubscriptionDetailsData
  ghl_api_getPaymentPageData(input: Ghl_api_getPaymentPageDataInput!): GetPaymentPageDataReturn
  ghl_api_getPaymentPageDataUNI(
    input: Ghl_api_getPaymentPageDataUNIInput!
  ): GetPaymentPageDataUNIReturn
  ghl_auth_getSSOInfo(input: Ghl_auth_getSSOInfoInput!): GHLAuthSSOInfo
  ghl_auth_getIntegrationDetails(input: Ghl_auth_getIntegrationDetailsInput!): GHLIntegrationDetails
  ghl_auth_checkPendingInstall(
    input: Ghl_auth_checkPendingInstallInput!
  ): GHLCheckPendingInstallReturn
  ghl_auth_checkGHLAccess(input: Ghl_auth_checkGHLAccessInput!): GHLCheckAccessReturn
  ghl_sync_product_list(input: Ghl_sync_product_listInput!): GHLSyncProductList
  import_stripe_sample(input: Import_stripe_sampleInput!): ImportStripeSampleOutput
}

union AuthenticatedItem = User

type KeystoneMeta {
  adminMeta: KeystoneAdminMeta!
}

type KeystoneAdminMeta {
  lists: [KeystoneAdminUIListMeta!]!
  list(key: String!): KeystoneAdminUIListMeta
}

type KeystoneAdminUIListMeta {
  key: String!
  itemQueryName: String!
  listQueryName: String!
  hideCreate: Boolean!
  hideDelete: Boolean!
  path: String!
  label: String!
  singular: String!
  plural: String!
  description: String
  initialColumns: [String!]!
  pageSize: Int!
  labelField: String!
  fields: [KeystoneAdminUIFieldMeta!]!
  groups: [KeystoneAdminUIFieldGroupMeta!]!
  initialSort: KeystoneAdminUISort
  isHidden: Boolean!
  isSingleton: Boolean!
}

type KeystoneAdminUIFieldMeta {
  path: String!
  label: String!
  description: String
  isOrderable: Boolean!
  isFilterable: Boolean!
  isNonNull: [KeystoneAdminUIFieldMetaIsNonNull!]
  fieldMeta: JSON
  viewsIndex: Int!
  customViewsIndex: Int
  createView: KeystoneAdminUIFieldMetaCreateView!
  listView: KeystoneAdminUIFieldMetaListView!
  itemView(id: ID): KeystoneAdminUIFieldMetaItemView
  search: QueryMode
}

enum KeystoneAdminUIFieldMetaIsNonNull {
  read
  create
  update
}

type KeystoneAdminUIFieldMetaCreateView {
  fieldMode: KeystoneAdminUIFieldMetaCreateViewFieldMode!
}

enum KeystoneAdminUIFieldMetaCreateViewFieldMode {
  edit
  hidden
}

type KeystoneAdminUIFieldMetaListView {
  fieldMode: KeystoneAdminUIFieldMetaListViewFieldMode!
}

enum KeystoneAdminUIFieldMetaListViewFieldMode {
  read
  hidden
}

type KeystoneAdminUIFieldMetaItemView {
  fieldMode: KeystoneAdminUIFieldMetaItemViewFieldMode
  fieldPosition: KeystoneAdminUIFieldMetaItemViewFieldPosition
}

enum KeystoneAdminUIFieldMetaItemViewFieldMode {
  edit
  read
  hidden
}

enum KeystoneAdminUIFieldMetaItemViewFieldPosition {
  form
  sidebar
}

type KeystoneAdminUIFieldGroupMeta {
  label: String!
  description: String
  fields: [KeystoneAdminUIFieldMeta!]!
}

type KeystoneAdminUISort {
  field: String!
  direction: KeystoneAdminUISortDirection!
}

enum KeystoneAdminUISortDirection {
  ASC
  DESC
}

input Cache_saveInput {
  keyword: String!
  data: String!
}

input Cache_getInput {
  keyword: String!
}

input Group_signGroupInput {
  groupID: String!
  signingURL: String!
}

type GroupFlagsFlags {
  key: String!
  description: String!
  category: String!
  name: String!
}

type GroupFlags {
  flags: [GroupFlagsFlags]!
}

input Group_flagsInput {
  q: String
}

type CreateInviteInfo {
  id: String!
}

input Group_createInviteInput {
  email: String!
  groupID: String!
  accessLevel: Float
  flags: [String]
}

type GroupInviteInfo {
  groupName: String!
  email: String!
  alreadySigned: Boolean!
}

input Group_getInviteInfoInput {
  inviteID: String!
}

input Group_claimInviteInput {
  inviteID: String!
}

input Group_declineInviteInput {
  inviteID: String!
}

type WebhookInfo {
  id: String!
  url: String!
  secret: String!
}

input Group_createWebhookInput {
  groupID: String!
  url: String!
  scope: String
}

type MCCReturnv2Items {
  id: String!
  description: String!
}

type MCCReturnv2 {
  items: [MCCReturnv2Items]
  count: Float
}

input Processor_tst_mccInput {
  pattern: String
  pageIndex: Float
  pageSize: Float
}

type BankRoutingReturnV2Data {
  rn: String!
  name: String!
}

type BankRoutingReturnV2 {
  data: BankRoutingReturnV2Data!
}

input Processor_tst_bank_routingInput {
  code: String!
}

type ZipSearchReturnV2Item {
  zip: String!
  latitude: Float!
  longitude: Float!
  city: String!
  state: String!
  country: String!
}

type ZipSearchReturnV2 {
  item: ZipSearchReturnV2Item
}

input Processor_tst_zip_searchInput {
  zipcode: String!
}

type ProcessorTSTApplicationCreate {
  testID: String!
  groupID: String!
}

input Processor_tst_draft_createInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_tst_draft_createInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_tst_draft_createInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_tst_draft_createInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_tst_draft_createInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_tst_draft_createInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_tst_draft_createInputDataAttestations {
  name: Processor_tst_draft_createInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_tst_draft_createInputDataStatus {
  message: String
}

input Processor_tst_draft_createInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_tst_draft_createInputData {
  businessInfo: Processor_tst_draft_createInputDataBusinessInfo
  transactionInfo: Processor_tst_draft_createInputDataTransactionInfo
  owners: [Processor_tst_draft_createInputDataOwners]
  files: [Processor_tst_draft_createInputDataFiles]
  bankInfo: Processor_tst_draft_createInputDataBankInfo
  attestations: [Processor_tst_draft_createInputDataAttestations]
  status: Processor_tst_draft_createInputDataStatus
  flags: Processor_tst_draft_createInputDataFlags
}

input Processor_tst_draft_createInput {
  data: Processor_tst_draft_createInputData!
}

type ProcessorTSTApplicationUpdate {
  testID: String!
  groupID: String!
}

input Processor_tst_draft_updateInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_tst_draft_updateInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_tst_draft_updateInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_tst_draft_updateInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_tst_draft_updateInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_tst_draft_updateInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_tst_draft_updateInputDataAttestations {
  name: Processor_tst_draft_updateInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_tst_draft_updateInputDataStatus {
  message: String
}

input Processor_tst_draft_updateInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_tst_draft_updateInputData {
  businessInfo: Processor_tst_draft_updateInputDataBusinessInfo
  transactionInfo: Processor_tst_draft_updateInputDataTransactionInfo
  owners: [Processor_tst_draft_updateInputDataOwners]
  files: [Processor_tst_draft_updateInputDataFiles]
  bankInfo: Processor_tst_draft_updateInputDataBankInfo
  attestations: [Processor_tst_draft_updateInputDataAttestations]
  status: Processor_tst_draft_updateInputDataStatus
  flags: Processor_tst_draft_updateInputDataFlags
}

input Processor_tst_draft_updateInput {
  groupId: String!
  data: Processor_tst_draft_updateInputData!
}

type TSTSubmission {
  urlForSigning: String!
  applicationId: String!
  applicationNumber: String!
}

input Processor_tst_draft_submitInput {
  groupId: String!
}

input Processor_tst_draft_signingurlInput {
  groupId: String!
}

type ApplicationStatusObjectBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

type ApplicationStatusObjectTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

type ApplicationStatusObjectOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

type ApplicationStatusObjectFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

type ApplicationStatusObjectBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum ApplicationStatusObjectAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

type ApplicationStatusObjectAttestations {
  name: ApplicationStatusObjectAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

type ApplicationStatusObjectStatus {
  message: String
}

type ApplicationStatusObjectFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

type ApplicationStatusObject {
  businessInfo: ApplicationStatusObjectBusinessInfo
  transactionInfo: ApplicationStatusObjectTransactionInfo
  owners: [ApplicationStatusObjectOwners]
  files: [ApplicationStatusObjectFiles]
  bankInfo: ApplicationStatusObjectBankInfo
  attestations: [ApplicationStatusObjectAttestations]
  status: ApplicationStatusObjectStatus
  flags: ApplicationStatusObjectFlags
}

input Processor_tst_draft_statusInput {
  groupId: String!
}

type LegalEntityTypesItems {
  id: String!
  description: String!
}

type LegalEntityTypes {
  items: [LegalEntityTypesItems]
  count: Float
}

input Processor_globalpay_legalEntityTypesInput {
  pattern: String
  pageIndex: Float
  pageSize: Float
}

type ProcessorGloPayApplicationCreate {
  applicationID: String!
  groupID: String!
}

input Processor_globalpay_draft_createInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_globalpay_draft_createInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_globalpay_draft_createInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_globalpay_draft_createInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_globalpay_draft_createInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_globalpay_draft_createInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_globalpay_draft_createInputDataAttestations {
  name: Processor_globalpay_draft_createInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_globalpay_draft_createInputDataStatus {
  message: String
}

input Processor_globalpay_draft_createInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_globalpay_draft_createInputData {
  businessInfo: Processor_globalpay_draft_createInputDataBusinessInfo
  transactionInfo: Processor_globalpay_draft_createInputDataTransactionInfo
  owners: [Processor_globalpay_draft_createInputDataOwners]
  files: [Processor_globalpay_draft_createInputDataFiles]
  bankInfo: Processor_globalpay_draft_createInputDataBankInfo
  attestations: [Processor_globalpay_draft_createInputDataAttestations]
  status: Processor_globalpay_draft_createInputDataStatus
  flags: Processor_globalpay_draft_createInputDataFlags
}

input Processor_globalpay_draft_createInput {
  data: Processor_globalpay_draft_createInputData!
}

type ProcessorGloPayApplicationUpdate {
  applicationID: String!
  groupID: String!
}

input Processor_globalpay_draft_updateInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_globalpay_draft_updateInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_globalpay_draft_updateInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_globalpay_draft_updateInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_globalpay_draft_updateInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_globalpay_draft_updateInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_globalpay_draft_updateInputDataAttestations {
  name: Processor_globalpay_draft_updateInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_globalpay_draft_updateInputDataStatus {
  message: String
}

input Processor_globalpay_draft_updateInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_globalpay_draft_updateInputData {
  businessInfo: Processor_globalpay_draft_updateInputDataBusinessInfo
  transactionInfo: Processor_globalpay_draft_updateInputDataTransactionInfo
  owners: [Processor_globalpay_draft_updateInputDataOwners]
  files: [Processor_globalpay_draft_updateInputDataFiles]
  bankInfo: Processor_globalpay_draft_updateInputDataBankInfo
  attestations: [Processor_globalpay_draft_updateInputDataAttestations]
  status: Processor_globalpay_draft_updateInputDataStatus
  flags: Processor_globalpay_draft_updateInputDataFlags
}

input Processor_globalpay_draft_updateInput {
  groupId: String!
  data: Processor_globalpay_draft_updateInputData!
}

type ProcessorGloPaySubmission {
  urlForSigning: String!
  applicationId: String!
  applicationNumber: String!
}

input Processor_globalpay_draft_submitInput {
  groupId: String!
}

input Processor_globalpay_draft_signingurlInput {
  groupId: String!
}

type GLPAYApplication {
  processor_globalpay_signingUrl: String
  processor_globalpay_applicationStatus: ApplicationStatusObject
}

input Processor_globalpay_draft_statusInput {
  groupId: String!
}

type MCCReturnItems {
  id: String!
  mccCode: String!
  description: String!
}

type MCCReturn {
  items: [MCCReturnItems]
  count: Float
}

input Processor_aur_mccInput {
  pattern: String
  pageIndex: Float
  pageSize: Float
}

type BankRoutingReturnItems {
  routing: String!
  bank: String!
}

type BankRoutingReturn {
  items: [BankRoutingReturnItems]
  count: Float
}

input Processor_aur_bank_routingInput {
  pattern: String
  pageIndex: Float
  pageSize: Float
}

type ZipSearchReturnItem {
  zip: String!
  latitude: Float!
  longitude: Float!
  city: String!
  state: String!
  country: String!
}

type ZipSearchReturn {
  item: ZipSearchReturnItem
}

input Processor_aur_zip_searchInput {
  zipcode: String!
}

type ProcessorAURApplicationCreate {
  auroraID: String!
  groupID: String!
}

input Processor_aur_draft_createInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_aur_draft_createInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_aur_draft_createInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_aur_draft_createInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_aur_draft_createInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_aur_draft_createInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_aur_draft_createInputDataAttestations {
  name: Processor_aur_draft_createInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_aur_draft_createInputDataStatus {
  message: String
}

input Processor_aur_draft_createInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_aur_draft_createInputData {
  businessInfo: Processor_aur_draft_createInputDataBusinessInfo
  transactionInfo: Processor_aur_draft_createInputDataTransactionInfo
  owners: [Processor_aur_draft_createInputDataOwners]
  files: [Processor_aur_draft_createInputDataFiles]
  bankInfo: Processor_aur_draft_createInputDataBankInfo
  attestations: [Processor_aur_draft_createInputDataAttestations]
  status: Processor_aur_draft_createInputDataStatus
  flags: Processor_aur_draft_createInputDataFlags
}

input Processor_aur_draft_createInput {
  data: Processor_aur_draft_createInputData!
}

type ProcessorAURApplicationUpdate {
  auroraID: String!
  groupID: String!
}

input Processor_aur_draft_updateInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_aur_draft_updateInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_aur_draft_updateInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_aur_draft_updateInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_aur_draft_updateInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_aur_draft_updateInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_aur_draft_updateInputDataAttestations {
  name: Processor_aur_draft_updateInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_aur_draft_updateInputDataStatus {
  message: String
}

input Processor_aur_draft_updateInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_aur_draft_updateInputData {
  businessInfo: Processor_aur_draft_updateInputDataBusinessInfo
  transactionInfo: Processor_aur_draft_updateInputDataTransactionInfo
  owners: [Processor_aur_draft_updateInputDataOwners]
  files: [Processor_aur_draft_updateInputDataFiles]
  bankInfo: Processor_aur_draft_updateInputDataBankInfo
  attestations: [Processor_aur_draft_updateInputDataAttestations]
  status: Processor_aur_draft_updateInputDataStatus
  flags: Processor_aur_draft_updateInputDataFlags
}

input Processor_aur_draft_updateInput {
  groupId: String!
  data: Processor_aur_draft_updateInputData!
}

type AURSubmission {
  urlForSigning: String!
  applicationId: String!
  applicationNumber: String!
}

input Processor_aur_draft_submitInput {
  groupId: String!
}

input Processor_aur_draft_file_deleteInput {
  groupId: String!
  fileID: String!
}

input Processor_aur_draft_signingurlInput {
  groupId: String!
}

input Processor_aur_draft_statusInput {
  groupId: String!
}

type AURFileUploadDataList {
  files: [AuraFileUploadData]
}

type AuraFileUploadData {
  id: String
  name: String
  extension: String
  contentType: String
  type: String
  createdAt: String
  deletedAt: String
  createdById: String
}

input Processor_aur_draft_uploadsInput {
  groupId: String!
}

type ProcessorApplicationCreate {
  submissionID: String!
  groupID: String!
}

input Processor_draft_createInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_draft_createInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_draft_createInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_draft_createInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_draft_createInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_draft_createInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_draft_createInputDataAttestations {
  name: Processor_draft_createInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_draft_createInputDataStatus {
  message: String
}

input Processor_draft_createInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_draft_createInputData {
  businessInfo: Processor_draft_createInputDataBusinessInfo
  transactionInfo: Processor_draft_createInputDataTransactionInfo
  owners: [Processor_draft_createInputDataOwners]
  files: [Processor_draft_createInputDataFiles]
  bankInfo: Processor_draft_createInputDataBankInfo
  attestations: [Processor_draft_createInputDataAttestations]
  status: Processor_draft_createInputDataStatus
  flags: Processor_draft_createInputDataFlags
}

input Processor_draft_createInput {
  data: Processor_draft_createInputData!
}

type ProcessorApplicationUpdate {
  submissionID: String!
  groupID: String!
}

input Processor_draft_updateInputDataBusinessInfo {
  legalBusinessName: String
  typeOfBusiness: String
  dbaName: String
  ein: String
  dateBusinessEstablished: String
  businessEmail: String
  businessPhone: String
  businessPhoneCountryCode: String
  website: String
  customerServicePhone: String
  customerServicePhoneCountryCode: String
  street: String
  zipCode: String
  city: String
  state: String
  country: String
  differentLegalAddress: Boolean
  legalMailingStreet: String
  legalMailingZipCode: String
  legalMailingCity: String
  legalMailingState: String
  legalMailingCountry: String
}

input Processor_draft_updateInputDataTransactionInfo {
  businessCategory: String
  description: String
  swipe: Float
  keyed: Float
  ecommerce: Float
  avgTransactionAmount: Float
  highestTransactionAmount: Float
  grossMonthlySalesVolume: Float
  amexAvgTransactionAmount: Float
  amexHighestTransactionAmount: Float
  amexGrossMonthlySalesVolume: Float
}

input Processor_draft_updateInputDataOwners {
  isControlOwner: Boolean
  firstName: String
  lastName: String
  title: String
  ownershipPercentage: Float
  phoneNumber: String
  phoneNumberCountryCode: String
  homeAddress: String
  country: String
  state: String
  city: String
  zipCode: String
  dateOfBirth: String
  ssn: String
  email: String
}

input Processor_draft_updateInputDataFiles {
  id: String!
  name: String!
  purpose: String
  category: String
  mimetype: String!
  b64: String!
  submittedOn: String
}

input Processor_draft_updateInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

enum Processor_draft_updateInputDataAttestationsName {
  propay_sub_merchant_terms_and_conditions
  beneficial_owner
}

input Processor_draft_updateInputDataAttestations {
  name: Processor_draft_updateInputDataAttestationsName
  ip_address: String
  time_of_attestation: String
  url: String
  signature: String
}

input Processor_draft_updateInputDataStatus {
  message: String
}

input Processor_draft_updateInputDataFlags {
  excludeCC: Boolean
  excludeACH: Boolean
}

input Processor_draft_updateInputData {
  businessInfo: Processor_draft_updateInputDataBusinessInfo
  transactionInfo: Processor_draft_updateInputDataTransactionInfo
  owners: [Processor_draft_updateInputDataOwners]
  files: [Processor_draft_updateInputDataFiles]
  bankInfo: Processor_draft_updateInputDataBankInfo
  attestations: [Processor_draft_updateInputDataAttestations]
  status: Processor_draft_updateInputDataStatus
  flags: Processor_draft_updateInputDataFlags
}

input Processor_draft_updateInput {
  groupId: String!
  data: Processor_draft_updateInputData!
}

type ProcessorSubmission {
  urlForSigning: String!
  applicationId: String!
  applicationNumber: String!
}

input Processor_draft_submitInput {
  groupId: String!
}

input Processor_draft_signingurlInput {
  groupId: String!
}

input Processor_draft_statusInput {
  groupId: String!
}

type DocumentUploadReturnDocumentID {
  name: String!
  id: String!
}

type DocumentUploadReturn {
  documentID: [DocumentUploadReturnDocumentID]!
}

input Processor_draft_documentUploadInputFiles {
  name: String!
  fileData: String!
  purpose: String
  category: String
  mimeType: String!
}

input Processor_draft_documentUploadInput {
  groupId: String!
  files: [Processor_draft_documentUploadInputFiles]!
}

type DocumentGetReturn {
  name: String!
  fileData: String!
  purpose: String
  category: String
  mimeType: String!
  id: String!
  groupID: String!
  submissionID: String!
}

input Processor_draft_documentGetInput {
  groupId: String!
  fileID: String!
}

type DocumentListReturnFiles {
  name: String!
  fileData: String!
  purpose: String
  category: String
  mimeType: String!
  id: String!
  groupID: String!
  submissionID: String!
}

type DocumentListReturn {
  groupID: String!
  submissionID: String!
  files: [DocumentListReturnFiles]!
}

input Processor_draft_documentListInput {
  groupId: String!
}

type DocumentForwardReturn {
  success: Boolean!
}

input Processor_draft_documentForwardInput {
  groupId: String!
  submissionID: String!
  fileIDs: [String]!
}

type ProcessorPayoutAccountUpdate {
  submissionID: String!
  groupID: String!
}

input Processor_account_payoutUpdateInputDataBankInfo {
  routingNumber: String
  accountNumber: String
  bankName: String
  nameOnAccount: String
}

input Processor_account_payoutUpdateInputData {
  bankInfo: Processor_account_payoutUpdateInputDataBankInfo
  otpSid: String
}

input Processor_account_payoutUpdateInput {
  groupId: String!
  data: Processor_account_payoutUpdateInputData!
}

type ProcessorAccountFeatureAdd {
  submissionID: String!
  groupID: String!
}

input Processor_account_addAccountFeatureInput {
  groupId: String!
  feature: String!
}

type ProcessorAccountStatusBank {
  holder_type: String
  last4: String
  bank_code: String
  bank_name: String
}

type ProcessorAccountStatus {
  bank: ProcessorAccountStatusBank!
  status: String
  status2: String
  pricing_profile: String
}

input Processor_account_statusInput {
  groupId: String!
}

type GPHFTokenResponse {
  token: String!
  merchantID: String!
}

input Gphf_tokenInput {
  merchantID: String
}

type TsepManifestResponse {
  deviceID: String!
  manifestKey: String!
  url: String!
}

type TsepCardReturnType {
  responseCode: String!
  status: String!
  message: String!
  tsepToken: String!
  maskedCardNumber: String!
  cardType: String!
  transactionID: String!
  expirationDate: String!
  cvv2: String!
  cardHolderName: String!
  zipCode: String!
}

type TsepVerificationResponse {
  status: String
  code: String
  message: String
  authCode: String
  cardType: String
  addressVerificationCode: String
  maskedCardNumber: String
  expirationDate: String
}

input Tsep_verifyInputCard {
  type: String!
  token: String!
  cvv: String!
  expiry: String!
  medium: String
  cof: Boolean
}

input Tsep_verifyInputAvs {
  firstName: String!
  lastName: String!
  addressLine1: String
  zipCode: String
}

input Tsep_verifyInput {
  card: Tsep_verifyInputCard!
  avs: Tsep_verifyInputAvs!
}

type PropayLinksResponseAction {
  id: String!
  type: String!
  time_created: String!
  result_code: String!
  app_id: String!
  app_name: String!
}

type PropayLinksResponse {
  id: String!
  account_name: String!
  status: String!
  type: String!
  function: String!
  time_created: String!
  time_last_updated: String!
  url: String!
  action: PropayLinksResponseAction!
}

input Propay_linksInputBody {
  type: String!
  function: String!
  hostURL: String!
  ip_address: String!
  ip_subnet: String
}

input Propay_linksInput {
  groupID: String!
  body: Propay_linksInputBody!
}

type PropayStatusResponseComponents {
  name: String!
  status: String!
  operational: Boolean!
}

type PropayStatusResponse {
  allRunning: Boolean!
  components: [PropayStatusResponseComponents]!
}

type GatewayTSTTransactionsOutputData {
  transactionID: String
  location: String
  date: String
  method: String
  customer: String
  brand: String
  last4: String
  amount: Float
  status: String
}

type GatewayTSTTransactionsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTTransactionsOutputPage {
  total: Float
  range: GatewayTSTTransactionsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTTransactionsOutput {
  data: [GatewayTSTTransactionsOutputData]
  page: GatewayTSTTransactionsOutputPage
}

enum Gateway_test_transactionsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_transactionsInputDataPageSort {
  field: String!
  order: Gateway_test_transactionsInputDataPageSortOrder!
}

enum Gateway_test_transactionsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_transactionsInputDataPageFilter {
  field: String!
  operation: Gateway_test_transactionsInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_transactionsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_transactionsInputDataPageSort
  filter: [Gateway_test_transactionsInputDataPageFilter]
  search: String
}

input Gateway_test_transactionsInputData {
  page: Gateway_test_transactionsInputDataPage
}

input Gateway_test_transactionsInput {
  groupID: String!
  data: Gateway_test_transactionsInputData!
}

type GatewayTSTTransactionOutputBreakdown {
  discount: Float
  tax: Float
  shipping: Float
  fees: Float
  actualFees: Float
  tip: Float
  subtotal: Float
  subscriptionTotal: Float
  rawTotal: Float
  total: Float
  expectedTotal: Float
}

type GatewayTSTTransactionOutputTransactionHistory {
  date: String
  status: String
  response: String
  avs: String
  cvv: String
}

type GatewayTSTTransactionOutputRelatedTransactions {
  transactionID: String
  amount: Float
  status: String
  date: String
  paymentType: String
}

type GatewayTSTTransactionOutputPurchaseDetails {
  productName: String
  productID: String
  quantity: Float
  price: Float
  discount: Float
  description: String
}

type GatewayTSTTransactionOutput {
  transactionID: String
  amount: Float
  status: String
  date: String
  brand: String
  createdBy: String
  paymentType: String
  method: String
  batchID: String
  paymentPlan: String
  source: String
  authCode: String
  entryMethod: String
  tokenSource: String
  gsa: String
  emv: String
  last4: String
  customerName: String
  customerID: String
  customerEmail: String
  customerPhone: String
  customerCountry: String
  customerBillingAddress: String
  commercialLevel: String
  result: String
  message: String
  brandReference: String
  breakdown: GatewayTSTTransactionOutputBreakdown
  transactionHistory: [GatewayTSTTransactionOutputTransactionHistory]
  relatedTransactions: [GatewayTSTTransactionOutputRelatedTransactions]
  purchaseDetails: [GatewayTSTTransactionOutputPurchaseDetails]
}

input Gateway_test_transactionInputData {
  transactionID: String!
}

input Gateway_test_transactionInput {
  groupID: String!
  data: Gateway_test_transactionInputData!
}

type GatewayTSTBatchesOutputData {
  batchID: String
  location: String
  date: String
  amount: Float
  status: String
}

type GatewayTSTBatchesOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTBatchesOutputPage {
  total: Float
  range: GatewayTSTBatchesOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTBatchesOutput {
  data: [GatewayTSTBatchesOutputData]
  page: GatewayTSTBatchesOutputPage
}

enum Gateway_test_batchesInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_batchesInputDataPageSort {
  field: String!
  order: Gateway_test_batchesInputDataPageSortOrder!
}

enum Gateway_test_batchesInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_batchesInputDataPageFilter {
  field: String!
  operation: Gateway_test_batchesInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_batchesInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_batchesInputDataPageSort
  filter: [Gateway_test_batchesInputDataPageFilter]
  search: String
}

input Gateway_test_batchesInputData {
  page: Gateway_test_batchesInputDataPage
}

input Gateway_test_batchesInput {
  groupID: String!
  data: Gateway_test_batchesInputData!
}

type GatewayTSTBatchOutputTransactions {
  transactionID: String
  date: String
  method: String
  name: String
  last4: String
  customer: String
  amount: Float
  brand: String
  status: String
}

type GatewayTSTBatchOutput {
  batchID: String
  status: String
  date: String
  location: String
  locationID: String
  amount: Float
  transactions: [GatewayTSTBatchOutputTransactions]
}

input Gateway_test_batchInputData {
  batchID: String!
}

input Gateway_test_batchInput {
  groupID: String!
  data: Gateway_test_batchInputData!
}

type GatewayTSTCloseBatchOutput {
  batchID: String
  status: String
}

input Gateway_test_closeBatchInputData {
  batchID: String!
}

input Gateway_test_closeBatchInput {
  groupID: String!
  data: Gateway_test_closeBatchInputData!
}

type GatewayTSTDepositsOutputData {
  depositID: String
  location: String
  date: String
  amount: Float
  status: String
}

type GatewayTSTDepositsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTDepositsOutputPage {
  total: Float
  range: GatewayTSTDepositsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTDepositsOutput {
  data: [GatewayTSTDepositsOutputData]
  page: GatewayTSTDepositsOutputPage
}

enum Gateway_test_depositsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_depositsInputDataPageSort {
  field: String!
  order: Gateway_test_depositsInputDataPageSortOrder!
}

enum Gateway_test_depositsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_depositsInputDataPageFilter {
  field: String!
  operation: Gateway_test_depositsInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_depositsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_depositsInputDataPageSort
  filter: [Gateway_test_depositsInputDataPageFilter]
  search: String
}

input Gateway_test_depositsInputData {
  page: Gateway_test_depositsInputDataPage
}

input Gateway_test_depositsInput {
  groupID: String!
  data: Gateway_test_depositsInputData!
}

type GatewayTSTDepositOutputBatches {
  batchID: String
  depositID: String
  total: Float
}

type GatewayTSTDepositOutputTransactions {
  transactionID: String
  date: String
  method: String
  name: String
  last4: String
  customer: String
  amount: Float
  brand: String
  status: String
}

type GatewayTSTDepositOutput {
  depositID: String
  date: String
  last4: String
  amount: Float
  sales: Float
  fees: Float
  status: String
  totalDeposit: Float
  batches: [GatewayTSTDepositOutputBatches]
  transactions: [GatewayTSTDepositOutputTransactions]
}

input Gateway_test_depositInputData {
  depositID: String!
}

input Gateway_test_depositInput {
  groupID: String!
  data: Gateway_test_depositInputData!
}

type GatewayTSTDisputesOutputData {
  caseID: String
  location: String
  date: String
  reason: String
  amount: Float
  brand: String
  transactionID: String
  status: String
}

type GatewayTSTDisputesOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTDisputesOutputPage {
  total: Float
  range: GatewayTSTDisputesOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTDisputesOutput {
  data: [GatewayTSTDisputesOutputData]
  page: GatewayTSTDisputesOutputPage
}

enum Gateway_test_disputesInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_disputesInputDataPageSort {
  field: String!
  order: Gateway_test_disputesInputDataPageSortOrder!
}

enum Gateway_test_disputesInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_disputesInputDataPageFilter {
  field: String!
  operation: Gateway_test_disputesInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_disputesInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_disputesInputDataPageSort
  filter: [Gateway_test_disputesInputDataPageFilter]
  search: String
}

input Gateway_test_disputesInputData {
  page: Gateway_test_disputesInputDataPage
}

input Gateway_test_disputesInput {
  groupID: String!
  data: Gateway_test_disputesInputData!
}

type GatewayTSTDisputeOutputProducts {
  productName: String
  productID: String
  quantity: Float
  price: Float
  discount: Float
  description: String
}

type GatewayTSTDisputeOutputHistory {
  action: String
  body: String
  actor: String
  createdAt: String
}

type GatewayTSTDisputeOutputFiles {
  fileUrl: String
  purpose: String
  fileFormat: String
  createdAt: String
  submittedAt: String
}

type GatewayTSTDisputeOutput {
  disputeID: String
  caseID: String
  location: String
  date: String
  reason: String
  amount: Float
  brand: String
  transactionID: String
  paymentType: String
  createdBy: String
  method: String
  batchID: String
  paymentPlan: String
  source: String
  authCode: String
  entryMethod: String
  tokenSource: String
  gsa: String
  emv: String
  last4: String
  customerID: String
  name: String
  email: String
  phone: String
  country: String
  billingAddress: String
  products: [GatewayTSTDisputeOutputProducts]
  history: [GatewayTSTDisputeOutputHistory]
  files: [GatewayTSTDisputeOutputFiles]
  status: String
}

input Gateway_test_disputeInputData {
  caseID: String!
}

input Gateway_test_disputeInput {
  groupID: String!
  data: Gateway_test_disputeInputData!
}

type GatewayTSTCustomersOutputData {
  id: String
  isDefault: Boolean
  last4: String
  brand: String
  expires: String
  name: String
  city_state: String
  zip: String
  email: String
  phone: String
}

type GatewayTSTCustomersOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTCustomersOutputPage {
  total: Float
  range: GatewayTSTCustomersOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTCustomersOutput {
  data: [GatewayTSTCustomersOutputData]
  page: GatewayTSTCustomersOutputPage
}

enum Gateway_test_customersInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_customersInputDataPageSort {
  field: String!
  order: Gateway_test_customersInputDataPageSortOrder!
}

enum Gateway_test_customersInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_customersInputDataPageFilter {
  field: String!
  operation: Gateway_test_customersInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_customersInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_customersInputDataPageSort
  filter: [Gateway_test_customersInputDataPageFilter]
  search: String
}

input Gateway_test_customersInputData {
  page: Gateway_test_customersInputDataPage
}

input Gateway_test_customersInput {
  groupID: String!
  data: Gateway_test_customersInputData!
}

type GatewayTSTAddCustomerOutput {
  customerID: String
  paymentCardID: String
}

input Gateway_test_addCustomerInputDataFormGpecomm {
  id: String
  cvv: String
}

input Gateway_test_addCustomerInputDataFormCard {
  cvc: String
  expiryDate: String
  cardToken: String
}

input Gateway_test_addCustomerInputDataFormAch {
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
}

input Gateway_test_addCustomerInputDataForm {
  nameOnCard: String!
  email: String!
  phone: String!
  billingAddress: String!
  billingCity: String!
  billingState: String!
  billingZip: String!
  billingCountry: String!
  skipMultiTokenConvert: Boolean
  gpecomm: Gateway_test_addCustomerInputDataFormGpecomm
  card: Gateway_test_addCustomerInputDataFormCard
  ach: Gateway_test_addCustomerInputDataFormAch
}

input Gateway_test_addCustomerInputData {
  form: Gateway_test_addCustomerInputDataForm!
}

input Gateway_test_addCustomerInput {
  groupID: String!
  data: Gateway_test_addCustomerInputData!
}

type GatewayTSTUpdateCustomerOutput {
  customerID: String
}

input Gateway_test_updateCustomerInputDataForm {
  nameOnCard: String!
  email: String!
  phone: String!
  billingAddress: String!
  billingCity: String!
  billingState: String!
  billingZip: String!
  billingCountry: String!
}

input Gateway_test_updateCustomerInputData {
  customerID: String!
  form: Gateway_test_updateCustomerInputDataForm!
}

input Gateway_test_updateCustomerInput {
  groupID: String!
  data: Gateway_test_updateCustomerInputData!
}

type GatewayTSTAddPaymentMethodOutput {
  customerID: String
  paymentCardID: String
}

input Gateway_test_addPaymentMethodInputDataFormCard {
  cvc: String
  expiryDate: String
  cardToken: String
}

input Gateway_test_addPaymentMethodInputDataFormAch {
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
}

input Gateway_test_addPaymentMethodInputDataFormGpecomm {
  id: String
  cvv: String
}

input Gateway_test_addPaymentMethodInputDataForm {
  setAsDefault: Boolean
  skipMultiTokenConvert: Boolean
  card: Gateway_test_addPaymentMethodInputDataFormCard
  ach: Gateway_test_addPaymentMethodInputDataFormAch
  gpecomm: Gateway_test_addPaymentMethodInputDataFormGpecomm
}

input Gateway_test_addPaymentMethodInputData {
  customerID: String!
  form: Gateway_test_addPaymentMethodInputDataForm!
}

input Gateway_test_addPaymentMethodInput {
  groupID: String!
  data: Gateway_test_addPaymentMethodInputData!
}

type GatewayTSTDeletePaymentMethodOutput {
  customerID: String
}

input Gateway_test_deletePaymentMethodInputData {
  customerID: String!
  cardID: String!
}

input Gateway_test_deletePaymentMethodInput {
  groupID: String!
  data: Gateway_test_deletePaymentMethodInputData!
}

type GatewayTSTCustomerOutputPaymentCards {
  cardID: String
  type: String
  isDefault: Boolean
  last4: String
  brand: String
  expires: String
  cvc: String
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
  gpEcommID: String
}

type GatewayTSTCustomerOutput {
  id: String
  nameOnCard: String
  email: String
  phone: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  paymentCards: [GatewayTSTCustomerOutputPaymentCards]
}

input Gateway_test_customerInputData {
  customerID: String!
}

input Gateway_test_customerInput {
  groupID: String!
  data: Gateway_test_customerInputData!
}

type GatewayTSTDeleteCustomerOutput {
  customerID: String
}

input Gateway_test_deleteCustomerInputData {
  customerID: String!
}

input Gateway_test_deleteCustomerInput {
  groupID: String!
  data: Gateway_test_deleteCustomerInputData!
}

type GatewayTSTPaymentPlansOutputData {
  planID: String
  planName: String
  startDate: String
  customerName: String
  customerID: String
  amount: Float
  last4: String
  expires: String
  duration: String
  status: String
}

type GatewayTSTPaymentPlansOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTPaymentPlansOutputPage {
  total: Float
  range: GatewayTSTPaymentPlansOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTPaymentPlansOutput {
  data: [GatewayTSTPaymentPlansOutputData]
  page: GatewayTSTPaymentPlansOutputPage
}

enum Gateway_test_paymentPlansInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_paymentPlansInputDataPageSort {
  field: String!
  order: Gateway_test_paymentPlansInputDataPageSortOrder!
}

enum Gateway_test_paymentPlansInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_paymentPlansInputDataPageFilter {
  field: String!
  operation: Gateway_test_paymentPlansInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_paymentPlansInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_paymentPlansInputDataPageSort
  filter: [Gateway_test_paymentPlansInputDataPageFilter]
  search: String
}

input Gateway_test_paymentPlansInputData {
  page: Gateway_test_paymentPlansInputDataPage
}

input Gateway_test_paymentPlansInput {
  groupID: String!
  data: Gateway_test_paymentPlansInputData!
}

type GatewayTSTPaymentPlanOutputHistory {
  transactionID: String
  date: String
  amount: Float
  status: String
}

type GatewayTSTPaymentPlanOutputLineItems {
  name: String
  description: String
  amount: Float
  price: Float
  total: Float
  sku: String
  metadata: String
  discount: Float
}

type GatewayTSTPaymentPlanOutput {
  planID: String
  createdAt: String
  planName: String
  amount: Float
  startDate: String
  endDate: String
  locationName: String
  locationID: String
  last4: String
  expires: String
  creator: String
  status: String
  history: [GatewayTSTPaymentPlanOutputHistory]
  customerName: String
  customerID: String
  customerEmail: String
  customerPhone: String
  customerCountry: String
  customerBillingAddress: String
  paymentID: String
  paymentMaskedCard: String
  paymentBrand: String
  paymentExpires: String
  paymentInterval: Float
  paymentEvery: Float
  lineItems: [GatewayTSTPaymentPlanOutputLineItems]
}

input Gateway_test_paymentPlanInputData {
  planID: String!
}

input Gateway_test_paymentPlanInput {
  groupID: String!
  data: Gateway_test_paymentPlanInputData!
}

type GatewayTSTCreatePaymentPlanOutput {
  planID: String
}

input Gateway_test_createPaymentPlanInputDataFormLineItems {
  name: String
  description: String
  amount: Float
  price: Float
  total: Float
  sku: String
  metadata: String
  discount: Float
}

input Gateway_test_createPaymentPlanInputDataForm {
  payNow: Boolean
  customerID: String!
  paymentID: String!
  planName: String!
  amount: Float!
  setupFee: Float
  startDate: String
  paymentEvery: Float
  paymentInterval: Float!
  endDate: String
  lineItems: [Gateway_test_createPaymentPlanInputDataFormLineItems]
}

input Gateway_test_createPaymentPlanInputData {
  form: Gateway_test_createPaymentPlanInputDataForm!
}

input Gateway_test_createPaymentPlanInput {
  groupID: String!
  data: Gateway_test_createPaymentPlanInputData!
}

type GatewayTSTScheduledOutputData {
  id: String
  planID: String
  planName: String
  customerID: String
  customerName: String
  nextPayment: String
  amount: Float
  last4: String
  expires: String
  datePaid: String
  amountPaid: Float
  status: String
}

type GatewayTSTScheduledOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTScheduledOutputPage {
  total: Float
  range: GatewayTSTScheduledOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTScheduledOutput {
  data: [GatewayTSTScheduledOutputData]
  page: GatewayTSTScheduledOutputPage
}

enum Gateway_test_scheduledInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_scheduledInputDataPageSort {
  field: String!
  order: Gateway_test_scheduledInputDataPageSortOrder!
}

enum Gateway_test_scheduledInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_scheduledInputDataPageFilter {
  field: String!
  operation: Gateway_test_scheduledInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_scheduledInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_scheduledInputDataPageSort
  filter: [Gateway_test_scheduledInputDataPageFilter]
  search: String
}

input Gateway_test_scheduledInputData {
  page: Gateway_test_scheduledInputDataPage
}

input Gateway_test_scheduledInput {
  groupID: String!
  data: Gateway_test_scheduledInputData!
}

type GatewayTSTCreateManualEntryOutput {
  transactionID: String
  subscriptionIDs: [String]
  status: String
}

enum Gateway_test_createManualEntryInputDataFormPaymentType {
  card
  ach
  gpecomm
}

input Gateway_test_createManualEntryInputDataFormCard {
  cardToken: String
  cvc: String
  expiryDate: String
}

input Gateway_test_createManualEntryInputDataFormAch {
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
}

input Gateway_test_createManualEntryInputDataFormGpecomm {
  id: String
  cvv: String
}

enum Gateway_test_createManualEntryInputDataFormMethodVerifyOrProcess {
  verify
  process
}

input Gateway_test_createManualEntryInputDataFormLineItemsProduct {
  sku: String!
  name: String!
  description: String
  price: Float!
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

input Gateway_test_createManualEntryInputDataFormLineItems {
  productId: String
  product: Gateway_test_createManualEntryInputDataFormLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum Gateway_test_createManualEntryInputDataFormTipType {
  percentage
  fixed
}

enum Gateway_test_createManualEntryInputDataFormTaxType {
  percentage
  fixed
}

input Gateway_test_createManualEntryInputDataForm {
  customerID: String
  nameOnCard: String
  email: String
  phoneNumber: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  customerCardID: String
  paymentType: Gateway_test_createManualEntryInputDataFormPaymentType
  card: Gateway_test_createManualEntryInputDataFormCard
  ach: Gateway_test_createManualEntryInputDataFormAch
  gpecomm: Gateway_test_createManualEntryInputDataFormGpecomm
  methodVerifyOrProcess: Gateway_test_createManualEntryInputDataFormMethodVerifyOrProcess
  lineItems: [Gateway_test_createManualEntryInputDataFormLineItems]
  amount: Float
  tip: Float
  tipType: Gateway_test_createManualEntryInputDataFormTipType
  tax: Float
  taxType: Gateway_test_createManualEntryInputDataFormTaxType
  discountCodes: [String]
  shipping: Float
}

enum Gateway_test_createManualEntryInputDataMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum Gateway_test_createManualEntryInputDataMetaDynamicTipType {
  percentage
  fixed
}

input Gateway_test_createManualEntryInputDataMetaDynamicTip {
  amount: Float!
  type: Gateway_test_createManualEntryInputDataMetaDynamicTipType!
}

input Gateway_test_createManualEntryInputDataMetaDynamicQuantityAmounts {
  id: String!
  quantity: Float!
}

input Gateway_test_createManualEntryInputDataMetaDynamic {
  discountCodes: [String]
  paymentType: Gateway_test_createManualEntryInputDataMetaDynamicPaymentType
  tip: Gateway_test_createManualEntryInputDataMetaDynamicTip
  quantityAmounts: [Gateway_test_createManualEntryInputDataMetaDynamicQuantityAmounts]
}

input Gateway_test_createManualEntryInputDataMetaReference {
  id: String!
  source: String!
}

input Gateway_test_createManualEntryInputDataMeta {
  source: String
  entrySource: String
  createdBy: String
  recurring: Boolean
  level2: Boolean
  narrative: String
  includeSurcharge: Boolean
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: Gateway_test_createManualEntryInputDataMetaDynamic
  reference: Gateway_test_createManualEntryInputDataMetaReference
}

input Gateway_test_createManualEntryInputData {
  form: Gateway_test_createManualEntryInputDataForm!
  meta: Gateway_test_createManualEntryInputDataMeta
}

input Gateway_test_createManualEntryInput {
  groupID: String!
  data: Gateway_test_createManualEntryInputData!
}

type GatewayTSTCreateCategoryOutput {
  id: String
}

enum Gateway_test_createCategoryInputDataStatus {
  active
  inactive
}

input Gateway_test_createCategoryInputData {
  name: String!
  status: Gateway_test_createCategoryInputDataStatus!
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

input Gateway_test_createCategoryInput {
  groupID: String!
  data: Gateway_test_createCategoryInputData!
}

type GatewayTSTDeleteCategoryOutput {
  id: String
}

input Gateway_test_deleteCategoryInputData {
  id: String!
}

input Gateway_test_deleteCategoryInput {
  groupID: String!
  data: Gateway_test_deleteCategoryInputData!
}

type GatewayTSTUpdateCategoryOutput {
  id: String
}

enum Gateway_test_updateCategoryInputDataStatus {
  active
  inactive
}

input Gateway_test_updateCategoryInputData {
  id: String!
  name: String!
  status: Gateway_test_updateCategoryInputDataStatus!
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

input Gateway_test_updateCategoryInput {
  groupID: String!
  data: Gateway_test_updateCategoryInputData!
}

enum GatewayTSTCategoryOutputStatus {
  active
  inactive
}

type GatewayTSTCategoryOutput {
  id: String
  name: String
  status: GatewayTSTCategoryOutputStatus
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

input Gateway_test_categoryInputData {
  id: String!
}

input Gateway_test_categoryInput {
  groupID: String!
  data: Gateway_test_categoryInputData!
}

type GatewayTSTCategoriesOutputData {
  id: String
  name: String
  status: String
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

type GatewayTSTCategoriesOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTCategoriesOutputPage {
  total: Float
  range: GatewayTSTCategoriesOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTCategoriesOutput {
  data: [GatewayTSTCategoriesOutputData]
  page: GatewayTSTCategoriesOutputPage
}

enum Gateway_test_categoriesInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_categoriesInputDataPageSort {
  field: String!
  order: Gateway_test_categoriesInputDataPageSortOrder!
}

enum Gateway_test_categoriesInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_categoriesInputDataPageFilter {
  field: String!
  operation: Gateway_test_categoriesInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_categoriesInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_categoriesInputDataPageSort
  filter: [Gateway_test_categoriesInputDataPageFilter]
  search: String
}

input Gateway_test_categoriesInputData {
  page: Gateway_test_categoriesInputDataPage
}

input Gateway_test_categoriesInput {
  groupID: String!
  data: Gateway_test_categoriesInputData!
}

type GatewayTSTProductsOutputData {
  id: String
  name: String
  price: Float
  discount: Float
  productStatus: String
  taxExempt: Boolean
  kitchenItem: Boolean
  sku: String
  brand: String
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayTSTProductsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTProductsOutputPage {
  total: Float
  range: GatewayTSTProductsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTProductsOutput {
  data: [GatewayTSTProductsOutputData]
  page: GatewayTSTProductsOutputPage
}

enum Gateway_test_productsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_productsInputDataPageSort {
  field: String!
  order: Gateway_test_productsInputDataPageSortOrder!
}

enum Gateway_test_productsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_productsInputDataPageFilter {
  field: String!
  operation: Gateway_test_productsInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_productsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_productsInputDataPageSort
  filter: [Gateway_test_productsInputDataPageFilter]
  search: String
}

input Gateway_test_productsInputData {
  page: Gateway_test_productsInputDataPage
}

input Gateway_test_productsInput {
  groupID: String!
  data: Gateway_test_productsInputData!
}

type GatewayTSTCreateProductOutput {
  productID: String
}

input Gateway_test_createProductInputDataFormProductImages {
  b64: String
  type: String
  url: String
}

input Gateway_test_createProductInputDataForm {
  name: String
  category: String
  subCategory: String
  sku: String
  brand: String
  price: Float
  itemWeight: Float
  length: Float
  breadth: Float
  width: Float
  description: String
  isInStore: Boolean
  isOnline: Boolean
  kitchenItem: String
  taxExempt: Boolean
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
  productImages: [Gateway_test_createProductInputDataFormProductImages]
}

input Gateway_test_createProductInputData {
  form: Gateway_test_createProductInputDataForm!
}

input Gateway_test_createProductInput {
  groupID: String!
  data: Gateway_test_createProductInputData!
}

type GatewayTSTUpdateProductOutput {
  productID: String
}

input Gateway_test_updateProductInputDataFormProductImages {
  b64: String!
  type: String!
}

input Gateway_test_updateProductInputDataForm {
  name: String
  category: String
  subCategory: String
  brand: String
  price: Float
  itemWeight: Float
  length: Float
  breadth: Float
  width: Float
  description: String
  isInStore: Boolean
  isOnline: Boolean
  kitchenItem: String
  taxExempt: Boolean
  sku: String
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
  productImages: [Gateway_test_updateProductInputDataFormProductImages]
}

input Gateway_test_updateProductInputData {
  productID: String!
  form: Gateway_test_updateProductInputDataForm!
}

input Gateway_test_updateProductInput {
  groupID: String!
  data: Gateway_test_updateProductInputData!
}

type GatewayTSTDeleteProductOutput {
  productID: String
}

input Gateway_test_deleteProductInputData {
  productID: String!
}

input Gateway_test_deleteProductInput {
  groupID: String!
  data: Gateway_test_deleteProductInputData!
}

type GatewayTSTProductOutputProductImages {
  url: String!
  name: String!
}

type GatewayTSTProductOutput {
  id: String
  name: String
  price: Float
  discount: Float
  productStatus: String
  taxExempt: Boolean
  kitchenItem: Boolean
  sku: String
  category: String
  subCategory: String
  brand: String
  itemWeight: Float
  length: Float
  breadth: Float
  width: Float
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
  description: String
  isInStore: Boolean
  isOnline: Boolean
  productImages: [GatewayTSTProductOutputProductImages]
}

input Gateway_test_productInputData {
  productID: String!
}

input Gateway_test_productInput {
  groupID: String!
  data: Gateway_test_productInputData!
}

enum GatewayTSTDiscountOutputType {
  percentage
  fixed
}

enum GatewayTSTDiscountOutputStatus {
  active
  inactive
}

type GatewayTSTDiscountOutput {
  id: String
  name: String
  type: GatewayTSTDiscountOutputType
  discount: Float
  status: GatewayTSTDiscountOutputStatus
  maxDiscount: Float
  validFrom: String
  validTo: String
  maxClaims: Float
  validSKU: [String]
}

input Gateway_test_discountInputData {
  id: String!
}

input Gateway_test_discountInput {
  groupID: String!
  data: Gateway_test_discountInputData!
}

enum GatewayTSTDiscountsOutputDataType {
  percentage
  fixed
}

enum GatewayTSTDiscountsOutputDataStatus {
  active
  inactive
}

type GatewayTSTDiscountsOutputData {
  id: String
  name: String
  type: GatewayTSTDiscountsOutputDataType
  discount: Float
  status: GatewayTSTDiscountsOutputDataStatus
}

type GatewayTSTDiscountsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayTSTDiscountsOutputPage {
  total: Float
  range: GatewayTSTDiscountsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayTSTDiscountsOutput {
  data: [GatewayTSTDiscountsOutputData]
  page: GatewayTSTDiscountsOutputPage
}

enum Gateway_test_discountsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_test_discountsInputDataPageSort {
  field: String!
  order: Gateway_test_discountsInputDataPageSortOrder!
}

enum Gateway_test_discountsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_test_discountsInputDataPageFilter {
  field: String!
  operation: Gateway_test_discountsInputDataPageFilterOperation!
  value: String!
}

input Gateway_test_discountsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_test_discountsInputDataPageSort
  filter: [Gateway_test_discountsInputDataPageFilter]
  search: String
}

input Gateway_test_discountsInputData {
  scope: String!
  page: Gateway_test_discountsInputDataPage
}

input Gateway_test_discountsInput {
  groupID: String!
  data: Gateway_test_discountsInputData!
}

type GatewayTSTCreateDiscountOutput {
  id: String
}

enum Gateway_test_createDiscountInputDataType {
  percentage
  fixed
}

enum Gateway_test_createDiscountInputDataStatus {
  active
  inactive
}

input Gateway_test_createDiscountInputData {
  name: String!
  type: Gateway_test_createDiscountInputDataType!
  discount: Float!
  status: Gateway_test_createDiscountInputDataStatus!
  maxDiscount: Float
  validFrom: String
  validTo: String
  maxClaims: Float
  validSKU: [String]
  scope: String!
}

input Gateway_test_createDiscountInput {
  groupID: String!
  data: Gateway_test_createDiscountInputData!
}

type GatewayTSTDeleteDiscountOutput {
  id: String
}

input Gateway_test_deleteDiscountInputData {
  id: String!
}

input Gateway_test_deleteDiscountInput {
  groupID: String!
  data: Gateway_test_deleteDiscountInputData!
}

type GatewayTSTUpdateDiscountOutput {
  id: String
}

enum Gateway_test_updateDiscountInputDataType {
  percentage
  fixed
}

enum Gateway_test_updateDiscountInputDataStatus {
  active
  inactive
}

input Gateway_test_updateDiscountInputData {
  id: String!
  name: String!
  type: Gateway_test_updateDiscountInputDataType!
  discount: Float!
  status: Gateway_test_updateDiscountInputDataStatus!
  maxDiscount: Float
  validFrom: String
  validTo: String
  maxClaims: Float
  validSKU: [String]
  scope: String!
}

input Gateway_test_updateDiscountInput {
  groupID: String!
  data: Gateway_test_updateDiscountInputData!
}

type GatewayFLPayInitOutput {
  merchantID: String!
  processorID: String!
}

input Gateway_flpayset_initializeInput {
  groupID: String!
}

enum GatewayFLPayProcessorListOutputDataStatus {
  active
  inactive
}

enum GatewayFLPayProcessorListOutputDataPayment_adj_type {
  flat
  percentage
}

type GatewayFLPayProcessorListOutputDataSettingsTsys_sierra {
  bin: String!
  mid: String!
  mvv: String!
  agent_bank_number: String!
  agent_chain_number: String!
  store_number: String!
  terminal_number: String!
  terminal_identification_number: String!
  industry_code: String!
  currency_code: String!
  country_code: String!
  city_code: String!
  language_indicator: String!
  timezone: String!
  mcc: String!
  merchant_name: String!
  merchant_location: String!
  merchant_state: String!
  merchant_location_number: String!
  classification: String!
}

type GatewayFLPayProcessorListOutputDataSettings {
  tsys_sierra: GatewayFLPayProcessorListOutputDataSettingsTsys_sierra!
}

type GatewayFLPayProcessorListOutputDataFeatures {
  hide_in_vt: Boolean!
  disable_auto_settle: Boolean!
}

type GatewayFLPayProcessorListOutputDataLimitsSale {
  single: Float!
  daily: Float!
  monthly: Float!
}

type GatewayFLPayProcessorListOutputDataLimitsCredit {
  single: Float!
  daily: Float!
  monthly: Float!
}

type GatewayFLPayProcessorListOutputDataLimits {
  sale: GatewayFLPayProcessorListOutputDataLimitsSale!
  credit: GatewayFLPayProcessorListOutputDataLimitsCredit!
}

type GatewayFLPayProcessorListOutputData {
  id: String!
  name: String!
  description: String!
  default_card: Boolean!
  default_ach: Boolean!
  default_apm: Boolean!
  default_cash: Boolean!
  timezone: String!
  tag: String!
  status: GatewayFLPayProcessorListOutputDataStatus!
  settle_at: String!
  max_daily: Float!
  max_monthly: Float!
  payment_adj_type: GatewayFLPayProcessorListOutputDataPayment_adj_type!
  payment_adj_val: Float!
  settings: GatewayFLPayProcessorListOutputDataSettings!
  features: GatewayFLPayProcessorListOutputDataFeatures!
  limits: GatewayFLPayProcessorListOutputDataLimits!
  supported_payment_methods: [String]!
  supported_currencies: [String]!
}

type GatewayFLPayProcessorListOutput {
  status: String!
  msg: String!
  data: [GatewayFLPayProcessorListOutputData]!
  total_count: Float!
}

input Gateway_flpayset_processor_listInput {
  groupID: String!
}

type GatewayFLPayProcessorUpdateOutput {
  merchantID: String!
  processorID: String!
}

input Gateway_flpayset_processor_updateInputDataSettingsTsys_sierra {
  industry_code: String!
  currency_code: String!
  country_code: String!
  language_indicator: String!
  classification: String!
  timezone: String!
  city_code: String!
  merchant_name: String!
  merchant_state: String!
  merchant_location: String!
  bin: String!
  mid: String!
  mvv: String!
  agent_bank_number: String!
  agent_chain_number: String!
  store_number: String!
  terminal_number: String!
  terminal_identification_number: String!
  mcc: String!
  merchant_location_number: String!
}

input Gateway_flpayset_processor_updateInputDataSettings {
  tsys_sierra: Gateway_flpayset_processor_updateInputDataSettingsTsys_sierra!
}

input Gateway_flpayset_processor_updateInputDataFeatures {
  hide_in_vt: Boolean!
  disable_auto_settle: Boolean!
}

input Gateway_flpayset_processor_updateInputDataLimitsSale {
  single: Float!
  daily: Float!
  monthly: Float!
}

input Gateway_flpayset_processor_updateInputDataLimitsCredit {
  single: Float!
  daily: Float!
  monthly: Float!
}

input Gateway_flpayset_processor_updateInputDataLimits {
  sale: Gateway_flpayset_processor_updateInputDataLimitsSale!
  credit: Gateway_flpayset_processor_updateInputDataLimitsCredit!
}

input Gateway_flpayset_processor_updateInputData {
  name: String
  description: String
  default_card: Boolean
  default_ach: Boolean
  default_apm: Boolean
  default_cash: Boolean
  timezone: String
  tag: String
  status: String
  settle_at: String
  max_daily: Float
  max_monthly: Float
  settings: Gateway_flpayset_processor_updateInputDataSettings
  features: Gateway_flpayset_processor_updateInputDataFeatures
  payment_adj_type: String
  payment_adj_val: Float
  limits: Gateway_flpayset_processor_updateInputDataLimits
}

input Gateway_flpayset_processor_updateInput {
  groupID: String!
  processorID: String!
  data: Gateway_flpayset_processor_updateInputData!
}

type GatewayUNITransactionsOutputData {
  transactionID: String
  location: String
  date: String
  method: String
  customer: String
  brand: String
  last4: String
  amount: Float
  status: String
}

type GatewayUNITransactionsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNITransactionsOutputPage {
  total: Float
  range: GatewayUNITransactionsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNITransactionsOutput {
  data: [GatewayUNITransactionsOutputData]
  page: GatewayUNITransactionsOutputPage
}

enum Gateway_transactionsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_transactionsInputDataPageSort {
  field: String!
  order: Gateway_transactionsInputDataPageSortOrder!
}

enum Gateway_transactionsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_transactionsInputDataPageFilter {
  field: String!
  operation: Gateway_transactionsInputDataPageFilterOperation!
  value: String!
}

input Gateway_transactionsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_transactionsInputDataPageSort
  filter: [Gateway_transactionsInputDataPageFilter]
  search: String
}

input Gateway_transactionsInputData {
  page: Gateway_transactionsInputDataPage
}

input Gateway_transactionsInput {
  groupID: String!
  data: Gateway_transactionsInputData!
}

type GatewayUNITransactionOutputBreakdown {
  discount: Float
  tax: Float
  shipping: Float
  fees: Float
  actualFees: Float
  tip: Float
  subtotal: Float
  subscriptionTotal: Float
  rawTotal: Float
  total: Float
  expectedTotal: Float
}

type GatewayUNITransactionOutputTransactionHistory {
  date: String
  status: String
  response: String
  avs: String
  cvv: String
}

type GatewayUNITransactionOutputRelatedTransactions {
  transactionID: String
  amount: Float
  status: String
  date: String
  paymentType: String
}

type GatewayUNITransactionOutputPurchaseDetails {
  productName: String
  productID: String
  quantity: Float
  price: Float
  discount: Float
  description: String
}

type GatewayUNITransactionOutput {
  transactionID: String
  amount: Float
  status: String
  date: String
  brand: String
  createdBy: String
  paymentType: String
  method: String
  batchID: String
  paymentPlan: String
  source: String
  authCode: String
  entryMethod: String
  tokenSource: String
  gsa: String
  emv: String
  last4: String
  customerName: String
  customerID: String
  customerEmail: String
  customerPhone: String
  customerCountry: String
  customerBillingAddress: String
  commercialLevel: String
  result: String
  message: String
  brandReference: String
  breakdown: GatewayUNITransactionOutputBreakdown
  transactionHistory: [GatewayUNITransactionOutputTransactionHistory]
  relatedTransactions: [GatewayUNITransactionOutputRelatedTransactions]
  purchaseDetails: [GatewayUNITransactionOutputPurchaseDetails]
}

input Gateway_transactionInputData {
  transactionID: String!
}

input Gateway_transactionInput {
  groupID: String!
  data: Gateway_transactionInputData!
}

type GatewayUNI_transactionBatchOutputBreakdown {
  discount: Float
  tax: Float
  shipping: Float
  fees: Float
  actualFees: Float
  tip: Float
  subtotal: Float
  subscriptionTotal: Float
  rawTotal: Float
  total: Float
  expectedTotal: Float
}

type GatewayUNI_transactionBatchOutputTransactionHistory {
  date: String
  status: String
  response: String
  avs: String
  cvv: String
}

type GatewayUNI_transactionBatchOutputRelatedTransactions {
  transactionID: String
  amount: Float
  status: String
  date: String
  paymentType: String
}

type GatewayUNI_transactionBatchOutputPurchaseDetails {
  productName: String
  productID: String
  quantity: Float
  price: Float
  discount: Float
  description: String
}

type GatewayUNI_transactionBatchOutput {
  transactionID: String
  amount: Float
  status: String
  date: String
  brand: String
  createdBy: String
  paymentType: String
  method: String
  batchID: String
  paymentPlan: String
  source: String
  authCode: String
  entryMethod: String
  tokenSource: String
  gsa: String
  emv: String
  last4: String
  customerName: String
  customerID: String
  customerEmail: String
  customerPhone: String
  customerCountry: String
  customerBillingAddress: String
  commercialLevel: String
  result: String
  message: String
  brandReference: String
  breakdown: GatewayUNI_transactionBatchOutputBreakdown
  transactionHistory: [GatewayUNI_transactionBatchOutputTransactionHistory]
  relatedTransactions: [GatewayUNI_transactionBatchOutputRelatedTransactions]
  purchaseDetails: [GatewayUNI_transactionBatchOutputPurchaseDetails]
}

enum Gateway__transactionBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__transactionBatchInputDataPageSort {
  field: String!
  order: Gateway__transactionBatchInputDataPageSortOrder!
}

enum Gateway__transactionBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__transactionBatchInputDataPageFilter {
  field: String!
  operation: Gateway__transactionBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__transactionBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__transactionBatchInputDataPageSort
  filter: [Gateway__transactionBatchInputDataPageFilter]
  search: String
}

input Gateway__transactionBatchInputData {
  page: Gateway__transactionBatchInputDataPage
}

input Gateway__transactionBatchInput {
  groupID: String!
  data: Gateway__transactionBatchInputData!
}

type GatewayUNIBatchesOutputData {
  batchID: String
  location: String
  date: String
  amount: Float
  status: String
}

type GatewayUNIBatchesOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIBatchesOutputPage {
  total: Float
  range: GatewayUNIBatchesOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIBatchesOutput {
  data: [GatewayUNIBatchesOutputData]
  page: GatewayUNIBatchesOutputPage
}

enum Gateway_batchesInputDataPageSortOrder {
  asc
  desc
}

input Gateway_batchesInputDataPageSort {
  field: String!
  order: Gateway_batchesInputDataPageSortOrder!
}

enum Gateway_batchesInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_batchesInputDataPageFilter {
  field: String!
  operation: Gateway_batchesInputDataPageFilterOperation!
  value: String!
}

input Gateway_batchesInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_batchesInputDataPageSort
  filter: [Gateway_batchesInputDataPageFilter]
  search: String
}

input Gateway_batchesInputData {
  page: Gateway_batchesInputDataPage
}

input Gateway_batchesInput {
  groupID: String!
  data: Gateway_batchesInputData!
}

type GatewayUNIBatchOutputTransactions {
  transactionID: String
  date: String
  method: String
  name: String
  last4: String
  customer: String
  amount: Float
  brand: String
  status: String
}

type GatewayUNIBatchOutput {
  batchID: String
  status: String
  date: String
  location: String
  locationID: String
  amount: Float
  transactions: [GatewayUNIBatchOutputTransactions]
}

input Gateway_batchInputData {
  batchID: String!
}

input Gateway_batchInput {
  groupID: String!
  data: Gateway_batchInputData!
}

type GatewayUNI_batchBatchOutputTransactions {
  transactionID: String
  date: String
  method: String
  name: String
  last4: String
  customer: String
  amount: Float
  brand: String
  status: String
}

type GatewayUNI_batchBatchOutput {
  batchID: String
  status: String
  date: String
  location: String
  locationID: String
  amount: Float
  transactions: [GatewayUNI_batchBatchOutputTransactions]
}

enum Gateway__batchBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__batchBatchInputDataPageSort {
  field: String!
  order: Gateway__batchBatchInputDataPageSortOrder!
}

enum Gateway__batchBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__batchBatchInputDataPageFilter {
  field: String!
  operation: Gateway__batchBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__batchBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__batchBatchInputDataPageSort
  filter: [Gateway__batchBatchInputDataPageFilter]
  search: String
}

input Gateway__batchBatchInputData {
  page: Gateway__batchBatchInputDataPage
}

input Gateway__batchBatchInput {
  groupID: String!
  data: Gateway__batchBatchInputData!
}

type GatewayUNICloseBatchOutput {
  batchID: String
  status: String
}

input Gateway_closeBatchInputData {
  batchID: String!
}

input Gateway_closeBatchInput {
  groupID: String!
  data: Gateway_closeBatchInputData!
}

type GatewayUNIDepositsOutputData {
  depositID: String
  location: String
  date: String
  amount: Float
  status: String
}

type GatewayUNIDepositsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIDepositsOutputPage {
  total: Float
  range: GatewayUNIDepositsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIDepositsOutput {
  data: [GatewayUNIDepositsOutputData]
  page: GatewayUNIDepositsOutputPage
}

enum Gateway_depositsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_depositsInputDataPageSort {
  field: String!
  order: Gateway_depositsInputDataPageSortOrder!
}

enum Gateway_depositsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_depositsInputDataPageFilter {
  field: String!
  operation: Gateway_depositsInputDataPageFilterOperation!
  value: String!
}

input Gateway_depositsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_depositsInputDataPageSort
  filter: [Gateway_depositsInputDataPageFilter]
  search: String
}

input Gateway_depositsInputData {
  page: Gateway_depositsInputDataPage
}

input Gateway_depositsInput {
  groupID: String!
  data: Gateway_depositsInputData!
}

type GatewayUNIDepositOutputBatches {
  batchID: String
  depositID: String
  total: Float
}

type GatewayUNIDepositOutputTransactions {
  transactionID: String
  date: String
  method: String
  name: String
  last4: String
  customer: String
  amount: Float
  brand: String
  status: String
}

type GatewayUNIDepositOutput {
  depositID: String
  date: String
  last4: String
  amount: Float
  sales: Float
  fees: Float
  status: String
  totalDeposit: Float
  batches: [GatewayUNIDepositOutputBatches]
  transactions: [GatewayUNIDepositOutputTransactions]
}

input Gateway_depositInputData {
  depositID: String!
}

input Gateway_depositInput {
  groupID: String!
  data: Gateway_depositInputData!
}

type GatewayUNI_depositBatchOutputBatches {
  batchID: String
  depositID: String
  total: Float
}

type GatewayUNI_depositBatchOutputTransactions {
  transactionID: String
  date: String
  method: String
  name: String
  last4: String
  customer: String
  amount: Float
  brand: String
  status: String
}

type GatewayUNI_depositBatchOutput {
  depositID: String
  date: String
  last4: String
  amount: Float
  sales: Float
  fees: Float
  status: String
  totalDeposit: Float
  batches: [GatewayUNI_depositBatchOutputBatches]
  transactions: [GatewayUNI_depositBatchOutputTransactions]
}

enum Gateway__depositBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__depositBatchInputDataPageSort {
  field: String!
  order: Gateway__depositBatchInputDataPageSortOrder!
}

enum Gateway__depositBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__depositBatchInputDataPageFilter {
  field: String!
  operation: Gateway__depositBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__depositBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__depositBatchInputDataPageSort
  filter: [Gateway__depositBatchInputDataPageFilter]
  search: String
}

input Gateway__depositBatchInputData {
  page: Gateway__depositBatchInputDataPage
}

input Gateway__depositBatchInput {
  groupID: String!
  data: Gateway__depositBatchInputData!
}

type GatewayUNIDisputesOutputData {
  caseID: String
  location: String
  date: String
  reason: String
  amount: Float
  brand: String
  transactionID: String
  status: String
}

type GatewayUNIDisputesOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIDisputesOutputPage {
  total: Float
  range: GatewayUNIDisputesOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIDisputesOutput {
  data: [GatewayUNIDisputesOutputData]
  page: GatewayUNIDisputesOutputPage
}

enum Gateway_disputesInputDataPageSortOrder {
  asc
  desc
}

input Gateway_disputesInputDataPageSort {
  field: String!
  order: Gateway_disputesInputDataPageSortOrder!
}

enum Gateway_disputesInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_disputesInputDataPageFilter {
  field: String!
  operation: Gateway_disputesInputDataPageFilterOperation!
  value: String!
}

input Gateway_disputesInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_disputesInputDataPageSort
  filter: [Gateway_disputesInputDataPageFilter]
  search: String
}

input Gateway_disputesInputData {
  page: Gateway_disputesInputDataPage
}

input Gateway_disputesInput {
  groupID: String!
  data: Gateway_disputesInputData!
}

type GatewayUNIDisputeOutputProducts {
  productName: String
  productID: String
  quantity: Float
  price: Float
  discount: Float
  description: String
}

type GatewayUNIDisputeOutputHistory {
  action: String
  body: String
  actor: String
  createdAt: String
}

type GatewayUNIDisputeOutputFiles {
  fileUrl: String
  purpose: String
  fileFormat: String
  createdAt: String
  submittedAt: String
}

type GatewayUNIDisputeOutput {
  disputeID: String
  caseID: String
  location: String
  date: String
  reason: String
  amount: Float
  brand: String
  transactionID: String
  paymentType: String
  createdBy: String
  method: String
  batchID: String
  paymentPlan: String
  source: String
  authCode: String
  entryMethod: String
  tokenSource: String
  gsa: String
  emv: String
  last4: String
  customerID: String
  name: String
  email: String
  phone: String
  country: String
  billingAddress: String
  products: [GatewayUNIDisputeOutputProducts]
  history: [GatewayUNIDisputeOutputHistory]
  files: [GatewayUNIDisputeOutputFiles]
  status: String
}

input Gateway_disputeInputData {
  caseID: String!
}

input Gateway_disputeInput {
  groupID: String!
  data: Gateway_disputeInputData!
}

type GatewayUNI_disputeBatchOutputProducts {
  productName: String
  productID: String
  quantity: Float
  price: Float
  discount: Float
  description: String
}

type GatewayUNI_disputeBatchOutputHistory {
  date: String
  status: String
  fileB64: String
  fileURL: String
  fileName: String
  fileMimetype: String
  description: String
  event: String
}

type GatewayUNI_disputeBatchOutput {
  disputeID: String
  caseID: String
  location: String
  date: String
  reason: String
  amount: Float
  brand: String
  transactionID: String
  paymentType: String
  createdBy: String
  method: String
  batchID: String
  paymentPlan: String
  source: String
  authCode: String
  entryMethod: String
  tokenSource: String
  gsa: String
  emv: String
  last4: String
  customerID: String
  name: String
  email: String
  phone: String
  country: String
  billingAddress: String
  products: [GatewayUNI_disputeBatchOutputProducts]
  history: [GatewayUNI_disputeBatchOutputHistory]
  status: String
}

enum Gateway__disputeBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__disputeBatchInputDataPageSort {
  field: String!
  order: Gateway__disputeBatchInputDataPageSortOrder!
}

enum Gateway__disputeBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__disputeBatchInputDataPageFilter {
  field: String!
  operation: Gateway__disputeBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__disputeBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__disputeBatchInputDataPageSort
  filter: [Gateway__disputeBatchInputDataPageFilter]
  search: String
}

input Gateway__disputeBatchInputData {
  page: Gateway__disputeBatchInputDataPage
}

input Gateway__disputeBatchInput {
  groupID: String!
  data: Gateway__disputeBatchInputData!
}

type GatewayUNIAttendDisputeOutput {
  disputeID: String
}

enum Gateway_attendDisputeInputDataAction {
  accept
  reject
}

input Gateway_attendDisputeInputData {
  disputeID: String!
  action: Gateway_attendDisputeInputDataAction!
}

input Gateway_attendDisputeInput {
  groupID: String!
  data: Gateway_attendDisputeInputData!
}

type GatewayUNISubmitDisputeDocumentOutput {
  disputeID: String
}

input Gateway_submitDisputeDocumentInputDataFiles {
  itemID: String!
}

input Gateway_submitDisputeDocumentInputData {
  disputeID: String!
  files: [Gateway_submitDisputeDocumentInputDataFiles]!
}

input Gateway_submitDisputeDocumentInput {
  groupID: String!
  data: Gateway_submitDisputeDocumentInputData!
}

type GatewayUNIUploadDisputeDocumentOutput {
  itemID: String
}

input Gateway_uploadDisputeDocumentInputDataUpload {
  url: String!
  size: Float!
  type: String!
  filetype: String!
  purpose: String!
}

input Gateway_uploadDisputeDocumentInputDataDelete {
  itemID: String!
}

input Gateway_uploadDisputeDocumentInputData {
  disputeID: String!
  upload: Gateway_uploadDisputeDocumentInputDataUpload
  delete: Gateway_uploadDisputeDocumentInputDataDelete
}

input Gateway_uploadDisputeDocumentInput {
  groupID: String!
  data: Gateway_uploadDisputeDocumentInputData!
}

type GatewayUNIWebhookEventsOutputData {
  eventID: String
  date: String
  type: String
  payload: String
}

type GatewayUNIWebhookEventsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIWebhookEventsOutputPage {
  total: Float
  range: GatewayUNIWebhookEventsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIWebhookEventsOutput {
  data: [GatewayUNIWebhookEventsOutputData]
  page: GatewayUNIWebhookEventsOutputPage
}

enum Gateway_webhookEventsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_webhookEventsInputDataPageSort {
  field: String!
  order: Gateway_webhookEventsInputDataPageSortOrder!
}

enum Gateway_webhookEventsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_webhookEventsInputDataPageFilter {
  field: String!
  operation: Gateway_webhookEventsInputDataPageFilterOperation!
  value: String!
}

input Gateway_webhookEventsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_webhookEventsInputDataPageSort
  filter: [Gateway_webhookEventsInputDataPageFilter]
  search: String
}

input Gateway_webhookEventsInputData {
  page: Gateway_webhookEventsInputDataPage
}

input Gateway_webhookEventsInput {
  groupID: String!
  data: Gateway_webhookEventsInputData!
}

type GatewayUNICustomersOutputData {
  id: String
  isDefault: Boolean
  last4: String
  brand: String
  expires: String
  name: String
  city_state: String
  zip: String
  email: String
  phone: String
}

type GatewayUNICustomersOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNICustomersOutputPage {
  total: Float
  range: GatewayUNICustomersOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNICustomersOutput {
  data: [GatewayUNICustomersOutputData]
  page: GatewayUNICustomersOutputPage
}

enum Gateway_customersInputDataPageSortOrder {
  asc
  desc
}

input Gateway_customersInputDataPageSort {
  field: String!
  order: Gateway_customersInputDataPageSortOrder!
}

enum Gateway_customersInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_customersInputDataPageFilter {
  field: String!
  operation: Gateway_customersInputDataPageFilterOperation!
  value: String!
}

input Gateway_customersInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_customersInputDataPageSort
  filter: [Gateway_customersInputDataPageFilter]
  search: String
}

input Gateway_customersInputData {
  page: Gateway_customersInputDataPage
}

input Gateway_customersInput {
  groupID: String!
  data: Gateway_customersInputData!
}

type GatewayUNIAddCustomerOutput {
  customerID: String
  paymentCardID: String
}

input Gateway_addCustomerInputDataFormGpecomm {
  id: String
  cvv: String
}

input Gateway_addCustomerInputDataFormCard {
  cvc: String
  expiryDate: String
  cardToken: String
}

input Gateway_addCustomerInputDataFormAch {
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
}

input Gateway_addCustomerInputDataForm {
  nameOnCard: String!
  email: String!
  phone: String!
  billingAddress: String!
  billingCity: String!
  billingState: String!
  billingZip: String!
  billingCountry: String!
  skipMultiTokenConvert: Boolean
  gpecomm: Gateway_addCustomerInputDataFormGpecomm
  card: Gateway_addCustomerInputDataFormCard
  ach: Gateway_addCustomerInputDataFormAch
}

input Gateway_addCustomerInputData {
  form: Gateway_addCustomerInputDataForm!
}

input Gateway_addCustomerInput {
  groupID: String!
  data: Gateway_addCustomerInputData!
}

type GatewayUNIUpdateCustomerOutput {
  customerID: String
}

input Gateway_updateCustomerInputDataForm {
  nameOnCard: String!
  email: String!
  phone: String!
  billingAddress: String!
  billingCity: String!
  billingState: String!
  billingZip: String!
  billingCountry: String!
}

input Gateway_updateCustomerInputData {
  customerID: String!
  form: Gateway_updateCustomerInputDataForm!
}

input Gateway_updateCustomerInput {
  groupID: String!
  data: Gateway_updateCustomerInputData!
}

type GatewayUNIAddPaymentMethodOutput {
  customerID: String
  paymentCardID: String
}

input Gateway_addPaymentMethodInputDataFormCard {
  cvc: String
  expiryDate: String
  cardToken: String
}

input Gateway_addPaymentMethodInputDataFormAch {
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
}

input Gateway_addPaymentMethodInputDataFormGpecomm {
  id: String
  cvv: String
}

input Gateway_addPaymentMethodInputDataForm {
  setAsDefault: Boolean
  skipMultiTokenConvert: Boolean
  card: Gateway_addPaymentMethodInputDataFormCard
  ach: Gateway_addPaymentMethodInputDataFormAch
  gpecomm: Gateway_addPaymentMethodInputDataFormGpecomm
}

input Gateway_addPaymentMethodInputData {
  customerID: String!
  form: Gateway_addPaymentMethodInputDataForm!
}

input Gateway_addPaymentMethodInput {
  groupID: String!
  data: Gateway_addPaymentMethodInputData!
}

type GatewayUNIDeletePaymentMethodOutput {
  customerID: String
}

input Gateway_deletePaymentMethodInputData {
  customerID: String!
  cardID: String!
}

input Gateway_deletePaymentMethodInput {
  groupID: String!
  data: Gateway_deletePaymentMethodInputData!
}

type GatewayUNISetDefaultPaymentMethodOutput {
  customerID: String
}

input Gateway_setDefaultPaymentMethodInputData {
  customerID: String!
  cardID: String!
}

input Gateway_setDefaultPaymentMethodInput {
  groupID: String!
  data: Gateway_setDefaultPaymentMethodInputData!
}

type GatewayUNICustomerOutputPaymentCards {
  cardID: String
  type: String
  isDefault: Boolean
  last4: String
  brand: String
  expires: String
  cvc: String
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
  gpEcommID: String
}

type GatewayUNICustomerOutput {
  id: String
  nameOnCard: String
  email: String
  phone: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  paymentCards: [GatewayUNICustomerOutputPaymentCards]
}

input Gateway_customerInputData {
  customerID: String!
}

input Gateway_customerInput {
  groupID: String!
  data: Gateway_customerInputData!
}

type GatewayUNIDeleteCustomerOutput {
  customerID: String
}

input Gateway_deleteCustomerInputData {
  customerID: String!
}

input Gateway_deleteCustomerInput {
  groupID: String!
  data: Gateway_deleteCustomerInputData!
}

type GatewayUNI_customerBatchOutputPaymentCards {
  cardID: String
  isDefault: Boolean
  last4: String
  brand: String
  expires: String
  cvc: String
}

type GatewayUNI_customerBatchOutput {
  id: String
  nameOnCard: String
  email: String
  phone: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  paymentCards: [GatewayUNI_customerBatchOutputPaymentCards]
}

enum Gateway__customerBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__customerBatchInputDataPageSort {
  field: String!
  order: Gateway__customerBatchInputDataPageSortOrder!
}

enum Gateway__customerBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__customerBatchInputDataPageFilter {
  field: String!
  operation: Gateway__customerBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__customerBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__customerBatchInputDataPageSort
  filter: [Gateway__customerBatchInputDataPageFilter]
  search: String
}

input Gateway__customerBatchInputData {
  page: Gateway__customerBatchInputDataPage
}

input Gateway__customerBatchInput {
  groupID: String!
  data: Gateway__customerBatchInputData!
}

type GatewayUNIPaymentPlansOutputData {
  planID: String
  planName: String
  startDate: String
  customerName: String
  customerID: String
  amount: Float
  last4: String
  expires: String
  duration: String
  status: String
}

type GatewayUNIPaymentPlansOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIPaymentPlansOutputPage {
  total: Float
  range: GatewayUNIPaymentPlansOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIPaymentPlansOutput {
  data: [GatewayUNIPaymentPlansOutputData]
  page: GatewayUNIPaymentPlansOutputPage
}

enum Gateway_paymentPlansInputDataPageSortOrder {
  asc
  desc
}

input Gateway_paymentPlansInputDataPageSort {
  field: String!
  order: Gateway_paymentPlansInputDataPageSortOrder!
}

enum Gateway_paymentPlansInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_paymentPlansInputDataPageFilter {
  field: String!
  operation: Gateway_paymentPlansInputDataPageFilterOperation!
  value: String!
}

input Gateway_paymentPlansInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_paymentPlansInputDataPageSort
  filter: [Gateway_paymentPlansInputDataPageFilter]
  search: String
}

input Gateway_paymentPlansInputData {
  page: Gateway_paymentPlansInputDataPage
}

input Gateway_paymentPlansInput {
  groupID: String!
  data: Gateway_paymentPlansInputData!
}

type GatewayUNIPaymentPlanOutputHistory {
  transactionID: String
  date: String
  amount: Float
  status: String
}

type GatewayUNIPaymentPlanOutputLineItems {
  name: String
  description: String
  amount: Float
  price: Float
  total: Float
  sku: String
  metadata: String
  discount: Float
}

type GatewayUNIPaymentPlanOutput {
  planID: String
  createdAt: String
  planName: String
  amount: Float
  startDate: String
  endDate: String
  locationName: String
  locationID: String
  last4: String
  expires: String
  creator: String
  status: String
  history: [GatewayUNIPaymentPlanOutputHistory]
  customerName: String
  customerID: String
  customerEmail: String
  customerPhone: String
  customerCountry: String
  customerBillingAddress: String
  paymentID: String
  paymentMaskedCard: String
  paymentBrand: String
  paymentExpires: String
  paymentInterval: Float
  paymentEvery: Float
  lineItems: [GatewayUNIPaymentPlanOutputLineItems]
}

input Gateway_paymentPlanInputData {
  planID: String!
}

input Gateway_paymentPlanInput {
  groupID: String!
  data: Gateway_paymentPlanInputData!
}

type GatewayUNICreatePaymentPlanOutput {
  planID: String
}

input Gateway_createPaymentPlanInputDataFormLineItems {
  name: String
  description: String
  amount: Float
  price: Float
  total: Float
  sku: String
  metadata: String
  discount: Float
}

input Gateway_createPaymentPlanInputDataForm {
  payNow: Boolean
  customerID: String!
  paymentID: String!
  planName: String!
  amount: Float!
  setupFee: Float
  startDate: String
  paymentEvery: Float
  paymentInterval: Float!
  endDate: String
  lineItems: [Gateway_createPaymentPlanInputDataFormLineItems]
}

input Gateway_createPaymentPlanInputData {
  form: Gateway_createPaymentPlanInputDataForm!
}

input Gateway_createPaymentPlanInput {
  groupID: String!
  data: Gateway_createPaymentPlanInputData!
}

type GatewayUNIUpdatePaymentPlanOutput {
  planID: String
}

input Gateway_updatePaymentPlanInputDataFormLineItems {
  name: String
  description: String
  amount: Float
  price: Float
  total: Float
  sku: String
  metadata: String
  discount: Float
}

input Gateway_updatePaymentPlanInputDataForm {
  customerID: String!
  paymentID: String!
  planName: String!
  amount: Float!
  startDate: String
  paymentEvery: Float
  paymentInterval: Float!
  endDate: String
  lineItems: [Gateway_updatePaymentPlanInputDataFormLineItems]
}

input Gateway_updatePaymentPlanInputData {
  id: String!
  form: Gateway_updatePaymentPlanInputDataForm!
}

input Gateway_updatePaymentPlanInput {
  groupID: String!
  data: Gateway_updatePaymentPlanInputData!
}

type GatewayUNICancelPaymentPlanOutput {
  planID: String
}

input Gateway_cancelPaymentPlanInputData {
  planID: String!
}

input Gateway_cancelPaymentPlanInput {
  groupID: String!
  data: Gateway_cancelPaymentPlanInputData!
}

type GatewayUNI_paymentPlanBatchOutputHistory {
  transactionID: String
  date: String
  amount: Float
  status: String
}

type GatewayUNI_paymentPlanBatchOutputLineItems {
  name: String
  description: String
  amount: Float
  price: Float
  total: Float
  sku: String
  metadata: String
  discount: Float
}

type GatewayUNI_paymentPlanBatchOutput {
  planID: String
  createdAt: String
  planName: String
  amount: Float
  startDate: String
  endDate: String
  locationName: String
  locationID: String
  last4: String
  expires: String
  creator: String
  status: String
  history: [GatewayUNI_paymentPlanBatchOutputHistory]
  customerName: String
  customerID: String
  customerEmail: String
  customerPhone: String
  customerCountry: String
  customerBillingAddress: String
  paymentID: String
  paymentMaskedCard: String
  paymentBrand: String
  paymentExpires: String
  paymentInterval: Float
  paymentEvery: Float
  lineItems: [GatewayUNI_paymentPlanBatchOutputLineItems]
}

enum Gateway__paymentPlanBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__paymentPlanBatchInputDataPageSort {
  field: String!
  order: Gateway__paymentPlanBatchInputDataPageSortOrder!
}

enum Gateway__paymentPlanBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__paymentPlanBatchInputDataPageFilter {
  field: String!
  operation: Gateway__paymentPlanBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__paymentPlanBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__paymentPlanBatchInputDataPageSort
  filter: [Gateway__paymentPlanBatchInputDataPageFilter]
  search: String
}

input Gateway__paymentPlanBatchInputData {
  page: Gateway__paymentPlanBatchInputDataPage
}

input Gateway__paymentPlanBatchInput {
  groupID: String!
  data: Gateway__paymentPlanBatchInputData!
}

type GatewayUNIScheduledOutputData {
  id: String
  planID: String
  planName: String
  customerID: String
  customerName: String
  nextPayment: String
  amount: Float
  last4: String
  expires: String
  datePaid: String
  amountPaid: Float
  status: String
}

type GatewayUNIScheduledOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIScheduledOutputPage {
  total: Float
  range: GatewayUNIScheduledOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIScheduledOutput {
  data: [GatewayUNIScheduledOutputData]
  page: GatewayUNIScheduledOutputPage
}

enum Gateway_scheduledInputDataPageSortOrder {
  asc
  desc
}

input Gateway_scheduledInputDataPageSort {
  field: String!
  order: Gateway_scheduledInputDataPageSortOrder!
}

enum Gateway_scheduledInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_scheduledInputDataPageFilter {
  field: String!
  operation: Gateway_scheduledInputDataPageFilterOperation!
  value: String!
}

input Gateway_scheduledInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_scheduledInputDataPageSort
  filter: [Gateway_scheduledInputDataPageFilter]
  search: String
}

input Gateway_scheduledInputData {
  page: Gateway_scheduledInputDataPage
}

input Gateway_scheduledInput {
  groupID: String!
  data: Gateway_scheduledInputData!
}

type GatewayUNIScheduledPaymentOutputTransactions {
  transactionID: String
  date: String
  amount: Float
  status: String
}

type GatewayUNIScheduledPaymentOutput {
  id: String
  planID: String
  planName: String
  customerID: String
  customerName: String
  nextPayment: String
  amount: Float
  last4: String
  expires: String
  datePaid: String
  amountPaid: Float
  status: String
  transactions: [GatewayUNIScheduledPaymentOutputTransactions]
}

input Gateway_scheduledPaymentInputData {
  id: String!
}

input Gateway_scheduledPaymentInput {
  groupID: String!
  data: Gateway_scheduledPaymentInputData!
}

type GatewayUNIComputeCheckoutOutputBreakdown {
  discount: Float!
  tax: Float!
  shipping: Float!
  fees: Float!
  actualFees: Float!
  tip: Float!
  subtotal: Float!
  subscriptionTotal: Float!
  rawTotal: Float!
  total: Float!
  expectedTotal: Float!
}

type GatewayUNIComputeCheckoutOutputLineItemsProduct {
  sku: String!
  name: String!
  description: String
  price: Float!
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayUNIComputeCheckoutOutputLineItems {
  productId: String
  product: GatewayUNIComputeCheckoutOutputLineItemsProduct
  amount: Float!
  total: Float!
  metadata: String
}

type GatewayUNIComputeCheckoutOutputDiscountBreakdown {
  code: String!
  amount: Float!
}

enum GatewayUNIComputeCheckoutOutputPaymentInputMethodVerifyOrProcess {
  verify
  process
}

type GatewayUNIComputeCheckoutOutputPaymentInputLineItemsProduct {
  sku: String!
  name: String!
  description: String
  price: Float!
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayUNIComputeCheckoutOutputPaymentInputLineItems {
  productId: String
  product: GatewayUNIComputeCheckoutOutputPaymentInputLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum GatewayUNIComputeCheckoutOutputPaymentInputTipType {
  percentage
  fixed
}

enum GatewayUNIComputeCheckoutOutputPaymentInputTaxType {
  percentage
  fixed
}

type GatewayUNIComputeCheckoutOutputPaymentInput {
  methodVerifyOrProcess: GatewayUNIComputeCheckoutOutputPaymentInputMethodVerifyOrProcess
  lineItems: [GatewayUNIComputeCheckoutOutputPaymentInputLineItems]
  amount: Float
  tip: Float
  tipType: GatewayUNIComputeCheckoutOutputPaymentInputTipType
  tax: Float
  taxType: GatewayUNIComputeCheckoutOutputPaymentInputTaxType
  discountCodes: [String]
  shipping: Float
}

enum GatewayUNIComputeCheckoutOutputMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum GatewayUNIComputeCheckoutOutputMetaDynamicTipType {
  percentage
  fixed
}

type GatewayUNIComputeCheckoutOutputMetaDynamicTip {
  amount: Float!
  type: GatewayUNIComputeCheckoutOutputMetaDynamicTipType!
}

type GatewayUNIComputeCheckoutOutputMetaDynamicQuantityAmounts {
  id: String!
  quantity: Float!
}

type GatewayUNIComputeCheckoutOutputMetaDynamic {
  discountCodes: [String]
  paymentType: GatewayUNIComputeCheckoutOutputMetaDynamicPaymentType
  tip: GatewayUNIComputeCheckoutOutputMetaDynamicTip
  quantityAmounts: [GatewayUNIComputeCheckoutOutputMetaDynamicQuantityAmounts]
}

type GatewayUNIComputeCheckoutOutputMetaReference {
  id: String!
  source: String!
}

type GatewayUNIComputeCheckoutOutputMeta {
  source: String
  entrySource: String
  createdBy: String
  recurring: Boolean
  level2: Boolean
  narrative: String
  includeSurcharge: Boolean
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: GatewayUNIComputeCheckoutOutputMetaDynamic
  reference: GatewayUNIComputeCheckoutOutputMetaReference
}

type GatewayUNIComputeCheckoutOutput {
  breakdown: GatewayUNIComputeCheckoutOutputBreakdown
  lineItems: [GatewayUNIComputeCheckoutOutputLineItems]
  discountBreakdown: [GatewayUNIComputeCheckoutOutputDiscountBreakdown]
  allowEdit: Boolean
  allowExtraDiscount: Boolean
  disabledACH: Boolean
  disabledCard: Boolean
  paymentInput: GatewayUNIComputeCheckoutOutputPaymentInput
  surcharge: Float
  meta: GatewayUNIComputeCheckoutOutputMeta
  referenceID: String
  onSuccessUrl: String
  onFailureUrl: String
}

enum Gateway_computeCheckoutInputDataFormFormPaymentType {
  card
  ach
  gpecomm
}

input Gateway_computeCheckoutInputDataFormFormCard {
  cardToken: String
  cvc: String
  expiryDate: String
}

input Gateway_computeCheckoutInputDataFormFormAch {
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
}

input Gateway_computeCheckoutInputDataFormFormGpecomm {
  id: String
  cvv: String
}

enum Gateway_computeCheckoutInputDataFormFormMethodVerifyOrProcess {
  verify
  process
}

input Gateway_computeCheckoutInputDataFormFormLineItemsProduct {
  sku: String!
  name: String!
  description: String
  price: Float!
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

input Gateway_computeCheckoutInputDataFormFormLineItems {
  productId: String
  product: Gateway_computeCheckoutInputDataFormFormLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum Gateway_computeCheckoutInputDataFormFormTipType {
  percentage
  fixed
}

enum Gateway_computeCheckoutInputDataFormFormTaxType {
  percentage
  fixed
}

input Gateway_computeCheckoutInputDataFormForm {
  customerID: String
  nameOnCard: String
  email: String
  phoneNumber: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  customerCardID: String
  paymentType: Gateway_computeCheckoutInputDataFormFormPaymentType
  card: Gateway_computeCheckoutInputDataFormFormCard
  ach: Gateway_computeCheckoutInputDataFormFormAch
  gpecomm: Gateway_computeCheckoutInputDataFormFormGpecomm
  methodVerifyOrProcess: Gateway_computeCheckoutInputDataFormFormMethodVerifyOrProcess
  lineItems: [Gateway_computeCheckoutInputDataFormFormLineItems]
  amount: Float
  tip: Float
  tipType: Gateway_computeCheckoutInputDataFormFormTipType
  tax: Float
  taxType: Gateway_computeCheckoutInputDataFormFormTaxType
  discountCodes: [String]
  shipping: Float
}

enum Gateway_computeCheckoutInputDataFormMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum Gateway_computeCheckoutInputDataFormMetaDynamicTipType {
  percentage
  fixed
}

input Gateway_computeCheckoutInputDataFormMetaDynamicTip {
  amount: Float!
  type: Gateway_computeCheckoutInputDataFormMetaDynamicTipType!
}

input Gateway_computeCheckoutInputDataFormMetaDynamicQuantityAmounts {
  id: String!
  quantity: Float!
}

input Gateway_computeCheckoutInputDataFormMetaDynamic {
  discountCodes: [String]
  paymentType: Gateway_computeCheckoutInputDataFormMetaDynamicPaymentType
  tip: Gateway_computeCheckoutInputDataFormMetaDynamicTip
  quantityAmounts: [Gateway_computeCheckoutInputDataFormMetaDynamicQuantityAmounts]
}

input Gateway_computeCheckoutInputDataFormMetaReference {
  id: String!
  source: String!
}

input Gateway_computeCheckoutInputDataFormMeta {
  source: String
  entrySource: String
  createdBy: String
  recurring: Boolean
  level2: Boolean
  narrative: String
  includeSurcharge: Boolean
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: Gateway_computeCheckoutInputDataFormMetaDynamic
  reference: Gateway_computeCheckoutInputDataFormMetaReference
}

input Gateway_computeCheckoutInputDataForm {
  form: Gateway_computeCheckoutInputDataFormForm!
  meta: Gateway_computeCheckoutInputDataFormMeta
}

input Gateway_computeCheckoutInputData {
  form: Gateway_computeCheckoutInputDataForm!
}

input Gateway_computeCheckoutInput {
  groupID: String!
  data: Gateway_computeCheckoutInputData!
}

type GatewayUNICreateManualEntryOutput {
  transactionID: String
  subscriptionIDs: [String]
  status: String
}

enum Gateway_createManualEntryInputDataFormPaymentType {
  card
  ach
  gpecomm
}

input Gateway_createManualEntryInputDataFormCard {
  cardToken: String
  cvc: String
  expiryDate: String
}

input Gateway_createManualEntryInputDataFormAch {
  accountNumber: String
  routingNumber: String
  accountType: String
  accountHolderType: String
}

input Gateway_createManualEntryInputDataFormGpecomm {
  id: String
  cvv: String
}

enum Gateway_createManualEntryInputDataFormMethodVerifyOrProcess {
  verify
  process
}

input Gateway_createManualEntryInputDataFormLineItemsProduct {
  sku: String!
  name: String!
  description: String
  price: Float!
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

input Gateway_createManualEntryInputDataFormLineItems {
  productId: String
  product: Gateway_createManualEntryInputDataFormLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum Gateway_createManualEntryInputDataFormTipType {
  percentage
  fixed
}

enum Gateway_createManualEntryInputDataFormTaxType {
  percentage
  fixed
}

input Gateway_createManualEntryInputDataForm {
  customerID: String
  nameOnCard: String
  email: String
  phoneNumber: String
  billingAddress: String
  billingCity: String
  billingState: String
  billingZip: String
  billingCountry: String
  customerCardID: String
  paymentType: Gateway_createManualEntryInputDataFormPaymentType
  card: Gateway_createManualEntryInputDataFormCard
  ach: Gateway_createManualEntryInputDataFormAch
  gpecomm: Gateway_createManualEntryInputDataFormGpecomm
  methodVerifyOrProcess: Gateway_createManualEntryInputDataFormMethodVerifyOrProcess
  lineItems: [Gateway_createManualEntryInputDataFormLineItems]
  amount: Float
  tip: Float
  tipType: Gateway_createManualEntryInputDataFormTipType
  tax: Float
  taxType: Gateway_createManualEntryInputDataFormTaxType
  discountCodes: [String]
  shipping: Float
}

enum Gateway_createManualEntryInputDataMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum Gateway_createManualEntryInputDataMetaDynamicTipType {
  percentage
  fixed
}

input Gateway_createManualEntryInputDataMetaDynamicTip {
  amount: Float!
  type: Gateway_createManualEntryInputDataMetaDynamicTipType!
}

input Gateway_createManualEntryInputDataMetaDynamicQuantityAmounts {
  id: String!
  quantity: Float!
}

input Gateway_createManualEntryInputDataMetaDynamic {
  discountCodes: [String]
  paymentType: Gateway_createManualEntryInputDataMetaDynamicPaymentType
  tip: Gateway_createManualEntryInputDataMetaDynamicTip
  quantityAmounts: [Gateway_createManualEntryInputDataMetaDynamicQuantityAmounts]
}

input Gateway_createManualEntryInputDataMetaReference {
  id: String!
  source: String!
}

input Gateway_createManualEntryInputDataMeta {
  source: String
  entrySource: String
  createdBy: String
  recurring: Boolean
  level2: Boolean
  narrative: String
  includeSurcharge: Boolean
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: Gateway_createManualEntryInputDataMetaDynamic
  reference: Gateway_createManualEntryInputDataMetaReference
}

input Gateway_createManualEntryInputData {
  form: Gateway_createManualEntryInputDataForm!
  meta: Gateway_createManualEntryInputDataMeta
}

input Gateway_createManualEntryInput {
  groupID: String!
  data: Gateway_createManualEntryInputData!
}

type GatewayUNIRevertTransactionOutput {
  transactionID: String
}

input Gateway_revertTransactionInputData {
  transactionID: String!
  amount: Float
}

input Gateway_revertTransactionInput {
  groupID: String!
  data: Gateway_revertTransactionInputData!
}

type GatewayUNIAccountFundsOutput {
  card_transaction_limit_amount: String
  card_monthly_limit_amount: String
  bank_transfer_per_transaction_limit_amount: String
  bank_transfer_monthly_limit_amount: String
  bank_transfer_month_to_date_processed_amount: String
  card_month_to_date_processed_amount: String
  available_balance_amount: String
  pending_balance_amount: String
  reserve_balance_amount: String
  allowed_negative_balance_amount: String
  disable_payout_card_limit_percentage: String
  disable_payout_bank_transfer_limit_percentage: String
  card_month_to_date_processed_percentage: String
  bank_transfer_month_to_date_processed_percentage: String
}

input Gateway_accountFundsInputData {
  id: String
}

input Gateway_accountFundsInput {
  groupID: String!
  data: Gateway_accountFundsInputData!
}

type GatewayUNICreateCategoryOutput {
  id: String
}

enum Gateway_createCategoryInputDataStatus {
  active
  inactive
}

input Gateway_createCategoryInputData {
  name: String!
  status: Gateway_createCategoryInputDataStatus!
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

input Gateway_createCategoryInput {
  groupID: String!
  data: Gateway_createCategoryInputData!
}

type GatewayUNIUpdateCategoryOutput {
  id: String
}

enum Gateway_updateCategoryInputDataStatus {
  active
  inactive
}

input Gateway_updateCategoryInputData {
  id: String!
  name: String!
  status: Gateway_updateCategoryInputDataStatus!
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

input Gateway_updateCategoryInput {
  groupID: String!
  data: Gateway_updateCategoryInputData!
}

type GatewayUNIDeleteCategoryOutput {
  id: String
}

input Gateway_deleteCategoryInputData {
  id: String!
}

input Gateway_deleteCategoryInput {
  groupID: String!
  data: Gateway_deleteCategoryInputData!
}

enum GatewayUNICategoryOutputStatus {
  active
  inactive
}

type GatewayUNICategoryOutput {
  id: String
  name: String
  status: GatewayUNICategoryOutputStatus
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

input Gateway_categoryInputData {
  id: String!
}

input Gateway_categoryInput {
  groupID: String!
  data: Gateway_categoryInputData!
}

type GatewayUNICategoriesOutputData {
  id: String
  name: String
  status: String
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

type GatewayUNICategoriesOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNICategoriesOutputPage {
  total: Float
  range: GatewayUNICategoriesOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNICategoriesOutput {
  data: [GatewayUNICategoriesOutputData]
  page: GatewayUNICategoriesOutputPage
}

enum Gateway_categoriesInputDataPageSortOrder {
  asc
  desc
}

input Gateway_categoriesInputDataPageSort {
  field: String!
  order: Gateway_categoriesInputDataPageSortOrder!
}

enum Gateway_categoriesInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_categoriesInputDataPageFilter {
  field: String!
  operation: Gateway_categoriesInputDataPageFilterOperation!
  value: String!
}

input Gateway_categoriesInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_categoriesInputDataPageSort
  filter: [Gateway_categoriesInputDataPageFilter]
  search: String
}

input Gateway_categoriesInputData {
  page: Gateway_categoriesInputDataPage
}

input Gateway_categoriesInput {
  groupID: String!
  data: Gateway_categoriesInputData!
}

enum GatewayUNI_categoryBatchOutputStatus {
  active
  inactive
}

type GatewayUNI_categoryBatchOutput {
  id: String
  name: String
  status: GatewayUNI_categoryBatchOutputStatus
  color: String
  description: String
  subCategory: [String]
  colors: [String]
}

enum Gateway__categoryBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__categoryBatchInputDataPageSort {
  field: String!
  order: Gateway__categoryBatchInputDataPageSortOrder!
}

enum Gateway__categoryBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__categoryBatchInputDataPageFilter {
  field: String!
  operation: Gateway__categoryBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__categoryBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__categoryBatchInputDataPageSort
  filter: [Gateway__categoryBatchInputDataPageFilter]
  search: String
}

input Gateway__categoryBatchInputData {
  page: Gateway__categoryBatchInputDataPage
}

input Gateway__categoryBatchInput {
  groupID: String!
  data: Gateway__categoryBatchInputData!
}

type GatewayUNIProductsOutputData {
  id: String
  name: String
  price: Float
  discount: Float
  productStatus: String
  taxExempt: Boolean
  kitchenItem: Boolean
  sku: String
  brand: String
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayUNIProductsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIProductsOutputPage {
  total: Float
  range: GatewayUNIProductsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIProductsOutput {
  data: [GatewayUNIProductsOutputData]
  page: GatewayUNIProductsOutputPage
}

enum Gateway_productsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_productsInputDataPageSort {
  field: String!
  order: Gateway_productsInputDataPageSortOrder!
}

enum Gateway_productsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_productsInputDataPageFilter {
  field: String!
  operation: Gateway_productsInputDataPageFilterOperation!
  value: String!
}

input Gateway_productsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_productsInputDataPageSort
  filter: [Gateway_productsInputDataPageFilter]
  search: String
}

input Gateway_productsInputData {
  page: Gateway_productsInputDataPage
}

input Gateway_productsInput {
  groupID: String!
  data: Gateway_productsInputData!
}

type GatewayUNI_productBatchOutputProductImages {
  url: String!
  name: String!
}

type GatewayUNI_productBatchOutput {
  id: String
  name: String
  price: Float
  discount: Float
  productStatus: String
  taxExempt: Boolean
  kitchenItem: Boolean
  sku: String
  category: String
  subCategory: String
  brand: String
  itemWeight: Float
  length: Float
  breadth: Float
  width: Float
  description: String
  isInStore: Boolean
  isOnline: Boolean
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
  productImages: [GatewayUNI_productBatchOutputProductImages]
}

enum Gateway__productBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__productBatchInputDataPageSort {
  field: String!
  order: Gateway__productBatchInputDataPageSortOrder!
}

enum Gateway__productBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__productBatchInputDataPageFilter {
  field: String!
  operation: Gateway__productBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__productBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__productBatchInputDataPageSort
  filter: [Gateway__productBatchInputDataPageFilter]
  search: String
}

input Gateway__productBatchInputData {
  page: Gateway__productBatchInputDataPage
}

input Gateway__productBatchInput {
  groupID: String!
  data: Gateway__productBatchInputData!
}

type GatewayUNICreateProductOutput {
  productID: String
}

input Gateway_createProductInputDataFormProductImages {
  b64: String
  type: String
  url: String
}

input Gateway_createProductInputDataForm {
  name: String
  category: String
  subCategory: String
  sku: String
  brand: String
  price: Float
  itemWeight: Float
  length: Float
  breadth: Float
  width: Float
  description: String
  isInStore: Boolean
  isOnline: Boolean
  kitchenItem: String
  taxExempt: Boolean
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
  productImages: [Gateway_createProductInputDataFormProductImages]
}

input Gateway_createProductInputData {
  form: Gateway_createProductInputDataForm!
}

input Gateway_createProductInput {
  groupID: String!
  data: Gateway_createProductInputData!
}

type GatewayUNIUpdateProductOutput {
  productID: String
}

input Gateway_updateProductInputDataFormProductImages {
  b64: String!
  type: String!
}

input Gateway_updateProductInputDataForm {
  name: String
  category: String
  subCategory: String
  brand: String
  price: Float
  itemWeight: Float
  length: Float
  breadth: Float
  width: Float
  description: String
  isInStore: Boolean
  isOnline: Boolean
  kitchenItem: String
  taxExempt: Boolean
  sku: String
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
  productImages: [Gateway_updateProductInputDataFormProductImages]
}

input Gateway_updateProductInputData {
  productID: String!
  form: Gateway_updateProductInputDataForm!
}

input Gateway_updateProductInput {
  groupID: String!
  data: Gateway_updateProductInputData!
}

type GatewayUNIDeleteProductOutput {
  productID: String
}

input Gateway_deleteProductInputData {
  productID: String!
}

input Gateway_deleteProductInput {
  groupID: String!
  data: Gateway_deleteProductInputData!
}

type GatewayUNIProductOutputProductImages {
  url: String!
  name: String!
}

type GatewayUNIProductOutput {
  id: String
  name: String
  price: Float
  discount: Float
  productStatus: String
  taxExempt: Boolean
  kitchenItem: Boolean
  sku: String
  category: String
  subCategory: String
  brand: String
  itemWeight: Float
  length: Float
  breadth: Float
  width: Float
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
  description: String
  isInStore: Boolean
  isOnline: Boolean
  productImages: [GatewayUNIProductOutputProductImages]
}

input Gateway_productInputData {
  productID: String!
}

input Gateway_productInput {
  groupID: String!
  data: Gateway_productInputData!
}

type GatewayUNICreateDiscountOutput {
  id: String
}

enum Gateway_createDiscountInputDataType {
  percentage
  fixed
}

enum Gateway_createDiscountInputDataStatus {
  active
  inactive
}

input Gateway_createDiscountInputData {
  name: String!
  type: Gateway_createDiscountInputDataType!
  discount: Float!
  status: Gateway_createDiscountInputDataStatus!
  maxDiscount: Float
  validFrom: String
  validTo: String
  maxClaims: Float
  validSKU: [String]
  scope: String!
}

input Gateway_createDiscountInput {
  groupID: String!
  data: Gateway_createDiscountInputData!
}

type GatewayUNIUpdateDiscountOutput {
  id: String
}

enum Gateway_updateDiscountInputDataType {
  percentage
  fixed
}

enum Gateway_updateDiscountInputDataStatus {
  active
  inactive
}

input Gateway_updateDiscountInputData {
  id: String!
  name: String!
  type: Gateway_updateDiscountInputDataType!
  discount: Float!
  status: Gateway_updateDiscountInputDataStatus!
  maxDiscount: Float
  validFrom: String
  validTo: String
  maxClaims: Float
  validSKU: [String]
  scope: String!
}

input Gateway_updateDiscountInput {
  groupID: String!
  data: Gateway_updateDiscountInputData!
}

type GatewayUNIDeleteDiscountOutput {
  id: String
}

input Gateway_deleteDiscountInputData {
  id: String!
}

input Gateway_deleteDiscountInput {
  groupID: String!
  data: Gateway_deleteDiscountInputData!
}

enum GatewayUNIDiscountOutputType {
  percentage
  fixed
}

enum GatewayUNIDiscountOutputStatus {
  active
  inactive
}

type GatewayUNIDiscountOutput {
  id: String
  name: String
  type: GatewayUNIDiscountOutputType
  discount: Float
  status: GatewayUNIDiscountOutputStatus
  maxDiscount: Float
  validFrom: String
  validTo: String
  maxClaims: Float
  validSKU: [String]
}

input Gateway_discountInputData {
  id: String!
}

input Gateway_discountInput {
  groupID: String!
  data: Gateway_discountInputData!
}

enum GatewayUNIDiscountsOutputDataType {
  percentage
  fixed
}

enum GatewayUNIDiscountsOutputDataStatus {
  active
  inactive
}

type GatewayUNIDiscountsOutputData {
  id: String
  name: String
  type: GatewayUNIDiscountsOutputDataType
  discount: Float
  status: GatewayUNIDiscountsOutputDataStatus
}

type GatewayUNIDiscountsOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIDiscountsOutputPage {
  total: Float
  range: GatewayUNIDiscountsOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIDiscountsOutput {
  data: [GatewayUNIDiscountsOutputData]
  page: GatewayUNIDiscountsOutputPage
}

enum Gateway_discountsInputDataPageSortOrder {
  asc
  desc
}

input Gateway_discountsInputDataPageSort {
  field: String!
  order: Gateway_discountsInputDataPageSortOrder!
}

enum Gateway_discountsInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_discountsInputDataPageFilter {
  field: String!
  operation: Gateway_discountsInputDataPageFilterOperation!
  value: String!
}

input Gateway_discountsInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_discountsInputDataPageSort
  filter: [Gateway_discountsInputDataPageFilter]
  search: String
}

input Gateway_discountsInputData {
  scope: String!
  page: Gateway_discountsInputDataPage
}

input Gateway_discountsInput {
  groupID: String!
  data: Gateway_discountsInputData!
}

enum GatewayUNI_discountBatchOutputType {
  percentage
  fixed
}

enum GatewayUNI_discountBatchOutputStatus {
  active
  inactive
}

type GatewayUNI_discountBatchOutput {
  id: String
  name: String
  type: GatewayUNI_discountBatchOutputType
  discount: Float
  status: GatewayUNI_discountBatchOutputStatus
  maxDiscount: Float
  validFrom: String
  validTo: String
  maxClaims: Float
  validSKU: [String]
}

enum Gateway__discountBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__discountBatchInputDataPageSort {
  field: String!
  order: Gateway__discountBatchInputDataPageSortOrder!
}

enum Gateway__discountBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__discountBatchInputDataPageFilter {
  field: String!
  operation: Gateway__discountBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__discountBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__discountBatchInputDataPageSort
  filter: [Gateway__discountBatchInputDataPageFilter]
  search: String
}

input Gateway__discountBatchInputData {
  page: Gateway__discountBatchInputDataPage
  scope: String!
}

input Gateway__discountBatchInput {
  groupID: String!
  data: Gateway__discountBatchInputData!
}

type GatewayUNICreatePayLinkOutput {
  payLinkID: String
  paymentData: String
  token: String
  groupID: String
  url: String
}

enum Gateway_createPayLinkInputDataFormPricingMethodVerifyOrProcess {
  verify
  process
}

input Gateway_createPayLinkInputDataFormPricingLineItemsProduct {
  sku: String!
  name: String!
  description: String
  price: Float!
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

input Gateway_createPayLinkInputDataFormPricingLineItems {
  productId: String
  product: Gateway_createPayLinkInputDataFormPricingLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum Gateway_createPayLinkInputDataFormPricingTipType {
  percentage
  fixed
}

enum Gateway_createPayLinkInputDataFormPricingTaxType {
  percentage
  fixed
}

input Gateway_createPayLinkInputDataFormPricing {
  methodVerifyOrProcess: Gateway_createPayLinkInputDataFormPricingMethodVerifyOrProcess
  lineItems: [Gateway_createPayLinkInputDataFormPricingLineItems]
  amount: Float
  tip: Float
  tipType: Gateway_createPayLinkInputDataFormPricingTipType
  tax: Float
  taxType: Gateway_createPayLinkInputDataFormPricingTaxType
  discountCodes: [String]
  shipping: Float
}

enum Gateway_createPayLinkInputDataFormMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum Gateway_createPayLinkInputDataFormMetaDynamicTipType {
  percentage
  fixed
}

input Gateway_createPayLinkInputDataFormMetaDynamicTip {
  amount: Float!
  type: Gateway_createPayLinkInputDataFormMetaDynamicTipType!
}

input Gateway_createPayLinkInputDataFormMetaDynamicQuantityAmounts {
  id: String!
  quantity: Float!
}

input Gateway_createPayLinkInputDataFormMetaDynamic {
  discountCodes: [String]
  paymentType: Gateway_createPayLinkInputDataFormMetaDynamicPaymentType
  tip: Gateway_createPayLinkInputDataFormMetaDynamicTip
  quantityAmounts: [Gateway_createPayLinkInputDataFormMetaDynamicQuantityAmounts]
}

input Gateway_createPayLinkInputDataFormMetaReference {
  id: String!
  source: String!
}

input Gateway_createPayLinkInputDataFormMeta {
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: Gateway_createPayLinkInputDataFormMetaDynamic
  reference: Gateway_createPayLinkInputDataFormMetaReference
}

input Gateway_createPayLinkInputDataForm {
  pricing: Gateway_createPayLinkInputDataFormPricing!
  meta: Gateway_createPayLinkInputDataFormMeta
  allowEdit: Boolean
  allowExtraDiscount: Boolean
  allowTip: Boolean
  disableCC: Boolean
  disableACH: Boolean
  validUntil: String
  referenceID: String
  onSuccessURL: String
  onFailureURL: String
}

input Gateway_createPayLinkInputData {
  form: Gateway_createPayLinkInputDataForm!
  tokenID: String
  autoToken: Boolean
  save: Boolean
}

input Gateway_createPayLinkInput {
  groupID: String!
  data: Gateway_createPayLinkInputData!
}

type GatewayUNIUpdatePayLinkOutput {
  payLinkID: String
  paymentData: String
  token: String
  groupID: String
}

enum Gateway_updatePayLinkInputDataFormPricingMethodVerifyOrProcess {
  verify
  process
}

input Gateway_updatePayLinkInputDataFormPricingLineItemsProduct {
  sku: String!
  name: String!
  description: String
  price: Float!
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

input Gateway_updatePayLinkInputDataFormPricingLineItems {
  productId: String
  product: Gateway_updatePayLinkInputDataFormPricingLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum Gateway_updatePayLinkInputDataFormPricingTipType {
  percentage
  fixed
}

enum Gateway_updatePayLinkInputDataFormPricingTaxType {
  percentage
  fixed
}

input Gateway_updatePayLinkInputDataFormPricing {
  methodVerifyOrProcess: Gateway_updatePayLinkInputDataFormPricingMethodVerifyOrProcess
  lineItems: [Gateway_updatePayLinkInputDataFormPricingLineItems]
  amount: Float
  tip: Float
  tipType: Gateway_updatePayLinkInputDataFormPricingTipType
  tax: Float
  taxType: Gateway_updatePayLinkInputDataFormPricingTaxType
  discountCodes: [String]
  shipping: Float
}

enum Gateway_updatePayLinkInputDataFormMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum Gateway_updatePayLinkInputDataFormMetaDynamicTipType {
  percentage
  fixed
}

input Gateway_updatePayLinkInputDataFormMetaDynamicTip {
  amount: Float!
  type: Gateway_updatePayLinkInputDataFormMetaDynamicTipType!
}

input Gateway_updatePayLinkInputDataFormMetaDynamicQuantityAmounts {
  id: String!
  quantity: Float!
}

input Gateway_updatePayLinkInputDataFormMetaDynamic {
  discountCodes: [String]
  paymentType: Gateway_updatePayLinkInputDataFormMetaDynamicPaymentType
  tip: Gateway_updatePayLinkInputDataFormMetaDynamicTip
  quantityAmounts: [Gateway_updatePayLinkInputDataFormMetaDynamicQuantityAmounts]
}

input Gateway_updatePayLinkInputDataFormMetaReference {
  id: String!
  source: String!
}

input Gateway_updatePayLinkInputDataFormMeta {
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: Gateway_updatePayLinkInputDataFormMetaDynamic
  reference: Gateway_updatePayLinkInputDataFormMetaReference
}

input Gateway_updatePayLinkInputDataForm {
  pricing: Gateway_updatePayLinkInputDataFormPricing!
  meta: Gateway_updatePayLinkInputDataFormMeta
  allowEdit: Boolean
  allowExtraDiscount: Boolean
  allowTip: Boolean
  disableCC: Boolean
  disableACH: Boolean
  validUntil: String
  referenceID: String
  onSuccessURL: String
  onFailureURL: String
}

input Gateway_updatePayLinkInputData {
  id: String!
  form: Gateway_updatePayLinkInputDataForm!
  tokenID: String
  autoToken: Boolean
  save: Boolean
}

input Gateway_updatePayLinkInput {
  groupID: String!
  data: Gateway_updatePayLinkInputData!
}

type GatewayUNIDeletePayLinkOutput {
  id: String
}

input Gateway_deletePayLinkInputData {
  id: String!
}

input Gateway_deletePayLinkInput {
  groupID: String!
  data: Gateway_deletePayLinkInputData!
}

enum GatewayUNIPayLinkOutputPaymentRawPricingMethodVerifyOrProcess {
  verify
  process
}

type GatewayUNIPayLinkOutputPaymentRawPricingLineItemsProduct {
  sku: String
  name: String
  description: String
  price: Float
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayUNIPayLinkOutputPaymentRawPricingLineItems {
  productId: String
  product: GatewayUNIPayLinkOutputPaymentRawPricingLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum GatewayUNIPayLinkOutputPaymentRawPricingTipType {
  percentage
  fixed
}

enum GatewayUNIPayLinkOutputPaymentRawPricingTaxType {
  percentage
  fixed
}

type GatewayUNIPayLinkOutputPaymentRawPricing {
  methodVerifyOrProcess: GatewayUNIPayLinkOutputPaymentRawPricingMethodVerifyOrProcess
  lineItems: [GatewayUNIPayLinkOutputPaymentRawPricingLineItems]
  amount: Float
  tip: Float
  tipType: GatewayUNIPayLinkOutputPaymentRawPricingTipType
  tax: Float
  taxType: GatewayUNIPayLinkOutputPaymentRawPricingTaxType
  discountCodes: [String]
  shipping: Float
}

enum GatewayUNIPayLinkOutputPaymentRawMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum GatewayUNIPayLinkOutputPaymentRawMetaDynamicTipType {
  percentage
  fixed
}

type GatewayUNIPayLinkOutputPaymentRawMetaDynamicTip {
  amount: Float
  type: GatewayUNIPayLinkOutputPaymentRawMetaDynamicTipType
}

type GatewayUNIPayLinkOutputPaymentRawMetaDynamicQuantityAmounts {
  id: String
  quantity: Float
}

type GatewayUNIPayLinkOutputPaymentRawMetaDynamic {
  discountCodes: [String]
  paymentType: GatewayUNIPayLinkOutputPaymentRawMetaDynamicPaymentType
  tip: GatewayUNIPayLinkOutputPaymentRawMetaDynamicTip
  quantityAmounts: [GatewayUNIPayLinkOutputPaymentRawMetaDynamicQuantityAmounts]
}

type GatewayUNIPayLinkOutputPaymentRawMetaReference {
  id: String
  source: String
}

type GatewayUNIPayLinkOutputPaymentRawMeta {
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: GatewayUNIPayLinkOutputPaymentRawMetaDynamic
  reference: GatewayUNIPayLinkOutputPaymentRawMetaReference
}

type GatewayUNIPayLinkOutputPaymentRaw {
  pricing: GatewayUNIPayLinkOutputPaymentRawPricing
  meta: GatewayUNIPayLinkOutputPaymentRawMeta
  allowEdit: Boolean
  allowExtraDiscount: Boolean
  allowTip: Boolean
  disableCC: Boolean
  disableACH: Boolean
  validUntil: String
  referenceID: String
  onSuccessURL: String
  onFailureURL: String
}

type GatewayUNIPayLinkOutputPaymentCheckBreakdown {
  discount: Float
  tax: Float
  shipping: Float
  fees: Float
  actualFees: Float
  tip: Float
  subtotal: Float
  subscriptionTotal: Float
  rawTotal: Float
  total: Float
  expectedTotal: Float
}

type GatewayUNIPayLinkOutputPaymentCheckLineItemsProduct {
  sku: String
  name: String
  description: String
  price: Float
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayUNIPayLinkOutputPaymentCheckLineItems {
  productId: String
  product: GatewayUNIPayLinkOutputPaymentCheckLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

type GatewayUNIPayLinkOutputPaymentCheckDiscountBreakdown {
  code: String
  amount: Float
}

enum GatewayUNIPayLinkOutputPaymentCheckPaymentInputMethodVerifyOrProcess {
  verify
  process
}

type GatewayUNIPayLinkOutputPaymentCheckPaymentInputLineItemsProduct {
  sku: String
  name: String
  description: String
  price: Float
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayUNIPayLinkOutputPaymentCheckPaymentInputLineItems {
  productId: String
  product: GatewayUNIPayLinkOutputPaymentCheckPaymentInputLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum GatewayUNIPayLinkOutputPaymentCheckPaymentInputTipType {
  percentage
  fixed
}

enum GatewayUNIPayLinkOutputPaymentCheckPaymentInputTaxType {
  percentage
  fixed
}

type GatewayUNIPayLinkOutputPaymentCheckPaymentInput {
  methodVerifyOrProcess: GatewayUNIPayLinkOutputPaymentCheckPaymentInputMethodVerifyOrProcess
  lineItems: [GatewayUNIPayLinkOutputPaymentCheckPaymentInputLineItems]
  amount: Float
  tip: Float
  tipType: GatewayUNIPayLinkOutputPaymentCheckPaymentInputTipType
  tax: Float
  taxType: GatewayUNIPayLinkOutputPaymentCheckPaymentInputTaxType
  discountCodes: [String]
  shipping: Float
}

enum GatewayUNIPayLinkOutputPaymentCheckMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum GatewayUNIPayLinkOutputPaymentCheckMetaDynamicTipType {
  percentage
  fixed
}

type GatewayUNIPayLinkOutputPaymentCheckMetaDynamicTip {
  amount: Float
  type: GatewayUNIPayLinkOutputPaymentCheckMetaDynamicTipType
}

type GatewayUNIPayLinkOutputPaymentCheckMetaDynamicQuantityAmounts {
  id: String
  quantity: Float
}

type GatewayUNIPayLinkOutputPaymentCheckMetaDynamic {
  discountCodes: [String]
  paymentType: GatewayUNIPayLinkOutputPaymentCheckMetaDynamicPaymentType
  tip: GatewayUNIPayLinkOutputPaymentCheckMetaDynamicTip
  quantityAmounts: [GatewayUNIPayLinkOutputPaymentCheckMetaDynamicQuantityAmounts]
}

type GatewayUNIPayLinkOutputPaymentCheckMetaReference {
  id: String
  source: String
}

type GatewayUNIPayLinkOutputPaymentCheckMeta {
  source: String
  entrySource: String
  createdBy: String
  recurring: Boolean
  level2: Boolean
  narrative: String
  includeSurcharge: Boolean
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: GatewayUNIPayLinkOutputPaymentCheckMetaDynamic
  reference: GatewayUNIPayLinkOutputPaymentCheckMetaReference
}

type GatewayUNIPayLinkOutputPaymentCheck {
  breakdown: GatewayUNIPayLinkOutputPaymentCheckBreakdown
  lineItems: [GatewayUNIPayLinkOutputPaymentCheckLineItems]
  discountBreakdown: [GatewayUNIPayLinkOutputPaymentCheckDiscountBreakdown]
  allowEdit: Boolean
  allowExtraDiscount: Boolean
  disabledACH: Boolean
  disabledCard: Boolean
  paymentInput: GatewayUNIPayLinkOutputPaymentCheckPaymentInput
  surcharge: Float
  meta: GatewayUNIPayLinkOutputPaymentCheckMeta
  referenceID: String
  onSuccessUrl: String
  onFailureUrl: String
}

type GatewayUNIPayLinkOutput {
  id: String
  paymentData: String
  paymentRaw: GatewayUNIPayLinkOutputPaymentRaw
  paymentCheck: GatewayUNIPayLinkOutputPaymentCheck
  createdAt: String
}

input Gateway_payLinkInputData {
  id: String!
}

input Gateway_payLinkInput {
  groupID: String!
  data: Gateway_payLinkInputData!
}

type GatewayUNIPayLinksOutputData {
  id: String
  paymentData: String
  items: [String]
  total: Float
  createdAt: String
}

type GatewayUNIPayLinksOutputPageRange {
  from: Float!
  to: Float!
}

type GatewayUNIPayLinksOutputPage {
  total: Float
  range: GatewayUNIPayLinksOutputPageRange
  page: Float
  pageSize: Float
}

type GatewayUNIPayLinksOutput {
  data: [GatewayUNIPayLinksOutputData]
  page: GatewayUNIPayLinksOutputPage
}

enum Gateway_payLinksInputDataPageSortOrder {
  asc
  desc
}

input Gateway_payLinksInputDataPageSort {
  field: String!
  order: Gateway_payLinksInputDataPageSortOrder!
}

enum Gateway_payLinksInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway_payLinksInputDataPageFilter {
  field: String!
  operation: Gateway_payLinksInputDataPageFilterOperation!
  value: String!
}

input Gateway_payLinksInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway_payLinksInputDataPageSort
  filter: [Gateway_payLinksInputDataPageFilter]
  search: String
}

input Gateway_payLinksInputData {
  page: Gateway_payLinksInputDataPage
}

input Gateway_payLinksInput {
  groupID: String!
  data: Gateway_payLinksInputData!
}

enum GatewayUNI_payLinkBatchOutputPaymentRawPricingMethodVerifyOrProcess {
  verify
  process
}

type GatewayUNI_payLinkBatchOutputPaymentRawPricingLineItemsProduct {
  sku: String
  name: String
  description: String
  price: Float
  isRecurring: Boolean
  recurringInterval: Float
  recurringFrequency: Float
  recurringTotalCycles: Float
  recurringTrialDays: Float
  recurringSetupFee: Float
}

type GatewayUNI_payLinkBatchOutputPaymentRawPricingLineItems {
  productId: String
  product: GatewayUNI_payLinkBatchOutputPaymentRawPricingLineItemsProduct
  amount: Float
  total: Float
  metadata: String
}

enum GatewayUNI_payLinkBatchOutputPaymentRawPricingTipType {
  percentage
  fixed
}

enum GatewayUNI_payLinkBatchOutputPaymentRawPricingTaxType {
  percentage
  fixed
}

type GatewayUNI_payLinkBatchOutputPaymentRawPricing {
  methodVerifyOrProcess: GatewayUNI_payLinkBatchOutputPaymentRawPricingMethodVerifyOrProcess
  lineItems: [GatewayUNI_payLinkBatchOutputPaymentRawPricingLineItems]
  amount: Float
  tip: Float
  tipType: GatewayUNI_payLinkBatchOutputPaymentRawPricingTipType
  tax: Float
  taxType: GatewayUNI_payLinkBatchOutputPaymentRawPricingTaxType
  discountCodes: [String]
  shipping: Float
}

enum GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicPaymentType {
  card
  ach
  gpecomm
}

enum GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicTipType {
  percentage
  fixed
}

type GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicTip {
  amount: Float
  type: GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicTipType
}

type GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicQuantityAmounts {
  id: String
  quantity: Float
}

type GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamic {
  discountCodes: [String]
  paymentType: GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicPaymentType
  tip: GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicTip
  quantityAmounts: [GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamicQuantityAmounts]
}

type GatewayUNI_payLinkBatchOutputPaymentRawMetaReference {
  id: String
  source: String
}

type GatewayUNI_payLinkBatchOutputPaymentRawMeta {
  encryptedCheckoutToken: String
  encrptedCheckoutTokenSecret: String
  dynamic: GatewayUNI_payLinkBatchOutputPaymentRawMetaDynamic
  reference: GatewayUNI_payLinkBatchOutputPaymentRawMetaReference
}

type GatewayUNI_payLinkBatchOutputPaymentRaw {
  pricing: GatewayUNI_payLinkBatchOutputPaymentRawPricing
  meta: GatewayUNI_payLinkBatchOutputPaymentRawMeta
  allowEdit: Boolean
  allowExtraDiscount: Boolean
  allowTip: Boolean
  disableCC: Boolean
  disableACH: Boolean
  validUntil: String
  referenceID: String
  onSuccessURL: String
  onFailureURL: String
}

type GatewayUNI_payLinkBatchOutput {
  id: String
  paymentData: String
  paymentRaw: GatewayUNI_payLinkBatchOutputPaymentRaw
  createdAt: String
}

enum Gateway__payLinkBatchInputDataPageSortOrder {
  asc
  desc
}

input Gateway__payLinkBatchInputDataPageSort {
  field: String!
  order: Gateway__payLinkBatchInputDataPageSortOrder!
}

enum Gateway__payLinkBatchInputDataPageFilterOperation {
  contains
  equals
  gt
  lt
  gte
  lte
  neq
}

input Gateway__payLinkBatchInputDataPageFilter {
  field: String!
  operation: Gateway__payLinkBatchInputDataPageFilterOperation!
  value: String!
}

input Gateway__payLinkBatchInputDataPage {
  page: Float
  pageSize: Float
  sort: Gateway__payLinkBatchInputDataPageSort
  filter: [Gateway__payLinkBatchInputDataPageFilter]
  search: String
}

input Gateway__payLinkBatchInputData {
  page: Gateway__payLinkBatchInputDataPage
}

input Gateway__payLinkBatchInput {
  groupID: String!
  data: Gateway__payLinkBatchInputData!
}

type DashboardSummaryOutputCapturedTimeEntries {
  time: Float
  value: String
}

type DashboardSummaryOutputCaptured {
  total: Float
  percentageChange: String
  timeStart: Float
  timeEnd: Float
  timeEntries: [DashboardSummaryOutputCapturedTimeEntries]
}

type DashboardSummaryOutputRefundsTimeEntries {
  time: Float
  value: String
}

type DashboardSummaryOutputRefunds {
  total: Float
  percentageChange: String
  timeStart: Float
  timeEnd: Float
  timeEntries: [DashboardSummaryOutputRefundsTimeEntries]
}

type DashboardSummaryOutputBatchedTimeEntries {
  time: Float
  value: String
}

type DashboardSummaryOutputBatched {
  total: Float
  percentageChange: String
  timeStart: Float
  timeEnd: Float
  timeEntries: [DashboardSummaryOutputBatchedTimeEntries]
}

type DashboardSummaryOutputDepositsTimeEntries {
  time: Float
  value: String
}

type DashboardSummaryOutputDeposits {
  total: Float
  percentageChange: String
  timeStart: Float
  timeEnd: Float
  timeEntries: [DashboardSummaryOutputDepositsTimeEntries]
}

type DashboardSummaryOutput {
  dateStart: String
  dateEnd: String
  totalPortfolio: Float
  captured: DashboardSummaryOutputCaptured
  refunds: DashboardSummaryOutputRefunds
  batched: DashboardSummaryOutputBatched
  deposits: DashboardSummaryOutputDeposits
}

input Dashboard_get_summaryInput {
  groupID: String
  startDate: String
  endDate: String
}

type LocationSummaryOutputData {
  locationID: String
  locationName: String
  changePercentage: String
  currentYearTotal: String
  lastYearTotal: String
  yearChangePercentage: String
}

type LocationSummaryOutput {
  data: [LocationSummaryOutputData]!
}

input Dashboard_location_summaryInputRange {
  dateStart: String!
  dateEnd: String!
}

input Dashboard_location_summaryInput {
  groupID: String
  range: Dashboard_location_summaryInputRange
}

type OTPGenerateOutput {
  sid: String!
  to: String!
}

enum Otp_generateInputChannel {
  sms
  email
}

input Otp_generateInput {
  contact: String!
  channel: Otp_generateInputChannel!
}

type OTPVerifyOutput {
  sid: String
  status: Boolean!
}

input Otp_verifyInput {
  sid: String!
  code: String!
}

input Authclient_mfa_checkInput {
  browserID: String!
  email: String!
}

type MFAGenerateOutput {
  sid: String!
  to: String!
}

enum Authclient_mfa_generateInputChannel {
  sms
  email
}

input Authclient_mfa_generateInput {
  browserID: String!
  email: String!
  channel: Authclient_mfa_generateInputChannel!
  oneTime: Boolean
}

type MFAVerifyOutput {
  sid: String
  status: Boolean!
}

input Authclient_mfa_verifyInput {
  sid: String!
  code: String!
}

enum Otp_auth_generateInputChannel {
  sms
  email
}

input Otp_auth_generateInput {
  channel: Otp_auth_generateInputChannel!
  purpose: String
  metadata: String
}

type AddressSearchAutoCompleteResultResultSummary {
  query: String!
  queryType: String!
  queryTime: Float!
  numResults: Float!
  offset: Float!
  totalResults: Float!
  fuzzyLevel: Float!
}

type AddressSearchAutoCompleteResultResultResultsAddress {
  streetNumber: String
  streetName: String!
  municipality: String!
  neighbourhood: String
  countrySecondarySubdivision: String!
  countrySubdivision: String!
  countrySubdivisionName: String!
  countrySubdivisionCode: String!
  postalCode: String!
  extendedPostalCode: String
  countryCode: String!
  country: String!
  countryCodeISO3: String!
  freeformAddress: String!
  localName: String!
}

type AddressSearchAutoCompleteResultResultResultsPosition {
  lat: Float!
  lon: Float!
}

type AddressSearchAutoCompleteResultResultResultsViewportTopLeftPoint {
  lat: Float!
  lon: Float!
}

type AddressSearchAutoCompleteResultResultResultsViewportBtmRightPoint {
  lat: Float!
  lon: Float!
}

type AddressSearchAutoCompleteResultResultResultsViewport {
  topLeftPoint: AddressSearchAutoCompleteResultResultResultsViewportTopLeftPoint!
  btmRightPoint: AddressSearchAutoCompleteResultResultResultsViewportBtmRightPoint!
}

type AddressSearchAutoCompleteResultResultResultsEntryPointsPosition {
  lat: Float!
  lon: Float!
}

type AddressSearchAutoCompleteResultResultResultsEntryPoints {
  type: String!
  position: AddressSearchAutoCompleteResultResultResultsEntryPointsPosition!
}

type AddressSearchAutoCompleteResultResultResultsAddressRangesFrom {
  lat: Float!
  lon: Float!
}

type AddressSearchAutoCompleteResultResultResultsAddressRangesTo {
  lat: Float!
  lon: Float!
}

type AddressSearchAutoCompleteResultResultResultsAddressRanges {
  rangeLeft: String
  rangeRight: String
  from: AddressSearchAutoCompleteResultResultResultsAddressRangesFrom!
  to: AddressSearchAutoCompleteResultResultResultsAddressRangesTo!
}

type AddressSearchAutoCompleteResultResultResults {
  type: String!
  id: String!
  score: Float!
  address: AddressSearchAutoCompleteResultResultResultsAddress!
  position: AddressSearchAutoCompleteResultResultResultsPosition!
  viewport: AddressSearchAutoCompleteResultResultResultsViewport!
  entryPoints: [AddressSearchAutoCompleteResultResultResultsEntryPoints]
  addressRanges: AddressSearchAutoCompleteResultResultResultsAddressRanges
}

type AddressSearchAutoCompleteResultResult {
  summary: AddressSearchAutoCompleteResultResultSummary!
  results: [AddressSearchAutoCompleteResultResultResults]!
}

type AddressSearchAutoCompleteResult {
  result: AddressSearchAutoCompleteResultResult!
}

input Address_searchInput {
  search: String!
  offset: Float
}

input Affiliation_setBankAccountInput {
  bank_routingNumber: String!
  bank_accountNumber: String!
  bank_accountName: String!
}

type AffiliateCodeOutput {
  code: String!
  description: String
}

input Affiliation_setAffiliateCodeInput {
  code: String!
  description: String
}

type AffiliatedUsersWithMerchantsOutputDataUser {
  id: String
  name: String
  email: String
  lastLogin: String
  title: String
  earnings: Float
}

type AffiliatedUsersWithMerchantsOutputDataMerchant {
  name: String
  city: String
  state: String
  status: String
  createdAt: String
}

type AffiliatedUsersWithMerchantsOutputData {
  user: AffiliatedUsersWithMerchantsOutputDataUser!
  merchant: [AffiliatedUsersWithMerchantsOutputDataMerchant]!
}

type AffiliatedUsersWithMerchantsOutput {
  totalEarnings: Float!
  totalWithdrawn: Float!
  totalWithdrawable: Float!
  data: [AffiliatedUsersWithMerchantsOutputData]!
}

input Affiliation_getAffiliatedUsersWithMerchantsInput {
  take: Float
  skip: Float
}

type GHLMerchantUserDataDataBusiness {
  address: String
  city: String
  state: String
  country: String
  postalCode: String
  name: String
  website: String
  timezone: String
  logoUrl: String
}

type GHLMerchantUserDataDataSocial {
  facebookUrl: String
  googlePlus: String
  linkedIn: String
  foursquare: String
  twitter: String
  yelp: String
  instagram: String
  youtube: String
  pinterest: String
  blogRss: String
  googlePlacesId: String
}

type GHLMerchantUserDataDataSettings {
  allowDuplicateContact: Boolean
  allowDuplicateOpportunity: Boolean
  allowFacebookNameMerge: Boolean
  disableContactTimezone: Boolean
}

type GHLMerchantUserDataData {
  id: String
  companyId: String
  name: String
  domain: String
  address: String
  city: String
  state: String
  logoUrl: String
  country: String
  postalCode: String
  website: String
  timezone: String
  firstName: String
  lastName: String
  email: String
  phone: String
  business: GHLMerchantUserDataDataBusiness
  social: GHLMerchantUserDataDataSocial
  settings: GHLMerchantUserDataDataSettings
}

type GHLMerchantUserData {
  data: GHLMerchantUserDataData!
}

input Ghl_api_getMerchantGHLInput {
  locationID: String!
  groupID: String!
}

type GetOrderDetailsDataDataItemsProductSeo {
  title: String
  description: String
}

type GetOrderDetailsDataDataItemsProduct {
  _id: String
  type: String
  parentId: String
  archived: Boolean
  deleted: Boolean
  image: String
  isTaxesEnabled: Boolean
  isLabelEnabled: Boolean
  locationId: String
  name: String
  productType: String
  description: String
  availableInStore: Boolean
  seo: GetOrderDetailsDataDataItemsProductSeo
  userId: String
  createdAt: String
  updatedAt: String
}

type GetOrderDetailsDataDataItemsPriceRecurring {
  interval: String
  intervalCount: Float
}

type GetOrderDetailsDataDataItemsPrice {
  _id: String
  deleted: Boolean
  trackInventory: Boolean
  name: String
  type: String
  currency: String
  amount: Float
  compareAtPrice: Float
  locationId: String
  userId: String
  createdAt: String
  updatedAt: String
  product: String
  recurring: GetOrderDetailsDataDataItemsPriceRecurring
  setupFee: Float
  trialPeriod: Float
  totalCycles: Float
}

type GetOrderDetailsDataDataItems {
  product: GetOrderDetailsDataDataItemsProduct
  _id: String
  name: String
  price: GetOrderDetailsDataDataItemsPrice
  qty: Float
  unitDiscount: Float
  unitDiscountWithoutSetupFee: Float
}

type GetOrderDetailsDataDataContactSnapshotCreatedBy {
  sourceId: String
  channel: String
  source: String
  timestamp: String
}

type GetOrderDetailsDataDataContactSnapshot {
  id: String
  country: String
  city: String
  source: String
  type: String
  locationId: String
  validEmailDate: String
  lastNameLowerCase: String
  emailLowerCase: String
  firstName: String
  email: String
  validEmail: String
  address1: String
  fullNameLowerCase: String
  lastName: String
  firstNameLowerCase: String
  createdBy: GetOrderDetailsDataDataContactSnapshotCreatedBy
  dateAdded: String
  deleted: Boolean
  postalCode: String
  unsubscribeEmail: Boolean
  phone: String
  state: String
  dateUpdated: String
}

type GetOrderDetailsDataDataAmountSummary {
  subtotal: Float
  discount: Float
  tax: Float
  shipping: Float
}

type GetOrderDetailsDataDataSource {
  type: String
  subType: String
  id: String
  name: String
}

type GetOrderDetailsDataDataCoupon {
  _id: String
  usageCount: Float
  hasAffiliateCoupon: Boolean
  deleted: Boolean
  limitPerCustomer: Float
  altId: String
  altType: String
  name: String
  code: String
  discountType: String
  discountValue: Float
  userId: String
  status: String
  startDate: String
  applyToFuturePayments: Boolean
  createdAt: String
  updatedAt: String
  couponSessionId: String
}

type GetOrderDetailsDataData {
  _id: String
  altId: String
  altType: String
  contactId: String
  contactName: String
  contactEmail: String
  currency: String
  amount: Float
  subtotal: Float
  discount: Float
  status: String
  paymentStatus: String
  liveMode: Boolean
  totalProducts: Float
  sourceType: String
  sourceName: String
  sourceId: String
  couponCode: String
  createdAt: String
  updatedAt: String
  sourceSubType: String
  fulfillmentStatus: String
  onetimeProducts: Float
  items: [GetOrderDetailsDataDataItems]
  markAsTest: Boolean
  automaticTaxesCalculated: Boolean
  contactSnapshot: GetOrderDetailsDataDataContactSnapshot
  amountSummary: GetOrderDetailsDataDataAmountSummary
  source: GetOrderDetailsDataDataSource
  coupon: GetOrderDetailsDataDataCoupon
  fingerprint: String
  trackingId: String
  traceId: String
}

type GetOrderDetailsData {
  data: GetOrderDetailsDataData!
}

input Ghl_api_getOrderDetailsInput {
  locationID: String!
  groupID: String!
  orderID: String!
}

type GetTransactionDetailsDataDataContactSnapshotCreatedBy {
  sourceId: String
  channel: String
  source: String
  timestamp: String
}

type GetTransactionDetailsDataDataContactSnapshot {
  id: String
  country: String
  city: String
  source: String
  type: String
  locationId: String
  validEmailDate: String
  lastNameLowerCase: String
  emailLowerCase: String
  firstName: String
  email: String
  validEmail: String
  address1: String
  fullNameLowerCase: String
  lastName: String
  firstNameLowerCase: String
  createdBy: GetTransactionDetailsDataDataContactSnapshotCreatedBy
  dateAdded: String
  deleted: Boolean
  postalCode: String
  unsubscribeEmail: Boolean
  phone: String
  state: String
  dateUpdated: String
}

type GetTransactionDetailsDataDataEntitySource {
  type: String
  subType: String
  id: String
  name: String
}

type GetTransactionDetailsDataDataPaymentProviderConnectedAccount {
  accountId: String
  name: String
  liveMode: Boolean
  providerId: String
}

type GetTransactionDetailsDataDataPaymentProvider {
  type: String
  connectedAccount: GetTransactionDetailsDataDataPaymentProviderConnectedAccount
  customProvider: String
}

type GetTransactionDetailsDataDataCustomProvider {
  name: String
  providerId: String
}

type GetTransactionDetailsDataDataMeta {
  sessionType: String
  message: String
}

type GetTransactionDetailsDataData {
  _id: String
  status: String
  markAsTest: Boolean
  isParent: Boolean
  amountRefunded: Float
  altType: String
  altId: String
  contactId: String
  contactName: String
  contactEmail: String
  contactSnapshot: GetTransactionDetailsDataDataContactSnapshot
  currency: String
  amount: Float
  liveMode: Boolean
  entityType: String
  entityId: String
  entitySource: GetTransactionDetailsDataDataEntitySource
  entitySourceType: String
  entitySourceSubType: String
  entitySourceName: String
  entitySourceId: String
  subscriptionId: String
  paymentProvider: GetTransactionDetailsDataDataPaymentProvider
  paymentProviderType: String
  customProvider: GetTransactionDetailsDataDataCustomProvider
  paymentProviderConnectedAccount: String
  meta: GetTransactionDetailsDataDataMeta
  createdAt: String
  updatedAt: String
  chargeId: String
  receiptId: String
}

type GetTransactionDetailsData {
  data: GetTransactionDetailsDataData!
}

input Ghl_api_getTransactionDetailsInput {
  locationID: String!
  groupID: String!
  transactionID: String!
}

type GetSubscriptionDetailsDataDataContactSnapshotCreatedBy {
  sourceId: String
  channel: String
  source: String
  timestamp: String
}

type GetSubscriptionDetailsDataDataContactSnapshot {
  id: String
  country: String
  city: String
  source: String
  type: String
  locationId: String
  validEmailDate: String
  lastNameLowerCase: String
  emailLowerCase: String
  firstName: String
  email: String
  validEmail: String
  address1: String
  fullNameLowerCase: String
  lastName: String
  firstNameLowerCase: String
  createdBy: GetSubscriptionDetailsDataDataContactSnapshotCreatedBy
  dateAdded: String
  deleted: Boolean
  postalCode: String
  unsubscribeEmail: Boolean
  phone: String
  state: String
  dateUpdated: String
}

type GetSubscriptionDetailsDataDataEntitySource {
  type: String
  subType: String
  id: String
  name: String
}

type GetSubscriptionDetailsDataDataRecurringProductProductSeo {
  title: String
  description: String
}

type GetSubscriptionDetailsDataDataRecurringProductProduct {
  _id: String
  type: String
  parentId: String
  archived: Boolean
  deleted: Boolean
  image: String
  isTaxesEnabled: Boolean
  isLabelEnabled: Boolean
  locationId: String
  name: String
  productType: String
  description: String
  availableInStore: Boolean
  seo: GetSubscriptionDetailsDataDataRecurringProductProductSeo
  userId: String
  createdAt: String
  updatedAt: String
}

type GetSubscriptionDetailsDataDataRecurringProductPriceRecurring {
  interval: String
  intervalCount: Float
}

type GetSubscriptionDetailsDataDataRecurringProductPrice {
  _id: String
  deleted: Boolean
  trackInventory: Boolean
  name: String
  type: String
  currency: String
  amount: Float
  compareAtPrice: Float
  locationId: String
  userId: String
  createdAt: String
  updatedAt: String
  product: String
  recurring: GetSubscriptionDetailsDataDataRecurringProductPriceRecurring
  setupFee: Float
  trialPeriod: Float
  totalCycles: Float
}

type GetSubscriptionDetailsDataDataRecurringProduct {
  product: GetSubscriptionDetailsDataDataRecurringProductProduct
  _id: String
  name: String
  price: GetSubscriptionDetailsDataDataRecurringProductPrice
  qty: Float
}

type GetSubscriptionDetailsDataDataPaymentProviderConnectedAccount {
  accountId: String
  name: String
  liveMode: Boolean
  providerId: String
}

type GetSubscriptionDetailsDataDataPaymentProvider {
  type: String
  connectedAccount: GetSubscriptionDetailsDataDataPaymentProviderConnectedAccount
}

type GetSubscriptionDetailsDataDataCustomProviderProviderConfigLive {
  liveMode: Boolean
  apiKey: String
  publishableKey: String
}

type GetSubscriptionDetailsDataDataCustomProviderProviderConfigTest {
  liveMode: Boolean
  apiKey: String
  publishableKey: String
}

type GetSubscriptionDetailsDataDataCustomProviderProviderConfig {
  live: GetSubscriptionDetailsDataDataCustomProviderProviderConfigLive
  test: GetSubscriptionDetailsDataDataCustomProviderProviderConfigTest
}

type GetSubscriptionDetailsDataDataCustomProvider {
  _id: String
  deleted: Boolean
  locationId: String
  marketplaceAppId: String
  name: String
  description: String
  imageUrl: String
  queryUrl: String
  paymentsUrl: String
  providerConfig: GetSubscriptionDetailsDataDataCustomProviderProviderConfig
  createdAt: String
  updatedAt: String
}

type GetSubscriptionDetailsDataDataCoupon {
  _id: String
  usageCount: Float
  hasAffiliateCoupon: Boolean
  deleted: Boolean
  limitPerCustomer: Float
  altId: String
  altType: String
  name: String
  code: String
  discountType: String
  discountValue: Float
  userId: String
  status: String
  startDate: String
  applyToFuturePayments: Boolean
  createdAt: String
  updatedAt: String
  couponSessionId: String
}

type GetSubscriptionDetailsDataData {
  _id: String
  markAsTest: Boolean
  altType: String
  altId: String
  contactId: String
  contactName: String
  contactEmail: String
  contactPhone: String
  contactSnapshot: GetSubscriptionDetailsDataDataContactSnapshot
  currency: String
  amount: Float
  status: String
  liveMode: Boolean
  entityType: String
  entityId: String
  entitySource: GetSubscriptionDetailsDataDataEntitySource
  entitySourceType: String
  entitySourceSubType: String
  entitySourceName: String
  entitySourceId: String
  subscriptionId: String
  recurringProduct: GetSubscriptionDetailsDataDataRecurringProduct
  paymentProvider: GetSubscriptionDetailsDataDataPaymentProvider
  paymentProviderType: String
  customProvider: GetSubscriptionDetailsDataDataCustomProvider
  paymentProviderConnectedAccount: String
  coupon: GetSubscriptionDetailsDataDataCoupon
  createdAt: String
  updatedAt: String
  traceId: String
}

type GetSubscriptionDetailsData {
  data: GetSubscriptionDetailsDataData!
}

input Ghl_api_getSubscriptionDetailsInput {
  locationID: String!
  groupID: String!
  subscriptionID: String!
}

type GetPaymentPageDataReturnDataAmountSummary {
  subtotal: Float
  discount: Float
  tax: Float
  shipping: Float
}

type GetPaymentPageDataReturnDataLineItems {
  id: String
  name: String
  description: String
  image: String
  quantity: Float
  price: Float
  currency: String
  total: Float
  discount: Float
}

type GetPaymentPageDataReturnDataSubscriptionItemsRecurring {
  interval: String
  intervalCount: Float
  delay: Float
  cycles: Float
}

type GetPaymentPageDataReturnDataSubscriptionItems {
  id: String
  name: String
  description: String
  image: String
  price: Float
  currency: String
  setupFee: Float
  quantity: Float
  total: Float
  recurring: GetPaymentPageDataReturnDataSubscriptionItemsRecurring
  discount: Float
  discountNoSetupFee: Float
}

type GetPaymentPageDataReturnDataCoupon {
  id: String
  name: String
  sessionID: String
  code: String
  discountType: String
  discountValue: Float
  hasAffiliateCoupon: Boolean
  userLimit: Float
  status: String
  usageCount: Float
}

type GetPaymentPageDataReturnDataCustomerData {
  name: String
  email: String
  contact: String
  first_name: String
  last_name: String
  company: String
  address_line_1: String
  address_line_2: String
  city: String
  state: String
  postal_code: String
  country: String
  phone: String
  fax: String
}

type GetPaymentPageDataReturnData {
  locationID: String!
  groupID: String!
  liveMode: Boolean!
  amount: Float!
  apiKey: String!
  createdAt: String!
  amountSummary: GetPaymentPageDataReturnDataAmountSummary
  subscriptionId: String
  lineItems: [GetPaymentPageDataReturnDataLineItems]
  subscriptionItems: [GetPaymentPageDataReturnDataSubscriptionItems]
  coupon: GetPaymentPageDataReturnDataCoupon
  customerData: GetPaymentPageDataReturnDataCustomerData!
}

type GetPaymentPageDataReturn {
  data: GetPaymentPageDataReturnData!
}

input Ghl_api_getPaymentPageDataInput {
  apiKey: String!
  transactionID: String!
}

type GetPaymentPageDataUNIReturnDataAmountSummary {
  subtotal: Float
  discount: Float
  tax: Float
  shipping: Float
}

type GetPaymentPageDataUNIReturnDataLineItems {
  id: String
  name: String
  description: String
  image: String
  quantity: Float
  price: Float
  currency: String
  total: Float
  discount: Float
}

type GetPaymentPageDataUNIReturnDataSubscriptionItemsRecurring {
  interval: String
  intervalCount: Float
  delay: Float
  cycles: Float
}

type GetPaymentPageDataUNIReturnDataSubscriptionItems {
  id: String
  name: String
  description: String
  image: String
  price: Float
  currency: String
  setupFee: Float
  quantity: Float
  total: Float
  recurring: GetPaymentPageDataUNIReturnDataSubscriptionItemsRecurring
  discount: Float
  discountNoSetupFee: Float
}

type GetPaymentPageDataUNIReturnDataCoupon {
  id: String
  name: String
  sessionID: String
  code: String
  discountType: String
  discountValue: Float
  hasAffiliateCoupon: Boolean
  userLimit: Float
  status: String
  usageCount: Float
}

type GetPaymentPageDataUNIReturnDataCustomerData {
  name: String
  email: String
  contact: String
  first_name: String
  last_name: String
  company: String
  address_line_1: String
  address_line_2: String
  city: String
  state: String
  postal_code: String
  country: String
  phone: String
  fax: String
}

type GetPaymentPageDataUNIReturnData {
  locationID: String!
  groupID: String!
  liveMode: Boolean!
  amount: Float!
  apiKey: String!
  createdAt: String!
  amountSummary: GetPaymentPageDataUNIReturnDataAmountSummary
  subscriptionId: String
  lineItems: [GetPaymentPageDataUNIReturnDataLineItems]
  subscriptionItems: [GetPaymentPageDataUNIReturnDataSubscriptionItems]
  coupon: GetPaymentPageDataUNIReturnDataCoupon
  customerData: GetPaymentPageDataUNIReturnDataCustomerData!
}

type GetPaymentPageDataUNIReturnPaymentLink {
  payLinkID: String
  paymentData: String!
  token: String!
  groupID: String!
  url: String
}

type GetPaymentPageDataUNIReturn {
  data: GetPaymentPageDataUNIReturnData!
  paymentLink: GetPaymentPageDataUNIReturnPaymentLink!
}

input Ghl_api_getPaymentPageDataUNIInput {
  apiKey: String!
  transactionID: String!
}

type SubmitPaymentUniReturnOrderDataResponse_bodyCard {
  id: String!
  card_type: String!
  first_six: String!
  last_four: String!
  masked_card: String!
  expiration_date: String!
  response: String!
  response_code: Float!
  auth_code: String!
  bin_type: String!
  type: String!
  avs_response_code: String
  cvv_response_code: String!
  processor_specific: String
  created_at: String!
  updated_at: String!
}

type SubmitPaymentUniReturnOrderDataResponse_body {
  card: SubmitPaymentUniReturnOrderDataResponse_bodyCard!
}

type SubmitPaymentUniReturnOrderDataBilling_address {
  first_name: String
  last_name: String
  company: String
  address_line_1: String
  address_line_2: String
  city: String
  state: String
  postal_code: String
  country: String
  phone: String
  fax: String
  email: String
}

type SubmitPaymentUniReturnOrderDataShipping_address {
  first_name: String
  last_name: String
  company: String
  address_line_1: String
  address_line_2: String
  city: String
  state: String
  postal_code: String
  country: String
  phone: String
  fax: String
  email: String
}

type SubmitPaymentUniReturnOrderData {
  id: String
  idempotency_time: Float
  type: String
  amount: Float
  base_amount: Float
  amount_authorized: Float
  amount_captured: Float
  amount_settled: Float
  amount_refunded: Float
  payment_adjustment: Float
  tip_amount: Float
  settlement_batch_id: String
  payment_type: String
  tax_amount: Float
  tax_exempt: Boolean
  shipping_amount: Float
  surcharge: Float
  discount_amount: Float
  service_fee: Float
  currency: String
  description: String
  order_id: String
  po_number: String
  ip_address: String
  transaction_source: String
  email_receipt: Boolean
  email_address: String
  customer_id: String
  customer_payment_type: String
  customer_payment_id: String
  subscription_id: String
  referenced_transaction_id: String
  response_body: SubmitPaymentUniReturnOrderDataResponse_body
  status: String
  response: String
  response_code: Float
  billing_address: SubmitPaymentUniReturnOrderDataBilling_address
  shipping_address: SubmitPaymentUniReturnOrderDataShipping_address
  created_at: String
  updated_at: String
  captured_at: String
  settled_at: String
}

type SubmitPaymentUniReturnOrder {
  status: String
  msg: String
  data: SubmitPaymentUniReturnOrderData
}

type SubmitPaymentUniReturnSubscriptionDataCustomer {
  id: String!
}

type SubmitPaymentUniReturnSubscriptionDataAdd_ons {
  id: String
  name: String
  description: String
  amount: Float
  percentage: Float
  duration: Float
  created_at: String
  updated_at: String
}

type SubmitPaymentUniReturnSubscriptionDataDiscounts {
  id: String
  name: String
  description: String
  amount: Float
  percentage: Float
  duration: Float
  created_at: String
  updated_at: String
}

type SubmitPaymentUniReturnSubscriptionDataEvents {
  created_at: String!
  customer_id: String!
  id: String!
  message: String!
  status: String!
  subscription_id: String!
}

type SubmitPaymentUniReturnSubscriptionData {
  id: String
  plan_id: String
  status: String
  description: String
  customer: SubmitPaymentUniReturnSubscriptionDataCustomer
  amount: Float
  total_adds: Float
  total_discounts: Float
  billing_cycle_interval: Float
  billing_frequency: String
  billing_days: String
  duration: Float
  next_bill_date: String
  expiration_date: String
  add_ons: [SubmitPaymentUniReturnSubscriptionDataAdd_ons]
  discounts: [SubmitPaymentUniReturnSubscriptionDataDiscounts]
  created_at: String
  updated_at: String
  charge_on_day: Boolean
  currency: String
  customer_name: String
  events: [SubmitPaymentUniReturnSubscriptionDataEvents]
}

type SubmitPaymentUniReturnSubscription {
  status: String
  msg: String
  data: SubmitPaymentUniReturnSubscriptionData
}

type SubmitPaymentUniReturn {
  txID: String!
  order: SubmitPaymentUniReturnOrder
  subscription: SubmitPaymentUniReturnSubscription
}

input Ghl_api_submitPayment_uniInputPaymentCard {
  cardToken: String!
  cardExpMonth: String!
  cardExpYear: String!
  cardCvv: String!
}

input Ghl_api_submitPayment_uniInputAch {
  accountNumber: String!
  routingNumber: String!
  accountType: String!
  accountHolderType: String!
}

input Ghl_api_submitPayment_uniInputGpecomm {
  id: String
}

input Ghl_api_submitPayment_uniInput {
  apiKey: String!
  transactionID: String!
  paymentCard: Ghl_api_submitPayment_uniInputPaymentCard
  ach: Ghl_api_submitPayment_uniInputAch
  gpecomm: Ghl_api_submitPayment_uniInputGpecomm
  ipAddress: String!
}

type GHLAuthSSOInfo {
  locationID: String!
  accountName: String!
  email: String!
  groupID: String
  groupName: String
}

input Ghl_auth_getSSOInfoInput {
  ssoToken: String!
}

type GHLAuthSSOBindReturn {
  localUserID: String!
  ghlUserID: String!
  locationID: String!
}

input Ghl_auth_bindInput {
  ssoToken: String!
}

type GHLAuthSSOReturn {
  sessionToken: String
  item: User
}

input Ghl_auth_ssoInput {
  ssoToken: String!
}

type GHLIntegrationDetails {
  locationID: String!
  locationName: String!
  accountName: String!
}

input Ghl_auth_getIntegrationDetailsInput {
  code: String!
}

input Ghl_auth_completeIntegrationInput {
  code: String!
  groupID: String!
  sync: Boolean
}

type GHLCheckPendingInstallReturn {
  code: String!
}

input Ghl_auth_checkPendingInstallInput {
  ssoToken: String!
}

type GHLCheckAccessReturn {
  hasAccess: Boolean!
}

input Ghl_auth_checkGHLAccessInput {
  ssoToken: String!
}

input Ghl_auth_uninstallPaymentIntegrationInput {
  locationID: String!
}

input Ghl_auth_testAESInput {
  data: String!
}

input Ghl_auth_lockedInput {
  data: String!
}

type GHLSyncProductListProductsVariantsOptions {
  id: String
  name: String
}

type GHLSyncProductListProductsVariants {
  id: String
  name: String
  options: [GHLSyncProductListProductsVariantsOptions]
}

type GHLSyncProductListProductsMedias {
  id: String
  title: String
  url: String
  type: String
  isFeatured: Boolean
}

type GHLSyncProductListProductsPricePricesMembershipOffers {
  label: String
  value: String
  _id: String
}

type GHLSyncProductListProductsPricePricesRecurring {
  interval: String
  intervalCount: Float
}

type GHLSyncProductListProductsPricePrices {
  _id: String
  membershipOffers: [GHLSyncProductListProductsPricePricesMembershipOffers]
  variantOptionIds: [String]
  locationId: String
  product: String
  userId: String
  name: String
  type: String
  currency: String
  amount: Float
  recurring: GHLSyncProductListProductsPricePricesRecurring
  createdAt: String
  updatedAt: String
  compareAtPrice: Float
  trackInventory: Boolean
  availableQuantity: Float
  allowOutOfStockPurchases: Boolean
}

type GHLSyncProductListProductsPrice {
  prices: [GHLSyncProductListProductsPricePrices]
  total: String
}

type GHLSyncProductListProducts {
  _id: String
  description: String
  variants: [GHLSyncProductListProductsVariants]
  medias: [GHLSyncProductListProductsMedias]
  locationId: String
  name: String
  productType: String
  availableInStore: Boolean
  userId: String
  createdAt: String
  updatedAt: String
  statementDescriptor: String
  image: String
  price: GHLSyncProductListProductsPrice
}

type GHLSyncProductListTotal {
  total: Float!
}

type GHLSyncProductList {
  products: [GHLSyncProductListProducts]!
  total: [GHLSyncProductListTotal]!
}

input Ghl_sync_product_listInput {
  locationID: String!
}

type GHLSyncFromGHLReturn {
  success: Boolean
  message: String
  data: JSON
}

input Ghl_sync_fromGHLInput {
  locationID: String!
  priceIDs: [String]
  maxProductToSync: Float
}

type GHLSyncToGHLReturn {
  success: Boolean
  message: String
  data: JSON
}

input Ghl_sync_toGHLInput {
  locationID: String!
  productIDs: [String]!
}

type LeadLoginOutput {
  sessionToken: String
  item: User
  email: String
  password: String
}

input Leads_loginInput {
  firstName: String!
  lastName: String!
  email: String!
  phoneNumber: String!
  companyName: String!
  companyRole: String
  notes: String
}

type FileUploadOutputFiles {
  url: String!
  filename: String!
}

type FileUploadOutput {
  files: [FileUploadOutputFiles]!
}

input File_uploadInputFiles {
  b64: String!
  mimetype: String
  filename: String!
}

input File_uploadInput {
  files: [File_uploadInputFiles]!
}

input Ai_determineMCCFromSentenceInput {
  sentence: String!
}

type ServiceAPIAccountCreateOutput {
  id: String!
  token: String!
  secret: String!
}

input Serviceacct_createInput {
  groupID: String!
  permissions: [String]
  ipWhitelist: [String]
}

type ServiceAPIAccountValidateOutput {
  valid: Boolean
  serviceAccount: ServiceAPIAccount
}

input Serviceacct_validateInput {
  token: String
  secret: String
}

type ReportDownloadOutput {
  data: String!
}

input Reportdownload_downloadInput {
  report: String!
  groupID: String!
  queryString: String
}

type ImportStripeOutput {
  type: String!
  processedItems: Float!
}

input Import_stripeInput {
  type: String!
  groupID: String!
  dataCSV: String!
}

type ImportStripeSampleOutput {
  sampleData: String!
}

input Import_stripe_sampleInput {
  type: String!
}

type StripeSyncTransactionsResponse {
  count: Float!
}

input Stripe_syncInput {
  groupId: String!
  type: String!
  limit: Float
  startingAfter: String
  endingBefore: String
}

input Stripe_setupInput {
  groupId: String!
  type: String!
  secretKey: String
  publishableKey: String
}

type PropayTables {
  tables: [String]!
}

input Propay_queryInput {
  table: String!
  queries: String
  sorts: String
  page: Float
  pageSize: Float
}
