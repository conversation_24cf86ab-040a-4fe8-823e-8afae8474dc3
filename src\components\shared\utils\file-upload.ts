export const placeholder = 'file-upload';

export const parseFilesToBase64 = async (
  files: File[],
): Promise<Array<{ b64: string; type: string }>> => {
  const promises = files.map((file) => {
    return new Promise<{ b64: string; type: string }>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve({
          b64: reader.result as string,
          type: file.type,
        });
      };
      reader.onerror = reject; // Handle errors
      reader.readAsDataURL(file); // Read the file as a data URL (Base64)
    });
  });

  return Promise.all(promises);
};
