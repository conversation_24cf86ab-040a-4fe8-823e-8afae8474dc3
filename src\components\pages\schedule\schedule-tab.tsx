import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ponent,
  <PERSON><PERSON><PERSON>,
  StatusFilter,
  useDataGridView,
  Variant,
} from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import { Gateway_ScheduledDocument } from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import { PaymentPlanDetailsModal } from './components/schedule-detail-modal';
import { DateFormat, formatToLocalDateString } from '@/components/shared/utils';
import { moneyFormat } from '@/lib/utils';
import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export enum SchedulePaymentsStatus {
  active = 'active',
  inactive = 'inactive',
  completed = 'completed',
  // pending = 'pending',
  // paid = 'paid',
  // unpaid = 'unpaid',
  // withdrawn = 'withdrawn',
  cancelled = 'cancelled',
}

export const getSchedulePaymentsStatus = (status: SchedulePaymentsStatus): [Variant, string] => {
  const statusMap: Record<SchedulePaymentsStatus, Variant> = {
    [SchedulePaymentsStatus.active]: 'success',
    [SchedulePaymentsStatus.inactive]: 'neutral',
    [SchedulePaymentsStatus.completed]: 'info',
    // [SchedulePaymentsStatus.pending]: 'warning',
    // [SchedulePaymentsStatus.paid]: 'success',
    // [SchedulePaymentsStatus.unpaid]: 'danger',
    // [SchedulePaymentsStatus.withdrawn]: 'danger',
    [SchedulePaymentsStatus.cancelled]: 'danger',
  };

  const variant = statusMap[status] || 'neutral';
  const label = `${status?.charAt(0).toUpperCase()}${status?.slice(1)}`;

  return [variant, label];
};

export const ScheduleTab = () => {
  const [selectedPaymentID, setSelectedPaymentID] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({
    onlyActive: true,
  });

  const {
    data: schedulePaymentsData,
    loading: schedulePaymentsLoading,
    refetch: refetchschedulePaymentsData,
    error: schedulePaymentsError,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    filterValue,
    setFilterValue,
    maxVariables,
  } = useDataGridView({
    query: Gateway_ScheduledDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
    initialFilterField: 'status',
    initialFilterValue: 'All',
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refetchschedulePaymentsData();
    }
  }, [locationFilter]);

  useEffect(() => {
    if (schedulePaymentsError) {
      toast.error(`API INTERGRATION ERROR: ${schedulePaymentsError.message}`);
    }
  }, [schedulePaymentsError]);

  const transactionRows = useMemo(() => {
    const data = schedulePaymentsData?.gateway_scheduled?.data;
    if (!data) return [];
    return data.filter((item): item is NonNullable<typeof item> => item !== null);
  }, [schedulePaymentsData?.gateway_scheduled?.data]);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_ScheduledDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_scheduled?.data ?? [];
  };

  const columns: Column[] = [
    {
      key: 'planID',
      header: 'Plan ID',
      width: '110px',
      onClick: (row) => setSelectedPaymentID(row.planID),
    },
    {
      key: 'planName',
      header: 'Plan name',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'customerName',
      header: 'Customer',
    },
    {
      key: 'nextPayment',
      header: 'Next payment',
      valueGetter: (row) =>
        formatToLocalDateString(row?.nextPayment, DateFormat.LOCAL_DATE_WITH_SPACE),
    },
    {
      key: 'amount',
      header: 'Amount',
      renderCell: (row) => <span>{moneyFormat(row?.amount)}</span>,
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'datePaid',
      header: 'Date Paid',
      valueGetter: (row) =>
        formatToLocalDateString(row?.datePaid, DateFormat.LOCAL_DATE_WITH_SPACE),
    },
    {
      key: 'amountPaid',
      header: 'Amount paid',
      width: '90px',
      renderCell: (row) => <span>{moneyFormat(row?.amountPaid)}</span>,
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getSchedulePaymentsStatus(row.status);
        return <StatusChip variant={status} label={label} />;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];

  return (
    <>
      <StaticInfoBox />
      <PageHeader text="Subscriptions" />
      <div className="mt-2 flex justify-between border-b border-gray-300">
        <div className="w-1/4">{locationSelectorElement}</div>
        <TopComponent value={searchValue} setValue={setSearchValue}>
          <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="schedules" />
        </TopComponent>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={transactionRows ?? []}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={schedulePaymentsLoading || loadingGroupList}
          actionComponent={
            <StatusFilter
              value={filterValue}
              setValue={setFilterValue}
              statusList={Object.values(SchedulePaymentsStatus)}
            />
          }
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={schedulePaymentsData?.gateway_scheduled?.page?.total ?? 0}
        />
      </div>
      <PaymentPlanDetailsModal
        isOpen={selectedPaymentID !== null}
        onClose={() => setSelectedPaymentID(null)}
        queryData={{
          planID: selectedPaymentID ?? '',
          groupID: locationFilter?.id ?? '',
        }}
      />
    </>
  );
};
