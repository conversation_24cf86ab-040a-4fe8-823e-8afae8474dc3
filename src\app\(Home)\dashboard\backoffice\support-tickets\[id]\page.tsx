'use client';

import { useQuery } from '@apollo/client';
import { useParams } from 'next/navigation';
import { Paperclip } from 'lucide-react';
import moment from 'moment';
import { SupportReplyForm } from './SupportReplyForm';
import { GroupSupportTicketDocument } from '@/graphql/generated/graphql';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

const SupportTicketPage = () => {
  const params = useParams<{ id: string }>();
  const { id } = params || {};

  const {
    data: supportData,
    loading,
    error,
  } = useQuery(GroupSupportTicketDocument, {
    variables: {
      where: { id },
    },
  });

  if (loading) {
    <div>Loading...</div>;
  }

  const data = supportData?.groupSupportTicket;
  if (!data) return null;

  const isRight = false;

  // return <pre className="mx-5">{JSON.stringify(data, null, 2)}</pre>;
  return (
    <Card className="mx-auto max-w-6xl">
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <CardTitle>Ticket #{data.id}</CardTitle>
            <div className="text-sm text-muted-foreground">
              <span className="font-medium">Group:</span> {data.group?.labelName || 'N/A'} |
              <span className="ml-2 font-medium">Created by:</span>{' '}
              {data.createdBy?.displayName || 'N/A'}
            </div>
          </div>
          <Badge variant={data.status === 'OPEN' ? 'secondary' : 'default'}>{data.status}</Badge>
        </div>
      </CardHeader>
      <CardContent className="p-5">
        <div className="flex items-center justify-between border-b pb-4">
          <div className="text-lg">{data.title}</div>
          <div className="text-sm text-muted-foreground">
            {moment(data.createdAt).format('DD/MM/YYYY hh:mm A')}
          </div>
        </div>
        <ScrollArea className="h-[700px] pr-4 pt-4">
          <div className="space-y-6">
            {data.description && (
              <div
                className="prose prose-sm max-w-none dark:prose-invert"
                dangerouslySetInnerHTML={{ __html: data.description }}
              />
            )}

            {data.messages?.map((message) => (
              <div
                key={message.id}
                className={`flex ${isRight ? 'justify-end' : 'justify-start'} gap-4`}
              >
                {!isRight && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      {message.actualName
                        ?.split(' ')
                        .map((n) => n[0])
                        .slice(0, 2)
                        .join('') ?? ''}
                    </AvatarFallback>
                  </Avatar>
                )}

                <div className={`flex max-w-[80%] flex-col gap-1`}>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{message.actualName}</span>
                    <span className="text-xs text-muted-foreground">
                      {moment(message.createdAt).format('DD/MM/YYYY hh:mm A')}
                    </span>
                  </div>

                  <div
                    className={`rounded-lg p-4 ${
                      isRight ? 'bg-primary text-primary-foreground' : 'bg-muted'
                    }`}
                  >
                    {message.message && (
                      <div
                        className="prose prose-sm dark:prose-invert"
                        dangerouslySetInnerHTML={{ __html: message.message }}
                      />
                    )}

                    {message.files && Array.isArray(message.files) && message.files.length > 0 && (
                      <div className="mt-2 border-t border-border/50 pt-2">
                        {message.files.map((file: any, index: number) => (
                          <a
                            key={index}
                            href={file.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`flex items-center gap-2 text-sm ${
                              isRight
                                ? 'text-primary-foreground/90 hover:text-primary-foreground'
                                : 'text-muted-foreground hover:text-foreground'
                            }`}
                          >
                            <Paperclip className="h-4 w-4" />
                            {file.filename}
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {isRight && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      {message.actualName
                        ?.split(' ')
                        .map((n) => n[0])
                        .join('') ?? ''}
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
          </div>

          <SupportReplyForm id={data.id} />
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default SupportTicketPage;
