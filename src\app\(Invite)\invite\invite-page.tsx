'use client';

import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ClaimInvite, DeclineInvite, GetInviteInfo } from '@/graphql/declarations/invite';
import { Me } from '@/graphql/declarations/me';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { set } from 'date-fns';
import { LoaderCircle, Lock } from 'lucide-react';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

const SharedLayout = ({ children }: { children: React.ReactNode }) => (
  <div className="flex h-screen w-screen items-center justify-center">
    <Card className="w-fit max-w-md space-y-6 border-2 p-6 shadow-lg">
      <div className="flex justify-center space-x-4">
        <img src="/logo.png" alt="ngnair-logo" className="h-12" />
      </div>
      {children}
    </Card>
  </div>
);

export default function InvitePage() {
  const { data: meData } = useQuery(Me, {});

  const [hasLoaded, setHasLoaded] = useState(false);
  const [groupName, setGroupName] = useState('Test LLC');
  const params = useSearchParams();
  const [userType, setUserType] = useState('');
  const [targetEmail, setTargetEmail] = useState('');

  const [status, setStatus] = useState('');
  const [isAccepting, setIsAccepting] = useState(false);
  const [isDeclining, setIsDeclining] = useState(false);

  const getQueryParams = () => {
    const search = window.location.search;
    const params = new URLSearchParams(search);
    return params.get('token');
  };

  const [errorLoading, setErrorLoading] = useState(false);

  const fetchData = async () => {
    const token = getQueryParams();
    if (!token) return;
    try {
      const res = await apolloClient.query({
        query: GetInviteInfo,
        variables: {
          input: {
            inviteID: token,
          },
        },
      });
      const data = await res.data.group_getInviteInfo;
      if (!data) return;
      setHasLoaded(true);
      setGroupName(data?.groupName ?? '');
      setUserType(data?.alreadySigned ? 'member' : 'register');
      setTargetEmail(data?.email ?? '');
    } catch (error) {
      console.error(error);
      setHasLoaded(true);
      setErrorLoading(true);
    }
  };

  useEffect(() => {
    if (!params?.get('token')) return;
    fetchData();
  }, [params]);

  if (!hasLoaded) {
    return (
      <SharedLayout>
        <LoaderCircle className="mx-auto" />
      </SharedLayout>
    );
  }

  const onAccept = async () => {
    setIsAccepting(true);
    const token = getQueryParams();
    if (!token) return;
    const res = await apolloClient.mutate({
      mutation: ClaimInvite,
      variables: {
        input: {
          inviteID: token,
        },
      },
    });
    setStatus('accepted');
    setIsAccepting(false);
  };

  const onDecline = async () => {
    setIsDeclining(true);
    const token = getQueryParams();
    if (!token) return;
    const res = await apolloClient.mutate({
      mutation: DeclineInvite,
      variables: {
        input: {
          inviteID: token,
        },
      },
    });
    setStatus('declined');
    setIsDeclining(false);
  };

  const redirectToLogin = () => {
    const path = window.location.pathname + window.location.search;
    window.location.href = `/login?redirect=${encodeURIComponent(path)}`;
  };

  const redirectToRegister = () => {
    let path = window.location.pathname + window.location.search;
    window.location.href =
      `/register?redirect=${encodeURIComponent(path)}` +
      `&email=${encodeURIComponent(targetEmail)}`;
  };

  if (errorLoading) {
    return (
      <SharedLayout>
        <p className="text-center">Error loading invitation</p>
      </SharedLayout>
    );
  }

  if (userType == 'member') {
    if (!meData?.authenticatedItem?.id) {
      return (
        <SharedLayout>
          <p className="text-center">
            You need to <span className="text-blue-600">login</span> to accept the invitation <br />
            to join <span className="font-semibold text-blue-600">{groupName}</span>
          </p>
          <div className="flex justify-center space-x-4">
            <Button onClick={redirectToLogin} className="bg-blue-500 text-white hover:bg-blue-600">
              Login
            </Button>
          </div>
        </SharedLayout>
      );
    }

    if (targetEmail != meData?.authenticatedItem?.email) {
      return (
        <SharedLayout>
          <p className="text-center">
            Invalid Invite. It seems like you are trying to accept an invitation that is not meant
            for you. <br />
          </p>
          <div className="flex justify-center space-x-4">
            <Button onClick={redirectToLogin} className="bg-blue-500 text-white hover:bg-blue-600">
              Login
            </Button>
          </div>
        </SharedLayout>
      );
    }
  }

  if (userType == 'register') {
    return (
      <SharedLayout>
        <p className="text-center">
          You are invited to join <span className="font-semibold text-blue-600">{groupName}</span>.
          <br />
          Create an Account now to accept the invitation.
        </p>
        <div className="flex justify-center space-x-4">
          <Button onClick={redirectToRegister} className="bg-blue-500 text-white hover:bg-blue-600">
            Create account
          </Button>
        </div>
      </SharedLayout>
    );
  }

  if (status == 'accepted') {
    return (
      <SharedLayout>
        <p className="text-center">
          You have <span className="text-green-600">successfully joined</span> <br />
          <span className="font-semibold text-blue-600">{groupName}</span>
        </p>
        <div className="flex justify-center space-x-4">
          <Link href="/dashboard">
            <Button className="bg-blue-500 text-white hover:bg-blue-600">Go to dashboard</Button>
          </Link>
        </div>
      </SharedLayout>
    );
  }

  if (status == 'declined') {
    return (
      <SharedLayout>
        <p className="text-center">
          You have <span className="text-red-600">declined</span> the invitation <br />
          to join <span className="font-semibold text-blue-600">{groupName}</span>
        </p>
        <div className="flex justify-center space-x-4">
          <Link href="/dashboard">
            <Button className="bg-blue-500 text-white hover:bg-blue-600">Go to dashboard</Button>
          </Link>
        </div>
      </SharedLayout>
    );
  }

  return (
    <SharedLayout>
      <p className="text-center">
        You are invited to join <span className="font-semibold text-blue-600">{groupName}</span>
      </p>
      <div className="flex justify-center space-x-4">
        <Button
          className="bg-blue-500 text-white hover:bg-blue-600"
          onClick={onAccept}
          disabled={isAccepting || isDeclining}
        >
          {isAccepting ? 'Accepting...' : 'Accept invitation'}
        </Button>
        <Button variant="outline" onClick={onDecline} disabled={isDeclining || isAccepting}>
          {isDeclining ? 'Declining...' : 'Decline'}
        </Button>
      </div>
      <div className="mx-auto w-fit space-y-2">
        <p className="flex items-center text-sm text-gray-600">
          <Lock className="mr-2 h-4 w-4" />
          Owners of {groupName} will be able to see:
        </p>
        <ul className="list-inside list-disc space-y-1 pl-4 text-sm text-gray-600">
          <li>Your public profile information</li>
          <li>Certain activity within this group</li>
          <li>Country of request origin</li>
          <li>Your access level for this repository</li>
          <li>Your IP address</li>
        </ul>
      </div>
    </SharedLayout>
  );
}
