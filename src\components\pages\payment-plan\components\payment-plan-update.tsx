import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { useLocationSelector } from '@/components/hooks';
import { message } from '@/components/shared/utils';
import {
  Gateway_CancelPaymentPlanDocument,
  Gateway_PaymentPlanDocument,
  Gateway_UpdatePaymentPlanDocument,
} from '@/graphql/generated/graphql';
import { useMutation, useQuery } from '@apollo/client';
import { Modal } from 'flowbite-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { PaymentFormData, paymentFormDataDefault } from '../utils';
import { PaymentPlanForm } from './payment-plan-form';
import { floatToInt2Dec } from '@/lib/utils';

type PaymentPlanUpdateProp = {
  isOpen: boolean;
  onClose: () => void;
  refetchListPage: () => void;
  queryData: {
    planID: string;
    groupID: string;
  };
};

export const PaymentPlanUpdate = ({
  isOpen,
  onClose,
  refetchListPage,
  queryData,
}: PaymentPlanUpdateProp) => {
  const { planID, groupID } = queryData;
  const { locationFilter } = useLocationSelector({ readonly: true, onlyActive: true });
  const [addPaymentModal, setPaymentPlanAdd] = useState(false);

  const {
    data: paymentPlanData,
    loading: paymentPlanDataLaoding,
    error: paymentPlanDataError,
    refetch: refetchPaymentPlanData,
  } = useQuery(Gateway_PaymentPlanDocument, {
    variables: {
      input: {
        data: {
          planID,
        },
        groupID,
      },
    },
    skip: !planID || !groupID,
  });

  const methods = useForm<PaymentFormData>({
    defaultValues: { ...paymentFormDataDefault },
  });

  const { reset } = methods;

  useEffect(() => {
    if (paymentPlanDataError) {
      toast.error(paymentPlanDataError.message);
    }
  }, [paymentPlanDataError]);

  useEffect(() => {
    refetchPaymentPlanData();
  }, [planID]);

  useEffect(() => {
    const paymentPlan = paymentPlanData?.gateway_paymentPlan;
    if (paymentPlan) {
      reset({
        customerID: paymentPlan.customerID ?? '',
        paymentID: paymentPlan.paymentID ?? '',
        planName: paymentPlan.planName ?? '',
        amount: paymentPlan.amount ?? 0,
        startDate: paymentPlan.startDate ?? new Date().toString(),
        paymentEvery: paymentPlan.paymentEvery ?? 1,
        endDate: paymentPlan.endDate ?? undefined,
        paymentInterval: (paymentPlan.paymentInterval ?? 1).toString(),
        planID: paymentPlan.planID ?? '',
      });
    }
  }, [paymentPlanData]);

  const [updatePaymentPlanMutation, { loading: updatePaymentPlanLoading }] = useMutation(
    Gateway_UpdatePaymentPlanDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successUpdate('PaymentPlan'));
        reset({ ...paymentFormDataDefault });
        onClose();
        refetchListPage();
      },
      onError: (error) => {
        toast.error(message.api.errorUpdate('PaymentPlan', error.message));
      },
    },
  );

  const [cacnelPaymentPlanMutation, { loading: cancelPaymentMutation }] = useMutation(
    Gateway_CancelPaymentPlanDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successUpdate('PaymentPlan'));
        reset({ ...paymentFormDataDefault });
        onClose();
        refetchListPage();
      },
      onError: (error) => {
        toast.error(message.api.errorUpdate('PaymentPlan', error.message));
      },
    },
  );

  const onSubmitForm = async (data: PaymentFormData) => {
    try {
      // console.log('data', data);
      await updatePaymentPlanMutation({
        variables: {
          input: {
            groupID,
            data: {
              form: {
                // customerID: data.customerID ?? '',
                paymentID: data.paymentID ?? '',
                planName: data.planName,
                amount: floatToInt2Dec(parseFloat(`${data.amount ?? ''}`)),
                // startDate: data.startDate,
                paymentEvery: parseInt(`${data.paymentEvery}`),
                paymentInterval: parseInt(`${data.paymentInterval}`),
                endDate: data.endDate ?? undefined,
                // lineItems: data.lineItems ?? [],
              },
              id: planID,
            },
          },
        },
      });
    } catch (e) {
      console.error('Update Plan Mutation error: ', e);
    }
  };

  const onCancelPlan = async () => {
    try {
      if (!planID) return;
      await cacnelPaymentPlanMutation({
        variables: {
          input: {
            groupID,
            data: {
              planID,
            },
          },
        },
      });
    } catch (e) {
      console.error('Cancel Plan Mutation error: ', e);
    }
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="5xl">
        <Modal.Header className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-blue-600">Update Payment Plan</h3>
        </Modal.Header>
        <Modal.Body>
          <SpinnerLoading isLoading={updatePaymentPlanLoading || paymentPlanDataLaoding} />
          <PaymentPlanForm
            methods={methods}
            onSubmitForm={onSubmitForm}
            isEdit
            cancelSubscription={onCancelPlan}
          />
        </Modal.Body>
      </Modal>
    </>
  );
};
