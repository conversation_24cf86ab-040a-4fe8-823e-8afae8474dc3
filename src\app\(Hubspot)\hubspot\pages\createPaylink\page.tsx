'use client';

import { Column, TopComponent } from '@/components/globals';
import { LoaderSquares } from '@/components/globals/Loaders/Square';
import DataGridView from '@/components/globals/sortable-table/data-grid-view';
import { ModalAddProductLinkProps } from '@/components/pages/paylinks/components/modal-product-link';
import { PayLinkForm, PaylinkForm } from '@/components/pages/paylinks/components/paylink-form';
import { Gateway_CreatePayLinkInputDataFormPricingTaxType } from '@/graphql/generated/graphql';
import { axiosClient } from '@/lib/axios';
import { moneyFormatString } from '@/lib/utils';
import { Button, Modal } from 'flowbite-react';
import { useSearchParams } from 'next/navigation';
import { Suspense, useState, useCallback, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

export default function CreatePaylinkPage() {
  const query = useSearchParams();
  const methods = useForm<PayLinkForm>({});
  const [generatedPayUrl, setGeneratedPayUrl] = useState<string>('');

  // Add expiration check
  const expiresAt = query?.get('expiresAt');
  const sendEmailTo = query?.get('sendEmailTo');
  const isExpired = expiresAt ? new Date(expiresAt) < new Date() : false;

  const ModalAddProductLink = ({ isOpen, onClose, onAddProduct }: ModalAddProductLinkProps) => {
    const [searchValue, setSearchValue] = useState('');
    const [productList, setProductList] = useState<
      Array<{
        id?: string;
        name: string;
        price: number;
        discount: string;
        taxExempt: boolean;
        kitchenItem: boolean;
        sku: string;
      }>
    >([]);
    const [pageSize, setPageSize] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [totalRecords, setTotalRecords] = useState(0);
    const [sortField, setSortField] = useState<string>('');
    const [isAscendOrder, setIsAscendOrder] = useState<boolean>(true);

    const loadData = useCallback(async () => {
      try {
        setIsLoading(true);
        const res = await axiosClient.post('/api/gateway/products', {
          groupID: query?.get('groupID'),
          token: query?.get('token'),
          data: {
            page: {
              page: currentPage,
              pageSize: pageSize,
              sort: sortField
                ? {
                    field: sortField,
                    order: isAscendOrder ? 'asc' : 'desc',
                  }
                : undefined,
              search: searchValue,
            },
          },
        });
        const resData = res.data;
        setProductList(resData.data);
        setTotalRecords(resData.page.total);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setIsLoading(false);
      }
    }, [currentPage, pageSize, searchValue, sortField, isAscendOrder]);

    const handlePageChange = (page: number) => {
      setCurrentPage(page);
    };

    const handlePageSizeChange = (newPageSize: number) => {
      setPageSize(newPageSize);
      setCurrentPage(1);
    };

    const handleSelectProduct = (
      productId: string,
      amount: number,
      name: string,
      price: number,
    ) => {
      onAddProduct(productId, amount, name, price);
      onClose();
    };

    const columns: Column[] = [
      {
        key: 'name',
        header: 'Name',
        onClick: (row) => handleSelectProduct(row.id ?? '', 1, row.name, row.price),
        sortable: true,
        onServerSort: (key) => {
          setSortField(key);
          setIsAscendOrder((v) => !v);
        },
      },
      {
        key: 'price',
        header: 'Price',
        sortable: true,
        onServerSort: (key) => {
          setSortField(key);
          setIsAscendOrder((v) => !v);
        },
        valueGetter: (row) => `${moneyFormatString(row.price)}`,
      },
      {
        key: 'isRecurring',
        header: 'Is Recurring',
        sortable: true,
        onServerSort: (key) => {
          setSortField(key);
          setIsAscendOrder((v) => !v);
        },
        valueGetter: (row) =>
          `${row.isRecurring ? 'Yes (per ' + row.recurringInterval + ' Days)' : 'N'}`,
      },
      {
        key: 'brand',
        header: 'Brand',
        sortable: true,
        onServerSort: (key) => {
          setSortField(key);
          setIsAscendOrder((v) => !v);
        },
      },
      {
        key: 'sku',
        header: 'SKU',
        sortable: true,
        onServerSort: (key) => {
          setSortField(key);
          setIsAscendOrder((v) => !v);
        },
      },
    ];

    useEffect(() => {
      if (isOpen) {
        loadData();
      }
    }, [isOpen, pageSize, currentPage, searchValue, sortField, isAscendOrder]);

    return (
      <Modal show={isOpen} onClose={onClose} size="7xl">
        <Modal.Header className="flex items-center justify-between">
          <div className="text-xl font-semibold">Add Product</div>
        </Modal.Header>
        <Modal.Body>
          <div className="">
            <div className="border-b border-gray-300 pb-2 sm:flex">
              {/* <div className="w-1/4">{locationSelectorElement}</div> */}
              <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
                <TopComponent value={searchValue} setValue={setSearchValue}>
                  <></>
                </TopComponent>
              </div>
            </div>
            <div>
              <DataGridView
                columns={columns}
                rows={productList}
                pageSize={pageSize}
                currentPage={currentPage}
                isLoading={isLoading}
                mode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                totalRecords={totalRecords}
              />
            </div>
          </div>
        </Modal.Body>
      </Modal>
    );
  };

  if (!query?.has('groupID') || !query?.has('token')) {
    return (
      <div>
        <LoaderSquares />
      </div>
    );
  }

  // Show expiration message if link is expired
  if (isExpired) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="mt-16 rounded-lg bg-red-50 p-8 text-center">
          <h2 className="text-xl font-semibold text-red-700">This NGNair Page Link is Expired.</h2>
          <p className="mt-2 text-red-600">Please retry again on your application</p>
        </div>
      </div>
    );
  }

  const onSubmitForm = async (data: PayLinkForm) => {
    try {
      let taxVal =
        typeof data.pricing.tax === 'string' ? parseFloat(data.pricing.tax) : data.pricing.tax;
      if (!taxVal) {
        taxVal = 0;
      }

      // do things here
      let groupID = query?.get('groupID') ?? '';
      let token = query?.get('token') ?? '';

      if (!groupID || !token) {
        throw new Error('Invalid groupID or token');
      }

      const res = await axiosClient.post('/api/gateway/createCheckoutData', {
        groupID: groupID,
        token: token,
        data: {
          autoToken: true,
          save: true,
          form: {
            allowExtraDiscount: data.allowExtraDiscount,
            allowTip: data.allowTip,
            allowEdit: data.allowEdit,
            pricing: {
              taxType: data.pricing.taxType as Gateway_CreatePayLinkInputDataFormPricingTaxType,
              tax: taxVal,
              lineItems: data.pricing.lineItems.map((item) => {
                return {
                  productId: item.productId,
                  amount: parseInt(item.amount as any),
                };
              }),
            },
          },
        },
      });

      const resData = res.data;

      if (resData.error) {
        throw new Error(resData.error);
      }

      const payURL = resData.url;
      setGeneratedPayUrl(payURL);

      // copy to clipboard
      await navigator.clipboard.writeText(payURL);

      toast.success('Paylink Copied to Clipboard');
      // Removed methods.reset() to keep the form data visible
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  const handleSendEmail = async (email: string) => {
    try {
      // Placeholder for email sending functionality
      // TODO: Implement actual email sending logic
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call
      toast.success(`Payment link successfully sent to ${email}`);
    } catch (error) {
      toast.error('Failed to send email. Please try again.');
    }
  };

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(generatedPayUrl);
      toast.success('Paylink Copied to Clipboard');
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  return (
    <Suspense>
      <FormProvider {...methods}>
        <form
          onSubmit={methods.handleSubmit(onSubmitForm)}
          className="mx-auto max-w-screen-lg space-y-4 p-8"
        >
          <PaylinkForm addProductModal={ModalAddProductLink} />
          <div className="flex flex-col gap-4">
            <div className="flex justify-end gap-5">
              <Button type="submit" color="blue">
                Create Payment Link
              </Button>
              {sendEmailTo ? (
                <Button
                  onClick={() => handleSendEmail(sendEmailTo)}
                  className="bg-green-500 text-white hover:bg-green-700"
                >
                  Send Email to {sendEmailTo}
                </Button>
              ) : null}
            </div>
            {generatedPayUrl && (
              <div className="mt-4 flex items-center gap-3 rounded-lg border border-gray-200 p-4">
                <div className="flex-1 break-all text-gray-700">{generatedPayUrl}</div>
                <Button onClick={handleCopyUrl} size="sm">
                  Copy Link
                </Button>
              </div>
            )}
          </div>
        </form>
      </FormProvider>
    </Suspense>
  );
}
