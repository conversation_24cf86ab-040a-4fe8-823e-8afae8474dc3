import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label, HelperText } from 'flowbite-react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';

export type FormDatepickerProps = {
  name: string;
  helperText?: string;
  id: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  visible?: boolean;
  readOnly?: boolean;
  minDate?: Date;
  maxDate?: Date;
  flex?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (date: Date | null) => void;
  label?: string;
  disabled?: boolean;
  defaultValue?: Date | null;
  tabIndex?: number;
  endAdornment?: React.ReactNode;
};

export const FormDatepicker = ({
  id,
  name,
  label,
  rules,
  disabled,
  helperText = '',
  defaultValue,
  visible = true,
  readOnly = false,
  minDate,
  maxDate,
  flex,
  tabIndex,
  tooltip,
  endAdornment,
  onChangeCallback = (date: Date | null) => {
    date;
  },
  ...props
}: FormDatepickerProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || null}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={`relative w-full ${visible ? '' : 'hidden'} ${flex ? `flex-${flex}` : ''}`}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div className="relative w-full">
            <DatePicker
              id={id}
              ref={ref}
              selected={value}
              onChange={(date) => {
                onChange(date);
                onChangeCallback(date);
              }}
              onBlur={onBlur}
              readOnly={readOnly}
              disabled={isDisabled()}
              minDate={minDate}
              maxDate={maxDate}
              placeholderText={`Select ${label}`}
              className={`bg-gray z-[300] mr-0 w-full rounded-lg border-gray-300 text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-500`}
              tabIndex={tabIndex}
              {...props}
            />
            {endAdornment && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                {endAdornment}
              </div>
            )}
          </div>
          <HelperText color={invalid ? 'failure' : 'default'}>
            {invalid ? error?.message : helperText}
          </HelperText>
        </div>
      )}
    />
  );
};

export default FormDatepicker;
