'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, XCircle } from 'lucide-react';
import { useMutation } from '@apollo/client';
import { Propay_StatusDocument } from '@/graphql/generated/graphql';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Service {
  id: string;
  name: string;
  isUp: boolean;
  lastChecked: Date;
}

export default function ServiceStatusPage() {
  const [checkStatus] = useMutation(Propay_StatusDocument, {
    onCompleted: (data) => {
      setServices(
        data?.propay_status?.components.map((service) => ({
          id: service?.name ?? '',
          isUp: service?.operational ?? false,
          name: service?.name ?? '',
          lastChecked: new Date(),
        })) ?? [],
      );
    },
  });

  useEffect(() => {
    checkStatus();
  }, []);

  const [services, setServices] = useState<Service[]>([]);

  useEffect(() => {
    checkStatus();
    const interval = setInterval(() => {
      checkStatus();
    }, 10_000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="container mx-auto p-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {services.map((service) => (
          <Card key={service.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{service.name}</CardTitle>
              <Badge
                variant={service.isUp ? 'default' : 'destructive'}
                style={{
                  backgroundColor: service.isUp ? '#10B981' : '#EF4444',
                }}
              >
                {service.isUp ? 'UP' : 'DOWN'}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                {service.isUp ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <CardDescription>
                  Last checked: {service.lastChecked.toLocaleTimeString()}
                </CardDescription>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
