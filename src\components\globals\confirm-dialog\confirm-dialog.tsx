'use client';

import { But<PERSON>, Modal } from 'flowbite-react';
import { useState } from 'react';

type ConfirmDialogProp = {
  header: JSX.Element;
  body: JSX.Element;
  actions: JSX.Element;
  open: boolean;
  setOpen: (boolean) => void;
};

export const ConfirmDialog = ({ header, body, actions, open, setOpen }: ConfirmDialogProp) => {
  return (
    <>
      <Modal show={open} onClose={() => setOpen(false)}>
        <Modal.Header>{header}</Modal.Header>
        <Modal.Body>{body}</Modal.Body>
        <Modal.Footer>{actions}</Modal.Footer>
      </Modal>
    </>
  );
};
