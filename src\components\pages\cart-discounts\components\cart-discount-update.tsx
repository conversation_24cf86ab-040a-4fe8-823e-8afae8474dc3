import { useEffect } from 'react';
import { Modal } from 'flowbite-react';
import { CartDiscountForm } from './cart-discount-form';
import { useMutation, useQuery } from '@apollo/client';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import {
  cardDiscountApiToFormMapper,
  cardDiscountFormToUpdteApiMapper,
  CartDiscountFormData,
  cartDiscountFormDataDefault,
} from '../utils';
import { useForm } from 'react-hook-form';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import {
  Gateway_DiscountDocument,
  Gateway_UpdateDiscountDocument,
} from '@/graphql/generated/graphql';

type CartDiscountUpdateProps = {
  isOpen: boolean;
  onClose: () => void;
  refetchListPage: () => void;
  queryData: {
    cartDiscountID: string;
    groupID: string;
  };
};

export const CartDiscountUpdate = ({
  isOpen,
  onClose,
  refetchListPage,
  queryData,
}: CartDiscountUpdateProps) => {
  const { cartDiscountID, groupID } = queryData;

  const {
    data: cartDiscount,
    loading: cartDiscountLaoding,
    error: cartDiscountError,
    refetch: refetchCartDiscountData,
  } = useQuery(Gateway_DiscountDocument, {
    variables: {
      input: {
        data: {
          id: cartDiscountID,
        },
        groupID,
      },
    },
    skip: !cartDiscountID || !groupID,
  });

  const methods = useForm<CartDiscountFormData>();

  const { reset } = methods;

  useEffect(() => {
    if (cartDiscountError) {
      toast.error(cartDiscountError.message);
    }
  }, [cartDiscountError]);

  useEffect(() => {
    refetchCartDiscountData();
  }, [cartDiscountID]);

  useEffect(() => {
    const discount = cartDiscount?.gateway_discount;
    if (discount) {
      reset(cardDiscountApiToFormMapper(discount));
    }
  }, [cartDiscount]);

  const [updateCategoyMutation, { loading: updateCategoyLoading }] = useMutation(
    Gateway_UpdateDiscountDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successUpdate('CartDiscount'));
        reset({ ...cartDiscountFormDataDefault });
        onClose();
        refetchListPage();
      },
      onError: (error) => {
        toast.error(message.api.errorUpdate('CartDiscount', error.message));
      },
    },
  );

  const onSubmitForm = async (data: CartDiscountFormData) => {
    try {
      await updateCategoyMutation({
        variables: {
          input: cardDiscountFormToUpdteApiMapper(data, groupID),
        },
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  const onDeleteCallBack = () => {
    onClose();
    refetchListPage();
  };

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="4xl">
        <Modal.Header className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-blue-600">Update Cart discount</h3>
        </Modal.Header>
        <Modal.Body>
          <SpinnerLoading isLoading={cartDiscountLaoding || updateCategoyLoading} />
          <CartDiscountForm
            methods={methods}
            onSubmit={onSubmitForm}
            isEdit
            onDelete={onDeleteCallBack}
          />
        </Modal.Body>
      </Modal>
    </>
  );
};
