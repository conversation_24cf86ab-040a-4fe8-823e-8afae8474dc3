'use client';

import { Button } from '@/components/ui/button';
import moment from 'moment';
import { useMemo } from 'react';

export const Style = (status: string) => {
  switch (status) {
    case 'Signed':
      return { color: '#34C38F', backgroundColor: '#DAF4EB' };
    case 'Underwriting (Unassigned)':
      return { color: '#F1B44C', backgroundColor: '#FEF5E2' };
    default:
      return { color: '#000000', backgroundColor: '#FFFFFF' };
  }
};

export const useDashboardColumn = (togglePlay: any, setPauseOpen: any, setActionData: any) => {
  const columns = useMemo(() => {
    const baseColumns = [
      // {
      //   accessorKey: 'Group Name',
      //   title: 'group_name',
      //   cell: (data: any) => {
      //     console.log(data);
      //     return data?.row?.original?.name ?? ' - ';
      //   },
      // },
      {
        accessorKey: 'DBA Name',
        title: 'DBA name',
        cell: (data: any) => {
          return (
            data?.row?.original?.processorAUR?.[0]?.processor_aur_applicationStatus
              ?.businessInformation?.dbaName ?? ' - '
          );
        },
      },
      // {
      //   accessorKey: 'Members Count',
      //   title: 'members_count',
      //   cell: (data: any) => {
      //     return data?.row?.original?.membersCount ?? ' - ';
      //   },
      // },
      // {
      //   accessorKey: 'Processor Status',
      //   title: 'processor_status',
      //   cell: (data: any) => {
      //     return data?.row?.original?.processorStatus ?? ' - ';
      //   },
      // },

      {
        accessorKey: 'Processor Status',
        title: 'Processor Status',
        cell: (data: any) => {
          return data?.row?.original?.processorStatus ?? ' - ';
        },
      },
      {
        accessorKey: 'Date Created',
        title: 'Date Created',
        cell: (data: any) => {
          return (
            moment(
              data?.row?.original?.processorAUR?.[0]?.processor_aur_applicationStatus?.createdAt,
            ).format('MMMM DD, YYYY') ?? ' - '
          );
        },
      },
      {
        accessorKey: 'Application Status',
        title: 'Application Status',
        cell: (data: any) => {
          const status =
            data?.row?.original?.processorAUR?.[0]?.processor_aur_applicationStatus
              ?.applicationStatusName ?? ' - ';

          const style = Style(status);
          const underwriting = status === 'Underwriting (Unassigned)';

          if (underwriting) {
            return (
              <div className="flex justify-between gap-2">
                <Button className="text-xs font-bold 2xl:text-sm" style={style}>
                  {status}
                </Button>
              </div>
            );
          }

          return (
            data?.row?.original?.processorAUR?.[0]?.processor_aur_applicationStatus
              ?.applicationStatusName ?? ' - '
          );
        },
      },
    ];

    return baseColumns;
  }, []);

  return columns;
};
