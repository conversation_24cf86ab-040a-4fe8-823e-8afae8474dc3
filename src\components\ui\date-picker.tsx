import { CalendarIcon } from '@radix-ui/react-icons';
import moment from 'moment';

import Error from './error';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';

import { cn } from '@/lib/utils';

export const DatePickerComponent = ({ title, key, values, required, errors }: any) => {
  return (
    <div className="flex flex-col gap-2" key={key}>
      <Label htmlFor={key} className="text-base font-normal text-zinc-600">
        {title}
        {required && <span className="text-red-400">*</span>}
      </Label>
      {/* <Popover>
          <PopoverTrigger asChild> */}
      <Button
        type="button"
        id={key}
        variant={'outline'}
        className={cn(
          'cursor-default border border-zinc-400 pl-3 text-left font-normal text-zinc-600',
          !values && 'text-muted-foreground',
          errors && 'border-red-400 text-red-400 hover:bg-transparent hover:text-red-400',
        )}
      >
        {values ? moment(values).format('MMM DD, YYYY') : <span>Pick a date</span>}
        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
      </Button>
      {/* </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              id="from"
              mode="single"
              selected={values.from}
              onSelect={(date) => setValue('from', date, { shouldValidate: true })}
              disabled={{ before: today }}
              initialFocus
            />
          </PopoverContent>
        </Popover> */}
      {errors && <Error message={errors.message} />}
    </div>
  );
};
