import { ChangeEvent } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label, Checkbox } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
import { cn } from '@/lib/utils';

export type FormToggleProps = {
  name: string;
  id: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  flex?: number;
  visible?: boolean;
  readOnly?: boolean;
  className?: string;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  disabled?: boolean;
  defaultValue?: boolean;
  tabIndex?: number;
};

export const FormToggle = ({
  id,
  name,
  label,
  rules,
  flex,
  disabled,
  defaultValue,
  className,
  visible = true,
  readOnly = false,
  tabIndex,
  tooltip,
  onChangeCallback = (e: ChangeEvent<HTMLInputElement>) => {
    e;
  },
  ...props
}: FormToggleProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || false}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={cn('relative w-full', className)}>
          <div>
            <label className="inline-flex cursor-pointer items-center">
              <Checkbox
                id={id}
                ref={ref}
                checked={value}
                className="peer sr-only"
                onChange={(e) => {
                  onChange(e);
                  onChangeCallback(e);
                }}
                onBlur={(e) => {
                  onChange(e);
                  onBlur();
                }}
                readOnly={readOnly}
                disabled={isDisabled()}
                tabIndex={tabIndex}
                color="primary"
                {...props}
              />
              <div className="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-0.5 after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:ring-4 peer-focus:ring-blue-300 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-blue-800 rtl:peer-checked:after:-translate-x-full"></div>
              {label && (
                <Label
                  htmlFor={id}
                  className="ms-3 flex text-sm font-medium text-gray-900 dark:text-gray-300"
                >
                  {label} {isRequired() && '*'}
                  {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
                </Label>
              )}
            </label>
            <HelperText color={invalid ? 'failure' : 'default'}>
              {invalid ? error?.message : ''}
            </HelperText>
          </div>
        </div>
      )}
    />
  );
};
