import { Variant } from '@/components/globals';
import { CategoryFormData } from './types';
import { capitalizeFirstLetter } from '@/components/shared/utils/strings';

export const categoryFormDataDefault: CategoryFormData = {
  name: '',
  description: '',
  subCategories: [],
  newSubCategory: '',
};

export const getCategoriesStatus = (status: string | undefined | null): [Variant, string] => {
  if (!status) return ['neutral', ''];
  const statusMap: Record<string, Variant> = {
    Success: 'success',
    Active: 'neutral',
  };

  const variant = statusMap[status] || 'neutral';
  const label = capitalizeFirstLetter(status);

  return [variant, label];
};
