// import { formatToLocalDateString } from '@/components/shared/utils';
// import { GatewayUniDisputeOutput } from '@/graphql/generated/graphql';
// import { HiCheck } from 'react-icons/hi';
// import { DisputeChallengeDocumentParams } from '../utils';

// type DisputeChallegeDocumentListProps = {
//   queuedDocuments: DisputeChallengeDocumentParams[];
//   documents: GatewayUniDisputeOutput['history'];
// };
// export const DisputeChallegeDocumentList = ({
//   documents,
//   queuedDocuments,
// }: DisputeChallegeDocumentListProps) => {
//   return (
//     <div className="h-[200px] overflow-y-auto px-2">
//       {queuedDocuments.map((file, index) => (
//         <div key={index} className="flex items-center justify-between border-b py-2">
//           <div>
//             <div className="flex gap-2">
//               <HiCheck className="text-gray-500" />
//               <span className="font-medium">{file?.file?.name}</span>
//             </div>
//             <div className="ml-6 text-sm text-gray-500">
//               <span>File Type: {file?.type}</span>
//               {/* {file?.type && <span> • {file?.type}</span>} */}
//             </div>
//           </div>
//           <span className="text-sm text-gray-500">Queued</span>
//         </div>
//       ))}
//       {documents?.map((file, index) => (
//         <div key={index} className="flex items-center justify-between border-b py-2">
//           <div>
//             <div className="flex gap-2">
//               <HiCheck className="text-green-500" />
//               <span className="font-medium">{file?.fileName}</span>
//             </div>
//             <div className="ml-6 text-sm text-gray-500">
//               <span>File Type: {file?.fileMimetype}</span>
//               {file?.description && <span> • {file.description}</span>}
//             </div>
//           </div>
//           <span className="text-sm text-gray-500">
//             {formatToLocalDateString(new Date(file?.date ?? ''))}
//           </span>
//         </div>
//       ))}
//     </div>
//   );
// };

export const DisputeChallengeDocumentList = () => {
  return <div>DisputeChallengeDocumentList</div>;
};
