import { TestModeBanner } from '@/components/globals/test-mode-banner';
import { ApolloWrapper } from '@/components/graphql/ApolloWrapper';
import { env } from 'next-runtime-env';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export default function HomeLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <ApolloWrapper>
        {env('NEXT_PUBLIC_TEST_MODE') === 'true' && <TestModeBanner />}
        <div>{children}</div>
        <ToastContainer />
      </ApolloWrapper>
    </>
  );
}
