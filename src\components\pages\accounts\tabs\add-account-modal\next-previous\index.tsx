import type { NextPage } from 'next';
import { Button } from '@/components/ui/button';

interface NextPreviousProps {
  showNext?: boolean;
  showPrevious?: boolean;
  showSubmit?: boolean;
  onNextClick?: () => void;
  onPreviousClick?: () => void;
  onSubmitClick?: () => void;
  isLoading?: boolean;
  errorMessage?: string | null;
}

const NextPrevious: NextPage<NextPreviousProps> = ({
  showNext = true,
  showPrevious = true,
  onNextClick,
  onPreviousClick,
  isLoading,
  showSubmit = false,
  onSubmitClick,
  errorMessage,
}) => {
  return (
    <>
      {errorMessage && (
        <div className="py-2 pr-4 text-right text-sm font-bold text-red-600">* {errorMessage}</div>
      )}

      <div className={`mx-8 mb-4 mt-2 flex items-center justify-between`}>
        <div>
          {showPrevious && (
            <Button onClick={onPreviousClick} disabled={isLoading} variant={'secondary'}>
              {isLoading ? 'Loading...' : 'Back'}
            </Button>
          )}
        </div>
        {showNext && (
          <Button onClick={onNextClick} disabled={isLoading} variant="primary">
            {isLoading ? 'Loading...' : 'Next'}
          </Button>
        )}
        {showSubmit && (
          <Button onClick={onSubmitClick} disabled={isLoading}>
            {isLoading ? 'Loading...' : 'Submit'}
          </Button>
        )}
      </div>
    </>
  );
};

export default NextPrevious;
