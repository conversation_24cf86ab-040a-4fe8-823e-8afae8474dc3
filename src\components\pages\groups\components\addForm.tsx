'use client';

import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog';

const AddGroupFrom = ({
  formOpen,
  setFormOpen,
  handleFormSubmitComplete,
  type,
  initialData,
}: any) => {
  return (
    <Dialog open={formOpen} onOpenChange={setFormOpen}>
      <DialogOverlay className="opacity-0">
        <DialogContent className="w-full p-8 md:min-w-[50vw]">
          <div className="flex flex-col">
            <div className="flex w-full items-center justify-between gap-4 text-center">
              <DialogTitle className="text-2xl text-[#495057]">
                {type === 'edit' ? 'Edit' : 'Create'}
              </DialogTitle>
              <DialogPrimitive.Close
                onClick={(e) => {
                  e.stopPropagation();
                }}
                className="h-6 w-6 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
              >
                <X className="h-6 w-6" />
                <span className="sr-only">Close</span>
              </DialogPrimitive.Close>
            </div>
          </div>

          <DialogDescription>test</DialogDescription>
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  );
};

export default AddGroupFrom;
