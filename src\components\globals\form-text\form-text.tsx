import { FC } from 'react';
import { Label, Tooltip } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { cn } from '@/lib/utils';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { useFormFieldHelpers } from '@/components/hooks';

export type FormTextDisplayProps = {
  name: string;
  helperText?: string;
  id: string;
  className?: string;
  tooltip?: FormToolTipProps['tooltip'];
  label?: string;
  disabled?: boolean;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  defaultValue?: string;
  processOutput?: (value: string) => React.ReactNode;
};

export const FormTextDisplay: FC<FormTextDisplayProps> = ({
  id,
  name,
  label,
  helperText = '',
  className,
  tooltip,
  disabled,
  rules,
  defaultValue,
  processOutput,
}) => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });
  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || ''}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={cn('relative w-full', className)}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div>
            <div className="relative w-full">
              {processOutput ? (
                processOutput(value)
              ) : (
                <span id={id} className="block w-full p-2.5">
                  {value}
                </span>
              )}
            </div>
            <HelperText color={invalid ? 'failure' : 'default'}>
              {invalid ? error?.message : helperText}{' '}
            </HelperText>
          </div>
        </div>
      )}
    />
  );
};

export default FormTextDisplay;
