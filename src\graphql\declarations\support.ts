import { graphql } from '../generated';

export const GroupSupportTickets = graphql(`
  query GroupSupportTickets(
    $where: GroupSupportTicketWhereInput!
    $orderBy: [GroupSupportTicketOrderByInput!]!
    $take: Int
    $skip: Int!
  ) {
    groupSupportTickets(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      title
      category
      description
      status
      messagesCount
      createdAt
      lastMessageCreatedAt
      group {
        actualName
        labelName
        name
        id
      }
      createdBy {
        displayName
        id
      }
    }
    groupSupportTicketsCount(where: $where)
  }
`);

export const GroupSupportTicketsCountDocument = graphql(`
  query GroupSupportTicketsCount($where: GroupSupportTicketWhereInput!) {
    groupSupportTicketsCount(where: $where)
  }
`);

export const GroupSupportTicket = graphql(`
  query GroupSupportTicket($where: GroupSupportTicketWhereUniqueInput!) {
    groupSupportTicket(where: $where) {
      id
      title
      status
      category
      description
      messagesCount
      createdAt
      lastMessageCreatedAt
      group {
        actualName
        labelName
        name
        id
      }
      createdBy {
        displayName
        id
      }
      messages {
        createdAt
        actualName
        name
        message
        files
        id
      }
    }
  }
`);

export const CreateGroupSupportTicket = graphql(`
  mutation CreateGroupSupportTicket($data: GroupSupportTicketCreateInput!) {
    createGroupSupportTicket(data: $data) {
      id
    }
  }
`);

export const CreateGroupSupportTicketMessage = graphql(`
  mutation CreateGroupSupportMessage($data: GroupSupportMessageCreateInput!) {
    createGroupSupportMessage(data: $data) {
      id
    }
  }
`);
