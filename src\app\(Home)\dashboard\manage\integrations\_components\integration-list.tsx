'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import StripeIntegrationForm from './stripe-integration-form';
import HubspotIntegrationForm from './hubspot-integration-form';

import { useLocationSelector } from '@/components/hooks';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import Image from 'next/image';
import { KeyRound, Globe } from 'lucide-react';
import ApiKeyManager from '@/app/(Home)/dashboard/admin/integrations/_components/api-key-manager';
import WebhookManager from '@/app/(Home)/dashboard/admin/integrations/_components/webhook-manager';
import OAuthClientManager from '@/app/(Home)/dashboard/admin/integrations/_components/oauth-client-manager';
import OAuthTokenManager from '@/app/(Home)/dashboard/admin/integrations/_components/oauth-token-manager';
import StripeOAuthIntegration from '@/app/(Home)/dashboard/admin/integrations/_components/stripe-oauth-integration';
import { ReactNode } from 'react';
import StripeSyncManager from '@/app/(Home)/dashboard/admin/integrations/_components/stripe-sync-manager';
import StripeDisconnectManager from '@/app/(Home)/dashboard/admin/integrations/_components/stripe-disconnect-manager';

// Section header component
function SectionHeader({ title }: { title: string }) {
  return (
    <div className="mb-4 border-b border-slate-200 pb-2">
      <h2 className="text-2xl font-bold text-slate-800">{title}</h2>
    </div>
  );
}

// Integration sub-item component
function IntegrationOption({
  title,
  description,
  buttonText,
  children,
}: {
  title: string;
  description?: string;
  buttonText: string;
  children: ReactNode;
}) {
  return (
    <div className="flex items-center justify-between border-t border-slate-100 py-3 first:border-t-0">
      <div className="flex-1">
        <h4 className="text-md font-medium text-slate-700">{title}</h4>
        {description && <p className="mt-1 text-sm text-slate-500">{description}</p>}
      </div>
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="ml-4">
            <span className="text-sm font-medium">{buttonText}</span>
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-[650px] overflow-visible">{children}</DialogContent>
      </Dialog>
    </div>
  );
}

// Integration category card component
function IntegrationCategory({
  title,
  description,
  icon,
  iconPath,
  children,
}: {
  title: string;
  description?: string;
  icon?: ReactNode;
  iconPath?: string;
  children: ReactNode;
}) {
  return (
    <div className="flex flex-col rounded-lg border border-slate-200 bg-white">
      <div className="flex items-start gap-4 p-4">
        <div className="relative aspect-square h-14 w-14 flex-shrink-0">
          {iconPath ? (
            <Image src={iconPath} alt={title} fill className="object-contain" />
          ) : (
            <div className="flex h-full w-full items-center justify-center rounded-md bg-blue-100 text-blue-600">
              {icon}
            </div>
          )}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-slate-800">{title}</h3>
          {description && <p className="mt-1 text-sm text-slate-600">{description}</p>}
        </div>
      </div>
      <div className="px-4 pb-4">{children}</div>
    </div>
  );
}

export default function IntegrationList() {
  const { locationSelectorElement, locationFilter } = useLocationSelector({});
  const groupId = locationFilter?.id ?? '';

  return (
    <div className="flex flex-col gap-8">
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-full sm:w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3"></div>
      </div>

      <section>
        <SectionHeader title="Payment Integrations" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Stripe Integration Card */}
          <IntegrationCategory
            iconPath="/images/logos/stripe-icon.webp"
            title="Stripe"
            description="Payment processing platform for businesses of all sizes"
          >
            <IntegrationOption
              title="Connect via API Keys"
              description="Connect using Stripe secret and publishable keys"
              buttonText="Configure"
            >
              <StripeIntegrationForm groupId={groupId} />
            </IntegrationOption>

            <IntegrationOption
              title="Connect via OAuth"
              description="Use Stripe OAuth for secure connection"
              buttonText="Connect"
            >
              <StripeOAuthIntegration groupId={groupId} />
            </IntegrationOption>

            <IntegrationOption
              title="Sync Data"
              description="Synchronize customer and payment data"
              buttonText="Sync Now"
            >
              <StripeSyncManager groupId={groupId} />
            </IntegrationOption>

            <IntegrationOption
              title="Disconnect Stripe"
              description="Remove Stripe integration from your account"
              buttonText="Disconnect"
            >
              <StripeDisconnectManager groupId={groupId} />
            </IntegrationOption>
          </IntegrationCategory>

          {/* Hubspot Integration Card */}
          <IntegrationCategory
            iconPath="/images/logos/hubspot.png"
            title="Hubspot"
            description="CRM platform for marketing, sales, and customer service"
          >
            <IntegrationOption
              title="Connect Hubspot"
              description="Connect your Hubspot account for customer data integration"
              buttonText="Configure"
            >
              <HubspotIntegrationForm groupId={groupId} />
            </IntegrationOption>
          </IntegrationCategory>
        </div>
      </section>

      <section>
        <SectionHeader title="API & Development" />
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* OAuth Management Card */}
          <IntegrationCategory
            icon={<Globe className="h-8 w-8" />}
            title="OAuth Management"
            description="Manage OAuth clients and access tokens"
          >
            <IntegrationOption
              title="OAuth Clients"
              description="Configure OAuth client applications"
              buttonText="Manage Clients"
            >
              <OAuthClientManager groupId={groupId} />
            </IntegrationOption>

            <IntegrationOption
              title="OAuth Tokens"
              description="View active access and refresh tokens"
              buttonText="View Tokens"
            >
              <OAuthTokenManager groupId={groupId} />
            </IntegrationOption>
          </IntegrationCategory>

          {/* API & Webhook Management Card */}
          <IntegrationCategory
            icon={<KeyRound className="h-8 w-8" />}
            title="API & Webhooks"
            description="Manage API keys and webhook notifications"
          >
            <IntegrationOption
              title="API Keys"
              description="Manage API keys for payment services"
              buttonText="Manage Keys"
            >
              <ApiKeyManager groupId={groupId} />
            </IntegrationOption>

            <IntegrationOption
              title="Webhooks"
              description="Configure webhook endpoints for event notifications"
              buttonText="Manage Webhooks"
            >
              <WebhookManager groupId={groupId} />
            </IntegrationOption>
          </IntegrationCategory>
        </div>
      </section>
    </div>
  );
}
