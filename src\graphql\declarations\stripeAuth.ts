import { graphql } from '../generated';

export const StripeDataApplications = graphql(`
  query StripeDataApplications {
    stripeDataApplications {
      id
      keyType
      publishableKey
      secretKey
      accessKey
      refreshKey
      userId
      stripeUserId
      accountId
      tokenType
      scope
      liveMode
    }
  }
`);

export const DeleteStripeDataApplications = graphql(`
  mutation DeleteStripeDataApplications($where: [StripeDataApplicationWhereUniqueInput!]!) {
    deleteStripeDataApplications(where: $where) {
      id
    }
  }
`);
