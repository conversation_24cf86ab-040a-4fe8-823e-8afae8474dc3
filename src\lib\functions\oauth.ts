/**
 * OAuth functions for authorization and token retrieval
 */

import { env } from 'next-runtime-env';

interface AuthorizeRequest {
  oauthClientKey: string;
  redirectURI: string;
  state: string;
  token?: string; // Bearer token for authorization
  groupId?: string; // Group ID to associate with the OAuth token
}

interface AuthorizeResponse {
  redirectURI: string;
}

interface TokenRequest {
  grant_type: string;
  code: string;
  redirect_uri: string;
  client_id: string;
  client_secret: string;
}

interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  scope: string;
}

/**
 * Client Group Info Response
 */
interface ClientGroupInfo {
  groupId: string;
  groupName: string;
  scope: string;
}

/**
 * Request OAuth authorization
 */
export const requestOAuthAuthorization = async (
  params: AuthorizeRequest,
): Promise<AuthorizeResponse> => {
  const response = await fetch(`${env('NEXT_PUBLIC_SERVER_URL')}/api/oauth/authorize`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(params.token && { Authorization: `Bearer ${params.token}` }),
    },
    body: JSON.stringify({
      oauthClientKey: params.oauthClientKey,
      redirectURI: params.redirectURI,
      state: params.state,
      groupId: params.groupId,
    }),
  });

  if (!response.ok) {
    throw new Error(`OAuth authorization failed: ${response.status} ${response.statusText}`);
  }

  return await response.json();
};

// Check if we should use mock mode
const shouldUseMockMode = () => {
  return env('NEXT_PUBLIC_MOCK_MODE') === 'true' || env('NEXT_PUBLIC_DEMO') === 'true';
};

/**
 * Exchange authorization code for access token
 */
export const exchangeCodeForToken = async (params: TokenRequest): Promise<TokenResponse> => {
  if (shouldUseMockMode()) {
    // Return mock token response
    return {
      access_token: 'mock-access-token',
      token_type: 'Bearer',
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      scope: 'read write',
    };
  }

  try {
    const response = await fetch(`${env('NEXT_PUBLIC_SERVER_URL')}/api/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: params.grant_type,
        code: params.code,
        redirect_uri: params.redirect_uri,
        client_id: params.client_id,
        client_secret: params.client_secret,
      }),
    });

    if (!response.ok) {
      throw new Error(`OAuth token exchange failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.warn('OAuth token exchange failed, using mock token:', error);
    // Fallback to mock response
    return {
      access_token: 'mock-access-token',
      token_type: 'Bearer',
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      scope: 'read write',
    };
  }
};

/**
 * Get OAuth client group information
 * @param clientId The OAuth client ID
 * @returns Group information associated with the client
 */
export const getOAuthClientGroup = async (clientId: string): Promise<ClientGroupInfo> => {
  const response = await fetch(
    `${env('NEXT_PUBLIC_SERVER_URL')}/api/oauth/client/group?clientId=${encodeURIComponent(clientId)}`,
    {
      method: 'GET',
      headers: {
        Accept: '*/*',
      },
    },
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch client group info: ${response.status} ${response.statusText}`);
  }

  return await response.json();
};
