'use client';

import { LoaderSquares } from '@/components/globals/Loaders/Square';
import { GHLSSOCheck, GHLSSOSignIn } from '@/graphql/declarations/gohighlevel-sso';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { GHLSetupCheck, GHLSetupGetPending } from '@/graphql/declarations/gohighlevel-setup';
import { Card, CardContent } from '@/components/ui/card';
import { Me } from '@/graphql/declarations/me';

export default function TestPage() {
  const [reconnect, setReconnect] = useState(false);

  useEffect(() => {
    ssoParse();
  });

  const getActualSSOToken = async () => {
    return (await new Promise((resolve) => {
      window.parent.postMessage({ message: 'REQUEST_USER_DATA' }, '*');
      window.addEventListener('message', ({ data }) => {
        if (data.message === 'REQUEST_USER_DATA_RESPONSE') {
          resolve(data.payload);
        }
      });
    })) as string;
  };

  const getSSOToken = async () => {
    return getActualSSOToken();
  };

  async function ssoParse() {
    const key = await getSSOToken();

    const isSetup = await initiateSetupFlow({ ssoToken: key });

    if (isSetup) {
      const result = await loginViaSSO({ ssoToken: key });

      if (result) {
        window.location.href = '/dashboard';
      } else {
        const urlEncodedKey = encodeURIComponent(key);
        // prompt to bind an account
        window.location.href = `/login?mode=ghl-binding&token=${urlEncodedKey}`;
      }
    }

    // const result = await loginViaSSO({ ssoToken: key });

    // if (result) {
    //   // return redirect('/dashboard');

    //   // check current location setup status, redirect to dashboard if it's already setup

    //   // if setup not done yet, check if code token exists for location, redirect to /integrate/ghl if available

    //   // if no token available, show warning message and a button to reconnect
    // } else {
    //   // prompt to bind an account
    // }
  }

  async function loginViaSSO(args: { ssoToken: string }) {
    try {
      const resp = await apolloClient.mutate({
        mutation: GHLSSOSignIn,
        variables: {
          input: {
            ssoToken: args.ssoToken,
          },
        },
      });

      if (resp.data?.ghl_auth_sso?.sessionToken) {
        const token = resp.data.ghl_auth_sso.sessionToken;

        AUTHSTORE.set(token);

        const locationData = await apolloClient.query({
          query: GHLSSOCheck,
          variables: {
            input: {
              ssoToken: args.ssoToken,
            },
          },
        });

        const userData = await apolloClient.query({
          query: Me,
        });

        if (locationData.data.ghl_auth_getSSOInfo?.groupID) {
          localStorage.setItem(
            `location_${userData.data.authenticatedItem?.id}`,
            JSON.stringify({
              id: locationData.data.ghl_auth_getSSOInfo?.groupID,
              label: locationData.data.ghl_auth_getSSOInfo?.groupName,
            }),
          );
          localStorage.setItem('iframeSelection', 'true');
        }

        toast.success('Logged in via SSO');

        return true;
      } else {
        toast.error('Failed to login via SSO');
      }
    } catch (error) {
      console.error('loginViaSSO', error);
      return false;
    }
  }

  async function initiateSetupFlow(args: { ssoToken: string }) {
    const res = await apolloClient.mutate({
      mutation: GHLSetupCheck,
      variables: {
        input: {
          ssoToken: args.ssoToken,
        },
      },
    });

    const isSetup = res.data?.ghl_auth_checkGHLAccess?.hasAccess;

    if (isSetup) {
      return true;
    }

    try {
      const codeToken = await apolloClient.mutate({
        mutation: GHLSetupGetPending,
        variables: {
          input: {
            ssoToken: args.ssoToken,
          },
        },
      });

      const code = codeToken.data?.ghl_auth_checkPendingInstall?.code;

      // if code token exists, redirect to /integrate/ghl to complete setup
      if (code) {
        window.location.href = `/integrate/ghl?code=${code}`;
        return false;
      }

      setReconnect(true);
      return false;
    } catch (error) {
      setReconnect(true);
      return false;
    }
  }

  if (reconnect) {
    return (
      <div className="flex h-screen w-screen items-center justify-center text-center">
        <Card className="max-w-screen-sm">
          <CardContent className="p-6">
            <p>
              Looks like something wrong happened with the installation. Please uninstall then
              reinstall our NGnair Payments to fix the issue
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex h-screen w-screen items-center justify-center">
      <LoaderSquares />
    </div>
  );
}
