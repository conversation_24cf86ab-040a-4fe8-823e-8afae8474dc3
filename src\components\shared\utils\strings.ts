/**
 * @param string The string to capitalize the first letter of
 * @returns The string with the first letter capitalized
 */
export const capitalizeFirstLetter = (string: string): string => {
  const lowerCase = string.toLowerCase();
  return lowerCase.charAt(0).toUpperCase() + lowerCase.slice(1);
};

export type ConcatStringsOptions = {
  /** The separator to use when joining the strings. Defaults to `' '`. */
  separator?: string;
  /** Removes falsy values from the array before joining it. Defaults to `true`. */
  removeFalsy?: boolean;
};

/**
 * Joins an array of strings together.
 *
 * @param strings The strings to join
 * @param options The options to use when joining the strings
 * @returns The joined string. Example1: `concatStrings(['a', 'b', 'c'])` => `'a b c'`, Example2: `concatStrings(['a', 'b', 'c'], { separator: ', ' })` => `'a, b, c'`
 */
export const concatStrings = (
  strings: (string | undefined | null)[],
  options?: ConcatStringsOptions,
): string => {
  const separator = options?.separator || ' ';
  const removeFalsy = options?.removeFalsy ?? true;

  if (!removeFalsy) {
    return strings.join(separator);
  }

  const filtered = strings.filter((value) => !!value);

  return filtered.join(separator);
};

export const stringToBase64 = (str: string): string => {
  return Buffer.from(str).toString('base64');
};

export const base64ToString = (str: string): string => {
  return Buffer.from(str, 'base64').toString();
};
