// import { message } from '@/components/shared/utils';
// import { FormProvider, useForm } from 'react-hook-form';
// import { Gateway_AddPaymentMethodDocument } from '@/graphql/generated/graphql';
// import { useMutation } from '@apollo/client';
// import { toast } from 'react-toastify';
// import { CardFormData, cardFormDefaults } from '../utils';
// import { Button, Tabs } from 'flowbite-react';
// import { ACHHostedComponent } from '@/components/payments/ach-local';
// import { CardGLPHostedComponent } from '@/components/payments/glpv2';

// type CustomerCardAddProps = {
//   groupID: string;
//   customerID: string;
//   refetchCustomer: () => void;
// };

// export const CustomerCardAdd = ({ groupID, customerID, refetchCustomer }: CustomerCardAddProps) => {
//   const customerFormMethods = useForm<CardFormData>({
//     defaultValues: {
//       ...cardFormDefaults,
//     },
//   });

//   const [addPaymentMutation, { loading: addPaymentLoading }] = useMutation(
//     Gateway_AddPaymentMethodDocument,
//     {
//       onCompleted: (_) => {
//         toast.success(message.api.successCreate('Payment'));
//         customerFormMethods.reset({
//           cardNumber: '',
//           cvc: '',
//           expiryDate: '',
//         });
//         refetchCustomer();
//       },
//       onError: (error) => {
//         toast.error(message.api.errorCreate('Payment', error.message));
//       },
//     },
//   );

//   const onSubmitForm = async (data: CardFormData) => {
//     try {
//       if (!data.processType) {
//         return toast.error('Please select a payment method');
//       }
//       if (data.processType === 'card' && !data.cardNumber) {
//         return toast.error('Please enter a valid card number');
//       } else if (data.processType === 'ach' && !data.accountNumber) {
//         return toast.error('Please enter a valid account number');
//       } else if (data.processType === 'gpecomm' && !data.gpEcomm) {
//         return toast.error('Please enter a valid GPEComm ID');
//       }

//       await addPaymentMutation({
//         variables: {
//           input: {
//             groupID,
//             data: {
//               customerID,
//               form: {
//                 card:
//                   data.processType === 'card'
//                     ? {
//                         cvc: data.cvc,
//                         expiryDate: data.expiryDate,
//                         cardToken: data.cardNumber,
//                       }
//                     : undefined,
//                 ach:
//                   data.processType === 'ach'
//                     ? {
//                         accountNumber: data.accountNumber!,
//                         routingNumber: data.routingNumber!,
//                         accountType: data.accountType!,
//                         accountHolderType: data.holderType!,
//                       }
//                     : undefined,
//                 gpecomm:
//                   data.processType === 'gpecomm'
//                     ? {
//                         id: data.gpEcomm!,
//                       }
//                     : undefined,
//               },
//             },
//           },
//         },
//       });
//     } catch (e) {
//       console.error('Add Customer Mutation error: ', e);
//     }
//   };

//   return (
//     <FormProvider {...customerFormMethods}>
//       <form onSubmit={customerFormMethods.handleSubmit(onSubmitForm)} className="space-y-4">
//         <Tabs
//           onActiveTabChange={(tabIndex) => {
//             customerFormMethods.setValue('processType', tabIndex === 0 ? 'gpecomm' : 'ach');
//           }}
//         >
//           {/* <Tabs.Item active title="Credit Card">
//             <TSEPHostedTokenizerComponent
//               // fieldErrors={fieldErrors}
//               onEvent={(eventType, event) => {
//                 console.log(eventType, event);
//               }}
//               labels={{
//                 cardNumber: 'Card Number',
//                 expiryDate: 'Expiry Date',
//                 cvv: 'CVC',
//                 // cardHolderName: 'Card Holder Name',
//                 // zipCode: 'Zip Code',
//               }}
//               allowOptionals={{
//                 cvv: true,
//                 // zipCode: true,
//                 // cardHolderName: true,
//               }}
//               onToken={(token) => {
//                 // methods.setValue('fullName', token.cardHolderName);
//                 customerFormMethods.setValue('cardNumber', token.tsepToken);
//                 customerFormMethods.setValue('cvc', token.cvv2);
//                 customerFormMethods.setValue('expiryDate', token.expirationDate);
//                 // methods.setValue('zipCode', token.zipCode);
//                 // methods.setValue('brand', token.cardType);
//                 // console.log('token', token);
//               }}
//               iframeComponentAttributes={{
//                 height: '230px',
//               }}
//               onTokenError={() => {
//                 // console.error('error', error);
//                 // methods.setValue('fullName', '');
//                 customerFormMethods.setValue('cardNumber', '');
//                 customerFormMethods.setValue('cvc', '');
//                 customerFormMethods.setValue('expiryDate', '');
//                 // methods.setValue('zipCode', '');
//                 // methods.setValue('brand', '');
//                 // setVerifyResult(undefined);
//               }}
//             />
//           </Tabs.Item> */}
//           <Tabs.Item active title="Credit Card">
//             <CardGLPHostedComponent
//               merchantID={groupID}
//               // fieldErrors={fieldErrors}
//               onEvent={(_eventType, _event) => {
//                 // console.log(eventType, event);
//               }}
//               onToken={(token) => {
//                 // methods.setValue('fullName', token.cardHolderName);
//                 customerFormMethods.setValue('gpEcomm', token.paymentID);
//                 // methods.setValue('zipCode', token.zipCode);
//                 // methods.setValue('brand', token.cardType);
//                 // console.log('token', token);
//               }}
//               onError={() => {
//                 // console.error('error', error);
//                 // methods.setValue('fullName', '');
//                 customerFormMethods.setValue('gpEcomm', '');
//                 // methods.setValue('zipCode', '');
//                 // methods.setValue('brand', '');
//                 // setVerifyResult(undefined);
//               }}
//               iframeComponentAttributes={{
//                 height: '230px',
//               }}
//             />
//           </Tabs.Item>
//           <Tabs.Item title="ACH">
//             <ACHHostedComponent
//               onToken={(token) => {
//                 console.log('token', token);
//                 customerFormMethods.setValue('accountNumber', token.accountNumber);
//                 customerFormMethods.setValue('routingNumber', token.routingNumber);
//                 customerFormMethods.setValue('accountType', token.accountType);
//                 customerFormMethods.setValue('holderType', token.holderType);
//               }}
//               onError={() => {
//                 customerFormMethods.setValue('accountNumber', '');
//                 customerFormMethods.setValue('routingNumber', '');
//                 customerFormMethods.setValue('accountType', '');
//                 customerFormMethods.setValue('holderType', '');
//               }}
//               iframeComponentAttributes={{
//                 height: '250px',
//               }}
//             />
//           </Tabs.Item>
//         </Tabs>
//         <Button
//           type="submit"
//           color="blue"
//           className="h-auto w-full px-4"
//           disabled={addPaymentLoading}
//         >
//           {addPaymentLoading ? 'Adding Payment Method...' : 'Add New Payment Method'}
//         </Button>
//       </form>
//     </FormProvider>
//   );
// Temporarily disabled customer functionality
export const CustomerCardAdd = () => {
  return null;
};
