'use client';
// import LocationTab from '@/components/pages/location/page/location-tab';
import { Suspense, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const Page = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect from old admin URL to new manage URL
    router.replace('/dashboard/manage');
  }, [router]);

  return (
    <Suspense>
      {/* <LocationTab /> */}
      <div className="mx-auto mt-8 py-0">
        <div className="mb-4 text-sm text-gray-600">Redirecting to the new management page...</div>
      </div>
    </Suspense>
  );
};

export default Page;
