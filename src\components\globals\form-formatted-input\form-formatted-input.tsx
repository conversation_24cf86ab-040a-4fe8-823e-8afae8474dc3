import { ChangeEvent } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
// import ReactInputMask from 'react-input-mask';

export type FormFormattedInputProps = {
  name: string;
  helperText?: string;
  id: string;

  mask: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  className?: string;
  visible?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  placeholder?: string;
  min?: number | string;
  max?: number | string;
  flex?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  variant?: 'primary' | 'secondary';
  label?: string;
  disabled?: boolean;
  defaultValue?: string;
  tabIndex?: number;
  endAdornment?: React.ReactNode;
  type?: string; // Add this line
  maskChar?: string;
};

export const FormFormattedInput = ({
  id,
  name,
  label,
  mask,
  rules,
  className,
  disabled,
  helperText = '',
  defaultValue,
  visible = true,
  readOnly = false,
  maxLength,
  min,
  max,
  flex,
  tabIndex,
  tooltip,
  endAdornment,
  placeholder,
  variant = 'primary',
  onChangeCallback = (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    e;
  },
  type = 'text', // Add this line
  ...props
}: FormFormattedInputProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || ''}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={`relative w-full ${visible ? '' : 'hidden'} ${flex ? `flex-${flex}` : ''}`}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div className="relative w-full">
            <input
              id={id}
              value={value}
              onChange={(e) => {
                // Trim spaces from the input value
                const trimmedValue = e.target.value.replace(/\s+/g, ''); // Remove all spaces
                if (maxLength && trimmedValue.length > maxLength) return;
                onChange(trimmedValue); // Use the trimmed value
                onChangeCallback(e);
              }}
              onBlur={(e) => {
                onChange(e);
                onBlur();
              }}
              readOnly={readOnly}
              disabled={isDisabled()}
              type={type}
              maxLength={maxLength}
              min={min}
              max={max}
              autoComplete="off"
              placeholder={`${placeholder ? placeholder : 'Enter ' + label}`}
              tabIndex={tabIndex}
              className={`block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500 ${endAdornment ? 'pr-12' : ''}`}
            />
            {endAdornment && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                {endAdornment}
              </div>
            )}
            {/* {tooltip && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <FormToolTip tooltip={tooltip} mode="input" />
              </div>
            )} */}
          </div>
          <HelperText color={invalid ? 'failure' : 'default'}>
            {invalid ? error?.message : helperText}{' '}
          </HelperText>
        </div>
      )}
    />
  );
};

export default FormFormattedInput;
