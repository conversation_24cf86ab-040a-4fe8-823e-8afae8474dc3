'use client';

import { TSEPHostedTokenizerComponent } from '@/components/payments/tsep/tokenizer';

export default function Page() {
  // create a url param safe string of the settings object
  const settings = {
    labels: {
      cardNumber: 'Number',
      expiryDate: 'Expiry',
      cvv: 'CVV',
      cardHolderName: 'Card Holder Name',
      zipCode: 'Zip',
    },
    allowOptionals: {
      cardHolderName: false,
      zipCode: false,
      cvv: true,
    },
  };

  const cssString = `
      #payment-form #tsep-cardNum {
         border-radius: 0px;
    }
  `;

  const onEvent = (data) => {
    console.log(data);
  };

  return (
    <div className="flex h-screen flex-col items-center justify-center gap-2 shadow-md">
      <p>Iframe Sample</p>
      <TSEPHostedTokenizerComponent
        labels={settings.labels}
        allowOptionals={settings.allowOptionals}
        onEvent={onEvent}
        onToken={onEvent}
        onTokenError={onEvent}
        cssString={cssString}
        iframeComponentAttributes={{
          width: '500px',
        }}
      />
    </div>
  );
}
