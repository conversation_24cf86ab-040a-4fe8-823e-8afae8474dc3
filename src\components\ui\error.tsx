import { ExclamationCircleIcon } from '@heroicons/react/24/outline';
type ErrorProps = {
  message: string;
  className?: string;
};
const Error = ({ message, className }: ErrorProps) => {
  return (
    <div className={`flex items-center gap-1 text-sm font-normal text-red-400 ${className}`}>
      <ExclamationCircleIcon className="h-5 w-5" />
      <p className="whitespace-nowrap text-sm">{message}</p>
    </div>
  );
};

export default Error;
