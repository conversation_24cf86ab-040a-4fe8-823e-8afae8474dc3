// import { useEffect, useState } from 'react';
// import { <PERSON><PERSON>, Modal } from 'flowbite-react';
// import { toast } from 'react-toastify';
// import { FaCircleCheck } from 'react-icons/fa6';
// import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
// import { useQuery } from '@apollo/client';
// import { Gateway_CustomerDocument } from '@/graphql/generated/graphql';
// import { SpinnerLoading } from '@/components/globals/spinner-loading';
// import { CustomerCardAdd } from './customer-card-add';
// import { CardBrand } from '@/components/shared/components';
// import { CustomerUpdate } from './customer-update';
// import { CustomerDelete } from './customer-delete';
// import { CustomerCardDelete } from './customer-card-delete';
// import { CustomerCardChangeDefault } from './customer-card-change-default';
// import { useRouter } from 'next/navigation';

type CustomerViewModalProps = {
  isOpen: boolean;
  onClose: () => void;
  queryData: {
    customerID: string;
    groupID: string;
  };
};
// export const CustomerViewModal = ({ isOpen, onClose, queryData }: CustomerViewModalProps) => {
//   const router = useRouter();
//   const { customerID, groupID } = queryData;
//   const { data, loading, error, refetch } = useQuery(Gateway_CustomerDocument, {
//     variables: {
//       input: {
//         data: {
//           customerID,
//         },
//         groupID,
//       },
//     },
//     skip: !customerID || !groupID,
//   });
//   const [isLoading, setIsLoading] = useState(false);

//   useEffect(() => {
//     refetch();
//   }, [customerID, groupID]);

//   useEffect(() => {
//     if (error) {
//       toast.error(error.message);
//     }
//   }, [error]);

//   const columns: Column[] = [
//     {
//       key: 'isDefault',
//       header: 'Default',
//       sortable: true,
//       renderCell: (row) => {
//         return (
//           <button className="text-red-500 hover:text-red-700">
//             {row.isDefault ? (
//               <FaCircleCheck color="green" size={20} />
//             ) : (
//               <CustomerCardChangeDefault
//                 card={row}
//                 customerID={customerID}
//                 setParentLoading={setIsLoading}
//                 refetch={refetch}
//               />
//             )}
//           </button>
//         );
//       },
//     },
//     {
//       key: 'last4',
//       header: 'Last 4',
//       sortable: true,
//     },
//     {
//       key: 'brand',
//       header: 'Brand',
//       sortable: true,
//       renderCell: (row) => <CardBrand brand={row.brand} />,
//     },
//     {
//       key: 'expires',
//       header: 'Expires',
//       sortable: true,
//     },
//     // {
//     //   key: 'added',
//     //   header: 'Added',
//     //   sortable: true,
//     // },
//     {
//       key: 'action',
//       header: 'Action',
//       sortable: true,
//       renderCell: (row) => {
//         return (
//           <div className="flex w-fit gap-2">
//             <Button
//               color="blue"
//               size="sm"
//               onClick={() => {
//                 const customerID = queryData.customerID;
//                 const cardID = row.cardID;

//                 const checkoutPath = new URL(
//                   '/dashboard/customers/manual-entry',
//                   window.location.origin,
//                 );
//                 checkoutPath.searchParams.set('customerID', customerID);
//                 checkoutPath.searchParams.set('cardID', cardID);
//                 router.push(checkoutPath.toString());
//               }}
//             >
//               Make Payment
//             </Button>
//             <CustomerCardDelete
//               card={row}
//               customerID={customerID}
//               setParentLoading={setIsLoading}
//               refetch={refetch}
//             />
//           </div>
//         );
//       },
//     },
//   ];

//   return (
//     <div>
//       <Modal show={isOpen} onClose={onClose} size="7xl">
//         <Modal.Header className="flex items-center justify-between">
//           <h3 className="text-xl font-semibold text-blue-600">Update Customer</h3>
//           {/* <p>CID: {queryData.customerID}</p>
//           <p>GID: {queryData.groupID}</p> */}
//         </Modal.Header>
//         <Modal.Body>
//           <SpinnerLoading isLoading={loading || isLoading} />
//           {!loading && (
//             <>
//               {data?.gateway_customer && (
//                 <CustomerUpdate
//                   customer={data?.gateway_customer}
//                   onClose={onClose}
//                   setParentLoading={setIsLoading}
//                   deleteAction={
//                     <CustomerDelete
//                       queryData={{
//                         customerID: data?.gateway_customer.id ?? '',
//                         groupID,
//                       }}
//                       onClose={onClose}
//                       setParentLoading={setIsLoading}
//                     />
//                   }
//                 />
//               )}

//               <div className="mt-8">
//                 <CustomerCardAdd
//                   groupID={groupID}
//                   customerID={customerID}
//                   refetchCustomer={refetch}
//                 />
//                 <div className="grid grid-cols-1 gap-3 md:grid-cols-5">
//                   <DataGridView
//                     className="col-span-5 space-y-4"
//                     columns={columns}
//                     rows={data?.gateway_customer?.paymentCards ?? []}
//                     disablePagination
//                     mode="client"
//                   />
//                 </div>
//               </div>
//             </>
//           )}
//         </Modal.Body>
//       </Modal>
//     </div>
//   );
// Temporarily disabled customer functionality
export const CustomerViewModal = () => {
  return null;
};
