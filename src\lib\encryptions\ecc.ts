import { createCipheriv, createDecipheriv, createHmac, randomBytes } from 'crypto';
import * as ec from 'elliptic';

const curve = new ec.ec('secp256k1');

export function generateECCKeys() {
  const keypair = curve.genKeyPair();
  return {
    privateKey: keypair.getPrivate('hex'),
    publicKey: keypair.getPublic('hex'),
  };
}

function hkdfExtract(ikm: Buffer, salt: Buffer): Buffer {
  return createHmac('sha256', salt).update(ikm).digest();
}

function hkdfExpand(prk: Buffer, info: Buffer, length: number): Buffer {
  let output = Buffer.alloc(0);
  let T = Buffer.alloc(0);
  let i = 0;

  while (output.length < length) {
    i++;
    // @ts-ignore
    T = createHmac('sha256', prk)
      .update(Buffer.concat([T, info, Buffer.from([i])]))
      .digest();
    output = Buffer.concat([output, T]);
  }

  return output.slice(0, length);
}

function pkcs7Pad(message: Buffer): Buffer {
  const paddingLength = 16 - (message.length % 16);
  const padding = Buffer.alloc(paddingLength, paddingLength);
  return Buffer.concat([message, padding]);
}

function pkcs7Unpad(message: Buffer): Buffer {
  const paddingLength = message[message.length - 1];
  return message.slice(0, message.length - paddingLength);
}

export function encryptECC(message: string, publicKeyHex: string): string {
  const ephemeral = curve.genKeyPair();
  const publicKey = curve.keyFromPublic(publicKeyHex, 'hex');
  const sharedSecret = ephemeral.derive(publicKey.getPublic());

  // Generate salt and derive keys using HKDF
  const salt = randomBytes(32);
  const prk = hkdfExtract(Buffer.from(sharedSecret.toString(16), 'hex'), salt);
  const keys = hkdfExpand(prk, Buffer.from('encryption_keys'), 48); // 32 for AES + 16 for IV

  // Split derived key material
  const encryptionKey = keys.slice(0, 32);
  const iv = keys.slice(32, 48);

  // Pad and encrypt the message
  const padded = pkcs7Pad(Buffer.from(message));
  const cipher = createCipheriv('aes-256-gcm', encryptionKey, iv);

  const encrypted = Buffer.concat([cipher.update(padded), cipher.final()]);
  const authTag = cipher.getAuthTag();

  const payload = {
    encrypted: encrypted.toString('hex'),
    ephemeralPublicKey: ephemeral.getPublic('hex'),
    authTag: authTag.toString('hex'),
    salt: salt.toString('hex'),
  };

  return Buffer.from(JSON.stringify(payload)).toString('base64');
}

export function decryptECC(encryptedBase64: string, privateKeyHex: string): string {
  const ciphertext = JSON.parse(Buffer.from(encryptedBase64, 'base64').toString());
  const privateKey = curve.keyFromPrivate(privateKeyHex, 'hex');
  const ephemeralPublicKey = curve.keyFromPublic(ciphertext.ephemeralPublicKey, 'hex');
  const sharedSecret = privateKey.derive(ephemeralPublicKey.getPublic());

  // Derive keys using HKDF
  const salt = Buffer.from(ciphertext.salt, 'hex');
  const prk = hkdfExtract(Buffer.from(sharedSecret.toString(16), 'hex'), salt);
  const keys = hkdfExpand(prk, Buffer.from('encryption_keys'), 48);

  // Split derived key material
  const encryptionKey = keys.slice(0, 32);
  const iv = keys.slice(32, 48);

  // Decrypt and verify
  const decipher = createDecipheriv('aes-256-gcm', encryptionKey, iv);
  decipher.setAuthTag(Buffer.from(ciphertext.authTag, 'hex'));

  const decrypted = Buffer.concat([
    decipher.update(Buffer.from(ciphertext.encrypted, 'hex')),
    decipher.final(),
  ]);

  return pkcs7Unpad(decrypted).toString();
}
