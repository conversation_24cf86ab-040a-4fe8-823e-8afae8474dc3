/* eslint-disable @next/next/no-img-element */
'use client';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LeadsInitialize, LeadsLogin } from '@/graphql/declarations/leads';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { BriefcaseBusiness, MailIcon, UserIcon } from 'lucide-react';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Checkbox } from 'flowbite-react';
import { FormPhoneNumberRaw } from '@/components/globals/form-phone-number';

const Page1 = (props: {
  form: any;
  setForm: any;
  onContinue: () => void;
  error?: string;
  submitting?: boolean;
  preEmail?: boolean;
  agreeTerms: boolean;
  setAgreeTerms: Dispatch<SetStateAction<boolean>>;
}) => {
  // const redirectToLogin = () => {
  //   // Get the current URL's query parameters
  //   const queryParams = new URLSearchParams(window.location.search);
  //
  //   // Construct the new URL for the register page
  //   const registerUrl = new URL('/login', window.location.origin);
  //
  //   // Append each query parameter to the new URL
  //   queryParams.forEach((value, key) => {
  //     registerUrl.searchParams.append(key, value);
  //   });
  //
  //   // Redirect to the new URL
  //   window.location.href = registerUrl.toString();
  // };

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="Logo" />
        </CardTitle>
        <CardDescription className="text-center">
          Create a free account to get started with our demo
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <div className="space-y-2">
            <Label htmlFor="name">First Name</Label>
            <div className="relative">
              <UserIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="name"
                placeholder="John"
                type="text"
                className="pl-10"
                value={props.form.firstName}
                onChange={(e) => props.setForm({ ...props.form, firstName: e.target.value })}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="last-name">Last Name</Label>
            <div className="relative">
              <UserIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="last-name"
                placeholder="Doe"
                type="text"
                className="pl-10"
                value={props.form.lastName}
                onChange={(e) => props.setForm({ ...props.form, lastName: e.target.value })}
              />
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <div className="relative">
            <MailIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              disabled={props.preEmail}
              id="email"
              placeholder="<EMAIL>"
              type="email"
              className="pl-10"
              value={props.form.email}
              onChange={(e) => props.setForm({ ...props.form, email: e.target.value })}
            />
          </div>
        </div>
        <div className="space-y-2">
          <FormPhoneNumberRaw
            id="phone"
            name="phone"
            label="Phone Number"
            country="us"
            preferredCountries={['us', 'ca']}
            value={props.form.phone}
            onChangeCallback={(phone: string) => props.setForm({ ...props.form, phone })}

            // rules={{ required: message.requiredField }}
          />
          {/*
          <Label htmlFor="phone">Phone Number</Label>
          <div className="relative">
            <PhoneIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="phone"
              placeholder="<EMAIL>"
              type="text"
              className="pl-10"
              value={props.form.phone}
              onChange={(e) => props.setForm({ ...props.form, phone: e.target.value })}
            />
          </div>
          */}
        </div>
        <div className="space-y-2">
          <Label htmlFor="company">Company Name</Label>
          <div className="relative">
            <BriefcaseBusiness className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="company"
              placeholder="Company Name"
              type="text"
              className="pl-10"
              value={props.form.company}
              onChange={(e) => props.setForm({ ...props.form, company: e.target.value })}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="title">Company Position</Label>
          <div className="relative">
            <BriefcaseBusiness className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="title"
              placeholder="CEO, Owner, Developer"
              type="text"
              className="pl-10"
              value={props.form.title}
              onChange={(e) => props.setForm({ ...props.form, title: e.target.value })}
            />
          </div>
        </div>
        <div>
          <Checkbox
            id="remember-me"
            checked={props.agreeTerms}
            onChange={(e) => props.setAgreeTerms(e.target.checked)}
          />{' '}
          <Label htmlFor="remember-me">
            I agree to the{' '}
            <a href="/terms-and-services" className="text-blue-500">
              Terms of Service
            </a>{' '}
            of NGnair Payments
          </Label>
        </div>
        <Button
          className="w-full"
          onClick={() => {
            props.onContinue();
          }}
          disabled={!props.agreeTerms || props.submitting}
          variant="primary"
        >
          {props.submitting ? 'Submitting...' : 'Continue'}
        </Button>
        {
          // show error if there is an error
          props.error && (
            <p className="mx-auto -mt-2 text-center text-xs text-red-600">{props.error}</p>
          )
        }
      </CardContent>
    </>
  );
};

const Page2 = (props: {
  form: any;
  setForm: any;
  onContinue: () => void;
  error?: string;
  submitting?: boolean;
  preEmail?: boolean;
}) => {
  useEffect(() => {
    const interval = setTimeout(() => {
      props.onContinue();
    }, 10);

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="Logo" />
        </CardTitle>
        <CardDescription className="text-center">Initializing your Account</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex min-h-[200px] flex-col items-center justify-center rounded-lg bg-gray-100 p-8">
          <div className="relative h-20 w-20">
            <svg
              className="h-full w-full animate-spin text-blue-500"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="h-3 w-3 animate-pulse rounded-full bg-blue-500"></div>
            </div>
          </div>
          <p className="mt-4 text-lg font-semibold text-gray-700">Processing...</p>
          <p className="mt-2 text-sm text-gray-500">
            Please wait for a bit while we're bootstrapping your test account for demo
          </p>
        </div>
      </CardContent>
      <CardFooter></CardFooter>
    </>
  );
};

export default function Component() {
  const [currentPage, setCurrentPage] = useState(1);
  const [form, setForm] = useState({
    email: '',
    firstName: '',
    lastName: '',
    phone: '+1',
    company: '',
    title: '',
  });
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [pendingToResend, setPendingToResend] = useState(0);
  const [preEmail, setPreEmail] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);

  const preloadEmail = async () => {
    // check query params if email is present
    const urlParams = new URLSearchParams(window.location.search);
    const email = urlParams.get('email');

    if (email) {
      setForm({ ...form, email });
      setPreEmail(true);
    }
  };

  useEffect(() => {
    preloadEmail();
  }, []);

  useEffect(() => {
    setError('');
  }, [form, currentPage]);

  useEffect(() => {
    setSubmitting(false);
  }, [currentPage]);

  useEffect(() => {
    if (pendingToResend) {
      const interval = setInterval(() => {
        setPendingToResend((prev) => (prev > 0 ? prev - 1 : 0));
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [pendingToResend]);

  const page1Submit = async () => {
    setSubmitting(true);
    if (!form.email) {
      setSubmitting(false);
      setError('Email is required');
      return;
    }

    // Remove all non-numeric and non-plus characters from phone number
    form.phone = '+' + form.phone.replace(/[^0-9]/g, '');
    // console.log(form.phone);

    // check if phone starts with +1 and is 12 characters long
    // if (!form.phone.match(/^\+1\d{10}$/)) {
    //   setSubmitting(false);
    //   setError('Invalid phone number. Please enter a valid US phone number');
    //   return;
    // }

    if (!agreeTerms) {
      setSubmitting(false);
      setError('Please accept the Terms of Service');
      return;
    }

    const resp = await apolloClient.mutate({
      mutation: LeadsLogin,
      variables: {
        input: {
          email: form.email,
          phoneNumber: form.phone,
          firstName: form.firstName,
          lastName: form.lastName,
          companyName: form.company,
          companyRole: form.title,
        },
      },
    });

    const token = resp.data?.leads_login?.sessionToken;

    if (token) {
      AUTHSTORE.set(token);
      setCurrentPage(2);
    }
  };

  const page2Submit = async () => {
    await apolloClient.mutate({
      mutation: LeadsInitialize,
    });

    window.location.href = '/dashboard';
  };

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        {currentPage === 1 && (
          <Page1
            form={form}
            setForm={setForm}
            onContinue={() => {
              page1Submit();
            }}
            error={error}
            submitting={submitting}
            preEmail={preEmail}
            agreeTerms={agreeTerms}
            setAgreeTerms={setAgreeTerms}
          />
        )}
        {currentPage === 2 && (
          <Page2
            form={form}
            setForm={setForm}
            onContinue={() => {
              page2Submit();
            }}
            error={error}
            submitting={submitting}
            preEmail={preEmail}
          />
        )}
      </Card>
    </div>
  );
}
