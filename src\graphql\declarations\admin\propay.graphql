mutation Propay_query($input: Propay_queryInput!) {
  propay_query(input: $input)
}

mutation Propay_tables {
  propay_tables {
    tables
  }
}

mutation Propay_status {
  propay_status {
    allRunning
    components {
      name
      status
      operational
    }
  }
}

mutation Propay_links($input: Propay_linksInput!) {
  propay_links(input: $input) {
    id
    account_name
    status
    type
    function
    time_created
    time_last_updated
    url
    action {
      id
      type
      time_created
      result_code
      app_id
      app_name
    }
  }
}
