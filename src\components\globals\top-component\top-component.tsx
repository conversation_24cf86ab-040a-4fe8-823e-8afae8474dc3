import { Label, TextInput } from 'flowbite-react';
import React from 'react';
import { HiSearch } from 'react-icons/hi';

type TopComponentProps = {
  value: string;
  setValue: (value: string) => void;
  children: React.ReactNode;
};

export const TopComponent = ({ value, setValue, children }: TopComponentProps) => {
  return (
    <div className="mb-3 hidden items-center gap-1 dark:divide-gray-700 sm:mb-0 sm:flex sm:divide-x sm:divide-gray-100">
      <form onSubmit={(e) => e.preventDefault()} className="hidden w-[300px] lg:block">
        <div className="flex">
          <Label htmlFor="search" className="sr-only">
            Search
          </Label>
          <TextInput
            className="h-10 w-full rounded-br-none rounded-tr-none"
            icon={HiSearch}
            id="search"
            name="search"
            placeholder="Search"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            type="search"
            style={{ height: '40px' }}
          />
        </div>
      </form>
      <div className="mt-3 flex space-x-2 pl-0 sm:mt-0 sm:py-2">
        {/* //NOTE: hide for summit */}
        {/* <Button className="h-[38px] p-0" color="gray">
          <div className="flex items-center gap-x-3">
            <HiFilter className="text-xl" />
            <span>Filter</span>
          </div>
        </Button> */}
      </div>
      <div className="flex items-center space-x-2 sm:space-x-3">{children}</div>
    </div>
  );
};
