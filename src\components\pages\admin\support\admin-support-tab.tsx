import { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { mockSupportTickets, supportCategories } from '@/lib/mock/support-mock';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import moment from 'moment';
import useDebounce from '@/components/hooks/useDebounce';

export const AdminSupportTab = () => {
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchValue, setSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [priorityFilter, setPriorityFilter] = useState('All');
  const [categoryFilter, setCategoryFilter] = useState('All');

  const router = useRouter();
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const filteredTickets = useMemo(() => {
    return mockSupportTickets.filter((ticket) => {
      const matchesSearch =
        !debouncedSearchQuery ||
        ticket.title.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        ticket.description.toLowerCase().includes(debouncedSearchQuery.toLowerCase());

      const matchesStatus = statusFilter === 'All' || ticket.status === statusFilter;
      const matchesPriority = priorityFilter === 'All' || ticket.priority === priorityFilter;
      const matchesCategory = categoryFilter === 'All' || ticket.category === categoryFilter;

      return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
    });
  }, [debouncedSearchQuery, statusFilter, priorityFilter, categoryFilter]);

  const columns: Column[] = [
    {
      key: 'id',
      header: 'Ticket ID',
      sortable: false,
      onClick: (row) => router.push(`/dashboard/admin/support/${row.id}`),
    },
    {
      key: 'title',
      header: 'Title',
      sortable: true,
    },
    {
      key: 'priority',
      header: 'Priority',
      sortable: true,
      renderCell: (row) => (
        <Badge
          variant={
            row.priority === 'HIGH'
              ? 'destructive'
              : row.priority === 'MEDIUM'
                ? 'secondary'
                : 'outline'
          }
        >
          {row.priority}
        </Badge>
      ),
    },
    {
      key: 'category',
      header: 'Category',
      sortable: true,
      valueGetter: (row) =>
        supportCategories.find((c) => c.id === row.category)?.label || row.category,
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      renderCell: (row) => (
        <Badge
          variant={
            row.status === 'OPEN'
              ? 'default'
              : row.status === 'IN_PROGRESS'
                ? 'secondary'
                : row.status === 'RESOLVED'
                  ? 'outline'
                  : 'destructive'
          }
        >
          {row.status}
        </Badge>
      ),
    },
    {
      key: 'assignedTo',
      header: 'Assigned To',
      sortable: true,
      renderCell: (row) => row.assignedTo || '-',
    },
    {
      key: 'createdAt',
      header: 'Created',
      sortable: true,
      renderCell: (row) => moment(row.createdAt).format('DD/MM/YYYY HH:mm'),
    },
    {
      key: 'lastMessageCreatedAt',
      header: 'Last Updated',
      sortable: true,
      renderCell: (row) => moment(row.lastMessageCreatedAt).format('DD/MM/YYYY HH:mm'),
    },
  ];

  return (
    <Card className="p-4">
      <div className="mb-4 flex flex-wrap items-center gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search tickets..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Status</SelectItem>
            <SelectItem value="OPEN">Open</SelectItem>
            <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
            <SelectItem value="RESOLVED">Resolved</SelectItem>
            <SelectItem value="CLOSED">Closed</SelectItem>
          </SelectContent>
        </Select>
        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Priorities</SelectItem>
            <SelectItem value="HIGH">High</SelectItem>
            <SelectItem value="MEDIUM">Medium</SelectItem>
            <SelectItem value="LOW">Low</SelectItem>
          </SelectContent>
        </Select>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">All Categories</SelectItem>
            {supportCategories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <DataGridView
        columns={columns}
        rows={filteredTickets}
        pageSize={pageSize}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        onPageSizeChange={setPageSize}
        totalRecords={filteredTickets.length}
        mode="client"
      />
    </Card>
  );
};
