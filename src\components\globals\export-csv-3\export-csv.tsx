import { Report_download } from '@/graphql/declarations/file';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { Button } from 'flowbite-react';
import { HiDocumentDownload } from 'react-icons/hi';

// Only pass flat data to  be able to export properly
const ExportCSV3 = (args: { reportType: string; groupID: string }) => {
  const exportCSVFile = async () => {
    const d = await apolloClient.mutate({
      mutation: Report_download,
      variables: {
        input: {
          groupID: args.groupID,
          report: args.reportType,
        },
      },
    });
    const csvData = d.data?.reportdownload_download?.data ?? '';
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${args.reportType}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <Button className="p-0" color="gray" onClick={exportCSVFile}>
      <div className="flex items-center gap-x-3">
        <HiDocumentDownload className="text-xl" />
        <span>Export</span>
      </div>
    </Button>
  );
};

export default ExportCSV3;
