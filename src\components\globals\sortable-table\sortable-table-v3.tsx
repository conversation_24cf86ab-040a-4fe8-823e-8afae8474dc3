import { useState, useMemo } from 'react';
import { Table, Checkbox, Button } from 'flowbite-react';
import { cn } from '@/lib/utils';
import { Column } from './data-grid-view';

const columns: Column[] = [
  { key: 'id', header: 'ID', width: '90px', sortable: true },
  { key: 'firstName', header: 'First name', width: '150px', editable: true, sortable: true },
  { key: 'lastName', header: 'Last name', width: '150px', editable: true, sortable: true },
  { key: 'age', header: 'Age', width: '110px', editable: true, type: 'number', sortable: true },
  {
    key: 'fullName',
    header: 'Full name',
    width: '160px',
    sortable: false,
    valueGetter: (row) => `${row.firstName || ''} ${row.lastName || ''}`,
  },
];

const rows = [
  { id: 1, lastName: 'Snow', firstName: 'Jon', age: 14 },
  { id: 2, lastName: 'Lannister', firstName: 'Cersei', age: 31 },
  { id: 3, lastName: 'Lannister', firstName: '<PERSON>', age: 31 },
  { id: 4, lastName: '<PERSON>', firstName: 'Arya', age: 11 },
  { id: 5, lastName: 'Targaryen', firstName: 'Daenerys', age: null },
  { id: 6, lastName: 'Melisandre', firstName: null, age: 150 },
  { id: 7, lastName: 'Clifford', firstName: 'Ferrara', age: 44 },
  { id: 8, lastName: 'Frances', firstName: 'Rossini', age: 36 },
  { id: 9, lastName: 'Roxie', firstName: 'Harvey', age: 65 },
];

const headerSortIcon = () => {
  return (
    <svg
      className="-mt-0.5 ml-1 inline-block h-4 w-4"
      fill="currentColor"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
    >
      <path
        clipRule="evenodd"
        fillRule="evenodd"
        d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z"
      />
    </svg>
  );
};

const DataGrid = () => {
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortConfig, setSortConfig] = useState({ key: 'id', direction: 'asc' });
  const rowsPerPage = 5;

  const totalPages = Math.ceil(rows.length / rowsPerPage);

  const handleCheckboxChange = (id) => {
    setSelectedRows((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id],
    );
  };

  const sortedRows = useMemo(() => {
    let sorted = [...rows];
    if (sortConfig.key && columns.find((col) => col.key === sortConfig.key)?.sortable) {
      sorted.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }
    return sorted;
  }, [sortConfig]);

  const paginatedRows = useMemo(() => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    return sortedRows.slice(startIndex, startIndex + rowsPerPage);
  }, [currentPage, sortedRows]);

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleSort = (key) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  return (
    <div className="w-full overflow-auto">
      <Table hoverable={true} className="min-w-full">
        <Table.Head>
          <Table.HeadCell className="w-12">
            <Checkbox
              checked={selectedRows.length === rows.length}
              onChange={() =>
                setSelectedRows(
                  selectedRows.length === rows.length ? [] : rows.map((row) => row.id),
                )
              }
            />
          </Table.HeadCell>
          {columns.map((col) => (
            <Table.HeadCell
              key={col.key}
              className={`w-[${col.width}] cursor-pointer`}
              onClick={() => col.sortable && handleSort(col.key)}
            >
              {col.header}
              {col.sortable && (
                <span
                  className={cn(
                    'ml-2 inline-block',
                    sortConfig.key === col.key && sortConfig.direction === 'asc'
                      ? 'text-blue-500'
                      : '',
                    sortConfig.key === col.key && sortConfig.direction === 'desc'
                      ? 'text-red-500'
                      : '',
                  )}
                >
                  {/* {sortConfig.key === col.key && sortConfig.direction === 'asc' ? '▲' : '▼'} */}
                  {headerSortIcon()}
                </span>
              )}
            </Table.HeadCell>
          ))}
        </Table.Head>
        <Table.Body className="divide-y">
          {paginatedRows.map((row) => (
            <Table.Row key={row.id} className="bg-white dark:bg-gray-800">
              <Table.Cell className="w-12">
                <Checkbox
                  checked={selectedRows.includes(row.id)}
                  onChange={() => handleCheckboxChange(row.id)}
                />
              </Table.Cell>
              {columns.map((col) => (
                <Table.Cell key={col.key} className={`w-[${col.width}]`}>
                  {col.valueGetter ? col.valueGetter(row) : row[col.key]}
                </Table.Cell>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
      {/* Pagination Controls */}
      <div className="mt-4 flex items-center justify-between">
        <Button disabled={currentPage === 1} onClick={() => handlePageChange(currentPage - 1)}>
          Previous
        </Button>
        <span>
          Page {currentPage} of {totalPages}
        </span>
        <Button
          disabled={currentPage === totalPages}
          onClick={() => handlePageChange(currentPage + 1)}
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export default DataGrid;
