.jeseLeos {
  position: relative;
  line-height: 150%;
  font-weight: 600;
}
.nameexamplecom {
  position: relative;
  line-height: 150%;
}
.navItem {
  width: 224px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 8px 16px;
  box-sizing: border-box;
}
.separator {
  width: 224px;
  position: relative;
  background-color: #f3f4f6;
  height: 1px;
}
.dropdownmenuItem {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 16px;
}
.navItem1 {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 4px 0px;
  color: #374151;
}
.navItem2 {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 4px 0px;
  color: #e02424;
}
.dropdownMenu {
  box-shadow:
    0px 4px 6px -1px rgba(0, 0, 0, 0.1),
    0px 2px 4px -2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  background-color: #fff;
  overflow: hidden;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  font-size: 14px;
  color: #111928;
  font-family: Inter;
}
