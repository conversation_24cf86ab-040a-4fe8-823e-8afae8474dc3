import styles from './index.module.css';
import { Promisable } from '@/types/types';
import { MutableRefObject, useEffect, useRef } from 'react';
import { FormProvider, useForm, useFieldArray, useWatch } from 'react-hook-form';
import { FormFormattedInput, FormInput } from '@/components/globals';
import { message } from '@/components/shared/utils';
import { updateData } from '../updateData';
import FormPhoneNumber from '@/components/globals/form-phone-number/form-phonenumber.index';
import { FormDatepickerv2 } from '@/components/globals/form-date-picker-v2';
import { useSearchParams } from 'next/navigation';
import FormCheckbox from '@/components/globals/form-checkbox/form-checkbox';
import { cn } from '@/lib/utils';
import FormTextDisplay from '@/components/globals/form-text/form-text';
import { Button } from 'flowbite-react';
import { CountryForm, StateForm } from '@/components/shared/components/stateCountryFormSelect';

const OwnershipPage = (args: {
  triggerSubmit?: MutableRefObject<() => Promisable<boolean>>;
  initialData?: any;
}) => {
  const methods = useForm({
    defaultValues: {
      owners: args.initialData ?? [
        {
          firstName: '',
          lastName: '',
          title: '',
          ownershipPercentage: '',
          phoneNumber: '',
          homeAddress: '',
          country: '',
          state: '',
          city: '',
          zipCode: '',
          dateOfBirth: '',
          ssn: '',
          email: '',
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: 'owners',
  });

  const searchParams = useSearchParams();

  useEffect(() => {
    if (args.triggerSubmit) {
      args.triggerSubmit.current = async () => {
        const isValid = await methods.trigger();
        if (isValid) {
          const val = methods.getValues();
          try {
            // for all owners, convert ownershipPercentage to a number
            val.owners.forEach((owner: any) => {
              owner.ownershipPercentage = Number(owner.ownershipPercentage);
            });
            // for all owners, clear null fields
            val.owners.forEach((owner: any) => {
              Object.keys(owner).forEach((key) => {
                if (owner[key] === null) {
                  delete owner[key];
                }
              });
            });
            return await updateData({
              submitType: 'ownership',
              data: val?.owners,
              groupID: searchParams?.get('groupID') ?? undefined,
              form: methods,
            });
          } catch (error) {
            console.log(error);
            return false;
          }
        }
        return false;
      };
    }
  }, [args.triggerSubmit]);

  const owners = useWatch({
    control: methods.control,
    name: 'owners',
  });

  const isControlOwner = owners?.some((owner: any) => owner.isControlOwner);

  let lastControl = useRef({});
  // console.log('owners', owners);

  useEffect(() => {
    // if an owner is a control owner, set ownershipPercentage to 100 and set country to US
    if (owners) {
      for (let index = 0; index < owners.length; index++) {
        const owner = owners[index];
        if (lastControl.current[index] === owner.isControlOwner) {
          continue;
        }
        console.log('updating');
        lastControl.current[index] = owner.isControlOwner;
        if (owner.isControlOwner) {
          // @ts-ignore
          methods.setValue(`owners[${index}].ownershipPercentage`, 100);
          // @ts-ignore
          methods.setValue(`owners[${index}].country`, 'US');
          // @ts-ignore
          let curPhone = methods.getValues(`owners[${index}].phoneNumber`);
          if (curPhone === '' && curPhone.startsWith('+1')) {
            // @ts-ignore
            methods.setValue(`owners[${index}].phoneNumber`, '+1');
          }
        }
      }
    }
  }, [owners]);

  // const updateLocByZipCode = async () => {
  //   if (owners) {
  //     for (let index = 0; index < owners.length; index++) {
  //       const owner = owners[index];
  //       let curZipCode = owner.zipCode;
  //       if (lastZipCode.current[index] === curZipCode) {
  //         continue;
  //       }
  //       // console.log('updating');
  //       lastZipCode.current[index] = curZipCode;
  //       // if zipcode is valid, update city and state (5 to 9 digits)
  //       if (curZipCode.length >= 5 && curZipCode.length <= 9) {
  //         const zipSearch = await apolloClient.query({
  //           query: processorTestDraftZipSearch,
  //           variables: {
  //             input: {
  //               zipcode: curZipCode,
  //             },
  //           },
  //         });
  //         const zipData = zipSearch.data.processor_tst_zip_search?.item;
  //         if (zipData) {
  //           // @ts-ignore
  //           methods.setValue(`owners[${index}].city`, zipData.city);
  //           // @ts-ignore
  //           methods.setValue(`owners[${index}].state`, zipData.state);
  //           // @ts-ignore
  //           methods.setValue(`owners[${index}].country`, zipData.country);
  //         }
  //       }
  //     }
  //   }
  // };

  // useEffect(() => {
  //   updateLocByZipCode();
  // }, [owners]);

  return (
    <FormProvider {...methods}>
      <form className="space-y-4">
        <div className={styles.ownership}>
          <div className={styles.inputWidgetLg}>
            <div className={styles.column}>
              <div className={styles.provideTheFollowingContainer}>
                <span>
                  <p className={styles.provideTheFollowing}>
                    Provide the following information for each individual who owns, directly or
                    indirectly, 25 % or more of the equity interest of your business. If no single
                    owner owns more than 25%, an individual with significant responsibility can be
                    added as principal 1*.
                  </p>
                  <p className={styles.provideTheFollowing}>&nbsp;</p>
                  <p
                    className={styles.provideTheFollowing}
                  >{`*Individual with significant responsibility includes an executive officer or owner with authority to control, manage, and direct the legal entity (e.g. a Chief Executive Officer, Chief Financial Officer, Managing Member, General Partner, President, Vice President, or Treasurer) or any individual with authority to perform such functions. `}</p>
                </span>
              </div>
            </div>
            {fields.slice(0, isControlOwner ? 1 : fields.length).map((field, index) => (
              <div
                key={field.id}
                className={cn(
                  styles.frameParent,
                  'rounded-md border border-black/10 p-4 shadow-xl',
                )}
              >
                {index === 0 && (
                  <FormCheckbox
                    id={`owners[${index}].isControlOwner`}
                    name={`owners[${index}].isControlOwner`}
                    label="Is an individual with significant responsibility"
                  />
                )}
                <div className={styles.labelParent}>
                  {owners?.[index]?.isControlOwner && (
                    <div className={styles.label1}>Control prong</div>
                  )}
                  {!owners?.[index]?.isControlOwner && (
                    <div className={styles.label1}>Owner {index + 1}</div>
                  )}
                  {index !== 0 && (
                    <div className={styles.sm}>
                      <Button color="failure" onClick={() => remove(index)}>
                        <div className={styles.label1}>Remove Owner</div>
                      </Button>
                    </div>
                  )}
                </div>
                <div className={styles.inputFieldParent}>
                  <FormInput
                    id={`owners[${index}].firstName`}
                    name={`owners[${index}].firstName`}
                    label="First Name"
                    rules={{ required: message.requiredField }}
                  />
                  <FormInput
                    id={`owners[${index}].lastName`}
                    name={`owners[${index}].lastName`}
                    label="Last Name"
                    rules={{ required: message.requiredField }}
                  />
                  <FormInput
                    id={`owners[${index}].title`}
                    name={`owners[${index}].title`}
                    label="Title / Position"
                    rules={{ required: message.requiredField }}
                  />
                  {!owners?.[index]?.isControlOwner && (
                    <FormInput
                      id={`owners[${index}].ownershipPercentage`}
                      name={`owners[${index}].ownershipPercentage`}
                      label="Ownership %"
                      rules={{
                        required: message.requiredField,
                        min: {
                          value: 1,
                          message: 'Value must be greater or equal to 1',
                        },
                        valueAsNumber: true,
                      }}
                    />
                  )}{' '}
                </div>
                <div className={styles.inputWidgetLgInner}>
                  <div className={styles.inputFieldGroup}>
                    <FormPhoneNumber
                      id={`owners[${index}].phoneNumber`}
                      name={`owners[${index}].phoneNumber`}
                      label="Phone Number"
                      country={owners?.[index]?.isControlOwner ? 'us' : 'us'}
                      onlyCountries={owners?.[index]?.isControlOwner ? ['us'] : undefined}
                      disableDropdown={owners?.[index]?.isControlOwner ? true : false}
                      countryCodeEditable={owners?.[index]?.isControlOwner ? false : true}
                      preferredCountries={['us', 'ca']}
                      onCountryChange={(country) => {
                        // @ts-ignore
                        methods.setValue(`owners[${index}].phoneNumberCountryCode`, country);
                      }}
                      rules={{ required: message.requiredField }}
                    />

                    <FormInput
                      id={`owners[${index}].homeAddress`}
                      name={`owners[${index}].homeAddress`}
                      label="Home Address"
                      rules={{ required: message.requiredField }}
                    />
                    {owners?.[index]?.isControlOwner && (
                      <FormTextDisplay
                        id={`owners[${index}].country`}
                        name={`owners[${index}].country`}
                        label="Country"
                      />
                    )}
                    {!owners?.[index]?.isControlOwner && (
                      // <FormInput
                      //   id={`owners[${index}].country`}
                      //   name={`owners[${index}].country`}
                      //   label="Country"
                      //   rules={{
                      //     required: message.requiredField,
                      //     minLength: {
                      //       value: 2,
                      //       message: 'Value must be 2 characters',
                      //     },
                      //     maxLength: {
                      //       value: 2,
                      //       message: 'Value must be 2 characters',
                      //     },
                      //   }}
                      // />
                      <CountryForm countryKey={`owners[${index}].country`} message={message} />
                      // <FormSelect
                      //   id={`owners[${index}].country`}
                      //   name={`owners[${index}].country`}
                      //   label="Country"
                      //   rules={{ required: message.requiredField }}
                      //   options={COUNTRIES}
                      // />
                    )}
                  </div>
                </div>
                <div className={styles.inputWidgetLgInner}>
                  <div className={styles.inputFieldGroup}>
                    {/* <FormTextDisplay
                      id={`owners[${index}].state`}
                      name={`owners[${index}].state`}
                      label="State"
                      rules={{
                        required: message.requiredField,
                        minLength: {
                          value: 2,
                          message: 'Value must be 2 characters',
                        },
                        maxLength: {
                          value: 2,
                          message: 'Value must be 2 characters',
                        },
                      }}
                    />
                    <FormTextDisplay
                      id={`owners[${index}].city`}
                      name={`owners[${index}].city`}
                      label="City"
                      rules={{ required: message.requiredField }}
                    /> */}

                    <StateForm
                      stateKey={`owners[${index}].state`}
                      countryKey={`owners[${index}].country`}
                      message={message}
                      methods={methods}
                    />

                    {/* <FormInput
                      id={`owners[${index}].state`}
                      name={`owners[${index}].state`}
                      label="State"
                      rules={{
                        required: message.requiredField,
                        minLength: {
                          value: 2,
                          message: 'Value must be 2 characters',
                        },
                        maxLength: {
                          value: 2,
                          message: 'Value must be 2 characters',
                        },
                      }}
                    /> */}
                    <FormInput
                      id={`owners[${index}].city`}
                      name={`owners[${index}].city`}
                      label="City"
                      rules={{ required: message.requiredField }}
                    />
                    <FormInput
                      id={`owners[${index}].zipCode`}
                      name={`owners[${index}].zipCode`}
                      label="Zip Code"
                      type="number"
                      rules={{
                        required: message.requiredField,
                        minLength: {
                          value: 5,
                          message: 'Value must be 5 characters or above',
                        },
                        maxLength: {
                          value: 9,
                          message: 'Value must not exceed 9 characters',
                        },
                      }}
                    />
                  </div>
                </div>
                <div className={styles.column1}>
                  <FormDatepickerv2
                    id={`owners[${index}].dateOfBirth`}
                    name={`owners[${index}].dateOfBirth`}
                    label="Date of Birth"
                    rules={{ required: message.requiredField }}
                  />
                  <FormFormattedInput
                    id={`owners[${index}].ssn`}
                    name={`owners[${index}].ssn`}
                    label="Social Security Number (SSN)"
                    mask="*********"
                    rules={{ required: message.requiredField }}
                  />
                  <FormInput
                    id={`owners[${index}].email`}
                    name={`owners[${index}].email`}
                    label="Email"
                    rules={{ required: message.requiredField }}
                  />
                </div>
              </div>
            ))}
            <div className={styles.sm1}>
              <Button
                color="success"
                disabled={isControlOwner}
                onClick={(e) => {
                  e.preventDefault();
                  append({
                    city: '',
                    country: '',
                    dateOfBirth: '',
                    email: '',
                    firstName: '',
                    homeAddress: '',
                    lastName: '',
                    ownershipPercentage: '',
                    phoneNumber: '',
                    ssn: '',
                    state: '',
                    title: '',
                    zipCode: '',
                  });
                }}
              >
                Add Owner
              </Button>
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default OwnershipPage;
