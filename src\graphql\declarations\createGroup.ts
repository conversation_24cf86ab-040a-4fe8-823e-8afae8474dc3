import { graphql } from '../generated';

export const createGroup = graphql(`
  mutation CreateGroup($data: GroupCreateInput!) {
    createGroup(data: $data) {
      id
      mainGateway
      mainProcessor
      processorStatus
      name
    }
  }
`);

export const updateGroup = graphql(`
  mutation UpdateGroup($where: GroupWhereUniqueInput!, $data: GroupUpdateInput!) {
    updateGroup(where: $where, data: $data) {
      id
    }
  }
`);
