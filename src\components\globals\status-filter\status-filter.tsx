import { cn } from '@/lib/utils';
import { Radio } from 'flowbite-react';

type StatusFilterProps = {
  /*value of selectetd filter*/
  value?: string;
  /*setter for the filter value*/
  setValue: (value: string) => void;
  /*List of statuses*/
  statusList: string[];
  className?: string;
};

export const StatusFilter = ({ value, setValue, statusList, className }: StatusFilterProps) => {
  return (
    <div className={cn('flex justify-start space-x-4 p-4', className)}>
      <label className="flex items-center space-x-2">
        <span>Show only: </span>
      </label>
      <label className="flex items-center space-x-2">
        <Radio
          id="all"
          name="status"
          value="All"
          checked={value === 'All'}
          onChange={() => setValue('All')}
        />
        <span>All</span>
      </label>

      {statusList.map((status) => (
        <label key={status} className="flex items-center space-x-2">
          <Radio
            id={status}
            name="status"
            value={status}
            checked={value === status}
            onChange={() => setValue(status)}
          />
          <span>{`${status?.charAt(0).toUpperCase()}${status?.slice(1)}`}</span>
        </label>
      ))}
    </div>
  );
};
