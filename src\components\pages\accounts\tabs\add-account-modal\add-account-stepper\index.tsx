import styles from './index.module.css';
import { cn } from '@/lib/utils';

const AddAccountStepper = (args: { currentStep?: number }) => {
  return (
    <div className={cn(styles.stepperNavigation, '')}>
      <div className={args.currentStep === 1 ? styles.navItem : styles.navItem1}>
        <div className={styles.text}>Business Info</div>
      </div>
      <img className={styles.lineIcon} alt="" src="Line.svg" />
      <div className={args.currentStep === 2 ? styles.navItem : styles.navItem1}>
        <div className={styles.text}>Transactions</div>
      </div>
      <img className={styles.lineIcon} alt="" src="Line.svg" />
      <div className={args.currentStep === 3 ? styles.navItem : styles.navItem1}>
        <div className={styles.text}>Ownership</div>
      </div>
      <img className={styles.lineIcon} alt="" src="Line.svg" />
      <div className={args.currentStep === 4 ? styles.navItem : styles.navItem1}>
        <div className={styles.text}>Banks</div>
      </div>
      <img className={styles.lineIcon} alt="" src="Line.svg" />
      <div className={args.currentStep === 5 ? styles.navItem : styles.navItem1}>
        <div className={styles.text}>Documents</div>
      </div>
      <img className={styles.lineIcon} alt="" src="Line.svg" />
      <div className={args.currentStep === 6 ? styles.navItem : styles.navItem1}>
        <div className={styles.text}>Terms</div>
      </div>
      <img className={styles.lineIcon} alt="" src="Line.svg" />
      <div className={args.currentStep === 7 ? styles.navItem : styles.navItem1}>
        <div className={styles.text}>Review</div>
      </div>
    </div>
  );
};

export default AddAccountStepper;
