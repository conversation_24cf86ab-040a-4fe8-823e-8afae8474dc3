'use client';

import TitleCase from '@/lib/title-case';
import { flexRender } from '@tanstack/react-table';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { RightArrow } from '@/assets/svg/navigation';
import { LeftArrow } from '@/assets/svg/navigation';

function Pagination({ table }: any) {
  let pageCount = table.getPageCount();
  let pageIndex = table.getState().pagination.pageIndex;

  const pages = Array.from({ length: pageCount }, (_, i) => i + 1);

  return (
    <div className="flex w-full items-center justify-end space-x-1 py-8 pr-10">
      <button
        onClick={() => table.setPageIndex(pageIndex - 1)}
        disabled={pageIndex === 0}
        aria-label="Previous"
        className={`flex h-8 w-8 cursor-pointer items-center justify-center rounded-full hover:bg-gray-100 ${
          pageIndex === 0 ? 'cursor-not-allowed opacity-50' : ''
        }`}
      >
        <LeftArrow className="h-6 w-6" />
      </button>
      {pages.map((page) => (
        <button
          key={page}
          onClick={() => table.setPageIndex(page - 1)}
          aria-label={`Page ${page}`}
          className={`h-8 w-8 rounded-full text-sm ${
            pageIndex === page - 1
              ? 'bg-primary text-white'
              : 'border-primary border bg-white text-black'
          }`}
        >
          {page}
        </button>
      ))}
      <button
        onClick={() => table.setPageIndex(pageIndex + 1)}
        disabled={pageIndex === pageCount - 1}
        aria-label="Next"
        className={`flex h-8 w-8 cursor-pointer items-center justify-center rounded-full hover:bg-gray-100 ${
          pageIndex === pageCount - 1 ? 'cursor-not-allowed opacity-50' : ''
        }`}
      >
        <RightArrow className="h-6 w-6" />
      </button>
    </div>
  );
}

const DefaultTable = ({
  head,
  body,
  clickRow,
  skeleton = false,
  disablePagination = false,
}: any) => {
  const renderSkeletonRows = () => {
    return Array.from({ length: 10 }, (_, rowIndex) => (
      <TableRow key={rowIndex}>
        {head.map((column: any, colIndex: any) => (
          <TableCell key={colIndex}>
            {column.cell()} {/* Render the skeleton cell */}
          </TableCell>
        ))}
      </TableRow>
    ));
  };

  return (
    <>
      <div className="w-full overflow-x-auto pb-10">
        <div>
          <Table className="min-w-[75vw]">
            <TableHeader>
              {skeleton ? (
                <TableRow>
                  {head.map((column: any, index: any) => (
                    <TableHead key={index} className="text-xs font-bold text-[#495057] 2xl:text-sm">
                      {TitleCase(column.title)}
                    </TableHead>
                  ))}
                </TableRow>
              ) : (
                body.getHeaderGroups().map((headerGroup: any) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header: any) => (
                      <TableHead
                        className="text-xs font-bold text-[#495057] 2xl:text-sm"
                        key={header.id}
                        colSpan={header.colSpan}
                      >
                        {TitleCase(flexRender(header.column.columnDef.title, header.getContext()))}
                      </TableHead>
                    ))}
                  </TableRow>
                ))
              )}
            </TableHeader>

            <TableBody>
              {skeleton ? (
                renderSkeletonRows()
              ) : body.getRowModel().rows?.length ||
                body.getRowModel().rows?.length === undefined ? (
                body.getRowModel().rows.map((row: any) => (
                  <TableRow
                    className="text-xs 2xl:text-sm"
                    key={row.id}
                    onClick={() => clickRow(row.original)}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell: any) => (
                      <TableCell className="text-zinc-800" key={cell.id}>
                        {TitleCase(flexRender(cell.column.columnDef.cell, cell.getContext()))}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={body.getAllColumns().length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
            {/* <TableBody>
          {body.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id} className="text-[#495057]">
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody> */}
          </Table>
        </div>
      </div>
      {!disablePagination && !skeleton && body.getRowModel().rows.length > 0 && (
        <Pagination table={body} />
      )}
    </>
  );
};

export default DefaultTable;
