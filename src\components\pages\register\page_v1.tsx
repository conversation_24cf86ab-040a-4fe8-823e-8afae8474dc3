'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Login, Register } from '@/graphql/declarations/auth';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormMessage, Form } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';
// import Spinner from '@/components/ui/spinner';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { AUTHSTORE } from '@/lib/auth-storage';
import { useRouter } from 'next/navigation';
import { generateOtp, verifyOtp } from '@/graphql/declarations/otpValues';
import { toast } from 'react-toastify';
import { Otp_GenerateInputChannel } from '@/graphql/generated/graphql';
import { Spinner } from '@/components/ui/spinner';

enum State {
  SignUp,
  Otp,
}

interface OTPValues {
  values?: { value?: string }[];
}

const FormSchema = z
  .object({
    email: z.string().email(),
    password: z.string().min(8, 'Password must be at least 8 characters long'),
    first_name: z.string().min(3),
    last_name: z.string().min(3),
    phone_number: z.string().min(10, 'Phone number must be atleast 10 digits'),
  })
  .strict();

const RegisterPage = () => {
  const { formState, ...form } = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: '',
      password: '',
      first_name: '',
      last_name: '',
      phone_number: '',
    },
  });

  const { watch, setValue, handleSubmit, register } = useForm({
    defaultValues: {
      values: [
        {
          value: '',
        },
        {
          value: '',
        },
        {
          value: '',
        },
        {
          value: '',
        },
        {
          value: '',
        },
        {
          value: '',
        },
      ],
      username: '',
    },
  });

  const { values } = watch();
  const { first_name, email, password, last_name, phone_number } = form.watch();

  const router = useRouter();

  const [state, setState] = useState<State>(State.SignUp);
  const [isSigningUp, setIsSigningUp] = useState(false);
  const [countdown, setCountdown] = useState(6);
  const [isSubmitting, setIsSubmitting] = useState<Boolean>(false);
  const [isInvalidOtp, setIsInvalidOtp] = useState<Boolean>(false);
  const [isVerifying, setIsVerifying] = useState<Boolean>(false);
  const [sid, setSid] = useState('');

  const inputRefs: React.RefObject<HTMLInputElement>[] = Array.from({ length: 6 }, () =>
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useRef<HTMLInputElement>(null),
  );

  const counter = () => {
    let counts = 60;
    const intervalCounter = setInterval(() => {
      if (counts > 0) {
        setCountdown((value) => value - 1);
        counts -= 1;
      } else {
        clearInterval(intervalCounter);
      }
    }, 1000);

    return intervalCounter;
  };

  useEffect(() => {
    const counterInterval = counter();
    return () => clearInterval(counterInterval);
  }, []);

  const onChangeValues = ({ target }: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = target.value;
    const val = value[value.length - 1];
    const nextRef: any = inputRefs?.[index + 1];

    if (value.length > 5) {
      const arrValue = value.split('');

      arrValue.forEach((val, i) => setValue(`values.${i}.value`, val ?? ''));
      inputRefs?.[5]?.current?.focus();
    } else {
      setValue(`values.${index}.value`, val ?? '');

      val && nextRef?.current?.focus();
    }
  };

  // make reusable later
  const registerData = async (args?: { email: string; password: string }) => {
    const resp = await apolloClient.mutate({
      mutation: Login,
      variables: {
        email: args?.email as string,
        password: args?.password as string,
      },
    });

    if (resp.data?.authclient_login?.__typename === 'ClientItemAuthenticationWithPasswordSuccess') {
      const token = resp.data.authclient_login.sessionToken;
      AUTHSTORE.set(token);

      apolloClient.resetStore();
    }
  };

  const handleRegister = async () => {
    // send otp
    setIsSigningUp(true);
    const response = await apolloClient.mutate({
      mutation: generateOtp,
      variables: {
        input: {
          channel: Otp_GenerateInputChannel.Sms,
          contact: phone_number.includes('+') ? `${phone_number}` : `+${phone_number}`,
        },
      },
    });

    setSid(response?.data?.otp_generate?.sid as string);
    setState(State.Otp);
  };

  const submitVerification = async () => {
    try {
      const value = values.map((item) => item.value).join('');
      const response = await apolloClient.mutate({
        mutation: verifyOtp,
        variables: {
          input: {
            code: value,
            sid,
          },
        },
      });

      if (response.data?.otp_verify?.status) {
        const resp = await apolloClient.mutate({
          mutation: Register,
          variables: {
            email,
            password,
            otpSid: sid,
            phoneNumber: `+${phone_number}`,
          },
        });

        if (!resp.data?.authclient_register) {
          toast.error('An error occurred while registering');
          return;
        }

        const auth = await apolloClient.mutate({
          mutation: Login,
          variables: {
            email,
            password,
          },
        });

        if (
          !(
            auth.data?.authclient_login?.__typename ===
            'ClientItemAuthenticationWithPasswordSuccess'
          )
        ) {
          toast.error('An error occurred while logging in');
          return;
        }

        AUTHSTORE.set(auth.data?.authclient_login?.sessionToken as string);

        router.push('/dashboard');
      } else {
        setIsInvalidOtp(true);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const resendCode = () => {};

  return (
    <div className="flex min-h-[100vh] items-center justify-center">
      {state === State.SignUp ? (
        <Form formState={formState} {...form}>
          <form onSubmit={form.handleSubmit(handleRegister)}>
            <div
              className="min-w-4/6 m-auto flex min-w-[520px] flex-col items-start justify-start gap-6 rounded-2xl p-8 shadow 2xl:w-9/12 2xl:gap-8 2xl:p-10"
              style={{ boxShadow: '0px 8px 16px 0px rgba(20, 20, 20, 0.05)' }}
            >
              <div className="flex h-5 flex-col items-start justify-start gap-4">
                <div className="self-stretch text-3xl font-bold text-[#1C1C1C]">
                  <span className="text-primary"> Create</span> an Account.
                </div>
              </div>
              <div className="mt-6 flex w-full flex-col items-start justify-start gap-2 self-stretch">
                <FormField
                  name="email"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <p className="font-inter text-base font-medium !text-[#495057] text-background">
                        Email address
                      </p>
                      <FormControl className="mt-2 !w-full">
                        <Input
                          placeholder="Enter Address"
                          className={`inline-flex w-full items-start justify-start gap-2.5 self-stretch rounded-md border border-gray-300 px-3 py-2 text-base font-normal text-[#1C1C1C] ${
                            formState.errors.email &&
                            'border-[#F46A6A] text-[#F46A6A] placeholder:text-[#F46A6A]'
                          } `}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="font-inter text-sm font-normal text-[#F46A6A]" />
                    </FormItem>
                  )}
                />
                <FormField
                  name="phone_number"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <p className="font-inter text-base font-medium !text-[#495057] text-background">
                        Phone Number
                      </p>
                      <FormControl className="mt-2 !w-full">
                        <Input
                          type="number"
                          placeholder="Enter Phone Number"
                          className={`inline-flex w-full items-start justify-start gap-2.5 self-stretch rounded-md border border-gray-300 px-3 py-2 text-base font-normal text-[#1C1C1C] ${
                            formState.errors.phone_number &&
                            'border-[#F46A6A] text-[#F46A6A] placeholder:text-[#F46A6A]'
                          } `}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="font-inter text-sm font-normal text-[#F46A6A]" />
                    </FormItem>
                  )}
                />
                <FormField
                  name="password"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <p className="font-inter text-base font-medium !text-[#495057] text-background">
                        Password
                      </p>
                      <FormControl className="mt-2 !w-full">
                        <Input
                          type="password"
                          placeholder="Enter Password"
                          className={`inline-flex w-full items-start justify-start gap-2.5 self-stretch rounded-md border border-gray-300 px-3 py-2 text-base font-normal text-[#1C1C1C] ${
                            formState.errors.password &&
                            'border-[#F46A6A] text-[#F46A6A] placeholder:text-[#F46A6A]'
                          } `}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="font-inter text-sm font-normal text-[#F46A6A]" />
                    </FormItem>
                  )}
                />
                <FormField
                  name="first_name"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <p className="font-inter text-base font-medium !text-[#495057] text-background">
                        First Name
                      </p>
                      <FormControl className="mt-2 !w-full">
                        <Input
                          placeholder="Enter First Name"
                          className={`inline-flex w-full items-start justify-start gap-2.5 self-stretch rounded-md border border-gray-300 px-3 py-2 text-base font-normal text-[#1C1C1C] ${
                            formState.errors.first_name &&
                            'border-[#F46A6A] text-[#F46A6A] placeholder:text-[#F46A6A]'
                          } `}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="font-inter text-sm font-normal text-[#F46A6A]" />
                    </FormItem>
                  )}
                />
                <FormField
                  name="last_name"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <p className="font-inter text-base font-medium !text-[#495057] text-background">
                        Last Name
                      </p>
                      <FormControl className="mt-2 !w-full">
                        <Input
                          placeholder="Enter Last Name"
                          className={`inline-flex w-full items-start justify-start gap-2.5 self-stretch rounded-md border border-gray-300 px-3 py-2 text-base font-normal text-[#1C1C1C] ${
                            formState.errors.last_name &&
                            'border-[#F46A6A] text-[#F46A6A] placeholder:text-[#F46A6A]'
                          } `}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="font-inter text-sm font-normal text-[#F46A6A]" />
                    </FormItem>
                  )}
                />
              </div>
              <div className="text-[#74788D]">
                Already have an account?
                <Link href="/login" className="text-primary">
                  {' '}
                  Sign In.
                </Link>
              </div>

              <Button
                variant="primary"
                type="submit"
                className={cn(
                  'leading-0 flex !h-auto w-full items-center justify-center rounded-lg px-12 py-3 text-center text-lg font-bold text-white',
                  isSigningUp && 'opacity-70',
                )}
              >
                Sign up {isSigningUp && <Spinner />}
              </Button>
            </div>
          </form>{' '}
        </Form>
      ) : (
        <div className="flex w-full max-w-[770px] flex-col">
          <div
            style={{ boxShadow: '0px 8px 16px 0px rgba(20, 20, 20, 0.15)' }}
            className="mb-10 mt-3 box-border flex h-fit flex-col overflow-hidden rounded-3xl border border-[#C6C6C6] bg-[#F9F9F9] px-10 py-14 text-white"
          >
            <div className="flex space-x-4">
              <h2 className="text-primary text-3xl font-bold">OTP Verification</h2>
            </div>
            <div className="font-inter py-4 font-normal tracking-tighter text-[#515151]">
              <p>We have sent a 6-digit code to your email address.</p>
            </div>
            <form onSubmit={handleSubmit(submitVerification)}>
              <div className="my-7 flex items-center justify-between">
                {
                  inputRefs.map((ref, i) => {
                    return (
                      <Input
                        key={i}
                        ref={ref}
                        value={values?.[i].value}
                        onChange={(e) => onChangeValues(e, i)}
                        className={cn(
                          'text-primary h-[87px] w-[100px] rounded-md border-[#C6C6C6] bg-[#FAFAFA] text-center text-3xl text-black',
                          isInvalidOtp && 'border-[#ED0039] bg-[#FBCCD7] text-[#ED0039]',
                        )}
                        onKeyDown={(e) => {
                          if (e.key === 'Backspace' && !values?.[i].value) {
                            const prevRef = inputRefs?.[i - 1];
                            prevRef?.current?.focus();
                          }
                        }}
                      />
                    );
                  }) as React.ReactNode
                }
              </div>

              <Button
                className={cn('mt-5 w-full py-[27px] text-lg font-bold text-background')}
                type="submit"
                variant="primary"
                // disabled={
                //   (isSubmitting as boolean) ||
                //   !values?.every((obj) => obj.value)
                // }
                disabled={isVerifying as boolean}
              >
                {isVerifying ? 'Verifying' : 'Verify'}
              </Button>
            </form>

            <p className="font-inter mt-5 text-center text-[13px] font-bold text-[#777]">
              Did not receive code?{' '}
              {countdown !== 0 ? (
                `Resend in ${countdown}s`
              ) : (
                <span onClick={resendCode} className="text-primary cursor-pointer underline">
                  Resend{' '}
                </span>
              )}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default RegisterPage;
