import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  StatusFilter,
  useDataGrid<PERSON>iew,
  PageHeader,
  TopComponent,
} from '@/components/globals';
import { Button } from 'flowbite-react';

import { useEffect, useMemo, useState } from 'react';
import moment from 'moment';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import { FaPlus } from 'react-icons/fa6';
import AddEquipmentModal from '../components/add-equipment-modal';
import { EquipmentDetailModal } from '../components';
import { Gateway_TransactionsDocument } from '@/graphql/generated/graphql';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export const EquipmentsTab = () => {
  const [statusFilter, setStatusFilter] = useState('All');
  const [searchValue, setSearchValue] = useState('');
  const [addEquipmentModalIsOpen, setAddEquipmentModalIsOpen] = useState(false);
  const [viewEquipmentDetailModal, setViewEquipmentDetailModal] = useState(false);
  const [equipmentData, setEquipmentData] = useState({
    terminalName: 'Terminal 1',
    terminalStatus: 'Enabled',
    dateAdded: '02/22/2024',
    terminalSerial: 'T12345',
    terminalType: 'Ingecio i225',
    nameOnCard: 'Bonnie Green',
  });

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({
    onlyActive: true,
  });

  const {
    data: transactionData,
    loading: groupLoading,
    refetch: refetchGroupData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    maxVariables,
  } = useDataGridView({
    query: Gateway_TransactionsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
    },
  });

  const handleSubmit = (formData) => {
    // Here you would typically send the data to your backend
    setAddEquipmentModalIsOpen(false);
  };

  useEffect(() => {
    if (locationFilter?.id) {
      refetchGroupData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locationFilter]);

  // Status needs to be predictable for us assign the right variant here;
  const getStatus = (status: string): [Variant, string] => {
    let variant: Variant = 'neutral';
    let label = 'n/a';

    if (status === 'voided') {
      variant = 'danger';
    }

    if (status === 'settled') {
      variant = 'success';
    }

    if (status === 'authorized') {
      variant = 'neutral';
    }

    return [variant, status];
  };

  interface TransactionRow extends Record<string, any> {
    transactionID: string;
    location: string;
    date: string;
    method: string;
    customer: string;
    brand: string;
    status: string;
    last4: string;
    amount: number;
  }

  const transactionRows = useMemo(() => {
    if (!transactionData?.gateway_transactions?.data) return [];

    const filteredData = transactionData.gateway_transactions.data.filter(
      (row): row is NonNullable<typeof row> =>
        row !== null && (statusFilter === 'All' || row.status === statusFilter),
    );

    return filteredData as TransactionRow[];
  }, [transactionData, statusFilter]);

  const statausFilter = useMemo(() => {
    if (!transactionData) return [];
    const statuses = transactionData?.gateway_transactions?.data?.map((row) => row?.status) ?? [];
    const uniqueStatus: string[] = [];

    if (!statuses.length) return uniqueStatus;
    statuses.forEach((status) => {
      if (!status) return;
      if (!uniqueStatus.includes(status)) {
        uniqueStatus.push(status);
      }
    });
    return uniqueStatus;
  }, [transactionData]);

  const handleUpdate = (updatedData) => {
    console.log('Equipment updated:', updatedData);
    setEquipmentData(updatedData);
    // Here you would typically send the updated data to your backend
    setViewEquipmentDetailModal(false);
  };

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_TransactionsDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_transactions?.data ?? [];
  };

  const columns: Column<TransactionRow>[] = [
    {
      key: 'transactionID',
      header: 'Transaction',
      width: '90px',
      sortable: true,
      onClick: () => setViewEquipmentDetailModal(true),
    },
    {
      key: 'location',
      header: 'Location',
      width: '90px',
      sortable: true,
    },
    {
      key: 'customer',
      header: 'Customer',
      width: '90px',
      sortable: true,
    },
    {
      key: 'date',
      header: 'Date',
      width: '90px',
      sortable: true,
      valueGetter: (row) => moment(row.date).format('MM/DD/YYYY'),
    },
    {
      key: 'method',
      header: 'Method',
      width: '90px',
      sortable: true,
    },
    {
      key: 'brand',
      header: 'Brand',
      width: '90px',
      sortable: true,
    },
    {
      key: 'last4',
      header: 'Last 4',
      width: '90px',
      sortable: true,
    },
    {
      key: 'amount',
      header: 'Amount',
      width: '90px',
      sortable: true,
    },
    {
      key: 'status',
      header: 'Status',
      width: '160px',
      renderCell: (row) => {
        const [status, label] = getStatus(row.status);
        return <StatusChip variant={status} label={label} />;
      },
    },
  ];

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Equipments" />
        <Button
          className="mt-[20px] h-[38px] p-0"
          color="blue"
          onClick={() => setAddEquipmentModalIsOpen(true)}
        >
          <div className="flex items-center gap-x-3">
            <FaPlus className="text-xl" />
            <span>Add Equipment</span>
          </div>
        </Button>
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
          <TopComponent value={searchValue} setValue={setSearchValue}>
            <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="equipments" />
          </TopComponent>
        </div>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={transactionRows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={groupLoading || loadingGroupList}
          actionComponent={
            <StatusFilter
              value={statusFilter}
              setValue={setStatusFilter}
              statusList={statausFilter}
            />
          }
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={transactionData?.gateway_transactions?.page?.total ?? 0}
        />
      </div>
      <AddEquipmentModal
        isOpen={addEquipmentModalIsOpen}
        onClose={() => setAddEquipmentModalIsOpen(false)}
        onSubmit={handleSubmit}
      />
      <EquipmentDetailModal
        isOpen={viewEquipmentDetailModal}
        onClose={() => setViewEquipmentDetailModal(false)}
        onUpdate={handleUpdate}
        equipmentData={equipmentData}
      />
    </>
  );
};
