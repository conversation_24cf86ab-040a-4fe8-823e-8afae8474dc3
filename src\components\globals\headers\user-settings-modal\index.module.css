.b {
  position: relative;
  line-height: 20px;
}
.infoIcon {
  width: 12px;
  position: relative;
  height: 12px;
  overflow: hidden;
  flex-shrink: 0;
}
.headerSm {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  font-size: 20px;
}
.avatarIcon {
  width: 72px;
  position: relative;
  border-radius: 100px;
  height: 72px;
  object-fit: cover;
}
.uploadIcon {
  width: 14px;
  position: relative;
  height: 14px;
  overflow: hidden;
  flex-shrink: 0;
}
.text {
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.button {
  align-self: stretch;
  border-radius: 8px;
  background-color: #1c64f2;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  gap: 8px;
}
.button1 {
  align-self: stretch;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  color: #111928;
}
.buttons {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.avatarButtons {
  width: 564px;
  display: none;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  color: #fff;
}
.label {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.inputText {
  flex: 1;
  position: relative;
  line-height: 125%;
  width: 100%;
  border: none;
  outline: none;
}
.inputText:focus {
  border: none;
  outline: none;
  box-shadow: none;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
  width: 100%;
}
.inputField {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
  width: 100%;
}
.inputFieldParent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
@media screen and (max-width: 768px) {
  .inputFieldParent {
    flex-direction: column;
  }
}
.inputField5 {
  flex: 1;
  height: 71px;
}
.button2 {
  border-radius: 8px;
  background-color: #1a56db;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
}
.buttons1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: flex-start;
  color: #fff;
}
.inputs {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.inputWidgetMd {
  align-self: stretch;
  box-shadow:
    0px 1px 3px rgba(0, 0, 0, 0.1),
    0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 24px;
  gap: 24px;
}
.b1 {
  width: 100%;
  position: relative;
  line-height: 18px;
  display: inline-block;
}
.thisWeekVisitors {
  position: relative;
  font-size: 16px;
  line-height: 150%;
  color: #6b7280;
}
.heading {
  width: 100%;
  height: 65px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 4px;
  font-size: 18px;
}
.link {
  position: relative;
  line-height: 150%;
}
.toggleChild {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0%;
  right: 0%;
  bottom: 0%;
  left: 0%;
  border-radius: 40px;
  background-color: #e5e7eb;
}
.toggleItem {
  position: absolute;
  top: 2px;
  left: 2.5px;
  border-radius: 40px;
  background-color: #fff;
  width: 16px;
  height: 16px;
}
.toggle1 {
  width: 40px;
  position: relative;
  height: 20px;
}
.toggle {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.header {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.description {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  text-align: left;
}
.card {
  align-self: stretch;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 16px;
  gap: 8px;
}
.brandLogosgoogleIcon {
  width: 56.5px;
  position: relative;
  height: 24px;
  overflow: hidden;
  flex-shrink: 0;
}
.description1 {
  align-self: stretch;
  position: relative;
  line-height: 150%;
}
.card1 {
  width: 100%;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  box-sizing: border-box;
  display: none;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 16px;
  gap: 8px;
  text-align: left;
}
.row {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 12px;
}
.integrations {
  width: 100%;
  border-radius: 8px;
  height: fit-content;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: center;
  color: #6b7280;
}
.sm {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 20px 0px 0px;
  box-sizing: border-box;
  color: #fff;
}
.formInput {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.leftColumn {
  align-self: stretch;
  height: 245px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.ensureThatThese {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  color: #6b7280;
}
.headingDescription {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 4px;
}
.requirement1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
}
.requirement3 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 6px;
}
.significantlyDifferentFrom {
  flex: 1;
  position: relative;
  line-height: 150%;
}
.requirements {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}
.passwordRequirements {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  height: 190px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 16px;
  box-sizing: border-box;
  gap: 16px;
}
.content5 {
}
.sm1 {
  align-self: stretch;
  border-top: 1px solid #e5e7eb;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 20px 0px 0px;
  color: #fff;
}
.inputWidgetMdParent {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0px 10px;
  box-sizing: border-box;
  gap: 10px;
}
.updateUserSettingsRightDra {
  background-color: #fff;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 10px 0px;
  box-sizing: border-box;
  text-align: left;
  font-size: 14px;
  color: #111928;
  font-family: Inter;
}
