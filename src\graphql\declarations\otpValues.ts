import { graphql } from '../generated';

export const generateOtp = graphql(`
  mutation Otp_generate($input: Otp_generateInput!) {
    otp_generate(input: $input) {
      sid
      to
    }
  }
`);

export const generateAuthenticatedOtp = graphql(`
  mutation Otp_auth_generate($input: Otp_auth_generateInput!) {
    otp_auth_generate(input: $input) {
      sid
      to
    }
  }
`);

export const verifyOtp = graphql(`
  mutation Otp_verify($input: Otp_verifyInput!) {
    otp_verify(input: $input) {
      sid
      status
    }
  }
`);

export const generateMFA = graphql(`
  mutation generateMFA($input: Authclient_mfa_generateInput!) {
    authclient_mfa_generate(input: $input) {
      sid
      to
    }
  }
`);

export const verifyMFA = graphql(`
  mutation verifyMFA($input: Authclient_mfa_verifyInput!) {
    authclient_mfa_verify(input: $input) {
      sid
      status
    }
  }
`);
