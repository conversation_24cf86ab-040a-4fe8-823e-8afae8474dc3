import { Modal } from 'flowbite-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { HiX } from 'react-icons/hi';
import { Building2, Mail, Phone, Calendar } from 'lucide-react';
import { InfoItem } from './InfoItem';

interface BaseAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  accountData: any;
  statusIcon: any;
  statusColor: string;
  children: React.ReactNode;
}

export const BaseAccountModal = ({
  isOpen,
  onClose,
  title,
  accountData,
  statusIcon: StatusIcon,
  statusColor,
  children,
}: BaseAccountModalProps) => (
  <Modal show={isOpen} onClose={onClose} size="2xl">
    <div className="flex items-center justify-between p-4">
      <div className="flex items-center gap-3">
        <StatusIcon className={`h-6 w-6 ${statusColor}`} />
        <h2 className="text-2xl font-bold">{title}</h2>
      </div>
      <Button variant="ghost" size="icon" onClick={onClose}>
        <HiX className="h-5 w-5" />
      </Button>
    </div>
    <Modal.Body>
      <div className="space-y-6">
        {/* Business Details */}
        <Card className="p-4">
          <h3 className="mb-4 text-lg font-semibold">Business Details</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <InfoItem icon={Building2} label="Business Name" value={accountData?.dbaName} />
            <InfoItem icon={Mail} label="Email" value={accountData?.email} />
            <InfoItem icon={Phone} label="Phone" value={accountData?.phone} />
            <InfoItem icon={Calendar} label="Date Added" value={accountData?.dateAdded} />
          </div>
        </Card>
        {children}
      </div>
    </Modal.Body>
  </Modal>
);
