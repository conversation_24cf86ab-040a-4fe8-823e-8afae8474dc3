.text {
  position: relative;
  line-height: 125%;
  font-weight: 500;
}
.navItem {
  align-self: stretch;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.lineIcon {
  flex: 1;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  height: 0px;
  object-fit: contain;
}
.navItem1 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.navItem4 {
  align-self: stretch;
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #1a56db;
}
.stepperNavigation {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0px 20px;
  gap: 16px;
  color: #6b7280;
}
.label {
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.div {
  flex: 1;
  position: relative;
  line-height: 125%;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input {
  flex: 1;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.inputField {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}
.label2 {
  position: relative;
  line-height: 150%;
  font-weight: 500;
  color: #6b7280;
}
.inputField1 {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}
.inputFieldParent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.inputWidgetLgInner {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.label3 {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.input1 {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.inputField2 {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.steveAdams {
  align-self: stretch;
  flex: 1;
  position: relative;
  line-height: 125%;
  display: flex;
  align-items: center;
}
.content3 {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input3 {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 12px;
  color: #6b7280;
}
.inputFieldGroup {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.label6 {
  width: 440px;
  position: relative;
  line-height: 150%;
  font-weight: 500;
  display: none;
}
.input4 {
  width: 440px;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  box-sizing: border-box;
  height: 42px;
  display: none;
}
.inputField5 {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 8px;
}
.inputField6 {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.frameParent {
  width: 896px;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.inputWidgetLg {
  align-self: stretch;
  box-shadow:
    0px 1px 3px rgba(0, 0, 0, 0.1),
    0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  height: 343px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 20px;
  box-sizing: border-box;
  gap: 16px;
  font-size: 14px;
}
.button {
  width: 85px;
  border-radius: 8px;
  background-color: #e5e7eb;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  box-sizing: border-box;
}
.button1 {
  border-radius: 8px;
  background-color: #1a56db;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  color: #fff;
}
.sm {
  align-self: stretch;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
}
.bank {
  width: 100%;
  position: relative;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  gap: 32px;
  text-align: left;
  font-size: 20px;
  color: #111928;
  font-family: Inter;
}
