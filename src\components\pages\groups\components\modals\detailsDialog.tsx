'use client';

import * as DialogPrimitive from '@radix-ui/react-dialog';
import { X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog';

const DetailsDialog = ({
  isDialogOpen,
  setIsDialogOpen,
  selectedRowData,
  togglePlay,
  setActionData,
  setPauseOpen,
  handleOpenForm,
}: any) => {
  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogOverlay className="opacity-0">
        <DialogContent className="p-10 md:min-w-[70vw] xl:min-w-[60vw]">
          <div className="flex w-full items-center justify-between gap-4 pb-2 text-center">
            <DialogTitle className="text-3xl font-bold text-[#495057]">Group Details</DialogTitle>
            <DialogPrimitive.Close
              onClick={(e) => {
                e.stopPropagation();
              }}
              className="h-6 w-6 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
            >
              <X className="h-6 w-6" />
              <span className="sr-only">Close</span>
            </DialogPrimitive.Close>
          </div>

          <DialogDescription>
            <div></div>
          </DialogDescription>
          <div className="flex w-full justify-center gap-8">
            <Button
              variant="ghost"
              className="border-primary text-primary w-full border"
              onClick={() => {
                handleOpenForm();
                setIsDialogOpen(false);
              }}
            >
              Edit
            </Button>
          </div>
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  );
};

export default DetailsDialog;
