import { AutoCompleteInput, AutoCompleteOption } from '@/components/globals';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { Me } from '@/graphql/declarations/me';
import { GroupWhereInput } from '@/graphql/generated/graphql';
import { QueryHookOptions, useQuery } from '@apollo/client';
import { useEffect, useMemo, useState } from 'react';
import { create } from 'zustand';

// Define the types for TData and TVariables based on your GraphQL schema
type TData = {
  groups: {
    id: string;
    name: string;
  }[];
};

type TVariables = {
  where?: GroupWhereInput;
};

type LocationSelectorProps = {
  options?: QueryHookOptions<TData, TVariables>;
  onlyActive?: boolean;
  readonly?: boolean;
};

const useLocationFilter = create<{
  location: AutoCompleteOption | null;
  setLocation: (location: AutoCompleteOption) => void;
}>((set) => ({
  location: null as AutoCompleteOption | null,
  setLocation: (location: AutoCompleteOption) => set({ location }),
}));

export const useLocationSelector = ({ options, readonly, onlyActive }: LocationSelectorProps) => {
  const defaultOption: QueryHookOptions<TData, TVariables> = {
    variables: {
      where: onlyActive
        ? {
            AND: [
              {
                processorStatus: {
                  equals: 'active',
                },
              },
            ],
          }
        : {},
    },
    fetchPolicy: 'cache-and-network',
  };

  const queryOption = options ?? defaultOption;

  const {
    data: groupList,
    loading,
    error,
    refetch,
  } = useQuery<TData, TVariables>(GET_GROUPS_LIST, queryOption);

  // const [locationFilter, setLocationFilter] = useState<AutoCompleteOption | null>(null);
  const [locationFilter, setLocationFilter] = useLocationFilter((state) => [
    state.location,
    state.setLocation,
  ]);
  const [isInIframe, setIsInIframe] = useState(false);

  const { data: myData } = useQuery(Me);

  // Load saved location from localStorage when component mounts
  useEffect(() => {
    if (myData) {
      const savedLocation = localStorage.getItem(`location_${myData.authenticatedItem?.id}`);
      if (savedLocation) {
        setLocationFilter(JSON.parse(savedLocation));
      } else if (!locationFilter && groupList?.groups?.length) {
        // preselect a group if there is no saved location and there are groups
        setLocationFilter({
          label: groupList.groups[0].name ?? '',
          id: groupList.groups[0].id ?? '',
          obj: groupList.groups[0],
        });
      }

      if (typeof window !== 'undefined') {
        setIsInIframe(window.self !== window.top);
      }
    }
  }, [myData, groupList]);

  // Handle changes and update localStorage
  const handleChange = (value: AutoCompleteOption) => {
    setLocationFilter(value);
    localStorage.setItem(`location_${myData?.authenticatedItem?.id}`, JSON.stringify(value)); // Persist to localStorage
  };

  // Memoized options for the AutoCompleteInput
  const locationOption = useMemo<AutoCompleteOption[]>(() => {
    if (!groupList?.groups) return [];
    return groupList.groups.map((group) => ({
      label: group.name ?? '',
      id: group.id ?? '',
    }));
  }, [groupList]);

  return {
    locationSelectorElement:
      isInIframe && locationFilter ? (
        <p className="p-2">
          <span className="">Current Account:</span>{' '}
          <span className="text-primary-600">{locationFilter.label}</span>
        </p>
      ) : (
        <AutoCompleteInput
          options={locationOption}
          value={locationFilter}
          disabled={readonly}
          placeholder="Search Location"
          onChange={handleChange}
          inputValue={locationFilter?.label ?? ''}
          className="w-full"
        />
      ),
    loading,
    error,
    locationOption,
    locationFilter,
    refetch,
    handleSetLocation: handleChange,
  };
};
// { ...locationFilter, id: 'cm1kb4ct40050umk42t9ipmlf' },
