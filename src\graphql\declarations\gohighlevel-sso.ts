import { graphql } from '../generated';

export const GHLSSOCheck = graphql(`
  query GHLSSOCheck($input: Ghl_auth_getSSOInfoInput!) {
    ghl_auth_getSSOInfo(input: $input) {
      locationID
      accountName
      email
      groupID
      groupName
    }
  }
`);

export const GHLSSOBind = graphql(`
  mutation GHLSSOBind($input: Ghl_auth_bindInput!) {
    ghl_auth_bind(input: $input) {
      ghlUserID
      localUserID
      locationID
    }
  }
`);

export const GHLSSOSignIn = graphql(`
  mutation GHLSSOSignIn($input: Ghl_auth_ssoInput!) {
    ghl_auth_sso(input: $input) {
      sessionToken
      item {
        id
        email
      }
    }
  }
`);
