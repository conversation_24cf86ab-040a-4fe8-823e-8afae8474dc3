import { int2DecToFloat, moneyFormat } from '@/lib/utils';
import { PaymentLineItem } from '.';

interface PaymentSuccessProps {
  total: number;
  items?: PaymentLineItem[];
}

export const PaymentSuccess = ({ total, items = [] }: PaymentSuccessProps) => {
  return (
    <div className="flex h-[100vh] w-full items-center justify-center">
      <div className="flex flex-col items-center gap-6 rounded-lg bg-white p-8 shadow-lg">
        <div className="rounded-full bg-green-100 p-4">
          <svg
            className="h-12 w-12 text-green-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-800">Payment Successful!</h2>
        <p className="text-center text-gray-600">
          Thank you for your payment. You will receive a confirmation email shortly.
        </p>

        {items.length > 0 && (
          <div className="w-full">
            <h3 className="mb-2 text-lg font-semibold text-gray-700">Purchased Items:</h3>
            <div className="space-y-2">
              {items.map((item, index) => (
                <div key={index} className="flex justify-between text-gray-600">
                  <span>
                    {item.name} {item.amount > 1 && `(x${item.amount})`}
                  </span>
                  <span>{moneyFormat(int2DecToFloat(item.total))}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {total > 0 && (
          <div className="mt-4 w-full border-t pt-4">
            <div className="flex justify-between">
              <span className="text-lg font-semibold text-gray-700">Total</span>
              <span className="text-xl font-bold text-gray-800">
                {moneyFormat(int2DecToFloat(total))}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
