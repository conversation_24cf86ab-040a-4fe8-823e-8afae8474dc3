'use client';

import { useEffect } from 'react';
import { But<PERSON> } from 'flowbite-react';
import { useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { LocationFormData, locationFormDataDefaults } from '../utils';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { FormAutoComplete, FormInput } from '@/components/globals';
import { message } from '@/components/shared/utils';
import { useLocationSelector } from '@/components/hooks';
import { UpdateGroupDocument } from '@/graphql/generated/graphql';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export default function LocationTab() {
  const {
    locationOption,
    refetch: refetchLocations,
    loading: locationIsLoading,
    handleSetLocation,
  } = useLocationSelector({ readonly: true });
  const locationMethods = useForm<LocationFormData>({
    defaultValues: { ...locationFormDataDefaults },
  });

  const { control, setValue, reset } = locationMethods;

  const [updateGroupMutation, { loading: updateGroupIsloading }] = useMutation(
    UpdateGroupDocument,
    {
      onCompleted: () => {
        toast.success(message.api.successUpdate('Location'));
        refetchLocations();
        // relaod page
        console.log({
          id: locationMethods.getValues('location')?.id ?? '',
          label: locationMethods.getValues('name'),
        });
        handleSetLocation({
          id: locationMethods.getValues('location')?.id ?? '',
          label: locationMethods.getValues('name'),
        });
        reset({ ...locationFormDataDefaults });
        window.location.reload();
      },
      onError: (error) => {
        toast.error(message.api.errorUpdate('Location', error.message));
      },
    },
  );

  const locatoinWatcher = useWatch({
    control: control,
    name: 'location',
  });

  useEffect(() => {
    if (locatoinWatcher) {
      setValue('name', locatoinWatcher?.label);
    }
  }, [locatoinWatcher]);

  const onSubmitForm = async (data: LocationFormData) => {
    try {
      await updateGroupMutation({
        variables: {
          where: {
            id: data.location?.id ?? '',
          },
          data: {
            labelName: data.name,
          },
        },
      });

      await apolloClient.resetStore();
    } catch (e) {
      console.error('Update Customer Mutation error: ', e);
    }
  };

  return (
    <div className="mx-auto mt-8 py-0">
      <FormProvider {...locationMethods}>
        <div className="mb-4 text-sm text-gray-600">
          You can update how your location name shows up in the NGnair platform. This won’t change
          the name on your merchant account or how it appears on your customers’ payment records
          (e.g. bank statements).
        </div>
        <form onSubmit={locationMethods.handleSubmit(onSubmitForm)} className="space-y-4">
          <div className="flex gap-4">
            <FormAutoComplete
              id="location"
              name="location"
              label="Locations"
              options={[...locationOption]}
              rules={{ required: message.requiredField }}
              optionsLoading={locationIsLoading}
            />
            <FormInput
              id="name"
              name="name"
              label="Friendly Name"
              type="text"
              rules={{
                required: message.requiredField,
              }}
            />
          </div>

          <Button
            color={'blue'}
            type="submit"
            className="w-full sm:w-auto"
            disabled={updateGroupIsloading}
          >
            {updateGroupIsloading ? 'Saving...' : 'Save Changes'}
          </Button>
        </form>
      </FormProvider>
    </div>
  );
}
