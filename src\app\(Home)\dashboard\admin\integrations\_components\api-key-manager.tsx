'use client';

// Add these imports
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ServiceAcct_create, ServiceAcct_delete } from '@/graphql/declarations/serviceAcct';
import { ServiceAcct_ListDocument } from '@/graphql/generated/graphql';
import { useMutation, useQuery } from '@apollo/client';
import { Copy, Key, Plus, Trash2, X } from 'lucide-react';
import { useEffect, useState, useMemo } from 'react';
import { toast } from 'react-toastify';
import { useLocationSelector } from '@/components/hooks';
import { apolloClient } from '@/lib/graphql/ApolloClient';

interface ApiKeyManagerProps {
  groupId: string;
}

interface ApiKey {
  id: string;
  name: string;
  createdAt: string;
  lastUsed: string;
  allowedMethods?: string[];
}

interface NewApiKey {
  clientId: string;
  clientSecret: string;
}

// Define available permissions
interface Permission {
  id: string;
  name: string;
  description: string;
}

export default function ApiKeyManager({ groupId }: ApiKeyManagerProps) {
  const [showNewKeyModal, setShowNewKeyModal] = useState(false);
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);
  const [newKey, setNewKey] = useState<NewApiKey | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [isCreatingKey, setIsCreatingKey] = useState(false);

  const { locationSelectorElement, locationFilter } = useLocationSelector({});

  const [groupID, setGroupID] = useState<string | null>(null);

  // Define available permissions
  const availablePermissions: Permission[] = [
    {
      id: 'MER_PMT',
      name: 'Paylinks and Payment Integration',
      description: 'Allow access to payment processing and paylinks functionality',
    },
  ];

  useEffect(() => {
    setGroupID(locationFilter?.id ?? groupId);
  }, [locationFilter?.id, groupId]);

  const { data, loading, refetch } = useQuery(ServiceAcct_ListDocument, {
    variables: {
      where: {
        group: {
          id: {
            equals: groupID,
          },
        },
      },
    },
    skip: !groupID,
  });

  // Filter to show only non-expiring tokens and sort by createdAt (newest first)
  const nonExpiringTokens = useMemo(() => {
    if (!data?.serviceAPIAccounts) return [];
    return data.serviceAPIAccounts
      .filter((account) => account.expiresAt === null)
      .map((account) => ({
        id: account.id,
        token: account.token,
        createdAt: account.createdAt,
        allowedMethods: account.allowedMethods,
      }))
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [data?.serviceAPIAccounts]);

  const [createApiKey] = useMutation(ServiceAcct_create);
  const [deleteApiKey] = useMutation(ServiceAcct_delete);

  const handleOpenPermissionsModal = () => {
    if (!groupID) {
      toast.error('Please select a group first');
      return;
    }
    setSelectedPermissions([]);
    setShowPermissionsModal(true);
  };

  const handleCreateKey = async () => {
    try {
      if (!groupID) {
        return;
      }

      setIsCreatingKey(true);

      // Prepare permissions array for API call
      const permissionsArray =
        selectedPermissions.length > 0 ? selectedPermissions.map((p) => p) : undefined;

      const result = await createApiKey({
        variables: {
          input: {
            groupID: groupID,
            permissions: permissionsArray,
          },
        },
      });

      setNewKey({
        clientId: result.data?.serviceacct_create?.token ?? '',
        clientSecret: result.data?.serviceacct_create?.secret ?? '',
      });
      await apolloClient.refetchQueries({ include: ['ServiceAcct_list'] });
      setShowNewKeyModal(true);
      refetch();
    } catch (error) {
      toast.error('Failed to create Payment Key');
    } finally {
      setIsCreatingKey(false);
      setShowPermissionsModal(false);
    }
  };

  const handleDeleteKey = async (keyId: string) => {
    try {
      await deleteApiKey({
        variables: {
          where: {
            id: keyId,
          },
        },
      });
      refetch();
      toast.success('Payment Key deleted successfully');
    } catch (error) {
      toast.error('Failed to delete Payment Key');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  return (
    <div className="space-y-4 p-4">
      <div className="space-y-2">
        <div>
          <h2 className="text-xl font-semibold">Payment Keys</h2>
          <p className="mt-1 text-sm text-slate-600">
            Payment Keys are used to authenticate REST API calls against resources and actions for a
            specific group. Each key provides secure access to your group's data through the API.
          </p>
        </div>

        <div className="">
          <label className="text-sm font-medium">Select Group</label>
          <div className="flex items-center gap-2">
            {locationSelectorElement}
            <Button
              onClick={handleOpenPermissionsModal}
              className="flex items-center gap-2"
              disabled={!groupID}
            >
              <Plus className="h-4 w-4" />
              Create New Key
            </Button>
          </div>
        </div>
      </div>

      {groupID && (
        <div className="rounded-lg border border-slate-200 bg-slate-50 p-3">
          <p className="text-sm font-medium">
            Group ID: <span className="font-mono">{groupID}</span>
          </p>
        </div>
      )}

      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
          </div>
        ) : nonExpiringTokens.length === 0 ? (
          <div
            className={cn(
              'flex flex-col items-center justify-center rounded-lg border border-dashed py-8',
              'bg-slate-50 text-center',
            )}
          >
            <Key className="h-8 w-8 text-slate-400" />
            <h3 className="mt-2 text-sm font-medium text-slate-900">
              No non-expiring Payment Keys
            </h3>
            <p className="mt-1 text-sm text-slate-500">
              Create an Payment Key to get started with API integration
            </p>
          </div>
        ) : (
          <div className="max-h-[50vh] overflow-y-auto rounded-lg border">
            {nonExpiringTokens.map((key) => (
              <div
                key={key.id}
                className="flex items-center justify-between border-b p-4 last:border-b-0"
              >
                <div className="flex items-center gap-3">
                  <Key className="h-5 w-5 text-slate-600" />
                  <div>
                    <p className="font-medium">{key.token}</p>
                    <p className="text-sm text-slate-500">
                      Created: {new Date(key.createdAt).toLocaleDateString()}
                    </p>
                    {key.allowedMethods && key.allowedMethods.length > 0 && (
                      <div className="mt-1 flex flex-wrap gap-1">
                        {key.allowedMethods.map((method, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800"
                          >
                            {method}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteKey(key.id)}
                  className="text-red-500 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Permissions Selection Modal */}
      <Dialog open={showPermissionsModal} onOpenChange={setShowPermissionsModal}>
        <DialogContent className="max-w-md p-4">
          <DialogHeader>
            <DialogTitle>Select Payment Key Permissions</DialogTitle>
            <DialogDescription>
              Choose what permissions to grant to this Payment Key. You can select multiple
              permissions.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {availablePermissions.map((permission) => (
              <div key={permission.id} className="flex items-start space-x-2">
                <Checkbox
                  id={`permission-${permission.id}`}
                  checked={selectedPermissions.includes(permission.id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedPermissions([...selectedPermissions, permission.id]);
                    } else {
                      setSelectedPermissions(
                        selectedPermissions.filter((p) => p !== permission.id),
                      );
                    }
                  }}
                />
                <div className="grid gap-1.5 leading-none">
                  <Label
                    htmlFor={`permission-${permission.id}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {permission.name}
                  </Label>
                  <p className="text-xs text-slate-500">{permission.description}</p>
                </div>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPermissionsModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateKey} disabled={isCreatingKey}>
              {isCreatingKey ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Payment Key'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Key Modal */}
      <Dialog open={showNewKeyModal} onOpenChange={setShowNewKeyModal}>
        <DialogContent className="max-w-screen-sm p-4">
          <div className="absolute right-4 top-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowNewKeyModal(false)}
              className="h-6 w-6 rounded-full p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogHeader>
            <DialogTitle>New Payment Key Created</DialogTitle>
            <DialogDescription>
              Store these credentials securely. The client secret will not be shown again.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Client ID</label>
              <div className="flex gap-2">
                <Input readOnly value={newKey?.clientId} />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(newKey?.clientId ?? '')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Client Secret</label>
              <div className="flex gap-2">
                <Input readOnly value={newKey?.clientSecret} />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(newKey?.clientSecret ?? '')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <div className="flex justify-end pt-4">
            <Button onClick={() => setShowNewKeyModal(false)}>Close</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
