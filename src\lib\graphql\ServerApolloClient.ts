// import { registerApolloClient } from "@apollo/experimental-nextjs-app-support";
// import { apolloClient } from "./ApolloClient";

// export const { getClient, query, PreloadQuery } = registerApolloClient(() => {
//   return apolloClient;
// });

import { ApolloClient, InMemoryCache, HttpLink } from '@apollo/client';
import { env } from 'next-runtime-env';
import { createMockApolloClient, shouldUseMockMode } from './MockApolloClient';
// import fetch from 'isomorphic-unfetch';

const createApolloClient = () => {
  // Use mock client if in mock mode
  if (shouldUseMockMode()) {
    return createMockApolloClient();
  }

  // Use real client for production
  return new ApolloClient({
    ssrMode: typeof window === 'undefined', // Disables force-fetching on the server (so queries are only run once)
    link: new HttpLink({
      uri: env('NEXT_PUBLIC_SERVER_URL'), // Your GraphQL API endpoint
      fetch,
    }),
    cache: new InMemoryCache(),
  });
};

export default createApolloClient;
