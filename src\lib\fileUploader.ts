import { File_upload } from '@/graphql/declarations/file';
import { apolloClient } from './graphql/ApolloClient';

export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      resolve(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  });
};

export const uploadB64Data = async (
  data: {
    b64: string;
    name: string;
    mimetype: string;
  }[],
) => {
  // await new Promise((resolve) => setTimeout(resolve, 3000));

  // return `https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRuzesjGhoxjo_d4f4TgmQlT_XLag63BzHwUA&s`;
  const upload = await apolloClient.mutate({
    mutation: File_upload,
    variables: {
      input: {
        files: data.map((file) => {
          return {
            b64: file.b64,
            filename: file.name,
            mimetype: file.mimetype,
          };
        }),
      },
    },
  });

  return upload.data?.file_upload?.files;
};

export const uploadFileToB64Data = async (
  data: {
    file: File;
    name: string;
    mimetype: string;
  }[],
) => {
  const b64Data = await Promise.all(
    data.map(async (file) => {
      const b64 = await fileToBase64(file.file);
      return {
        b64,
        name: file.name,
        mimetype: file.mimetype,
      };
    }),
  );

  return uploadB64Data(b64Data);
};
