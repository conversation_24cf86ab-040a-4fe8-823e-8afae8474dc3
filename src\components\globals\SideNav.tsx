'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

import * as DialogPrimitive from '@radix-ui/react-dialog';
import clsx from 'clsx';
import { X } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Button } from '../ui/button';
import { Sheet, SheetClose, SheetContent, SheetHeader, SheetTrigger } from '../ui/sheet';

import { cn } from '@/lib/utils';

import useUIStore from '@/store/main';
import { UserIcon, RightArrow } from '../../assets/svg/navigation';
import { useUserStore } from '@/store/user-store';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';

const Brand = ({ collapsed }: { collapsed?: boolean }) => {
  const fillColor = collapsed ? 'none' : '#3C5B80';
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleIconClick = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const brandClasses = collapsed
    ? 'absolute transition-all duration-300 ease-in-out left-1/2 transform -translate-x-1/2'
    : 'transition-all duration-300 ease-in-out';

  return (
    <div className={brandClasses}>
      <svg
        width="31"
        height="30"
        viewBox="0 0 41 40"
        xmlns="http://www.w3.org/2000/svg"
        className="transition-all duration-500"
      >
        <path
          d="M1 18.1917V1H18.1805V18.1917H1Z"
          stroke="#3C5B80"
          strokeWidth="2"
          fill={fillColor}
        />
        <path
          d="M30.4945 1C35.2382 1 39.0848 4.84794 39.0848 9.59583C39.0848 14.3437 35.2382 18.1917 30.4945 18.1917C25.7508 18.1917 21.9043 14.3437 21.9043 9.59583C21.9043 4.84794 25.7508 1 30.4945 1Z"
          stroke="#ED6B4C"
          strokeWidth="2"
          fill={collapsed ? 'none' : '#ED6B4C'}
        />
        <path
          d="M18.1805 38.9724C16.1345 38.8596 14.1199 38.4014 12.2224 37.6149C10.0164 36.7005 8.01206 35.3602 6.32377 33.6705C4.63547 31.9809 3.29634 29.975 2.38287 27.7674C1.59702 25.8682 1.13946 23.8518 1.02726 21.8041H18.1805V38.9724Z"
          stroke="#3C5B80"
          strokeWidth="2"
          fill={collapsed ? 'none' : '#3C5B80'}
        />
        <path
          d="M21.9043 38.9958V21.8041H39.0848V38.9958H21.9043Z"
          stroke="#98C1D9"
          strokeWidth="2"
          fill={collapsed ? 'none' : '#98C1D9'}
        />
      </svg>
    </div>
  );
};

const Sidenav = ({ navItems }: any) => {
  const { isSidebarCollapsed, toggleSidebar } = useUIStore();

  const { user } = useUserStore();
  const router = useRouter();

  const path = usePathname();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openDialog = () => {
    setIsModalOpen(true);
  };

  const loading = false;

  const logout = async () => {
    AUTHSTORE.clear();
    apolloClient.resetStore();

    router.push('/login');
  };

  return (
    <aside
      className={`z-50 bg-white text-sm transition-all duration-300 ease-in-out ${
        isSidebarCollapsed ? 'w-full md:w-20' : 'w-full px-3 lg:w-[177px]'
      } h-full md:border-r md:py-8`}
    >
      <div className="flex w-full justify-between py-4 md:hidden">
        <Brand />
        <Sheet>
          <SheetTrigger className="md:hidden"></SheetTrigger>
          <SheetContent className="w-full p-0" side="left">
            <SheetHeader className="h-20 flex-row items-center justify-between px-4">
              <SheetClose asChild>
                <Brand />
              </SheetClose>
              <SheetClose>
                <X className="h-6 w-6" />
              </SheetClose>
            </SheetHeader>
            <nav className="flex-1">
              <ul className="flex select-none flex-col gap-2">
                {navItems.map(({ label, link, submenu, icon: Icon }: any, index: number) => (
                  <li key={link}>
                    {submenu?.length ? (
                      <div className="relative flex-col">
                        <input
                          type="checkbox"
                          id={`toggle-menu-${index}`}
                          className="peer hidden"
                        />
                        <label
                          htmlFor={`toggle-menu-${index}`}
                          className="text-primary inline-flex w-full cursor-pointer items-center gap-2 p-4"
                        >
                          <Icon className="h-6 w-6" />
                          {label}
                        </label>
                        <RightArrow
                          htmlFor={`toggle-menu-${index}`}
                          className="absolute right-[30px] top-5 h-5 w-5 transition-all peer-checked:rotate-90"
                        />
                        <SheetClose className="ml-12 flex h-0 flex-col overflow-hidden transition-all peer-checked:h-[280px]">
                          {submenu.map(
                            ({ href, label }: { href: string; label: string }, index: number) => (
                              <Link
                                key={index}
                                href={href}
                                className="text-primary inline-flex w-full items-center gap-2 p-4"
                              >
                                <Icon className="h-6 w-6" />
                                {label}
                              </Link>
                            ),
                          )}
                        </SheetClose>
                      </div>
                    ) : (
                      <SheetClose asChild>
                        <Link
                          href={link}
                          className="text-primary inline-flex w-full items-center gap-2 p-4"
                        >
                          <Icon className="h-6 w-6" />
                          {label}
                        </Link>
                      </SheetClose>
                    )}
                  </li>
                ))}
                <li>
                  <SheetClose asChild>
                    <Link
                      href="/"
                      className="text-primary inline-flex w-full items-center gap-2 p-4"
                    >
                      <UserIcon /> Login
                    </Link>
                  </SheetClose>
                </li>
                <li className="px-4">
                  <SheetClose asChild>
                    <Button>Sign Up</Button>
                  </SheetClose>
                </li>
              </ul>
            </nav>
          </SheetContent>
        </Sheet>
      </div>
      <div className="hidden h-full flex-col gap-2 px-1 md:flex">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div
                onClick={toggleSidebar}
                className="relative mb-4 flex cursor-pointer items-baseline justify-center gap-1"
              >
                <Brand collapsed={isSidebarCollapsed} />
                <div
                  className={`text-primary text-3xl font-extrabold ${
                    isSidebarCollapsed
                      ? 'invisible opacity-0'
                      : 'delay-[3500ms] visible opacity-100 transition-all duration-500 ease-in-out'
                  }`}
                >
                  NGnair
                </div>
                <div
                  className={`text-xs font-bold text-slate-600 ${
                    isSidebarCollapsed
                      ? 'invisible opacity-0'
                      : 'delay-[3500ms] visible opacity-100 transition-all duration-500 ease-in-out'
                  }`}
                ></div>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isSidebarCollapsed ? 'expand' : 'collapse'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <div className="tall:py-8 hidden w-full flex-col items-center justify-center md:flex">
          {loading ? (
            <div className="flex flex-col items-center justify-center gap-3">
              <Skeleton className="h-14 w-14 rounded-full p-2"></Skeleton>
              <div
                className={`flex flex-col items-center justify-start gap-1 ${
                  isSidebarCollapsed ? 'hidden' : ''
                }`}
              >
                <Skeleton className="h-7 w-32"> </Skeleton>
                <Skeleton className="h-5 w-20"> </Skeleton>
              </div>
            </div>
          ) : (
            <div>
              <div className="hidden flex-col items-center justify-center gap-8 rounded-full md:flex">
                <div className="inline-flex items-center justify-center">
                  <Dialog>
                    <DialogTrigger>
                      <Avatar
                        className={cn(
                          'transition-all duration-300 ease-in-out',
                          isSidebarCollapsed ? 'h-14 w-14' : 'h-16 w-16 2xl:h-20 2xl:w-20',
                        )}
                      >
                        <AvatarImage src="https://cdn3.iconfinder.com/data/icons/professions-1-4/132/31-1024.png" />
                        <AvatarFallback
                          className={cn(
                            'bg-gradient-to-br from-gray-400 to-gray-100 font-bold text-white',
                            isSidebarCollapsed ? 'text-3xl' : 'text-4xl',
                          )}
                        >
                          Test
                        </AvatarFallback>
                      </Avatar>
                    </DialogTrigger>
                  </Dialog>
                </div>
              </div>
              <div className="flex h-12 flex-col items-center justify-start gap-1 pt-1 2xl:pt-2">
                <div className={`${isSidebarCollapsed ? 'hidden' : ''}`}>
                  <div className="text-center text-sm font-bold text-[#495057] 2xl:text-base">
                    {user?.name + ' ' + user?.lastName}
                  </div>
                  <div className="text-center text-xs font-normal text-neutral-500 2xl:text-sm"></div>
                </div>
              </div>
            </div>
          )}
        </div>
        <ul className="flex flex-col gap-2 transition-all duration-300 ease-in-out 2xl:pt-4">
          {navItems.map(
            (
              {
                label,
                link,
                icon: Icon,
                submenu,
              }: {
                label: string;
                link: string;
                icon: any;
                submenu: any;
              },
              index: number,
            ) => (
              <li key={index}>
                <Link
                  href={link}
                  className={clsx(
                    'hover:text-primary flex flex-col items-center gap-1 rounded-lg px-2 py-1 text-xs transition-all duration-300 ease-in-out hover:bg-[#EBF6FF] 2xl:gap-2 2xl:px-4 2xl:py-1.5 2xl:text-sm',
                    path?.startsWith(link)
                      ? 'text-primary bg-[#EBF6FF] font-bold'
                      : 'text-[#7B7B7B]',
                    isSidebarCollapsed ? 'justify-center' : '',
                  )}
                >
                  <Icon />
                  <span
                    className={` ${
                      isSidebarCollapsed
                        ? 'hidden text-base transition-all duration-500 ease-in-out'
                        : 'transition-opacity delay-1000 duration-300'
                    }`}
                  >
                    {label}
                  </span>
                </Link>
              </li>
            ),
          )}
        </ul>

        <div
          className={
            'mt-auto flex cursor-pointer items-start gap-2 rounded-lg px-6 py-1 text-[#ED6B4C] transition-all duration-300 ease-in-out'
          }
        >
          <Dialog open={isModalOpen}>
            <DialogTrigger>
              <div onClick={openDialog} className="flex items-center justify-center gap-2">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.89844 7.56017C9.20844 3.96017 11.0584 2.49017 15.1084 2.49017H15.2384C19.7084 2.49017 21.4984 4.28017 21.4984 8.75017V15.2702C21.4984 19.7402 19.7084 21.5302 15.2384 21.5302H15.1084C11.0884 21.5302 9.23844 20.0802 8.90844 16.5402"
                    stroke="#ED6B4C"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2 12.0002H14.88"
                    stroke="#ED6B4C"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12.6484 8.65018L15.9984 12.0002L12.6484 15.3502"
                    stroke="#ED6B4C"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Logout
              </div>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center justify-center"></DialogTitle>
                <DialogDescription className="pb-10 text-center text-2xl font-bold text-[#495057]">
                  Are you sure you want to log out?
                </DialogDescription>
                <div className="flex w-full flex-row gap-4">
                  <DialogTrigger className="w-1/2">
                    <DialogPrimitive.Close
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    >
                      <Button
                        variant="outline"
                        className="hover:text-primary inline-flex w-[224px] bg-white font-semibold text-[#3C5B80] ring-1 ring-[#3C5B80]"
                        onClick={() => setIsModalOpen(false)}
                      >
                        Cancel
                      </Button>
                    </DialogPrimitive.Close>
                  </DialogTrigger>

                  <Button
                    onClick={logout}
                    className="inline-flex w-[224px]"
                    type="submit"
                    variant="destructive"
                  >
                    Logout
                  </Button>
                </div>
              </DialogHeader>
            </DialogContent>
          </Dialog>

          {/* {!isSidebarCollapsed && (
            <span className='hidden text-sm transition-opacity duration-300 delay-1000 lg:inline'>
              <Dialog>
                <DialogTrigger onClick={openDialog}>Logout</DialogTrigger>

                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className='flex items-center justify-center'>
                      <Logout />
                    </DialogTitle>
                    <DialogDescription className='text-center pb-10 text-2xl font-bold  text-[#495057]'>
                      Are you sure you want to log out?
                    </DialogDescription>
                    <div className='flex flex-row w-full gap-4'>
                      <DialogTrigger className='w-1/2'>
                        <DialogPrimitive.Close
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <Button className='bg-white w-[224px] inline-flex ring-1 font-semibold ring-[#3C5B80] text-[#3C5B80]'>
                            Cancel
                          </Button>
                        </DialogPrimitive.Close>
                      </DialogTrigger>

                      <Button
                        onClick={logout}
                        className='inline-flex w-[224px]'
                        type='submit'
                      >
                        Logout
                      </Button>
                    </div>
                  </DialogHeader>
                </DialogContent>
              </Dialog>
            </span>
          )} */}
        </div>
      </div>
    </aside>
  );
};

export default Sidenav;
