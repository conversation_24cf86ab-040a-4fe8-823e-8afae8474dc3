query Gateway_transactions($input: Gateway_transactionsInput!) {
  gateway_transactions(input: $input) {
    page {
      total
      range {
        from
        to
      }
      page
      pageSize
    }
    data {
      transactionID
      location
      date
      method
      customer
      brand
      status
      last4
      amount
    }
  }
}

query Gateway_transaction($input: Gateway_transactionInput!) {
  gateway_transaction(input: $input) {
    transactionID
    status
    date
    createdBy
    paymentType
    method
    batchID
    paymentPlan
    source
    authCode
    amount
    entryMethod
    tokenSource
    gsa
    emv
    last4
    customerName
    customerID
    customerEmail
    customerPhone
    customerCountry
    customerBillingAddress
    commercialLevel
    result
    message
    brandReference
    processorInfo {
      name
      type
      id
    }
    breakdown {
      discount
      directDiscount
      actualDiscount
      tax
      shipping
      shippingDiscount
      shippingDirectDiscount
      shippingActualDiscount
      fees
      actualFees
      tip
      subtotal
      subscriptionTotal
      rawTotal
      total
      expectedTotal
    }
    transactionHistory {
      date
      status
      response
      avs
      cvv
    }
    purchaseDetails {
      productName
      productID
      quantity
      price
      discount
    }
    relatedTransactions {
      transactionID
      amount
      status
      date
      paymentType
    }
  }
}
