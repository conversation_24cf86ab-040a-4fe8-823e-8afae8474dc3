import { cn } from '@/lib/utils';
import { int2DecToFloat, moneyFormat } from '@/lib/utils';
import styles from './index.module.css';
import { PaymentLineItem } from './index';

interface OrderItemsProps {
  lineItems: PaymentLineItem[];
  edit?: boolean;
  setCustomAmount?: (amounts: { [key: string]: number }) => void;
  currentAmount?: { [key: string]: number };
}

export const OrderItems = ({
  lineItems,
  edit,
  setCustomAmount,
  currentAmount,
}: OrderItemsProps) => {
  if (!lineItems?.length) return null;

  const handleAmountChange = (id: string, amount: number) => {
    if (setCustomAmount) {
      setCustomAmount({
        ...currentAmount,
        [id]: amount,
      });
    }
  };

  return (
    <div className={cn(styles.orderDetails, 'px-4 pb-6 pt-4')}>
      <div className={cn(styles.headerSm, 'p-4 py-2 pb-1')}>
        <b className={cn(styles.b, 'text-sm text-gray-500')}>Items</b>
        <img className={styles.infoIcon} alt="" src="info.svg" />
      </div>
      <div className={styles.table}>
        <div className={cn(styles.tableColumns, '')}>
          <div className={cn(styles.column, 'gap-4')}>
            {lineItems.map((item) => (
              <div key={item.id} className="flex w-full justify-between gap-4">
                <div className={cn('flex flex-[2] flex-col pl-4')}>
                  <p className="text-lg font-bold">{item.name}</p>
                  <p className="text-xs text-gray-500">ID: {item.id}</p>
                  <p className="py-0.5 text-xs text-gray-500">{item.description}</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className={cn('flex flex-1 flex-col pr-4')}>
                    <p className="text-right text-lg font-bold">
                      {moneyFormat(int2DecToFloat(item.total))}
                    </p>
                    <div className="flex items-center justify-end gap-2">
                      <p className="text-right text-gray-500">
                        {moneyFormat(int2DecToFloat(item.price))} each
                      </p>
                      <span className="text-gray-500">
                        (x{currentAmount?.[item.id] ?? item.amount})
                      </span>
                    </div>
                  </div>
                  {edit ? (
                    <div className="flex items-center gap-1">
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          handleAmountChange(
                            item.id,
                            (currentAmount?.[item.id] ?? item.amount) - 1,
                          );
                        }}
                        disabled={(currentAmount?.[item.id] ?? item.amount) <= 1}
                        className="flex h-6 w-6 items-center justify-center rounded border border-gray-300 text-sm disabled:text-gray-300"
                      >
                        -
                      </button>
                      <input
                        type="number"
                        min="1"
                        value={currentAmount?.[item.id] ?? item.amount}
                        onChange={(e) => handleAmountChange(item.id, parseInt(e.target.value) || 0)}
                        className="h-fit w-12 rounded-lg border border-gray-300 p-1 text-center text-sm"
                      />
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          handleAmountChange(
                            item.id,
                            (currentAmount?.[item.id] ?? item.amount) + 1,
                          );
                        }}
                        className="flex h-6 w-6 items-center justify-center rounded border border-gray-300 text-sm"
                      >
                        +
                      </button>
                    </div>
                  ) : (
                    <></>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
