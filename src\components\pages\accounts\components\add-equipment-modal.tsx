import React from 'react';
import { Mo<PERSON>, Button, TextInput, Select } from 'flowbite-react';
import { HiX, HiOutlineInformationCircle } from 'react-icons/hi';

const AddEquipmentModal = ({ isOpen, onClose, onSubmit }) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    // You would typically gather form data here and pass it to onSubmit
    onSubmit({
      // Form data
    });
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="xl">
      <Modal.Header className="flex items-center justify-between border-b">
        <h3 className="text-xl font-semibold text-blue-600">Add Equipment</h3>
      </Modal.Header>
      <Modal.Body>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="manufacturer"
                className="mb-2 block text-sm font-medium text-gray-700"
              >
                Manufacturer
              </label>
              <TextInput id="manufacturer" type="text" placeholder="Bonnie Green" />
            </div>
            <div>
              <label htmlFor="model" className="mb-2 block text-sm font-medium text-gray-700">
                Model
              </label>
              <Select id="model">
                <option>Machine</option>
                <option>Other models...</option>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="serialNumber"
                className="mb-2 block text-sm font-medium text-gray-700"
              >
                Serial #
              </label>
              <TextInput id="serialNumber" type="text" placeholder="338440-858572" />
            </div>
            <div>
              <label htmlFor="tpn" className="mb-2 block text-sm font-medium text-gray-700">
                TPN <HiOutlineInformationCircle className="ml-1 inline-block text-gray-400" />
              </label>
              <TextInput id="tpn" type="text" placeholder="City" />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="authKey" className="mb-2 block text-sm font-medium text-gray-700">
                Auth Key
              </label>
              <TextInput id="authKey" type="text" placeholder="123 456 7890" />
            </div>
            <div>
              <label htmlFor="registerId" className="mb-2 block text-sm font-medium text-gray-700">
                Register Id
              </label>
              <Select id="registerId">
                <option>San Francisco</option>
                <option>Other cities...</option>
              </Select>
            </div>
          </div>
          <Button type="submit" color="blue">
            + Add Equipment
          </Button>
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default AddEquipmentModal;
