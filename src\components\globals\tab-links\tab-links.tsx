import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';

type TabLinksProps = {
  items: Array<{ link: string; title: string; reload?: boolean }>;
  navbarHeight?: number; // Height of your navbar in pixels
};

export const TabLinks = ({ items, navbarHeight = 57 }: TabLinksProps) => {
  const pathname = usePathname();
  const [isSticky, setIsSticky] = useState(true);
  const [tabWidth, setTabWidth] = useState<number>(1232);

  // useEffect(() => {
  //   setIsSticky(true);
  // }, [navbarHeight]);

  // // Capture the initial width of the tab container
  // useEffect(() => {
  //   const tabElement = document.getElementById('tab-wrapper');
  //   if (tabElement && !isSticky) {
  //     console.log('offsetWidth', tabElement.offsetWidth);
  //     setTabWidth(tabElement.offsetWidth);
  //   }
  // }, [isSticky]);

  return (
    <div className={cn('container mx-auto px-4 pt-2 transition-all duration-300')}>
      {/* Wrapper for sticky behavior */}
      <div
        className={cn(
          'transition-all duration-300',
          isSticky ? 'fixed left-0 right-0 top-0 z-[35]' : '',
        )}
        style={{ top: isSticky ? `${navbarHeight}px` : 'auto' }}
      >
        {/* Keep the width fixed when sticky */}
        <div
          id="tab-wrapper"
          className="mx-auto"
          style={isSticky && tabWidth ? { width: `${tabWidth}px` } : undefined}
        >
          <ul
            className={cn(
              'flex flex-wrap border-b border-gray-200 bg-white text-center text-sm font-medium dark:border-gray-700',
              isSticky ? '' : '-mb-2px',
            )}
            id="default-styled-tab"
          >
            {items.map((page) => (
              <li className="me-2" role="presentation" key={page.link}>
                {page.reload && (
                  <a
                    href={`${page.link}`}
                    className={cn(
                      'inline-block rounded-t-lg border-b-2 p-4 pt-6',
                      pathname === page.link
                        ? 'border-primary-600 text-primary-600'
                        : 'border-transparent text-gray-500 dark:text-gray-400',
                    )}
                    id={`${page.link}-styled-tab`}
                    type="button"
                    role="tab"
                    aria-controls={page.link}
                    aria-selected="false"
                  >
                    {page.title}
                  </a>
                )}
                {!page.reload && (
                  <Link
                    href={`${page.link}`}
                    className={cn(
                      'inline-block rounded-t-lg border-b-2 p-4 pt-6',
                      pathname === page.link
                        ? 'border-primary-600 text-primary-600'
                        : 'border-transparent text-gray-500 dark:text-gray-400',
                    )}
                    id={`${page.link}-styled-tab`}
                    type="button"
                    role="tab"
                    aria-controls={page.link}
                    aria-selected="false"
                  >
                    {page.title}
                  </Link>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
      {isSticky && <div style={{ height: '48px' }} />} {/* Placeholder to prevent content jump */}
    </div>
  );
};
