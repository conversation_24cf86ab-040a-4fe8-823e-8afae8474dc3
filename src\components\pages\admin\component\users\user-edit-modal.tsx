'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Accordion, Modal, TextInput } from 'flowbite-react';
import { HiX } from 'react-icons/hi';
import { toast } from 'react-toastify';
import { useState } from 'react';
import { mockUsers } from '@/mock/users-data';

type UserEditModalProps = {
  isOpen: boolean;
  onClose: () => void;
  userId?: string;
  inviteId?: string;
  groupId?: string;
};

export const UserEditModal = ({
  isOpen,
  onClose,
  userId,
  inviteId,
  groupId,
}: UserEditModalProps) => {
  // Find user in mock data
  const user = mockUsers.find(
    (u) => u.user?.id === userId || (u.invite?.id === inviteId && u.group?.id === groupId),
  );

  const [formData, setFormData] = useState({
    firstName: user?.user?.name || '',
    lastName: user?.user?.lastName || '',
    email: user?.user?.email || user?.invite?.email || '',
    phone: '',
    title: '',
    isAdmin: user?.access === 3,
  });

  const isInvite = !user?.user;
  const [loading, setLoading] = useState(false);

  const [flags, setFlags] = useState<{ [key: string]: boolean }>({
    VIEW_TRANSACTIONS: true,
    MANAGE_USERS: formData.isAdmin,
    VIEW_REPORTS: true,
    MANAGE_SETTINGS: formData.isAdmin,
  });

  const flagCategories = {
    General: ['VIEW_TRANSACTIONS', 'VIEW_REPORTS'],
    Administration: ['MANAGE_USERS', 'MANAGE_SETTINGS'],
  };

  const handleSave = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const userFlags = Object.entries(flags)
      .filter(([_, enabled]) => enabled)
      .map(([flag]) => flag);

    // Simulate saving changes
    toast.success('User permissions updated successfully');
    setLoading(false);
    onClose();
  };

  const handleRevokeAccess = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    toast.success(
      isInvite ? 'Invitation revoked successfully' : 'User access revoked successfully',
    );
    setLoading(false);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="4xl" className="overflow-y-auto">
      <div className="p-6">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button onClick={onClose} variant="outline">
              Back
            </Button>
            <h2 className="text-xl font-semibold">User Permissions</h2>
          </div>
          <Button onClick={onClose} variant="ghost" size="sm">
            <HiX className="h-5 w-5" />
          </Button>
        </div>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSave();
          }}
        >
          <section className="mb-8">
            <h3 className="mb-4 text-lg font-semibold">General Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <TextInput id="firstName" value={formData.firstName} disabled={true} />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <TextInput id="lastName" value={formData.lastName} disabled={true} />
              </div>
              <div className="col-span-2">
                <Label htmlFor="email">Email</Label>
                <TextInput id="email" value={formData.email} disabled={true} />
              </div>
            </div>
          </section>

          <section className="mb-8">
            <h3 className="mb-4 text-lg font-semibold">Location Permissions</h3>
            <div className="mb-4 flex items-center gap-2">
              <Switch
                id="adminAccess"
                checked={formData.isAdmin}
                onCheckedChange={(checked) => {
                  setFormData((prev) => ({ ...prev, isAdmin: checked }));
                  if (checked) {
                    setFlags((prev) => ({
                      ...prev,
                      MANAGE_USERS: true,
                      MANAGE_SETTINGS: true,
                    }));
                  }
                }}
              />
              <Label htmlFor="adminAccess">
                <div>Location Admin</div>
                <p className="text-xs text-gray-500">Allow user to manage location permissions</p>
              </Label>
            </div>
          </section>

          <Accordion>
            {Object.entries(flagCategories).map(([category, categoryFlags]) => (
              <Accordion.Panel key={category}>
                <Accordion.Title>{category}</Accordion.Title>
                <Accordion.Content>
                  <div className="space-y-4">
                    <div className="mb-2 flex justify-end">
                      <Button
                        variant="link"
                        className="text-blue-600"
                        onClick={() => {
                          const allEnabled = categoryFlags.every((flag) => flags[flag]);
                          const newFlags = { ...flags };
                          categoryFlags.forEach((flag) => {
                            newFlags[flag] = !allEnabled;
                          });
                          setFlags(newFlags);
                        }}
                      >
                        Toggle all
                      </Button>
                    </div>
                    {categoryFlags.map((flag) => (
                      <div key={flag} className="flex items-center justify-between py-2">
                        <div>
                          <Label htmlFor={flag} className="font-medium">
                            {flag.split('_').join(' ')}
                          </Label>
                          <p className="text-sm text-gray-500">
                            {`Permission to ${flag.split('_').join(' ').toLowerCase()}`}
                          </p>
                        </div>
                        <Switch
                          id={flag}
                          checked={flags[flag]}
                          onCheckedChange={(checked) => {
                            setFlags((prev) => ({
                              ...prev,
                              [flag]: checked,
                            }));
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </Accordion.Content>
              </Accordion.Panel>
            ))}
          </Accordion>

          <div className="mt-8 flex items-center justify-between">
            <Button onClick={handleRevokeAccess} variant="destructive" disabled={loading}>
              {loading ? 'Revoking...' : isInvite ? 'Revoke Invite' : 'Revoke Access'}
            </Button>

            <div className="flex gap-4">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
};
