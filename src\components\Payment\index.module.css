.payment1 {
  position: relative;
  line-height: 150%;
  font-weight: 600;
  text-align: center;
}
.b {
  position: relative;
  line-height: 20px;
}
.infoIcon {
  width: 12px;
  position: relative;
  height: 12px;
  /* overflow: hidden; */
  flex-shrink: 0;
}
.headerSm {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}
.text1 {
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.text {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.tablescell {
  align-self: stretch;
  border-bottom: 1px solid #e5e7eb;
  box-sizing: border-box;
  height: 54px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 16px;
}
.column {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.text6 {
  width: 101px;
  position: relative;
  line-height: 150%;
  font-weight: 500;
  display: inline-block;
  height: 22px;
  flex-shrink: 0;
}
.tablescell4 {
  align-self: stretch;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 16px;
}
.tableColumns {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.table {
  align-self: stretch;
  background-color: #fff;
  /* overflow: hidden; */
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  font-size: 14px;
}
.orderDetails {
  align-self: stretch;
  box-shadow:
    0px 1px 3px rgba(0, 0, 0, 0.1),
    0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  /* overflow: hidden; */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  font-size: 20px;
}
.label {
  align-self: stretch;
  position: relative;
  line-height: 150%;
  font-weight: 500;
}
.inputText {
  flex: 1;
  position: relative;
  line-height: 125%;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}
.input {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 16px;
  color: #6b7280;
}
.inputField {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.row {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.calendarMonthIcon {
  width: 14px;
  position: relative;
  height: 14px;
  /* overflow: hidden; */
  flex-shrink: 0;
}
.selectDate {
  align-self: stretch;
  flex: 1;
  position: relative;
  line-height: 125%;
  display: flex;
  align-items: center;
}
.content3 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}
.inputs {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
}
.button {
  align-self: stretch;
  border-radius: 8px;
  background-color: #1a56db;
  /* overflow: hidden; */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  color: #fff;
}
.button:disabled {
  background-color: #e5e7eb;
  color: #6b7280;
}
.cardDetails {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  /* padding: 32px; */
  /* gap: 24px; */
}
.heading {
  position: relative;
  line-height: 150%;
}
.listItem {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.heading3 {
  position: relative;
  line-height: 150%;
  color: #0e9f6e;
}
.listItems {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
}
.listItem5 {
  position: relative;
  line-height: 150%;
  font-weight: 800;
}
.listItem4 {
  align-self: stretch;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0px 0px;
  font-size: 18px;
}
.totalPrice {
  align-self: stretch;
  border-radius: 8px;
  background-color: #f9fafb;
  /* overflow: hidden; */
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 24px;
}
.brandLogospaypalIcon {
  width: 95.3px;
  position: relative;
  height: 32px;
  /* overflow: hidden; */
  flex-shrink: 0;
}
.brandLogosvisaIcon {
  width: 58.7px;
  position: relative;
  height: 32px;
  /* overflow: hidden; */
  flex-shrink: 0;
}
.brandLogosmastercardIcon {
  width: 40px;
  position: relative;
  height: 32px;
  /* overflow: hidden; */
  flex-shrink: 0;
}
.brandLogos {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 32px;
}
.inputs1 {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 16px;
  font-size: 14px;
}
.billingDetails {
  align-self: stretch;
  box-shadow:
    0px 1px 3px rgba(0, 0, 0, 0.1),
    0px 1px 2px -1px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  /* padding: 16px; */
  gap: 16px;
  font-size: 20px;
}
.priceLogos {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
  font-size: 16px;
}
.cards {
  align-self: stretch;
  display: flex;
  gap: 48px;
  font-size: 14px;
}
.payu {
  text-decoration: underline;
  font-weight: 500;
  color: #1a56db;
}
.helper {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  font-size: 16px;
  color: #6b7280;
}
.container {
  display: flex;
  gap: 16px;
}
.payment {
  width: 100%;
  position: relative;
  background-color: #f9fafb;
  /* overflow: hidden; */
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  padding: 64px 0px 96px;
  box-sizing: border-box;
  text-align: left;
  font-size: 24px;
  color: #111928;
  font-family: Inter;
}
