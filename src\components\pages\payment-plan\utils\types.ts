export type PaymentFormData = {
  planID?: string;
  payNow?: boolean;
  customer?: {
    id: string;
    label: string;
    obj: any;
  };
  card?: {
    id: string;
    label: string;
    obj: any;
  };
  customerID: string;
  paymentID: string;
  planName: string;
  amount: number;
  startDate: string;
  paymentEvery: number;
  paymentInterval: string; // but it should be number
  endDate?: string;
  lineItems?: {
    name;
    description;
    amount;
    price;
    total;
    sku;
    metadata;
    discount;
  }[];
};
