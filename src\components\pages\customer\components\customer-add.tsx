// Temporarily disabled customer functionality
// import { useState } from 'react';
// import { <PERSON><PERSON>, Card, Modal, Tabs } from 'flowbite-react';
// import { FaPlus } from 'react-icons/fa';
// import { FormFormattedInput, FormInput } from '@/components/globals';
// import { SpinnerLoading } from '@/components/globals/spinner-loading';
// import { useLocationSelector } from '@/components/hooks';
// import { message, emailPatternRegex } from '@/components/shared/utils';
// import { Gateway_AddCustomerDocument } from '@/graphql/generated/graphql';
// import { useMutation } from '@apollo/client';
// import { useForm, FormProvider } from 'react-hook-form';
// import { toast } from 'react-toastify';
// import { CustomerFormData, customerFormDefaults } from '../utils';
// import FormPhoneNumber from '@/components/globals/form-phone-number/form-phonenumber.index';
// import StateCountryForms from '@/components/shared/components/stateCountryFormSelect';
// import { ACHHostedComponent } from '@/components/payments/ach-local';
// import { CardGLPHostedComponent } from '@/components/payments/glpv2';

// type CustomerAddProps = {
//   refetchListPage: () => void;
// };

// export const CustomerAdd = ({ refetchListPage }: CustomerAddProps) => {
//   const [addCustomerModal, setCustomerAddModal] = useState(false);
//   const [isReady, setIsReady] = useState(false);
//   const { locationSelectorElement, locationFilter } = useLocationSelector({
//     readonly: true,
//     onlyActive: true,
//   });
//   const [addCustomerMutation, { loading }] = useMutation(Gateway_AddCustomerDocument, {
//     onCompleted: (_) => {
//       refetchListPage();
//       toast.success(message.api.successCreate('Customer'));
//       setCustomerAddModal(false);
//     },
//     onError: (error) => {
//       toast.error(message.api.errorCreate('Customer', error.message));
//     },
//   });

//   const methods = useForm<CustomerFormData>({
//     defaultValues: { ...customerFormDefaults },
//   });

//   const onSubmitForm = async (data: CustomerFormData) => {
//     try {
//       // console.log({
//       //   data,
//       // });
//       if (data.processType === 'card' && !data.cardToken) {
//         return toast.error('Please enter a valid card number');
//       } else if (data.processType === 'ach' && !data.accountNumber) {
//         return toast.error('Please enter a valid account number');
//       } else if (data.processType === 'gpecomm' && !data.gpEcomm) {
//         return toast.error('Please enter a valid GPEComm ID');
//       }

//       await addCustomerMutation({
//         variables: {
//           input: {
//             data: {
//               form: {
//                 nameOnCard: data.nameOnCard,
//                 email: data.email,
//                 phone: data.phoneNumber,
//                 billingAddress: data.billingAddress,
//                 billingCity: data.city,
//                 billingState: data.state,
//                 billingZip: data.zipCode,
//                 billingCountry: data.country,
//                 card:
//                   data.processType === 'card'
//                     ? {
//                         cvc: data.cvc,
//                         expiryDate: data.expiryDate,
//                         cardToken: data.cardToken,
//                       }
//                     : undefined,
//                 ach:
//                   data.processType === 'ach'
//                     ? {
//                         accountNumber: data.accountNumber,
//                         routingNumber: data.routingNumber,
//                         accountType: data.accountType,
//                         accountHolderType: data.holderType,
//                       }
//                     : undefined,
//                 gpecomm: data.processType === 'gpecomm' ? { id: data.gpEcomm } : undefined,
//               },
//             },
//             groupID: locationFilter?.id ?? '',
//           },
//         },
//       });
//     } catch (e) {
//       console.error('Add Customer Mutation error: ', e);
//     }
//   };
//   return (
//     <div>
//       <Button
//         className="mt-[20px] h-[38px] p-0"
//         color="blue"
//         onClick={() => setCustomerAddModal(true)}
//       >
//         <div className="flex items-center gap-x-3">
//           <FaPlus className="text-xl" />
//           <span>Add Customer</span>
//         </div>
//       </Button>

//       <SpinnerLoading isLoading={loading} />
//       <Modal show={addCustomerModal} onClose={() => setCustomerAddModal(false)} size="5xl">
//         <Modal.Header className="flex items-center justify-between">
//           <h3 className="text-xl font-semibold text-blue-600">Add Customer</h3>
//         </Modal.Header>
//         <Modal.Body>
//           <FormProvider {...methods}>
//             <form onSubmit={methods.handleSubmit(onSubmitForm)} className="space-y-4">
//               <div className="w-full md:w-1/4">{locationSelectorElement}</div>
//               <div className="grid grid-cols-1 gap-3 md:grid-cols-5">
//                 <Card className="col-span-5 space-y-4">
//                   <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
//                     <div>
//                       <FormInput
//                         id="nameOnCard"
//                         name="nameOnCard"
//                         label="Name on Card"
//                         rules={{ required: message.requiredField }}
//                       />
//                       <FormInput
//                         id="email"
//                         name="email"
//                         label="Email"
//                         type="email"
//                         rules={{
//                           required: message.requiredField,
//                           pattern: {
//                             value: emailPatternRegex,
//                             message: message.emailPattern,
//                           },
//                         }}
//                       />
//                       {/* <FormFormattedInput
//                         id="phoneNumber"
//                         name="phoneNumber"
//                         label="Phone Number"
//                         mask="(*************"
//                         rules={{ required: message.requiredField }}
//                       /> */}
//                       <FormPhoneNumber
//                         id="phoneNumber"
//                         name="phoneNumber"
//                         label="Phone Number"
//                         country="us"
//                         preferredCountries={['us', 'ca']}
//                         rules={{ required: message.requiredField }}
//                       />
//                     </div>
//                     <div>
//                       <FormInput
//                         id="billingAddress"
//                         name="billingAddress"
//                         label="Billing Address"
//                         rules={{ required: message.requiredField }}
//                       />
//                       <StateCountryForms
//                         countryKey="country"
//                         stateKey="state"
//                         message={message}
//                         methods={methods}
//                       />
//                       <FormInput
//                         id="city"
//                         name="city"
//                         label="City"
//                         rules={{ required: message.requiredField }}
//                       />
//                       <FormFormattedInput
//                         id="zipCode"
//                         name="zipCode"
//                         label="Zip Code"
//                         mask="99999"
//                         tooltip={message.tooltip.zipCode}
//                         rules={{
//                           required: message.requiredField,
//                           maxLength: {
//                             value: 5,
//                             message: message.maxLength(5),
//                           },
//                         }}
//                       />
//                       {/* HIDE atm Nov. 3 */}
//                       {/* <FormSelect
//                         id="country"
//                         name="country"
//                         label="Country"
//                         rules={{ required: message.requiredField }}
//                         options={COUNTRIES}
//                       /> */}
//                     </div>
//                   </div>
//                 </Card>

//                 <div className="col-span-2 space-y-4">
//                   <Tabs
//                     onActiveTabChange={(index) => {
//                       methods.setValue('processType', index === 0 ? 'gpecomm' : 'ach');
//                     }}
//                   >
//                     {/* <Tabs.Item active title="Credit Card">
//                       <TSEPHostedTokenizerComponent
//                         // fieldErrors={fieldErrors}
//                         onEvent={(eventType, event) => {
//                           console.log(eventType, event);
//                         }}
//                         labels={{
//                           cardNumber: 'Card Number',
//                           expiryDate: 'Expiry Date',
//                           cvv: 'CVC',
//                           // cardHolderName: 'Card Holder Name',
//                           // zipCode: 'Zip Code',
//                         }}
//                         allowOptionals={{
//                           cvv: true,
//                           // zipCode: true,
//                           // cardHolderName: true,
//                         }}
//                         iframeComponentAttributes={{
//                           height: '230px',
//                         }}
//                         onToken={(token) => {
//                           // methods.setValue('fullName', token.cardHolderName);
//                           methods.setValue('cardToken', token.tsepToken);
//                           methods.setValue('cvc', token.cvv2);
//                           methods.setValue('expiryDate', token.expirationDate);
//                           setIsReady(true);
//                           // methods.setValue('zipCode', token.zipCode);
//                           // methods.setValue('brand', token.cardType);
//                           // console.log('token', token);
//                         }}
//                         onTokenError={() => {
//                           // console.error('error', error);
//                           // methods.setValue('fullName', '');
//                           methods.setValue('cardToken', '');
//                           methods.setValue('cvc', '');
//                           methods.setValue('expiryDate', '');
//                           // methods.setValue('zipCode', '');
//                           // methods.setValue('brand', '');
//                           // setVerifyResult(undefined);
//                         }}
//                       />
//                     </Tabs.Item> */}
//                     <Tabs.Item active title="Credit Card">
//                       <CardGLPHostedComponent
//                         merchantID={locationFilter?.id ?? ''}
//                         // fieldErrors={fieldErrors}
//                         onEvent={() => {
//                           // console.log(eventType, event);
//                         }}
//                         iframeComponentAttributes={{
//                           height: '230px',
//                         }}
//                         onToken={(token) => {
//                           // methods.setValue('fullName', token.cardHolderName);
//                           methods.setValue('gpEcomm', token.paymentID);
//                           setIsReady(true);
//                           // methods.setValue('zipCode', token.zipCode);
//                           // methods.setValue('brand', token.cardType);
//                           // console.log('token', token);
//                         }}
//                         onError={() => {
//                           methods.setValue('gpEcomm', '');
//                           setIsReady(false);
//                         }}
//                       />
//                     </Tabs.Item>
//                     <Tabs.Item title="ACH">
//                       <ACHHostedComponent
//                         onToken={(token) => {
//                           methods.setValue('accountNumber', token.accountNumber);
//                           methods.setValue('routingNumber', token.routingNumber);
//                           methods.setValue('accountType', token.accountType);
//                           methods.setValue('holderType', token.holderType);
//                           setIsReady(true);
//                         }}
//                         onError={() => {
//                           methods.setValue('accountNumber', '');
//                           methods.setValue('routingNumber', '');
//                           methods.setValue('accountType', '');
//                           methods.setValue('holderType', '');
//                         }}
//                       />
//                     </Tabs.Item>
//                   </Tabs>
//                 </div>
//               </div>
//               <div>
//                 <Button type="submit" color="blue" disabled={!isReady}>
//                   Add Customer
//                 </Button>
//               </div>
//             </form>
//           </FormProvider>
//         </Modal.Body>
//       </Modal>
//     </div>
//   );
export const CustomerAdd = () => {
  return null;
};
