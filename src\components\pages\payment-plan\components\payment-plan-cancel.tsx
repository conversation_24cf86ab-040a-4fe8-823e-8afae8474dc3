import { ConfirmDialog } from '@/components/globals';
import { message } from '@/components/shared/utils';
import { Gateway_CancelPaymentPlanDocument } from '@/graphql/generated/graphql';
import { useMutation } from '@apollo/client';
import { Button } from 'flowbite-react';
import React, { useState } from 'react';
import { FaInfoCircle } from 'react-icons/fa';
import { toast } from 'react-toastify';

type PaymentPlanCancelProp = {
  setIsDataUpdating: (value: boolean) => void;
  refetch: () => void;
  queryData: {
    planID: string;
    groupID: string;
  };
};

export const PaymentPlanCancel = ({
  queryData,
  refetch,
  setIsDataUpdating,
}: PaymentPlanCancelProp) => {
  const { planID, groupID } = queryData;
  const [confirmModal, setConfirmModal] = useState(false);

  const [cancelPaymentPlanMutation, { loading: cancelPlanIsLoading }] = useMutation(
    Gateway_CancelPaymentPlanDocument,
    {
      onCompleted: () => {
        toast.success('Payment plan is cancelled successfully');
        setIsDataUpdating(false);
        refetch();
        close();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Error message', error.message));
      },
    },
  );

  const handleCancelPlan = async () => {
    try {
      setConfirmModal(false);
      setIsDataUpdating(true);
      await cancelPaymentPlanMutation({
        variables: {
          input: {
            groupID,
            data: {
              planID,
            },
          },
        },
      });
    } catch (e) {
      console.error('Delete Customer Mutation error: ', e);
    }
  };

  return (
    <>
      <Button color="light" onClick={() => setConfirmModal(true)}>
        Cancel Plan
      </Button>
      <ConfirmDialog
        header={<FaInfoCircle color="info" className="w-full pl-[10px] text-center" />}
        body={
          <div className="flex w-full items-center justify-center">
            <h3 className="mb-5 text-center text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to cancel this Plan?
            </h3>
          </div>
        }
        actions={
          <div className="flex w-full items-center justify-center gap-4">
            <Button color="gray" onClick={() => setConfirmModal(false)}>
              No, cancel
            </Button>
            <Button color="failure" onClick={handleCancelPlan}>
              Yes, I'm sure
            </Button>
          </div>
        }
        open={confirmModal}
        setOpen={setConfirmModal}
      />
    </>
  );
};
