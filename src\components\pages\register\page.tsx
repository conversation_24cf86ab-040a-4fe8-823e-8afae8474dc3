/* eslint-disable @next/next/no-img-element */
'use client';

import { useBrowserSessionID } from '@/components/hooks/useSession';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Login, Register } from '@/graphql/declarations/auth';
import { generateOtp, verifyOtp } from '@/graphql/declarations/otpValues';
import { Otp_GenerateInputChannel } from '@/graphql/generated/graphql';
import { AUTHSTORE } from '@/lib/auth-storage';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { BriefcaseBusiness, MailIcon, PhoneIcon, UserIcon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import PhoneInput from 'react-phone-input-2';
import { toast } from 'react-toastify';

import 'react-phone-input-2/lib/bootstrap.css';
import { Checkbox } from 'flowbite-react';
import { getCookie } from '@/lib/cookie';

const Page1 = (props: {
  form: any;
  setForm: any;
  onContinue: () => void;
  error?: string;
  submitting?: boolean;
  preEmail?: boolean;
}) => {
  const [agreeToTermAndService, setAgreeToTermAndService] = useState(false);
  const redirectToLogin = () => {
    // Get the current URL's query parameters
    const queryParams = new URLSearchParams(window.location.search);

    // Construct the new URL for the register page
    const registerUrl = new URL('/login', window.location.origin);

    // Append each query parameter to the new URL
    queryParams.forEach((value, key) => {
      registerUrl.searchParams.append(key, value);
    });

    // Redirect to the new URL
    window.location.href = registerUrl.toString();
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="Logo" />
        </CardTitle>
        <CardDescription className="text-center">Register for an account</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <div className="space-y-2">
            <Label htmlFor="name">First Name</Label>
            <div className="relative">
              <UserIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="name"
                placeholder="John"
                type="text"
                className="pl-10"
                value={props.form.firstName}
                onChange={(e) => props.setForm({ ...props.form, firstName: e.target.value })}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="last-name">Last Name</Label>
            <div className="relative">
              <UserIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="last-name"
                placeholder="Doe"
                type="text"
                className="pl-10"
                value={props.form.lastName}
                onChange={(e) => props.setForm({ ...props.form, lastName: e.target.value })}
              />
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <div className="relative">
            <MailIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              disabled={props.preEmail}
              id="email"
              placeholder="<EMAIL>"
              type="email"
              className="pl-10"
              value={props.form.email}
              onChange={(e) => props.setForm({ ...props.form, email: e.target.value })}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <div className="relative">
            <PhoneIcon className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            {/* <Input
              id="phone"
              placeholder="****** 567 8900"
              type="text"
              className="pl-10"
              value={formatPhoneNumber(props.form.phone)}
              onChange={(e) => props.setForm({ ...props.form, phone: e.target.value })}
            /> */}
            <PhoneInput
              value={props.form.phone}
              inputClass="!py-2.5 !text-sm !w-full bg-gray"
              onChange={(phone) => {
                props.setForm({ ...props.form, phone });
              }}
              placeholder={`****** 567 8900`}
              countryCodeEditable={true}
              autoFormat={true}
              country={'us'}
              preferredCountries={['us', 'ca']}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="title">Title of User</Label>
          <div className="relative">
            <BriefcaseBusiness className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="title"
              placeholder="CEO, Owner, Developer"
              type="text"
              className="pl-10"
              value={props.form.title}
              onChange={(e) => props.setForm({ ...props.form, title: e.target.value })}
            />
          </div>
        </div>
        <div>
          <Checkbox
            id="remember-me"
            checked={agreeToTermAndService}
            onChange={(e) => setAgreeToTermAndService(e.target.checked)}
          />{' '}
          <Label htmlFor="remember-me">
            I agree to the{' '}
            <a href="/terms-and-services" className="text-blue-500">
              Terms of Service
            </a>{' '}
            of NGnair Payments
          </Label>
        </div>
        <Button
          className="w-full"
          onClick={() => {
            if (agreeToTermAndService) {
              props.onContinue();
            }
          }}
          disabled={props.submitting}
          variant="primary"
        >
          {props.submitting ? 'Submitting...' : 'Continue'}
        </Button>
        {
          // show error if there is an error
          props.error && (
            <p className="mx-auto -mt-2 text-center text-xs text-red-600">{props.error}</p>
          )
        }
      </CardContent>
      <CardFooter>
        <div className="w-full text-center text-sm text-muted-foreground">
          Already have an account?{' '}
          <button
            onClick={redirectToLogin}
            className="text-primary h-auto p-0 font-normal"
            color="blue"
          >
            Sign in
          </button>
        </div>
      </CardFooter>
    </>
  );
};

const Page2 = (props: {
  onVerify: (code: string) => void;
  onResend: () => void;
  onBack: () => void;
  error?: string;
  submitting?: boolean;
  pendingToResend?: number;
}) => {
  const [code, setCode] = useState(['', '', '', '', '', '']);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      // Move focus to the next input
      if (value !== '' && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="Logo" />
        </CardTitle>
        <CardDescription className="text-center">
          Enter the 6-digit code sent to your device
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="code-0" className="sr-only">
            2FA Code
          </Label>
          <div className="mx-auto flex max-w-xs justify-between">
            {code.map((digit, index) => (
              <Input
                key={index}
                id={`code-${index}`}
                type="text"
                inputMode="numeric"
                pattern="\d*"
                maxLength={1}
                className="h-12 w-12 text-center text-2xl"
                value={digit}
                onChange={(e) => handleCodeChange(index, e.target.value)}
              />
            ))}
          </div>
        </div>
        <Button
          className="w-full"
          type="submit"
          onClick={() => props.onVerify(code.join(''))}
          disabled={props.submitting}
        >
          {props.submitting ? 'Verifying...' : 'Verify'}
        </Button>
        {props.error && (
          <p className="mx-auto -mt-2 text-center text-xs text-red-600">{props.error}</p>
        )}
      </CardContent>
      <CardFooter className="-mt-4 flex flex-col space-y-2">
        {!props.pendingToResend ? (
          <Button variant="link" className="text-sm text-muted-foreground" onClick={props.onResend}>
            Resend code
          </Button>
        ) : (
          <p className="text-center text-sm text-muted-foreground">
            Resend code in {props.pendingToResend} seconds
          </p>
        )}
        <div className="text-center text-sm text-muted-foreground">
          <Button
            variant="link"
            className="text-primary h-auto p-0 font-normal"
            onClick={props.onBack}
          >
            Go Back
          </Button>
        </div>
      </CardFooter>
    </>
  );
};

const Page3 = (props: {
  form: any;
  setForm: any;
  onSignUp: () => void;
  error?: string;
  submitting?: boolean;
}) => {
  return (
    <>
      <CardHeader className="space-y-1">
        <CardTitle className="text-center text-2xl font-bold">
          <img src="/logo.webp" className="mx-auto h-12" alt="Logo" />
        </CardTitle>
        <CardDescription className="text-center">Finalize Your Account</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="password">Password</Label>
          <div className="relative">
            <BriefcaseBusiness className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="password"
              placeholder="Password"
              type="password"
              className="pl-10"
              value={props.form.password}
              onChange={(e) => props.setForm({ ...props.form, password: e.target.value })}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="confirm-password">Confirm Password</Label>
          <div className="relative">
            <BriefcaseBusiness className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              id="confirm-password"
              placeholder="Confirm Password"
              type="password"
              className="pl-10"
              value={props.form.confirmPassword}
              onChange={(e) =>
                props.setForm({
                  ...props.form,
                  confirmPassword: e.target.value,
                })
              }
            />
          </div>
        </div>
        <Button className="w-full" onClick={props.onSignUp} disabled={props.submitting}>
          {props.submitting ? 'Submitting...' : 'Sign Up'}
        </Button>
        {props.error && (
          <p className="mx-auto -mt-2 text-center text-xs text-red-600">{props.error}</p>
        )}
      </CardContent>
    </>
  );
};

export default function Component() {
  const [currentPage, setCurrentPage] = useState(1);
  const [form, setForm] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '+1',
    title: '',
  });
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const sidCode = useRef('');
  const [pendingToResend, setPendingToResend] = useState(0);
  const [preEmail, setPreEmail] = useState(false);

  const preloadEmail = async () => {
    // check query params if email is present
    const urlParams = new URLSearchParams(window.location.search);
    const email = urlParams.get('email');

    if (email) {
      setForm({ ...form, email });
      setPreEmail(true);
    }
  };

  useEffect(() => {
    preloadEmail();
  }, []);

  useEffect(() => {
    setError('');
  }, [form, currentPage]);

  useEffect(() => {
    setSubmitting(false);
  }, [currentPage]);

  useEffect(() => {
    if (pendingToResend) {
      const interval = setInterval(() => {
        setPendingToResend((prev) => (prev > 0 ? prev - 1 : 0));
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [pendingToResend]);

  const generateOTP = async () => {
    const response = await apolloClient.mutate({
      mutation: generateOtp,
      variables: {
        input: {
          channel: Otp_GenerateInputChannel.Sms,
          contact: form.phone,
        },
      },
    });

    if (response.data?.otp_generate?.sid) {
      setPendingToResend(60);
      sidCode.current = response.data.otp_generate.sid;
      return true;
    } else {
      return false;
    }
  };

  const page1Submit = async () => {
    setSubmitting(true);
    if (!form.email) {
      setSubmitting(false);
      setError('Email is required');
      return;
    }

    // get phone number, remove all non-numeric characters, remain the '+' and numbers
    form.phone = '+' + form.phone.replace(/\D/g, '');

    // Skip OTP verification - go directly to password setup
    try {
      // Mock OTP generation for compatibility
      sidCode.current = 'mock-sid-' + Date.now();
      setSubmitting(false);
      setCurrentPage(3); // Skip page 2 (OTP) and go directly to page 3 (password)
    } catch (e) {
      setSubmitting(false);
      setError('An error occurred. Please try again.');
    }
  };

  const rerequestOtp = async () => {
    try {
      setSubmitting(true);
      const otpStatus = await generateOTP();

      if (otpStatus) {
        setSubmitting(false);
        setPendingToResend(60);
      } else {
        setSubmitting(false);
        setError('Provided phone number is invalid. Please use another phone number');
      }
    } catch (e) {
      setSubmitting(false);
      setError('Provided phone number is invalid. Please use another phone number');
    }
  };

  const page2Submit = async (code: string) => {
    if (code.length !== 6) {
      setError('Invalid code. Please enter a 6-digit code');
      return;
    }

    if (!sidCode.current) {
      setError('SID Code missing. Please try again');
      return;
    }

    setSubmitting(true);

    const response = await apolloClient.mutate({
      mutation: verifyOtp,
      variables: {
        input: {
          sid: sidCode.current,
          code,
        },
      },
    });

    if (response.data?.otp_verify?.status) {
      setSubmitting(false);
      setCurrentPage(3);
    } else {
      setSubmitting(false);
      setError('Invalid code. Please try again');
    }
  };

  const { id: browserID } = useBrowserSessionID();

  const page3Submit = async () => {
    if (form.password !== form.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    form.phone = '+' + form.phone.replace(/\D/g, '');

    const affiliateCode = getCookie('affiliateCode');

    setSubmitting(true);
    const resp = await apolloClient.mutate({
      mutation: Register,
      variables: {
        email: form.email,
        password: form.password,
        otpSid: sidCode.current,
        phoneNumber: form.phone,
        firstName: form.firstName,
        lastName: form.lastName,
        browserId: browserID,
        title: form.title,
        affiliateId: affiliateCode,
      },
    });

    console.log('successfully registered');

    if (!resp.data?.authclient_register) {
      toast.error('An error occurred while registering');
      return;
    }

    const auth = await apolloClient.mutate({
      mutation: Login,
      variables: {
        email: form.email,
        password: form.password,
        browserId: browserID,
      },
    });

    if (
      !(auth.data?.authclient_login?.__typename === 'ClientItemAuthenticationWithPasswordSuccess')
    ) {
      toast.error('An error occurred while logging in');
      return;
    }

    AUTHSTORE.set(auth.data?.authclient_login?.sessionToken as string);

    // check if there's a redirect query param
    const searchParams = new URLSearchParams(window.location.search);
    const redirect = searchParams.get('redirect');
    if (redirect) {
      window.location.href = redirect;
      return;
    } else {
      // console.log("redirecting to dashboard");
      window.location.href = '/dashboard';
      // router.replace("/dashboard");
      // console.log(window.location.href);
    }

    setSubmitting(false);
  };

  return (
    <div className="from-primary/20 to-secondary/20 flex min-h-screen items-center justify-center bg-gradient-to-br p-4">
      <Card className="w-full max-w-md border shadow-xl">
        {currentPage === 1 && (
          <Page1
            form={form}
            setForm={setForm}
            onContinue={() => {
              page1Submit();
            }}
            error={error}
            submitting={submitting}
            preEmail={preEmail}
          />
        )}

        {currentPage === 3 && (
          <Page3
            form={form}
            setForm={setForm}
            onSignUp={() => {
              page3Submit();
            }}
            error={error}
            submitting={submitting}
          />
        )}
      </Card>
    </div>
  );
}
