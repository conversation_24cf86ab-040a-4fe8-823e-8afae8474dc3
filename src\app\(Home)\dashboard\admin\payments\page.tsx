'use client';

import { useLocationSelector } from '@/components/hooks';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { GET_GROUPS_LIST } from '@/graphql/declarations/group';
import { UpdateGroupDocument } from '@/graphql/generated/graphql';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { useQuery } from '@apollo/client';
import { ChevronDown, ChevronUp, InfoIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { SurchargeCalculator } from '@/components/SurchargeCalculator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

export default function Component() {
  const [transactionOpen, setTransactionOpen] = useState(true);
  const [customerOpen, setCustomerOpen] = useState(true);
  const [surchargeOpen, setSurchargeOpen] = useState(false);

  const { locationFilter, locationSelectorElement } = useLocationSelector({
    onlyActive: true,
  });

  const { data: groupData } = useQuery(GET_GROUPS_LIST, {
    variables: {
      where: {
        id: { equals: locationFilter?.id ?? '' },
      },
    },
    skip: !locationFilter?.id,
  });

  const [settings, setSettings] = useState({
    customersPayPlatformSurcharge: false,
    default_globalEnableCC: false,
    default_globalEnableACH: false,
  });

  useEffect(() => {
    if (!groupData) return;
    const group = groupData.groups?.[0];
    if (!group) return;

    setSettings({
      customersPayPlatformSurcharge: group.default_includeSurcharge || false,
      default_globalEnableCC: !(group.default_globalDisableCC || false),
      default_globalEnableACH: !(group.default_globalDisableACH || false),
    });
  }, [groupData]);

  const handleSave = async () => {
    try {
      if (!locationFilter?.id) return;

      await apolloClient.mutate({
        mutation: UpdateGroupDocument,
        variables: {
          where: {
            id: locationFilter?.id,
          },
          data: {
            default_includeSurcharge: settings.customersPayPlatformSurcharge,
            default_globalDisableCC: !settings.default_globalEnableCC,
            default_globalDisableACH: !settings.default_globalEnableACH,
          },
        },
      });

      toast.success('Payment settings saved successfully.');
      await apolloClient.refetchQueries({
        include: ['getGroupList'],
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Error saving settings. Please try again.');
    }
  };

  return (
    <div className="w-full space-y-4 py-0">
      <div className="w-1/4">{locationSelectorElement}</div>

      <Collapsible open={transactionOpen} onOpenChange={setTransactionOpen}>
        <Card>
          <CollapsibleTrigger asChild>
            <div className="flex cursor-pointer items-center justify-between p-6">
              <h3 className="text-lg font-semibold">Manual Entry and Transaction Settings</h3>
              {transactionOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <table className="w-full">
                <thead>
                  <tr className="border-b text-left">
                    <th className="pb-2">POLICY</th>
                    <th className="pb-2"></th>
                    {/* <th className="pb-2">REQUIRED</th> */}

                    {/* // NOTE: comment this atm, cleanup before summit */}
                    {/* <th className="pb-2">INPUT VALUE</th> */}
                  </tr>
                </thead>
                <tbody>
                  {[
                    // NOTE: comment this atm, cleanup before summit
                    // { name: 'Tip at Time of Sale', hasInfo: true },
                    // { name: 'Virtual Terminal Tax', hasInfo: true },
                    {
                      id: 'customersPayPlatformSurcharge',
                      name: 'Enable Customer Surcharge',
                      description:
                        'If enabled, customers will pay the platform surcharge. Check the Surcharge Breakdown section for more details.',
                    },
                    {
                      id: 'default_globalEnableCC',
                      name: 'Enable Credit Card Payments',
                      description:
                        'If enabled, credit card payments will be accepted for this location.',
                    },
                    {
                      id: 'default_globalEnableACH',
                      name: 'Enable ACH Payments',
                      description: 'If enabled, ACH payments will be accepted for this location.',
                    },
                    // {
                    //   name: 'Convenience Fee',
                    //   hasInfo: true,
                    //   inputType: 'currency',
                    //   placeholder: '0.00',
                    // },
                    // {
                    //   name: 'Surcharge',
                    //   hasInfo: true,
                    //   inputType: 'percentage',
                    //   placeholder: '0.00',
                    // },
                  ].map((setting, index) => (
                    <tr key={index} className="border-b last:border-b-0">
                      <td className="flex items-center py-4">
                        {setting.name}
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <InfoIcon className="ml-2 h-4 w-4 text-gray-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{setting.description}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </td>
                      {/* <td className="py-4">
                        <Checkbox />
                      </td> */}
                      <td className="py-4">
                        <Switch
                          checked={settings[setting.id]}
                          onCheckedChange={(v) =>
                            setSettings((prev) => ({
                              ...prev,
                              [setting.id]: v,
                            }))
                          }
                        />
                      </td>

                      {/* // NOTE: comment this atm, cleanup before summit */}
                      {/* <td className="py-4">
                        {setting.inputType && (
                          <div className="flex items-center">
                            <Label className="mr-2">
                              {setting.inputType === 'currency' ? '$' : '%'}
                            </Label>
                            <Input type="text" placeholder={setting.placeholder} className="w-24" />
                          </div>
                        )}
                      </td> */}
                    </tr>
                  ))}
                </tbody>
              </table>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>
      <Collapsible open={surchargeOpen} onOpenChange={setSurchargeOpen}>
        <Card>
          <CollapsibleTrigger asChild>
            <div className="flex cursor-pointer items-center justify-between p-6">
              <h3 className="text-lg font-semibold">Surcharge Breakdown</h3>
              {surchargeOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="space-y-6">
                <Alert variant="default" className="border-yellow-200 bg-yellow-50">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    <p className="mb-2">
                      Important: Some states and jurisdictions prohibit or regulate surcharge fees.
                      It is the merchant's responsibility to verify and comply with local laws
                      before enabling surcharges. NGnair shall not be held liable for any
                      non-compliance with applicable regulations.
                    </p>
                    <p className="mb-2 text-sm">Currently affected locations as of 2024:</p>
                    <ul className="list-disc space-y-1 pl-5 text-sm">
                      <li>
                        US States: Colorado, Connecticut, Kansas, Maine, Massachusetts (special
                        case: allowed only for credit cards, not debit)
                      </li>
                      <li>
                        Countries: Australia (regulated caps apply), India (prohibited), Malaysia,
                        China
                      </li>
                    </ul>
                    <p className="mt-2 text-xs italic">
                      Note: Regulations are subject to change without notice. This list may not be
                      comprehensive and merchants should conduct their own due diligence.
                    </p>
                  </AlertDescription>
                </Alert>
                <div>
                  <h4 className="mb-2 font-semibold">Credit Card Payments</h4>
                  <ul className="list-disc space-y-2 pl-6">
                    <li>Flat rate of 3% per transaction</li>
                    <li>Applies to all card brands (Visa, Mastercard, American Express, etc.)</li>
                    <li>
                      Example: For a $100 transaction:
                      <ul className="mt-1 list-none pl-6">
                        <li>Surcharge = $100 × 3%</li>
                        <li>Surcharge = $3</li>
                        <li>Total amount = $103</li>
                      </ul>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="mb-2 font-semibold">ACH Payments</h4>
                  <ul className="list-disc space-y-2 pl-6">
                    <li>0.6% surcharge rate up to $1,667</li>
                    <li>Discounted surcharge rate of 0.1% fee for extra amounts beyond $1,667</li>
                    <li>
                      Example for $2,000 transaction:
                      <ul className="mt-1 list-none pl-6">
                        <li>Total surcharge: $10.33</li>
                        <li>*After calculation of the base then discounted surcharge</li>
                      </ul>
                    </li>
                  </ul>
                </div>

                <SurchargeCalculator />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>
      <div className="mt-6 flex justify-end">
        <Button variant={'primary'} onClick={handleSave}>
          Save Changes
        </Button>
      </div>

      {/* // NOTE: comment this atm, cleanup before summit */}
      {/* <Collapsible open={customerOpen} onOpenChange={setCustomerOpen}>
        <Card>
          <CollapsibleTrigger asChild>
            <div className="flex cursor-pointer items-center justify-between p-6">
              <h3 className="text-lg font-semibold">Customer Details</h3>
              {transactionOpen ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </div>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <table className="w-full">
                <thead>
                  <tr className="border-b text-left">
                    <th className="pb-2">POLICY</th>
                    <th className="pb-2">ENABLE</th>
                    <th className="pb-2">REQUIRED</th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    { name: 'Company Name', hasInfo: true },
                    { name: 'Address', hasInfo: true },
                    { name: 'Address 2', hasInfo: true },
                    { name: 'City', hasInfo: true },
                    { name: 'State', hasInfo: true },
                    { name: 'Zip', hasInfo: true },
                    { name: 'Country', hasInfo: true },
                    { name: 'Phone', hasInfo: true },
                    { name: 'Email', hasInfo: true },
                  ].map((setting, index) => (
                    <tr key={index} className="border-b last:border-b-0">
                      <td className="flex items-center py-4">
                        {setting.name}
                        {setting.hasInfo && <InfoIcon className="ml-2 h-4 w-4 text-gray-500" />}
                      </td>
                      <td className="py-4">
                        <Checkbox />
                      </td>
                      <td className="py-4">
                        <Checkbox />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible> */}
    </div>
  );
}
