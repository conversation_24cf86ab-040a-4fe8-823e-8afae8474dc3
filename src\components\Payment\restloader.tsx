import PaymentForm, { PaymentFormProps } from './index';
import { useEffect, useState } from 'react';
import { axiosClient } from '@/lib/axios';
import useDebounce from '../hooks/useDebounce';

export namespace ProductComputeResponse {
  export interface ProductComputeResponse {
    breakdown: Breakdown;
    lineItems: LineItem[];
    discountBreakdown: DiscountBreakdown[];
    allowEdit: boolean;
    allowExtraDiscount: boolean;
    disableACH: boolean;
    disableCard: boolean;
    paymentInput: PaymentInput;
    surcharge: number;
    meta: Meta;
    referenceID: string;
    onSuccessUrl: string;
    onFailureUrl: string;
    customerID: string;
    customerData: CustomerData;
    allowTip: boolean;
    customerPaymentID: string;
    prefilledAddress: PrefilledAddress;
    disableCustomAddress: boolean;
    disableCustomPayment: boolean;
    disablePreselectCard: boolean;
    transactionHistory: {
      transactionID: string;
      date: string;
      amount: number;
      status: string;
      note: string;
    }[];
    overideLineItemsAmount: boolean;
  }

  export interface Breakdown {
    discount: number;
    directDiscount: number;
    actualDiscount: number;
    tax: number;
    shipping: number;
    shippingDiscount: number;
    shippingDirectDiscount: number;
    shippingActualDiscount: number;
    fees: number;
    actualFees: number;
    tip: number;
    subtotal: number;
    subscriptionTotal: number;
    rawTotal: number;
    total: number;
    expectedTotal: number;
  }

  export interface CustomerData {
    id: string;
    nameOnCard: string;
    email: string;
    phone: string;
    billingAddress: string;
    billingCity: string;
    billingState: string;
    billingZip: string;
    billingCountry: string;
    paymentCards: PaymentCard[];
  }

  export interface PaymentCard {
    cardID: string;
    type: string;
    isDefault: boolean;
    last4: string;
    brand: string;
    expires: string;
    cvc: string;
    accountNumber: string;
    routingNumber: string;
    accountType: string;
    accountHolderType: string;
    gpEcommID: string;
  }

  export interface DiscountBreakdown {
    code: string;
    amount: number;
  }

  export interface LineItem {
    productId: string;
    product: Product;
    amount: number;
    total: number;
    metadata: string;
  }

  export interface Product {
    sku: string;
    name: string;
    description: string;
    price: number;
    isRecurring: boolean;
    recurringMode: string;
    recurringInterval: number;
    recurringFrequency: number;
    recurringTotalCycles: number;
    recurringTrialDays: number;
    recurringSetupFee: number;
    recurringRefundable: boolean;
  }

  export interface Meta {
    includeSurcharge: boolean;
    encryptedCheckoutToken: string;
    encrptedCheckoutTokenSecret: string;
    dynamic: Dynamic;
    reference: Reference;
  }

  export interface Dynamic {
    discountCodes: string[];
    paymentType: string;
    tip: Tip;
    quantityAmounts: QuantityAmount[];
  }

  export interface QuantityAmount {
    id: string;
    quantity: number;
  }

  export interface Tip {
    amount: number;
    type: string;
  }

  export interface Reference {
    id: string;
    source: string;
  }

  export interface PaymentInput {
    methodVerifyOrProcess: string;
    lineItems: LineItem[];
    amount: number;
    tip: number;
    tipType: string;
    tax: number;
    taxType: string;
    discountCodes: string[];
    shipping: number;
  }

  export interface PrefilledAddress {
    email: string;
    phone: string;
    country: string;
    state: string;
    city: string;
    zip: string;
    address: string;
    nameOnCard: string;
  }
}

export namespace ProductInput {
  export interface ProductInput {
    groupID: string;
    token?: string | null;
    data: Data;
  }

  export interface Data {
    paymentData?: string;
    paymentDataID?: string;
    dynamicData: DynamicData;
  }

  export interface DynamicData {
    discountCodes: string[];
    quantityAmounts: QuantityAmount[];
  }

  export interface QuantityAmount {
    id: string;
    quantity: number;
  }
}

export const LoaderPaymentForm = (props: {
  input: ProductInput.ProductInput;
  customPaymentFunction?: PaymentFormProps['customPaymentFunction'];
  onAfterPayment?: PaymentFormProps['onAfterPayment'];
  onBeforePayment?: PaymentFormProps['onBeforePayment'];
  initialValues?: PaymentFormProps['initialValues'];
}) => {
  const [customDiscountCodes, setCustomDiscountCodes] = useState<string[]>([]);
  const [paymentType, setPaymentType] = useState('gpecomm');

  const [opts, setOpts] = useState({
    allowEdit: false,
    allowExtraDiscount: false,
    allowTip: false,
    onSuccessUrl: '',
    onFailureUrl: '',
    disabledCC: false,
    disabledACH: false,
  });

  const [lineItems, setLineItems] = useState<PaymentFormProps['initialLineItems']>([]);
  const [discountBreakdown, setDiscountBreakdown] = useState<PaymentFormProps['initialDiscounts']>(
    [],
  );
  const [breakdown, setBreakdown] = useState<PaymentFormProps['initialCalculations']>({});
  const [tipAmount, setTipAmount] = useState(0);
  const [customAmounts, setCustomAmounts] = useState<{ [key: string]: number }>({});
  const [total, setTotal] = useState(0);
  const [subscriptionTotal, setSubscriptionTotal] = useState(0);

  // New state for customer data
  const [customerData, setCustomerData] = useState<ProductComputeResponse.CustomerData | null>(
    null,
  );
  const [customerID, setCustomerID] = useState<string | null>(null);
  const [customerPaymentID, setCustomerPaymentID] = useState<string | null>(null);
  const [prefilledAddress, setPrefilledAddress] =
    useState<ProductComputeResponse.PrefilledAddress | null>(null);
  const [disableCustomAddress, setDisableCustomAddress] = useState(false);
  const [disableCustomPayment, setDisableCustomPayment] = useState(false);
  const [disablePreselectCard, setDisablePreselectCard] = useState(false);
  const [overideLineItemsAmount, setOverideLineItemsAmount] = useState(false);
  const [transactionHistory, setTransactionHistory] = useState<any>([]);

  const debounctedTipAmount = useDebounce(tipAmount, 500);
  const debouncedCustomAmounts = useDebounce(customAmounts, 500);

  const loadCheckoutPrice = async () => {
    const uniqueDiscountCodes = [
      ...Array.from(
        new Set([...props.input.data.dynamicData.discountCodes, ...customDiscountCodes]),
      ),
      new Date().getTime().toString(),
    ];

    // if payment type is blank, return
    if (!paymentType) {
      return {
        data: {
          allowEdit: false,
          allowExtraDiscount: false,
          allowTip: false,
          onSuccessUrl: '',
          onFailureUrl: '',
          lineItems: [],
          discountBreakdown: [],
          breakdown: {
            subtotal: 0,
            discount: 0,
            tax: 0,
            shipping: 0,
            fees: 0,
            tip: 0,
            subscriptionTotal: 0,
            expectedTotal: 0,
            total: 0,
          },
        },
      };
    }

    const data = await axiosClient.post<ProductComputeResponse.ProductComputeResponse>(
      '/api/gateway/computeTokenizedCheckout',
      {
        ...props.input,
        data: {
          ...props.input.data,
          dynamicData: {
            paymentType: paymentType,
            discountCodes: uniqueDiscountCodes,
            quantityAmounts: Object.keys(debouncedCustomAmounts).map((key) => ({
              id: key,
              quantity: debouncedCustomAmounts[key],
            })),
            tip: {
              amount: debounctedTipAmount,
              type: 'percentage',
            },
          },
        },
      } as ProductInput.ProductInput,
    );

    // Set customer data from response
    if (data.data.customerData) {
      setCustomerData(data.data.customerData);
    }

    if (data.data.customerID) {
      setCustomerID(data.data.customerID);
    }

    if (data.data.customerPaymentID) {
      setCustomerPaymentID(data.data.customerPaymentID);
    }

    if (data.data.prefilledAddress) {
      setPrefilledAddress(data.data.prefilledAddress);
    }

    setDisableCustomAddress(data.data.disableCustomAddress || false);
    setDisableCustomPayment(data.data.disableCustomPayment || false);
    setDisablePreselectCard(data.data.disablePreselectCard || false);
    setOverideLineItemsAmount(data.data.overideLineItemsAmount || false);
    setTransactionHistory(data.data.transactionHistory || []);

    setOpts({
      allowEdit: data.data.allowEdit,
      allowExtraDiscount: data.data.allowExtraDiscount,
      allowTip: data.data.allowTip,
      onSuccessUrl: data.data.onSuccessUrl || '',
      onFailureUrl: data.data.onFailureUrl || '',
      disabledCC: data.data.disableCard || false,
      disabledACH: data.data.disableACH || false,
    });

    return data;
  };

  const prepareData = async () => {
    const data = await loadCheckoutPrice();

    setLineItems(
      data.data.lineItems.map((item) => ({
        id: item.productId || item.product.sku,
        name: item.product.name,
        description: item.product.description,
        amount: item.amount,
        total: item.total,
        price: item.product.price,
        isRecurring: item.product.isRecurring,
        recurringMode: item.product.recurringMode,
        recurringFrequency: item.product.recurringFrequency,
        recurringInterval: item.product.recurringInterval,
        recurringSetupFee: item.product.recurringSetupFee,
        recurringTotalCycles: item.product.recurringTotalCycles,
        recurringTrialDays: item.product.recurringTrialDays,
      })),
    );

    setDiscountBreakdown(
      data.data.discountBreakdown.map((discount) => ({
        code: discount.code,
        amount: discount.amount,
      })),
    );

    setBreakdown({
      'Original Price': data.data.breakdown.subtotal,
      Discount: data.data.breakdown.discount,
      Tax: data.data.breakdown.tax,
      Shipping: data.data.breakdown.shipping,
      'Processing Surcharge': data.data.breakdown.fees,
      Tip: data.data.breakdown.tip,
    });

    setTotal(data.data.breakdown.total);
    setSubscriptionTotal(data.data.breakdown.subscriptionTotal);
  };

  useEffect(() => {
    prepareData();
  }, [customDiscountCodes, debounctedTipAmount, debouncedCustomAmounts, paymentType]);

  // Prepare initial values from prefilled address or customer data
  const getInitialValues = () => {
    if (customerData) {
      // Use customer data if available
      return {
        fullName: customerData.nameOnCard || '',
        email: customerData.email || '',
        phoneNumber: customerData.phone || '',
        street: customerData.billingAddress || '',
        city: customerData.billingCity || '',
        state: customerData.billingState || 'AZ',
        country: customerData.billingCountry || 'US',
        zipCode: customerData.billingZip || '',
      };
    } else if (prefilledAddress) {
      // Use prefilled address if available
      return {
        fullName: prefilledAddress.nameOnCard || '',
        email: prefilledAddress.email || '',
        phoneNumber: prefilledAddress.phone || '',
        street: prefilledAddress.address || '',
        city: prefilledAddress.city || '',
        state: prefilledAddress.state || 'AZ',
        country: prefilledAddress.country || 'US',
        zipCode: prefilledAddress.zip || '',
      };
    }

    // Otherwise use any provided initial values
    return props.initialValues;
  };

  return (
    <>
      <PaymentForm
        initialValues={getInitialValues()}
        onAfterPayment={props.onAfterPayment}
        onBeforePayment={props.onBeforePayment}
        customPaymentFunction={props.customPaymentFunction}
        paymentData={props.input}
        initialLineItems={lineItems}
        initialDiscounts={discountBreakdown}
        initialCalculations={breakdown}
        initialTotal={total}
        initialSubscriptionTotal={subscriptionTotal}
        onSetDiscounts={(discounts) => {
          setCustomDiscountCodes(discounts.map((discount) => discount.code));
        }}
        onTipChange={(tip) => {
          setTipAmount(tip);
        }}
        customAmounts={customAmounts}
        setCustomAmounts={setCustomAmounts}
        options={{
          allowEdit: customerID && customerPaymentID ? false : opts.allowEdit,
          allowExtraDiscount: opts.allowExtraDiscount,
          allowTip: opts.allowTip,
          disabledCC: opts.disabledCC,
          disabledACH: opts.disabledACH,
          onSuccessUrl: opts.onSuccessUrl,
          onFailureUrl: opts.onFailureUrl,
          disableCustomAddress: disableCustomAddress,
          customerData: customerData || undefined,
          customerID: customerID || undefined,
          customerPaymentID: customerPaymentID || undefined,
          disableCustomPayment: disableCustomPayment,
          disablePreselectCard: disablePreselectCard,
          overideLineItemsAmount: overideLineItemsAmount,
          transactionHistory: transactionHistory,
        }}
        discountCodes={customDiscountCodes}
        tipAmount={tipAmount}
        paymentType={paymentType}
        setPaymentType={setPaymentType}
      />
    </>
  );
};
