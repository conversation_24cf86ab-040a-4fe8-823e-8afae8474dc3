import { graphql } from '../generated';

export const processorTestDraftCreate = graphql(`
  mutation processorTestDraftCreate($input: Processor_tst_draft_createInput!) {
    processor_tst_draft_create(input: $input) {
      testID
      groupID
    }
  }
`);

export const processorTestDraftUpdate = graphql(`
  mutation processorTestDraftUpdate($input: Processor_tst_draft_updateInput!) {
    processor_tst_draft_update(input: $input) {
      testID
      groupID
    }
  }
`);

export const processorTestDraftSubmit = graphql(`
  mutation processorTestDraftSubmit($input: Processor_tst_draft_submitInput!) {
    processor_tst_draft_submit(input: $input) {
      urlForSigning
      applicationId
      applicationNumber
    }
  }
`);

export const processorTestDraftGet = graphql(`
  query Group($where: GroupWhereUniqueInput!) {
    group(where: $where) {
      id
      processorTST {
        applicationID
        applicationNumber
        id
        processor_tst_signingUrl
        processor_tst_applicationStatus {
          businessInfo {
            legalBusinessName
            typeOfBusiness
            dbaName
            ein
            dateBusinessEstablished
            businessEmail
            businessPhone
            website
            customerServicePhone
            street
            zipCode
            city
            state
            country
            differentLegalAddress
            legalMailingStreet
            legalMailingZipCode
            legalMailingCity
            legalMailingState
            legalMailingCountry
          }
          transactionInfo {
            businessCategory
            description
            swipe
            keyed
            ecommerce
            avgTransactionAmount
            highestTransactionAmount
            grossMonthlySalesVolume
            amexAvgTransactionAmount
            amexHighestTransactionAmount
            amexGrossMonthlySalesVolume
          }
          owners {
            isControlOwner
            firstName
            lastName
            title
            ownershipPercentage
            phoneNumber
            homeAddress
            country
            state
            city
            zipCode
            dateOfBirth
            ssn
            email
          }
          files {
            name
            category
            mimetype
            b64
          }
          bankInfo {
            routingNumber
            accountNumber
            bankName
            nameOnAccount
          }
        }
      }
    }
  }
`);

export const processorTestDraftZipSearch = graphql(`
  query processorTestDraftZipSearch($input: Processor_tst_zip_searchInput!) {
    processor_tst_zip_search(input: $input) {
      item {
        zip
        latitude
        longitude
        city
        state
        country
      }
    }
  }
`);

export const processorTestDraftMCC = graphql(`
  query processorTestDraftMCC($input: Processor_tst_mccInput!) {
    processor_tst_mcc(input: $input) {
      items {
        id
        description
      }
      count
    }
  }
`);

export const GET_BANK_INFO_BY_ROUTING = graphql(`
  query processorTestDraftBankRouting($input: Processor_tst_bank_routingInput!) {
    processor_tst_bank_routing(input: $input) {
      data {
        rn
        name
      }
    }
  }
`);
