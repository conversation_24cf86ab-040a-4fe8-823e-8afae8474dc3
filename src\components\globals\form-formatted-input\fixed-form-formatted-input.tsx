import { ChangeEvent, useCallback } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

export type FormFormattedInputProps = {
  name: string;
  helperText?: string;
  id: string;
  mask: string;
  formatChars?: { [key: string]: RegExp };
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  className?: string;
  visible?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  placeholder?: string;
  min?: number | string;
  max?: number | string;
  flex?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLInputElement>) => void;
  variant?: 'primary' | 'secondary';
  label?: string;
};

const defaultFormatChars = {
  '9': /\d/,
  a: /[a-zA-Z]/,
  '*': /[a-zA-Z0-9]/,
};

export const FormFormattedInput = ({
  id,
  name,
  label,
  rules,
  helperText = '',
  visible = true,
  readOnly = false,
  maxLength,
  placeholder,
  min,
  max,
  flex,
  tooltip,
  onChangeCallback = (e) => {},
  variant = 'primary',
  className,
  mask,
  formatChars = defaultFormatChars,
  ...props
}: FormFormattedInputProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired } = useFormFieldHelpers({ rules });

  const formatValue = useCallback(
    (value: string, mask: string): string => {
      let formatted = '';
      let rawIdx = 0;

      // Remove any non-raw characters based on formatChars
      const rawValue = value.replace(/[^a-zA-Z0-9]/g, '');

      for (let i = 0; i < mask.length && rawIdx < rawValue.length; i++) {
        const maskChar = mask[i];
        const format = formatChars[maskChar];

        if (format) {
          // This is a position that needs validation
          while (rawIdx < rawValue.length) {
            const char = rawValue[rawIdx];
            rawIdx++;
            if (format.test(char)) {
              formatted += char;
              break;
            }
          }
        } else {
          // This is a static mask character
          formatted += maskChar;
        }
      }

      return formatted;
    },
    [formatChars],
  );

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue=""
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => {
        const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
          const newValue = formatValue(e.target.value, mask);
          onChange(newValue);
          onChangeCallback(e);
        };

        return (
          <div
            className={`relative w-full ${visible ? '' : 'hidden'} ${flex ? `flex-${flex}` : ''}`}
          >
            {label && (
              <Label htmlFor={id} className="mb-2 flex">
                {label} {isRequired() && '*'}
                {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
              </Label>
            )}
            <div className="relative w-full">
              <Input
                id={id}
                value={value || ''}
                onChange={handleChange}
                placeholder={placeholder}
                ref={ref}
                onBlur={onBlur}
                readOnly={readOnly}
                maxLength={maxLength}
                min={min}
                max={max}
                className={cn(className, invalid && 'border-red-500')}
                {...props}
              />
            </div>
            <HelperText color={invalid ? 'failure' : 'default'}>
              {invalid ? error?.message : helperText}
            </HelperText>
          </div>
        );
      }}
    />
  );
};
