import { format } from 'date-fns';

type FormatOptions = {
  /** <PERSON>ps handling of 1970 dates. If `true` dates from year 1970 will be returned. When falsy, returning empty string if year is 1970. */
  skip1970Handling?: boolean;
};

export enum DateFormat {
  LOCAL_DATE_WITH_DASH = 'yyyy-MM-dd',
  LOCAL_DATE_WITH_UNDERSCORE = 'dd_MM_yyyy',
  LOCAL_DATE_TIME_WITH_DASH = 'yyyy-MM-dd HH:mm',
  LOCAL_DATE_WITH_SPACE = 'dd MMM yyyy',
  LOCAL_DATE_TIME_WITH_SPACE = 'dd MMM yyyy HH:mm',
  LOCAL_DATE_TIME_WITH_DOT = 'dd.MM.yyyy HH:mm',
  NON_DELIMITED_DATE_TIME_STR_FORMAT = 'ddMMyyyy',
}

/**
 * Format a Date & time object a string
 *
 * @param date The `Date and time` object. If `null` or `undefined`, this function will return an empty string.
 */
export const formatToLocalDateAndTimeString = (date: Date | null | undefined): string => {
  try {
    return date ? format(date, "yyyy-MM-dd'T'HH:mm:ss") : '';
  } catch (e) {
    console.warn('Error formatting date:', e);
    return '';
  }
};

/**
 * Formats a `Date` object into a string that can be parsed in the backend as `LocalDate`.
 *
 * @param date The `Date` object. If `null` or `undefined`, this function will return an empty string.
 * @param dateFormat Enums date format. If `null` or `undefined` default is LOCAL_DATE_TIME_WITH_SPACE,
 */
export const formatToLocalDateString = (
  date: Date | null | undefined,
  dateFormat?: DateFormat,
): string => {
  try {
    if (dateFormat) return date ? format(date, dateFormat) : '';
    return date ? format(date, DateFormat.LOCAL_DATE_TIME_WITH_SPACE) : '';
  } catch (e) {
    console.warn('Error formatting date:', e);
    return '';
  }
};

export const formatDateStringUnstable = (
  date: Date | string | number | null | undefined,
  dateFormat?: DateFormat,
): string => {
  // date can either be date, string or number, or number on string
  if (!date) {
    return '';
  }

  // if date is string with number, convert to number
  if (typeof date === 'string' && !isNaN(Number(date))) {
    // @ts-ignore
    date = Number(date);
  }

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  return format(dateObj, dateFormat ?? DateFormat.LOCAL_DATE_TIME_WITH_SPACE);
};

/**
 * Formats a `Date` object into a given format.
 * Should be used to display dates at a given format in the ui.
 *
 * @param date The `Date` object. If `null` or `undefined`, this function will return an empty string.
 * @param dateFormat Enums date format etc: LOCAL_DATE_TIME_WITH_SPACE
 * @param options Options for the date formatting.
 */
export const formatToDateString = (
  date: Date | null | undefined,
  dateFormat: DateFormat,
  options?: FormatOptions,
): string => {
  try {
    if (!options?.skip1970Handling && date?.getFullYear() === 1970) {
      return '';
    }
    return date ? format(date, dateFormat) : '';
  } catch (e) {
    console.warn('Error formatting date:', e);
    return '';
  }
};

/**
 * Formats a `string` to date time
 *
 * @param strDate The date string
 */
export const stringToDateTime = (strDate: string): Date => {
  const date = new Date(strDate);
  return date;
};

/**
 * Formats a date to a formatted time string.
 *
 * @param date The date to format
 * @returns A formatted time string. Example: '10:00'
 */
export const dateToStringTime = (date: Date): string => {
  const formatNumber = (number: number): string => {
    if (number < 10) {
      return `0${number}`;
    }
    return `${number}`;
  };

  return `${formatNumber(date.getHours())}:${formatNumber(date.getMinutes())}`;
};

/**
 * Converts a date to the current browser timezone.
 *
 * @param date The UTC date to conevert.
 * @returns The date in the browser timezone.
 */
export const convertUTCDateToLocalDate = (date: Date): Date => {
  const newDate = new Date(date.getTime() - date.getTimezoneOffset() * 60 * 1000);
  return newDate;
};

/**
 * Converts a date string from the API to a date object in UTC time.
 *
 * @param apiDateString The date string from the API: 2021-09-01T00:00:00
 * @returns A date object in UTC time.
 */
export const convertAPIDateTimeStringToUTCDate = (apiDateString: string) => {
  const dateComponents = apiDateString.split(/[-T:.]/).map((component) => parseInt(component));
  const [year, month, day, hour, minute, second] = dateComponents;

  const utcDate = new Date(Date.UTC(year, month - 1, day, hour, minute, second));

  return utcDate;
};
