'use client';

import { useLocationSelector } from '@/components/hooks';
import { CRM_PROCESSORS, POS_PROCESSORS } from '@/consts/processors';
import { ProcessorCard } from '@/components/pages/manage/processor/processor-card';
import { useState } from 'react';
import { Processor } from '@/types/processors';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

export default function Integrations() {
  const { locationSelectorElement } = useLocationSelector({});
  const [crmProcessors, setCrmProcessors] = useState(CRM_PROCESSORS);
  const [posProcessors, setPosProcessors] = useState(POS_PROCESSORS);

  const handleToggle = (processor: Processor, enabled: boolean) => {
    const updateProcessor = (p: Processor) => ({
      ...p,
      enabled: p.id === processor.id ? enabled : p.enabled,
    });

    switch (processor.type) {
      case 'CRM':
        setCrmProcessors(crmProcessors.map(updateProcessor));
        break;
      case 'POS':
        setPosProcessors(posProcessors.map(updateProcessor));
        break;
    }
  };

  return (
    <div className="py-5">
      <div className="flex flex-col gap-1 pb-5">
        <h1 className="text-2xl font-semibold text-slate-800">Integrations</h1>
        <p className="text-sm text-gray-600">
          Manage and configure your integrations with various services here. Ensure that all
          necessary keys and settings are correctly entered to enable seamless connectivity.
        </p>
      </div>
      <div className="mb-6 border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
      </div>

      <Tabs defaultValue="crm" className="space-y-6">
        <TabsList className="h-12 w-full justify-start space-x-4 bg-transparent p-0">
          <TabsTrigger value="crm" className="data-[state=active]:bg-primary h-10 rounded-md px-6">
            CRM Systems
          </TabsTrigger>
          <TabsTrigger value="pos" className="data-[state=active]:bg-primary h-10 rounded-md px-6">
            POS Systems
          </TabsTrigger>
        </TabsList>

        <TabsContent value="crm" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {crmProcessors.map((processor) => (
              <ProcessorCard
                key={processor.id}
                processor={processor}
                onToggle={(enabled) => handleToggle(processor, enabled)}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="pos" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {posProcessors.map((processor) => (
              <ProcessorCard
                key={processor.id}
                processor={processor}
                onToggle={(enabled) => handleToggle(processor, enabled)}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
