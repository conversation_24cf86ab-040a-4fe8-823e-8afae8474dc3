export type ProcessorType = 'PAYMENT' | 'CRM' | 'POS';

export interface ProcessorStats {
  totalTransactions: number;
  successfulTransactions: number;
  revenue: number;
  lastSync?: Date;
  conversionRate?: number;
  activeCustomers?: number;
}

export interface Processor {
  id: string;
  name: string;
  type: ProcessorType;
  logo?: string;
  enabled: boolean;
  requiresConfig: boolean;
  configFields: {
    clientId?: boolean;
    clientSecret?: boolean;
    apiKey?: boolean;
  };
  stats?: ProcessorStats;
  config?: ProcessorConfig;
}

export interface ProcessorConfig {
  clientId?: string;
  clientSecret?: string;
  apiKey?: string;
}
