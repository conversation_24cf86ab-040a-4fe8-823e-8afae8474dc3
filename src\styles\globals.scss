@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  // background: white;
  margin: 0;
  line-height: normal;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --primary: #3c5b80;
    --primary-shade: #8c9eb4;
    --primary-foreground: #fff;

    --secondary: #ed6b4c;
    --secondary-foreground: #f4a795;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 71.4% 4.1%;

    --radius: 0.5rem;
  }
}

@keyframes spinner {
  from {
    stroke-dashoffset: calc(3.14159265358979 * 50 * 2);
    transform: rotateZ(0deg);
  }
  to {
    stroke-dashoffset: calc(calc(3.14159265358979 * 50 * 2) * -1);
    transform: rotateZ(720deg);
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 11px;
  height: 8px;
  background-color: #e2e2e2;
}

::-webkit-scrollbar-track {
  background: #f8f8f8;
}

::-webkit-scrollbar-thumb {
  border-radius: 15px;
  background: #d3d3d3;
}

::-webkit-scrollbar-thumb:hover {
  cursor: pointer;
  background: #969696;
}

@layer utilities {
  .scrollbar-medium::-webkit-scrollbar {
    width: 5px !important;
  }
}

html {
  // overflow-y: scroll;
  // overflow-x: hidden;
  // margin-right: calc(-1 * (100vw - 100%));
}

/* HTML: <div class="loader"></div> */
.square-loader {
  display: inline-flex;
  gap: 5px;
}
.square-loader:before,
.square-loader:after {
  content: '';
  width: 25px;
  aspect-ratio: 1;
  box-shadow: 0 0 0 3px inset #000;
  animation: l4 1.5s infinite;
}
.square-loader:after {
  --s: -1;
  animation-delay: 0.75s;
}
@keyframes l4 {
  0% {
    transform: scaleX(var(--s, 1)) translate(0) rotate(0);
  }
  16.67% {
    transform: scaleX(var(--s, 1)) translate(-50%) rotate(0);
  }
  33.33% {
    transform: scaleX(var(--s, 1)) translate(-50%) rotate(90deg);
  }
  50%,
  100% {
    transform: scaleX(var(--s, 1)) translate(0) rotate(90deg);
  }
}

.react-datepicker-wrapper {
  width: 100%;
}

// .mdxeditor-root-contenteditable {
//   height: 100%;
//   width: 100%;

//   div {
//     height: 100%;
//     width: 100%;
//   }
// }

.hide-scrollbar-thumb::-webkit-scrollbar {
  background-color: transparent;
  width: 0px;
  scrollbar-width: 0px;
  background-color: transparent;
  border-radius: 0px;
}

.noScrollbar::-webkit-scrollbar {
  display: none;
}

.noScrollbar::-webkit-scrollbar-thumb {
  display: none;
}

.noScrollbar::-webkit-scrollbar-track {
  display: none;
}

.noScrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
