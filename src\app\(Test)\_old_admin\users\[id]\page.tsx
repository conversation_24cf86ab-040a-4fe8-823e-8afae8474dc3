'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown } from 'lucide-react';

export default function Component() {
  return (
    <div className="container mx-auto space-y-4">
      <button
        className="text-primary-700"
        onClick={() => {
          // go back to the previous page
          window.history.back();
        }}
      >
        &lt; Return to Users
      </button>

      <h2 className="text-2xl font-bold">Manage User</h2>
      <div className="space-y-4">
        <div>
          <Label htmlFor="firstName">First Name</Label>
          <Input id="firstName" />
        </div>
        <div>
          <Label htmlFor="lastName">Last Name</Label>
          <Input id="lastName" />
        </div>
        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" />
        </div>
        <div>
          <Label htmlFor="timeZone">Time Zone</Label>
          <Select>
            <option>(UTC-05:00) America/New_York</option>
          </Select>
        </div>
        <div>
          <Label htmlFor="userType">User Type</Label>
          <Select>
            <option>Standard</option>
          </Select>
        </div>
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Location Permissions</h3>
        <div className="space-y-2">
          <div className="flex items-center">
            <Checkbox id="toggleAllLocations" />
            <Label htmlFor="toggleAllLocations" className="ml-2">
              Toggle All Locations
            </Label>
          </div>
          <div className="flex items-center">
            <Checkbox id="merchant1" checked />
            <Label htmlFor="merchant1" className="ml-2">
              Merchant 1
            </Label>
          </div>
        </div>
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Site Permissions</h3>
        <div className="space-y-4">
          <div>
            <div className="flex items-center">
              <Checkbox id="catalogAdmin" />
              <Label htmlFor="catalogAdmin" className="ml-2 font-semibold">
                Catalog Admin
              </Label>
            </div>
            <p className="ml-6 text-sm text-gray-500">
              Grant user access to managing Products, Categories, and Discounts within the Catalog
            </p>
          </div>
          <div>
            <div className="flex items-center">
              <Checkbox id="virtualTerminal" />
              <Label htmlFor="virtualTerminal" className="ml-2 font-semibold">
                Virtual Terminal
              </Label>
            </div>
            <div className="ml-6">
              <div className="mt-2 flex items-center">
                <Checkbox id="verify" />
                <Label htmlFor="verify" className="ml-2">
                  Verify
                </Label>
              </div>
              <p className="text-sm text-gray-500">
                Allow user to process a verify in the Virtual Terminal
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
