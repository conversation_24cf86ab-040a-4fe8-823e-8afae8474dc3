import { ChangeEvent } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label, Textarea, TextInput } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
import { cn } from '@/lib/utils';

export type FormInputProps = {
  name: string;
  helperText?: string;
  id: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  flex?: number;
  visible?: boolean;
  readOnly?: boolean;
  className?: string;
  maxLength?: number;
  min?: number | string;
  max?: number | string;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  variant?: 'primary' | 'secondary';
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  defaultValue?: string;
  tabIndex?: number;
  endAdornment?: React.ReactNode;
  type?: string; // Add this line
  inputClassName?: string;
  multiple?: boolean; // Indicates if the input should be a textarea
  row?: number; // Number of rows for the textarea
  details?: string;
};

export const FormInput = ({
  id,
  name,
  label,
  placeholder,
  rules,
  flex,
  disabled,
  helperText = '',
  defaultValue,
  className,
  visible = true,
  readOnly = false,
  maxLength,
  min,
  max,
  tabIndex,
  tooltip,
  endAdornment,
  inputClassName,
  variant = 'primary',
  details,
  multiple,
  row = 7, // Default to 4 rows if multiple is true
  onChangeCallback = (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    e;
  },
  type = 'text', // Add this line
  ...props
}: FormInputProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || ''}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={cn('relative w-full', className)}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div>
            <div className="relative">
              {multiple ? (
                <Textarea
                  id={id}
                  ref={ref}
                  value={value}
                  onChange={(e) => {
                    if (maxLength && e.target.value.length > maxLength) return;
                    onChange(e);
                    onChangeCallback(e);
                  }}
                  onBlur={(e) => {
                    onChange(e);
                    onBlur();
                  }}
                  readOnly={readOnly}
                  disabled={isDisabled()}
                  rows={row} // Set the number of rows for the textarea
                  maxLength={maxLength}
                  autoComplete="off"
                  placeholder={placeholder ? placeholder : `Enter ${label}`}
                  tabIndex={tabIndex}
                  className={`w-full ${endAdornment ? 'pr-12' : ''} ${inputClassName}`}
                  {...props}
                />
              ) : (
                <TextInput
                  id={id}
                  ref={ref}
                  value={value}
                  onChange={(e) => {
                    if (maxLength && e.target.value.length > maxLength) return;
                    onChange(e);
                    onChangeCallback(e);
                  }}
                  onBlur={(e) => {
                    onChange(e);
                    onBlur();
                  }}
                  readOnly={readOnly}
                  disabled={isDisabled()}
                  type={type}
                  maxLength={maxLength}
                  min={min}
                  max={max}
                  autoComplete="off"
                  placeholder={placeholder ? placeholder : `Enter ${label}`}
                  tabIndex={tabIndex}
                  className={`w-full ${endAdornment ? 'pr-12' : ''} ${inputClassName}`}
                  {...props}
                />
              )}
              {endAdornment && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  {endAdornment}
                </div>
              )}
            </div>
            <HelperText color={invalid ? 'failure' : 'default'}>
              <span className={cn('text-xs', helperText && !invalid && 'text-gray-700')}>
                {invalid ? error?.message : helperText}{' '}
              </span>
            </HelperText>
          </div>
        </div>
      )}
    />
  );
};

export default FormInput;
