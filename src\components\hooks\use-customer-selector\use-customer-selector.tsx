import { AutoCompleteInput, AutoCompleteOption } from '@/components/globals';
import {
  Gateway_CustomersDocument,
  Gateway_CustomersInputDataPageSortOrder,
  GatewayUniCustomersOutputData,
} from '@/graphql/generated/graphql';
import { useQuery } from '@apollo/client';
import { useEffect, useMemo, useState } from 'react';
import useDebounce from '../useDebounce';

type CustomerSelectorProps = {
  groupdId: string;
  preselectedID?: string;
  canPay?: boolean;
};

export const useCustomerSelector = ({ groupdId, preselectedID, canPay }: CustomerSelectorProps) => {
  const [displayText, setDisplayText] = useState<string>('');
  const [customer, setCustomer] = useState<AutoCompleteOption | null>(null);

  const debouncedSearchQuery = useDebounce(displayText, 500);

  const {
    data: customerList,
    loading,
    error,
    refetch,
  } = useQuery(Gateway_CustomersDocument, {
    variables: {
      input: {
        groupID: groupdId,
        data: {
          page: {
            page: 1,
            pageSize: 10,
            search: debouncedSearchQuery || preselectedID || '',
            sort: {
              field: 'name',
              order: Gateway_CustomersInputDataPageSortOrder.Asc,
            },
          },
        },
      },
    },
    skip: !groupdId,
  });

  const handleChange = (value: AutoCompleteOption) => {
    setCustomer(value);
  };

  // Memoized options for the AutoCompleteInput
  const customerOptions = useMemo<AutoCompleteOption<GatewayUniCustomersOutputData>[]>(() => {
    if (!customerList?.gateway_customers) return [];
    return customerList?.gateway_customers?.data?.map((customer) => {
      return {
        id: customer?.id,
        label: customer?.name,
        obj: customer,
        disabled: canPay ? !customer?.isDefault : false,
        disabledMessage: canPay ? 'Customer has no default payment method' : '',
      };
    }) as AutoCompleteOption<GatewayUniCustomersOutputData>[];
  }, [customerList, groupdId, refetch, displayText]);

  useEffect(() => {
    refetch();
  }, [displayText]);

  useEffect(() => {
    if (preselectedID) {
      const selectedCustomer = customerOptions.find((option) => option.id === preselectedID);
      if (selectedCustomer) {
        setCustomer(selectedCustomer);
      }
    }
  }, [preselectedID, customerOptions]);

  return {
    customerSelectorElement: (
      <AutoCompleteInput
        options={customerOptions}
        value={customer}
        placeholder="Select Customer"
        onChange={(option: AutoCompleteOption) => handleChange(option as AutoCompleteOption)}
        inputValue={customer?.label ?? ''}
        onQueryText={setDisplayText}
      />
    ),
    loading,
    error,
    customerOptions,
    customer,
  };
};
