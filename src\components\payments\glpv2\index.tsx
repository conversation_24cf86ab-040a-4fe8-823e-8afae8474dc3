'use client';

import { env } from 'next-runtime-env';
import { useEffect, useMemo, useRef } from 'react';
import GLPayTokenizerTemplate, { TokenizerSuccessToken } from '../glp/glp_tokenizer_template';
import { useMutation } from '@apollo/client';
import { GPHFToken } from '@/graphql/declarations/tsep';
import { LoaderSquares } from '@/components/globals/Loaders/Square';

export type CardGLPResponse = {
  paymentID: string;
};

interface CardGLPFieldsProps {
  onToken?: (response: CardGLPResponse) => void;
  onEvent?: (eventType: string, event: any) => void;
  onError?: (error: any) => void;
  merchantID: string;
  labels?: {
    cardNumber?: string;
    cvv?: string;
    expiration?: string;
    submitText?: string;
  };
  styles?: {
    input?: {
      borderRadius?: string;
      borderColor?: string;
      focusBorderColor?: string;
      padding?: string;
      marginBottom?: string;
      backgroundColor?: string;
      color?: string;
    };
    button?: {
      borderRadius?: string;
      borderColor?: string;
      textColor?: string;
      hoverBgColor?: string;
      hoverTextColor?: string;
      padding?: string;
      margin?: string;
      fontSize?: string;
    };
    label?: {
      fontSize?: string;
      fontWeight?: string;
      color?: string;
      marginBottom?: string;
    };
    font?: {
      family?: string;
      style?: string;
      weight?: string;
      display?: string;
      src?: string;
      unicodeRange?: string;
    };
  };
}

export const CardGLPFields = ({
  onToken,
  labels,
  styles,
  onEvent,
  onError,
  merchantID,
}: CardGLPFieldsProps) => {
  const onSubmission = (resp: TokenizerSuccessToken) => {
    onToken?.({
      paymentID: resp.paymentReference,
    });
  };
  const tokenizerRef = useRef(null);

  const [fetchToken, { data, loading }] = useMutation(GPHFToken, {
    variables: {
      input: {
        merchantID: merchantID,
      },
    },
  });

  useEffect(() => {
    fetchToken();
  }, []);

  if (loading || !data?.gphf_token?.token || !merchantID) {
    return (
      <div className="flex flex-col items-center justify-center gap-2 p-4">
        <LoaderSquares />
        {!data?.gphf_token?.token && <p className="text-sm text-gray-500">Fetching token...</p>}
        {!merchantID && <p className="text-sm text-gray-500">Merchant ID not found</p>}
      </div>
    );
  }

  return (
    <GLPayTokenizerTemplate
      onSubmission={onSubmission}
      onLogs={(log) => {
        onEvent?.('log', log);
      }}
      onError={(error) => {
        onError?.(error);
      }}
      apiKey={data?.gphf_token?.token ?? ''}
      liveMode={env('NEXT_PUBLIC_CARDGLP_ISLIVE') === 'true'}
      merchantID={data?.gphf_token?.merchantID ?? ''}
      tokenizerRef={tokenizerRef}
      labels={{
        cardNumber: labels?.cardNumber,
        cvv: labels?.cvv,
        expiration: labels?.expiration,
        submitText: labels?.submitText,
      }}
      styles={styles}
      config={{
        ids: {
          cardNumber: '#card-number',
          cardExpiration: '#card-expiration',
          cardCvv: '#card-cvv',
          submitButton: '#submit-button',
          paymentForm: '#payment-form',
        },
        text: {
          submitText: labels?.submitText,
        },
      }}
    />
  );
};

interface CardHostedComponentProps {
  onEvent?: (eventType: string, event: any) => void;
  onToken?: (response: CardGLPResponse) => void;
  onError?: (error: any) => void;
  labels?: {
    cardNumber?: string;
    cvv?: string;
    expiration?: string;
  };
  parentComponentAttributes?: { [key: string]: any };
  iframeComponentAttributes?: { [key: string]: any };
  merchantID: string;
}

export const CardGLPHostedComponent = (props: CardHostedComponentProps) => {
  const registerListener = () => {
    window.addEventListener('message', (event) => {
      const eventType = event.data.type;
      const data = event.data.data;

      switch (eventType) {
        case 'token': {
          props.onToken?.(data);
          break;
        }
        case 'log': {
          props.onEvent?.('log', data);
          break;
        }
        case 'error': {
          props.onError?.(data);
          break;
        }
      }
    });
  };

  useEffect(() => {
    registerListener();
  }, []);

  const settingsString = JSON.stringify({
    labels: props.labels,
  });

  const iframeURL = useMemo(() => {
    const baseURL = env('NEXT_PUBLIC_CARDGLP_HOSTED_URL');
    if (!baseURL) {
      return '';
    }
    const url = new URL(baseURL);
    url.searchParams.append('settings', settingsString);
    url.searchParams.append('merchantID', props.merchantID);
    return url.toString();
  }, [settingsString]);

  return (
    <div {...(props.parentComponentAttributes ?? {})}>
      <iframe
        src={iframeURL}
        width={props.iframeComponentAttributes?.width ?? '100%'}
        height={props.iframeComponentAttributes?.height ?? '450px'}
        {...(props.iframeComponentAttributes ?? {})}
      />
    </div>
  );
};
