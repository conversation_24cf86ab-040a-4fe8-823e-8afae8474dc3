import {
  <PERSON><PERSON><PERSON><PERSON>,
  TopComponent,
  StatusChip,
  useDataGridView,
  Variant,
} from '@/components/globals';

import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import DepositDetailsModal from './deposit-details-modal';
import { Gateway_DepositsDocument } from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import { moneyFormat } from '@/lib/utils';
import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { mockDeposits } from '@/mock/deposits-data';

export enum DepositStatus {
  accept = 'accept',
  DEPOSITED = 'DEPOSITED',
}

export const getDepositStatus = (status: DepositStatus): [Variant, string] => {
  const statusMap: Record<DepositStatus, Variant> = {
    [DepositStatus.accept]: 'success',
    [DepositStatus.DEPOSITED]: 'success',
    // [DepositStatus.submittedForm]: 'neutral',
    // [DepositStatus.underReview]: 'warning',
    // [DepositStatus.requestMoreInfo]: 'warning',
    // [DepositStatus.resolved]: 'success',
    // [DepositStatus.failed]: 'danger',
  };

  const variant = statusMap[status] || 'neutral';
  const label = `${status?.charAt(0).toUpperCase()}${status?.slice(1)}`;

  return [variant, label];
};

export const DepositsTab = () => {
  const [selectedDepositID, setSelectedDepositID] = useState<string | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const { locationFilter, locationSelectorElement, loading } = useLocationSelector({
    onlyActive: true,
  });

  const {
    data: depositeListDate,
    loading: depositeListDateLoading,
    refetch: refetchDepositListData,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    maxVariables,
  } = useDataGridView({
    query: Gateway_DepositsDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
    initialFilterField: 'status',
    initialFilterValue: 'All',
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refetchDepositListData();
    }
  }, [locationFilter]);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_DepositsDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_deposits?.data ?? [];
  };

  type DepositRow = {
    depositID: string;
    location: string;
    date: string;
    amount: number;
    status: string;
  };

  const columns: Column<DepositRow>[] = [
    {
      key: 'depositID',
      header: 'Deposit #',
      sortable: true,
      onClick: (row) => setSelectedDepositID(row.depositID),
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'location',
      header: 'Location',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
      valueGetter: (row) =>
        locationFilter?.id === row.location ? locationFilter?.label : row.location,
    },
    {
      key: 'date',
      header: 'Date',
      valueGetter: (row) => moment(row.date).format('MM/DD/YYYY'),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'amount',
      header: 'Amount',
      renderCell: (row) => <span>{moneyFormat(row.amount)}</span>,
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'status',
      header: 'Status',
      valueGetter: (row) => row.status,
      renderCell: (row) => {
        const [status, label] = getDepositStatus(row.status as DepositStatus);
        return <StatusChip variant={'success'} label={label} />;
      },
    },
  ];

  const depositeRow = useMemo(() => {
    // Use mock data for testing the new deposit structure
    return mockDeposits.map(
      (deposit) =>
        ({
          depositID: deposit.depositID ?? '',
          location: deposit.location ?? '',
          date: deposit.date ?? '',
          amount: deposit.amount ?? 0,
          status: deposit.status ?? '',
        }) as DepositRow,
    );
  }, []);

  return (
    <>
      <StaticInfoBox />
      <PageHeader text="Deposits" />
      <div className="mt-2 flex justify-between border-b border-gray-300">
        <div className="w-1/4">{locationSelectorElement}</div>
        <TopComponent value={searchValue} setValue={setSearchValue}>
          <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="deposits" />
        </TopComponent>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={depositeRow}
          isLoading={false}
          mode="client"
          pageSize={pageSize}
          currentPage={currentPage}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={mockDeposits.length}
          onRowClick={(row) => setSelectedDepositID(row.depositID ?? null)}
        />
        <DepositDetailsModal
          isOpen={selectedDepositID !== null}
          onClose={() => setSelectedDepositID(null)}
          queryData={{
            depositID: selectedDepositID ?? '',
          }}
        />
      </div>
    </>
  );
};
