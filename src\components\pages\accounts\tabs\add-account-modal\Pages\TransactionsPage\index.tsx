import { FormAutoComplete, FormInput } from '@/components/globals';
import { FormTextArea } from '@/components/globals/form-textarea';
import useLocalStorage from '@/components/hooks/use-local-storage/useLocalStorage';
import useDebounce from '@/components/hooks/useDebounce';
import { message } from '@/components/shared/utils';
import { processorTestDraftMCC } from '@/graphql/declarations/processor-test';
import { manualReviewMCCs } from '@/lib/mccs_lowRisk';
import { Promisable } from '@/types/types';
import { useQuery } from '@apollo/client';
import { Badge } from 'flowbite-react';
import { useSearchParams } from 'next/navigation';
import { MutableRefObject, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { updateData } from '../updateData';
import styles from './index.module.css';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { AiMccFromDescription } from '@/graphql/declarations/ai';
import { cn } from '@/lib/utils';
import { toast } from 'react-toastify';

const TransactionsPage = (args: {
  triggerSubmit?: MutableRefObject<() => Promisable<boolean>>;
  initialData?: any;
}) => {
  const [, setStoreRecommendedMCC] = useLocalStorage<{
    id: string;
    description: string;
    riskLevel: string;
  }>('RECOMMENDED_MCC', undefined);

  const methods = useForm({
    defaultValues: args.initialData ?? {
      businessCategory: '',
      description: '',
      swipe: '',
      keyed: '',
      ecommerce: '',
      avgTransactionAmount: '',
      highestTransactionAmount: '',
      grossMonthlySalesVolume: '',
      amexAvgTransactionAmount: '',
      amexHighestTransactionAmount: '',
      amexGrossMonthlySalesVolume: '',
    },
  });

  const searchParams = useSearchParams();

  useEffect(() => {
    if (args.triggerSubmit) {
      args.triggerSubmit.current = async () => {
        const isValid = await methods.trigger();
        if (isValid) {
          let val = methods.getValues();
          // if a field is numeric, convert it to a number
          Object.keys(val).forEach((key) => {
            // skip if field is businessCategory or description
            if (['businessCategory', 'description'].includes(key)) return;
            if (!isNaN(val[key])) {
              val[key] = Number(val[key]);
            }
          });
          try {
            return await updateData({
              submitType: 'transaction',
              data: val,
              groupID: searchParams?.get('groupID') ?? undefined,
              form: methods,
            });
          } catch (error) {
            console.log(error);
            return false;
          }
        }
        return false;
      };
    }
  }, [args.triggerSubmit]);

  const descriptionText = methods.watch('description');
  const debouncedDescriptionText = useDebounce(descriptionText, 500);

  const [recommendedMccLoading, setRecommendedMccLoading] = useState<boolean>(false);
  const [recommendedMcc, setRecommendedMcc] = useState<{
    id: string;
    description: string;
    riskLevel: string;
  } | null>(null);

  useEffect(() => {
    async function loadRecomm() {
      if (!debouncedDescriptionText) return;
      setRecommendedMccLoading(true);
      const mcc = await apolloClient.mutate({
        mutation: AiMccFromDescription,
        variables: {
          input: {
            sentence: debouncedDescriptionText,
          },
        },
      });

      // console.log(mcc?.data?.ai_determineMCCFromSentence);

      // return (mcc.data?.ai_determineMCCFromSentence ?? []) as any[];
      setRecommendedMcc(mcc.data?.ai_determineMCCFromSentence[0] ?? null);
      setStoreRecommendedMCC(mcc.data?.ai_determineMCCFromSentence[0] ?? {});
      setRecommendedMccLoading(false);
    }
    loadRecomm();
  }, [debouncedDescriptionText]);

  const businessCategory = methods.watch('businessCategory');
  const [categoryText, setCategoryText] = useState<string>(businessCategory);
  const debouncedCategoryText = useDebounce(categoryText, 500);
  const { data: mccInitialList, loading: mccLoading } = useQuery(processorTestDraftMCC, {
    variables: {
      input: {
        pattern: debouncedCategoryText || '',
      },
    },
  });

  const presetValue = (mcc: { id: string; description: string; riskLevel: string }) => {
    // toast.success('Applying MCC');
    methods.setValue('businessCategory', mcc.id);

    // TODO: change in the future
    methods.setValue('swipe', 10);
    methods.setValue('keyed', 10);
    methods.setValue('ecommerce', 80);

    methods.setValue('avgTransactionAmount', 100);
    methods.setValue('highestTransactionAmount', 1000);
    methods.setValue('grossMonthlySalesVolume', 10000);

    methods.setValue('amexAvgTransactionAmount', 100);
    methods.setValue('amexHighestTransactionAmount', 1000);
    methods.setValue('amexGrossMonthlySalesVolume', 10000);

    setCategoryText(mcc.description);
  };

  // useEffect(() => {
  //   if (recommendedMcc) {
  //     presetValue(recommendedMcc);
  //   }
  // }, [recommendedMcc]);

  return (
    <FormProvider {...methods}>
      <form className="space-y-4">
        <div className={styles.transactions}>
          <div className={styles.inputWidgetLg}>
            <div className={styles.column}>
              <FormTextArea
                id="description"
                name="description"
                label="Description of what you sell"
                rules={{ required: message.requiredField }}
              />
            </div>
            <p className="text-sm text-gray-600">
              * Based of your business description, we will recommend a MCC code and preset values
              for the fields below. You can change them as needed.
            </p>
            {(recommendedMccLoading || mccLoading) && <Badge>Loading...</Badge>}
            <div>
              <div className={styles.label2}>Recommended MCCs:</div>
              <div className={cn('')}>
                {!mccLoading &&
                  recommendedMcc &&
                  recommendedMcc.id !== businessCategory &&
                  [recommendedMcc].slice(0, 1).map((mcc) => (
                    <Badge
                      color="primary"
                      key={mcc?.id}
                      // className="cursor-pointer bg-primary-600 text-lg text-white"
                      className={cn('cursor-pointer text-sm hover:underline')}
                      onClick={() => {
                        toast.success('Applying MCC');
                        presetValue(mcc!);
                      }}
                    >
                      ({mcc?.id}) {mcc?.description} ({mcc?.riskLevel} risk)
                    </Badge>
                  ))}
              </div>
            </div>
            <div className={styles.column}>
              <FormAutoComplete
                id="businessCategory"
                name="businessCategory"
                label="Business Category"
                rules={{ required: message.requiredField }}
                onChangeStrategy="id"
                initialText={
                  mccInitialList?.processor_tst_mcc?.items?.find((d) => d?.id === businessCategory)
                    ?.description ?? ''
                }
                onTextChange={(cat) => {
                  // setCategoryLoading(true);
                  setCategoryText(cat);
                }}
                options={
                  mccInitialList?.processor_tst_mcc?.items?.map((d) => ({
                    id: `${d?.id!}`,
                    label: d?.description!,
                  })) ?? []
                }
                optionsLoading={mccLoading}
              />
            </div>
            {manualReviewMCCs.find(
              (id) =>
                mccInitialList?.processor_tst_mcc?.items?.find((d) => d?.id === businessCategory)
                  ?.id === id,
            ) && (
              // <Badge className="-mt-4" color="red">
              //   Manual Review Required
              // </Badge>
              <></>
            )}
            <div className={styles.frameParent}>
              <div className={styles.labelParent}>
                <div className={styles.label2}>How Do You Accept Card?</div>
                <div className={styles.label3}>(must equal 100%)</div>
              </div>
              <div className={styles.inputFieldParent}>
                <FormInput
                  id="swipe"
                  name="swipe"
                  label="Swipe (Card Present)"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 0,
                      message: 'Value must be greater or equal to 0',
                    },
                    max: {
                      value: 100,
                      message: 'Value must be less or equal to 100',
                    },
                  }}
                />
                <FormInput
                  id="keyed"
                  name="keyed"
                  label="Keyed (Manual Entry)"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 0,
                      message: 'Value must be greater or equal to 0',
                    },
                    max: {
                      value: 100,
                      message: 'Value must be less or equal to 100',
                    },
                  }}
                />
                <FormInput
                  id="ecommerce"
                  name="ecommerce"
                  label="Ecommerce (Card Not Present)"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 0,
                      message: 'Value must be greater or equal to 0',
                    },
                    max: {
                      value: 100,
                      message: 'Value must be less or equal to 100',
                    },
                  }}
                />
              </div>
            </div>
            <div className={styles.frameParent}>
              <div className={styles.content}>
                <div className={styles.label2}>Visa / Mastercard / Discover</div>
              </div>
              <div className={styles.inputFieldParent}>
                <FormInput
                  id="avgTransactionAmount"
                  name="avgTransactionAmount"
                  label="Average Transaction Amount"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 1,
                      message: 'Value must be greater or equal to 1',
                    },
                  }}
                />
                <FormInput
                  id="highestTransactionAmount"
                  name="highestTransactionAmount"
                  label="Highest Transaction Amount"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 1,
                      message: 'Value must be greater or equal to 1',
                    },
                  }}
                />
                <FormInput
                  id="grossMonthlySalesVolume"
                  name="grossMonthlySalesVolume"
                  label="Gross Monthly Sales Volume"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 1,
                      message: 'Value must be greater or equal to 1',
                    },
                  }}
                />
              </div>
            </div>
            <div className={styles.frameParent}>
              <div className={styles.content}>
                <div className={styles.label2}>American Express (AMEX):</div>
              </div>
              <div className={styles.inputFieldParent}>
                <FormInput
                  id="amexAvgTransactionAmount"
                  name="amexAvgTransactionAmount"
                  label="Average Transaction Amount"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 1,
                      message: 'Value must be greater or equal to 1',
                    },
                  }}
                />
                <FormInput
                  id="amexHighestTransactionAmount"
                  name="amexHighestTransactionAmount"
                  label="Highest Transaction Amount"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 1,
                      message: 'Value must be greater or equal to 1',
                    },
                  }}
                />
                <FormInput
                  id="amexGrossMonthlySalesVolume"
                  name="amexGrossMonthlySalesVolume"
                  label="Gross Monthly Sales Volume"
                  type="number"
                  min={0}
                  rules={{
                    required: message.requiredField,
                    min: {
                      value: 1,
                      message: 'Value must be greater or equal to 1',
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default TransactionsPage;
