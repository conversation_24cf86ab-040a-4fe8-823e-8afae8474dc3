'use client';

import { useEffect, useRef, useState } from 'react';
import Header from './Headerv3';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Me } from '@/graphql/declarations/me';
import { useQuery } from '@apollo/client';
import { LoaderSquares } from './Loaders/Square';
import { usePathname } from 'next/navigation';
import React from 'react';

export default function HomeLayoutComponent({ children }: { children: React.ReactNode }) {
  const [checkingAuth, setCheckingAuth] = useState(true);
  const retries = useRef(0);
  const { data: meData, loading: meLoading } = useQuery(Me);

  const checkAuth = async () => {
    if (meLoading) {
      return;
    }
    if (meData?.authenticatedItem?.id) {
      setCheckingAuth(false);
      return;
    } else {
      if (retries.current > 3) {
        retries.current += 1;

        setTimeout(async () => {
          checkAuth();
        }, 2000);
        return;
      } else {
        window.location.href = '/login';
      }
    }
  };

  useEffect(() => {
    if (meLoading) {
      return;
    }
    checkAuth();
  }, [meData, meLoading]);

  const path = usePathname();

  const checkIfInIframe = () => {
    // if the parent iframe is gohighlevel, then we are in an iframe
    if (window.self !== window.top) {
      return true;
    } else {
      localStorage.removeItem('iframeSelection');
    }
  };

  useEffect(() => {
    checkIfInIframe();
  }, []);

  return (
    <>
      {/* put a loading circle and 'loading' text */}
      {checkingAuth && (
        <div
          className="fixed bottom-0 top-0 z-[1000] flex h-screen w-full items-center justify-center bg-gray-100 opacity-75"
          style={{ borderTop: '1px solid #e5e7eb' }}
        >
          <div className="flex flex-col items-center justify-center">
            <LoaderSquares />
          </div>
        </div>
      )}
      <div className="relative mx-auto min-h-screen space-y-6 md:block">
        <div className="flex w-full flex-col">
          <Header />
          <div className="noScrollbar mx-auto w-full max-w-screen-xl px-2 pb-16">
            <main>{children}</main>
          </div>
        </div>
      </div>
      <ToastContainer />
      {/* <Script src="/_next/static/chunks/public/flowbite/flowbite.js" strategy="beforeInteractive" /> */}
    </>
  );
}
