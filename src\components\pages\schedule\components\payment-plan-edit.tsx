import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Modal } from 'flowbite-react';
import { useMutation } from '@apollo/client';
import { Gateway_UpdatePaymentPlanDocument } from '@/graphql/generated/graphql';
import { toast } from 'react-toastify';
import { useForm, FormProvider } from 'react-hook-form';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { FormInput, FormSelect, FormDatepicker } from '@/components/globals';
import FormCheckbox from '@/components/globals/form-checkbox/form-checkbox';

type PaymentPlanEditProps = {
  queryData: {
    planID: string;
    groupID: string;
  };
  planData: any;
  refetch: () => void;
  setIsDataUpdating: (value: boolean) => void;
};

export const PaymentPlanEdit: React.FC<PaymentPlanEditProps> = ({
  queryData,
  planData,
  refetch,
  setIsDataUpdating,
}) => {
  const { planID, groupID } = queryData;
  const [isOpen, setIsOpen] = useState(false);
  const [updatePaymentPlanMutation, { loading: updatePaymentPlanLoading }] = useMutation(
    Gateway_UpdatePaymentPlanDocument,
    {
      onCompleted: () => {
        toast.success('Payment plan updated successfully');
        setIsDataUpdating(false);
        refetch();
        handleClose();
      },
      onError: (error) => {
        toast.error(`Failed to update payment plan: ${error.message}`);
        setIsDataUpdating(false);
      },
    },
  );

  console.log(planData);

  const methods = useForm({
    defaultValues: {
      planName: planData?.planName || '',
      mode: planData?.mode || 'YEARLY',
      amount: planData?.amount ? planData.amount : 0,
      paymentEvery: planData?.paymentEvery || 1,
      endDate: planData?.endDate || '',
      paymentInterval: planData?.paymentInterval || 30,
      paymentID: planData?.paymentID || '',
      amountChangeWaitsNextCycle: false,
    },
  });

  // Reset form values when planData changes
  useEffect(() => {
    if (planData) {
      methods.reset({
        planName: planData?.planName || '',
        mode: planData?.mode || 'YEARLY',
        amount: planData?.amount ? planData.amount : 0,
        paymentEvery: planData?.paymentEvery || 1,
        endDate: planData?.endDate || '',
        paymentInterval: planData?.paymentInterval || 30,
        paymentID: planData?.paymentID || '',
        amountChangeWaitsNextCycle: false,
      });
    }
  }, [planData]);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  const onSubmit = async (data: any) => {
    try {
      setIsDataUpdating(true);
      await updatePaymentPlanMutation({
        variables: {
          input: {
            data: {
              form: {
                options: {
                  amountChangeWaitsNextCycle: data.amountChangeWaitsNextCycle,
                },
                mode: data.mode,
                amount: Math.round(data.amount * 100), // Convert to cents
                paymentEvery: parseInt(data.paymentEvery),
                endDate: data.endDate,
                paymentInterval: data.mode ? undefined : parseInt(data.paymentInterval),
                paymentID: data.paymentID,
                planName: data.planName,
              },
              id: planID,
            },
            groupID,
          },
        },
      });
    } catch (e) {
      console.error('Update Payment Plan error:', e);
      setIsDataUpdating(false);
    }
  };

  return (
    <>
      <Button color="light" onClick={handleOpen}>
        Edit Plan
      </Button>
      <Modal show={isOpen} onClose={handleClose} size="xl">
        <Modal.Header>
          <h3 className="text-xl font-semibold text-blue-600">Edit Subscription</h3>
        </Modal.Header>
        <Modal.Body>
          <SpinnerLoading isLoading={updatePaymentPlanLoading} />
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 py-12 md:grid-cols-2">
                <FormInput
                  id="planName"
                  name="planName"
                  label="Plan Name"
                  rules={{ required: 'Plan name is required' }}
                />
                <FormInput
                  id="amount"
                  name="amount"
                  label="Amount"
                  type="number"
                  rules={{
                    required: 'Amount is required',
                    min: { value: 0.01, message: 'Amount must be greater than 0' },
                  }}
                />
                <FormSelect
                  id="mode"
                  name="mode"
                  label="Billing Mode"
                  rules={{ required: 'Billing mode is required' }}
                  options={[
                    { label: 'Daily', value: 'DAILY' },
                    { label: 'Weekly', value: 'WEEKLY' },
                    { label: 'Monthly', value: 'MONTHLY' },
                    { label: 'Yearly', value: 'YEARLY' },
                    { label: 'Custom', value: '' },
                  ]}
                />
                <FormInput
                  id="paymentEvery"
                  name="paymentEvery"
                  label="Payment Every"
                  type="number"
                  rules={{
                    required: 'Payment frequency is required',
                    min: { value: 1, message: 'Must be at least 1' },
                  }}
                />
                {!methods.watch('mode') && (
                  <FormInput
                    id="paymentInterval"
                    name="paymentInterval"
                    label="Payment Interval (days)"
                    type="number"
                    rules={{ required: 'Payment interval is required when mode is custom' }}
                  />
                )}
                <FormDatepicker id="endDate" name="endDate" label="End Date" />
                <div className="col-span-1 md:col-span-2">
                  <FormCheckbox
                    id="amountChangeWaitsNextCycle"
                    name="amountChangeWaitsNextCycle"
                    label="Defer amount changes to next billing cycle"
                  />
                </div>
              </div>
              <div className="mt-4 flex justify-end space-x-3">
                <Button color="gray" onClick={handleClose}>
                  Cancel
                </Button>
                <Button type="submit" color="blue" disabled={updatePaymentPlanLoading}>
                  Update Subscription
                </Button>
              </div>
            </form>
          </FormProvider>
        </Modal.Body>
      </Modal>
    </>
  );
};
