import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { useForm, FormProvider } from 'react-hook-form';
import { message } from '@/components/shared/utils';
import { Gateway_CreateProductDocument } from '@/graphql/generated/graphql';
import { useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { useLocationSelector } from '@/components/hooks';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { ProductForm } from './product-form';
import { ProductFormData } from '../utils/types';
import { floatToInt2Dec } from '@/lib/utils';

type ProductAddModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ProductAddModal = ({ isOpen, onClose }: ProductAddModalProps) => {
  const [images, setImages] = useState<File[]>([]);
  const { locationFilter } = useLocationSelector({ readonly: true });

  const methods = useForm<ProductFormData>({});

  const { reset } = methods;

  const [createProductMutation, { loading: createProductLoading }] = useMutation(
    Gateway_CreateProductDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Product'));
        reset();
        onClose();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Product', error.message));
      },
    },
  );

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setImages([...images, ...Array.from(event.target.files)]);
    }
  };

  const removeImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const parseImages = async (imageList: File[]): Promise<Array<{ b64: string; type: string }>> => {
    const promises = imageList.map((image) => {
      return new Promise<{ b64: string; type: string }>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          resolve({
            b64: reader.result as string,
            type: image.type,
          });
        };
        reader.onerror = reject; // Handle errors
        reader.readAsDataURL(image); // Read the file as a data URL (Base64)
      });
    });

    return Promise.all(promises);
  };

  const onSubmitForm = async (data: ProductFormData) => {
    try {
      const base64Images = await parseImages(images);
      await createProductMutation({
        variables: {
          input: {
            groupID: locationFilter?.id ?? '',
            data: {
              form: {
                name: data.productName,
                category: data.category,
                subCategory: data.subCategory,
                brand: data.brand,
                price: floatToInt2Dec(parseFloat(data.price)),
                // itemWeight: data.itemWeight ? Number(data.itemWeight) : 0,
                // length: data.length ? Number(data.length) : 0,
                // breadth: data.breadth ? Number(data.breadth) : 0,
                // width: data.width ? Number(data.width) : 0,
                description: data.description,
                isInStore: data.taxableItem === 'inStore' || data.taxableItem === 'both',
                isOnline: data.taxableItem === 'online' || data.taxableItem === 'both',
                productImages: base64Images,
                sku: data.sku,
                isRecurring: data.isRecurring,

                recurringMode: data.recurringMode ?? '',
                recurringInterval: data.recurringInterval
                  ? parseInt(data.recurringInterval)
                  : undefined,
                recurringFrequency: data.recurringFrequency
                  ? parseInt(data.recurringFrequency)
                  : undefined,
                recurringTotalCycles: data.recurringTotalCycles
                  ? parseInt(data.recurringTotalCycles)
                  : undefined,
                recurringTrialDays: data.recurringTrialDays
                  ? parseInt(data.recurringTrialDays)
                  : undefined,
                recurringSetupFee: data.recurringSetupFee
                  ? floatToInt2Dec(parseInt(data.recurringSetupFee))
                  : undefined,
              },
            },
          },
        },
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  return (
    <Modal show={isOpen} onClose={onClose} size="2xl">
      <Modal.Header className="flex items-center justify-between">
        <h3 className="text-xl font-semibold">Add Product</h3>
      </Modal.Header>
      <Modal.Body>
        <SpinnerLoading isLoading={createProductLoading} />
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmitForm)} className="space-y-4">
            <ProductForm
              handleImageUpload={handleImageUpload}
              images={images}
              removeImage={removeImage}
            />
            <div className="flex gap-5">
              <Button type="submit" color="blue">
                Add product
              </Button>
            </div>
          </form>
        </FormProvider>
      </Modal.Body>
    </Modal>
  );
};
