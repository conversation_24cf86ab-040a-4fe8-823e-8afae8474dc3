// import { ConfirmDialog } from '@/components/globals';
// import { useLocationSelector } from '@/components/hooks';
// import { message } from '@/components/shared/utils';
// import {
//   Gateway_SetDefaultPaymentMethodDocument,
//   GatewayUniCustomerOutputPaymentCards,
// } from '@/graphql/generated/graphql';
// import { useMutation } from '@apollo/client';
// import { Button } from 'flowbite-react';
// import { Dispatch, SetStateAction, useEffect, useState } from 'react';
// import { FaRegCheckCircle } from 'react-icons/fa';
// import { toast } from 'react-toastify';
import { GatewayUniCustomerOutputPaymentCards } from '@/graphql/generated/graphql';
import { Dispatch, SetStateAction } from 'react';

type CustomerCardChangeDefaultProps = {
  card: GatewayUniCustomerOutputPaymentCards;
  setParentLoading: Dispatch<SetStateAction<boolean>>;
  refetch: () => void;
  customerID: string;
};

// export const CustomerCardChangeDefault = ({
//   card,
//   customerID,
//   refetch,
//   setParentLoading,
// }: CustomerCardChangeDefaultProps) => {
//   const { locationFilter } = useLocationSelector({ readonly: true, onlyActive: true });
//   const [delatePaymentConfirmDialog, setChangePaymentConfirmDialog] = useState(false);

//   const [changePaymentMethodMutation, { loading: changePaymentMethodLoading }] = useMutation(
//     Gateway_SetDefaultPaymentMethodDocument,
//     {
//       onCompleted: (_) => {
//         toast.success(message.api.successUpdate('Payment Method'));
//         refetch();
//       },
//       onError: (error) => {
//         toast.error(message.api.errorUpdate('Payment Method', error.message));
//       },
//     },
//   );
//   const handleChangePaymentMethod = async () => {
//     try {
//       setChangePaymentConfirmDialog(false);
//       await changePaymentMethodMutation({
//         variables: {
//           input: {
//             groupID: locationFilter?.id ?? '',
//             data: {
//               cardID: card.cardID ?? '',
//               customerID,
//             },
//           },
//         },
//       });
//     } catch (e) {
//       console.error('Delete Payment method Mutation error: ', e);
//     }
//   };

//   useEffect(() => {
//     setParentLoading(changePaymentMethodLoading);
//   }, [changePaymentMethodLoading]);

//   return (
//     <div>
//       <FaRegCheckCircle size={20} onClick={() => setChangePaymentConfirmDialog(true)} />
//       <ConfirmDialog
//         header={<h1>Update default card</h1>}
//         body={
//           <div className="flex w-full items-center justify-center">
//             <h3 className="mb-5 text-center text-lg font-normal text-gray-500 dark:text-gray-400">
//               Are you sure you want to use <b>********{card.last4}</b> as you default card?
//             </h3>
//           </div>
//         }
//         actions={
//           <div className="flex w-full items-center justify-center gap-4">
//             <Button color="gray" onClick={() => setChangePaymentConfirmDialog(false)}>
//               No, cancel
//             </Button>
//             <Button color="failure" onClick={handleChangePaymentMethod}>
//               Yes, I'm sure
//             </Button>
//           </div>
//         }
//         open={delatePaymentConfirmDialog}
//         setOpen={setChangePaymentConfirmDialog}
//       />
//     </div>
//   );

// Temporarily disabled customer functionality
export const CustomerCardChangeDefault = () => {
  return null;
};
