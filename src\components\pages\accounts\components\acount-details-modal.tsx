import React from 'react';
import { StatusChip } from '@/components/globals';
import { moneyFormat } from '@/lib/utils';
import { <PERSON>ton, Card, Modal } from 'flowbite-react';
import { useState } from 'react';
import { FaRegFlag } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { getMockAccountDetails } from '@/lib/mock/account-details-data';

export const AccountDetailsModal = ({
  isOpen,
  onClose,
  accountData: initialData,
  onApprove,
  onReject,
  onEdit,
  onDelete,
}) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedData, setEditedData] = useState(initialData);

  // Load saved data from localStorage on mount
  React.useEffect(() => {
    const savedData = localStorage.getItem(`account_${initialData.id}`);
    if (savedData) {
      const parsed = JSON.parse(savedData);
      setEditedData(parsed);
    }
  }, [initialData.id]);

  // Load mock account details based on the account ID
  const baseAccountData = getMockAccountDetails(initialData.id);
  const accountData = isEditMode ? editedData : { ...baseAccountData, ...editedData };

  const handleEdit = () => {
    setIsEditMode(true);
  };

  const handleSave = () => {
    // Save only rate and address to localStorage
    const saveData = {
      rate: editedData.rate,
      address: editedData.address,
    };
    localStorage.setItem(`account_${initialData.id}`, JSON.stringify(saveData));
    setIsEditMode(false);
    toast.success('Rate and address updated successfully!');
  };

  const handleCancel = () => {
    setEditedData(baseAccountData);
    setIsEditMode(false);
  };

  const handleFieldChange = (field: string, value: string) => {
    setEditedData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Debug: Log the account status
  console.log('Account Status:', accountData.accountStatus);

  return (
    <>
      <Modal show={isOpen} onClose={onClose} size="5xl">
        <Modal.Header className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text text-xl font-semibold text-blue-600">{accountData.friendlyName}</h3>
            <StatusChip
              variant={
                accountData.accountStatus === 'active' || accountData.accountStatus === 'approved'
                  ? 'success'
                  : accountData.accountStatus === 'pending' || accountData.accountStatus === 'sent'
                    ? 'info'
                    : accountData.accountStatus === 'reject' ||
                        accountData.accountStatus === 'inactive'
                      ? 'danger'
                      : accountData.accountStatus === 'submitted'
                        ? 'warning'
                        : 'neutral'
              }
              label={
                accountData.accountStatus.charAt(0).toUpperCase() +
                accountData.accountStatus.slice(1)
              }
              big
            />
          </div>
        </Modal.Header>
        <Modal.Body>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <Card className="mb-4">
                <DetailItem label="Date Added" value={accountData.dateAdded} />
                <DetailItem
                  label="Account Status"
                  value={
                    accountData.paymentType.charAt(0).toUpperCase() +
                    accountData.paymentType.slice(1)
                  }
                />
                <div className="flex flex-col gap-3 rounded border-t py-2">
                  <div className="font-semibold text-gray-700">Summary</div>
                  <DetailItem
                    label="This Month Sales"
                    value={moneyFormat(accountData.metrics.monthlySales)}
                  />
                  <DetailItem
                    label="Closed Transactions"
                    value={accountData.metrics.closedTransactions.toLocaleString()}
                  />
                  <DetailItem
                    label="Available Balance"
                    value={moneyFormat(accountData.metrics.availableBalance)}
                  />
                </div>

                <div className="flex flex-col gap-3 rounded border-t py-2">
                  <div className="font-semibold text-gray-700">Limits</div>
                  <DetailItem
                    label="[Card] Per Transaction"
                    value={moneyFormat(accountData.limits.cardPerTransaction)}
                  />
                  <DetailItem
                    label="[Card] Monthly"
                    value={moneyFormat(accountData.limits.cardMonthly)}
                  />
                  <DetailItem
                    label="[Card] Monthly Limit Percentage"
                    value={`${accountData.limits.cardMonthlyPercentage}%`}
                  />
                  <DetailItem
                    label="[Bank Transfer] Per Transaction"
                    value={moneyFormat(accountData.limits.bankTransferPerTransaction)}
                  />
                  <DetailItem
                    label="[Bank Transfer] Monthly"
                    value={moneyFormat(accountData.limits.bankTransferMonthly)}
                  />
                  <DetailItem
                    label="[Bank Transfer] Monthly Limit Percentage"
                    value={`${accountData.limits.bankTransferMonthlyPercentage}%`}
                  />
                </div>
              </Card>
            </div>

            <div>
              <Card className="mb-4">
                {isEditMode ? (
                  <>
                    <EditableDetailItem
                      label="Business Name"
                      value={accountData.dbaName}
                      field="dbaName"
                      onChange={handleFieldChange}
                    />
                    <EditableDetailItem
                      label="Email"
                      value={accountData.email}
                      field="email"
                      type="email"
                      onChange={handleFieldChange}
                    />
                    <EditableDetailItem
                      label="Phone"
                      value={accountData.phone}
                      field="phone"
                      type="tel"
                      onChange={handleFieldChange}
                    />
                    <EditableDetailItem
                      label="Country"
                      value={accountData.country}
                      field="country"
                      onChange={handleFieldChange}
                    />
                    <EditableDetailItem
                      label="Billing Address"
                      value={accountData.billingAddress}
                      field="billingAddress"
                      onChange={handleFieldChange}
                    />
                    <EditableDetailItem
                      label="Rate"
                      value={accountData.rate}
                      field="rate"
                      type="number"
                      onChange={handleFieldChange}
                    />
                    <EditableDetailItem
                      label="Address"
                      value={accountData.address}
                      field="address"
                      onChange={handleFieldChange}
                    />
                  </>
                ) : (
                  <>
                    <DetailItem label="Business Name" value={accountData.dbaName} />
                    <DetailItem label="Email" value={accountData.email} />
                    <DetailItem label="Phone" value={accountData.phone} />
                    <DetailItem
                      label="Country"
                      value={
                        <span className="flex items-center">
                          <FaRegFlag className="mr-2 text-red-500" />
                          {accountData.country}
                        </span>
                      }
                    />
                    <DetailItem label="Billing Address" value={accountData.billingAddress} />
                    <DetailItem label="Rate" value={accountData.rate} />
                    <DetailItem label="Address" value={accountData.address} />
                  </>
                )}
              </Card>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <div className="flex w-full justify-between">
            <div className="flex gap-2">
              {(accountData.accountStatus === 'pending' ||
                accountData.accountStatus === 'sent') && (
                <>
                  <Button
                    color="success"
                    onClick={() => {
                      onApprove?.();
                      onClose();
                    }}
                  >
                    Approve
                  </Button>
                  <Button
                    color="failure"
                    onClick={() => {
                      onReject?.();
                      onClose();
                    }}
                  >
                    Reject
                  </Button>
                </>
              )}
              {(accountData.accountStatus === 'sent' ||
                accountData.accountStatus === 'draft' ||
                accountData.accountStatus === 'active' ||
                accountData.accountStatus === 'approved' ||
                accountData.accountStatus === 'disabled') &&
                !isEditMode && (
                  <Button color="info" onClick={handleEdit}>
                    Edit
                  </Button>
                )}
              {isEditMode && (
                <>
                  <Button color="success" onClick={handleSave}>
                    Save
                  </Button>
                  <Button color="gray" onClick={handleCancel}>
                    Cancel
                  </Button>
                </>
              )}
              {(accountData.accountStatus === 'pending' ||
                accountData.accountStatus === 'sent' ||
                accountData.accountStatus === 'draft' ||
                accountData.accountStatus === 'active' ||
                accountData.accountStatus === 'approved' ||
                accountData.accountStatus === 'disabled' ||
                accountData.accountStatus === 'reject') && (
                <Button
                  color="failure"
                  onClick={() => {
                    onDelete?.();
                    onClose();
                  }}
                >
                  Delete
                </Button>
              )}
            </div>
            <Button color="gray" onClick={onClose}>
              Close
            </Button>
          </div>
        </Modal.Footer>
      </Modal>
    </>
  );
};

const DetailItem = ({ label, value }) => (
  <div className="flex justify-between border-b py-1 last:border-b-0">
    <span className="font-semibold text-gray-700">{label}</span>
    <span className="text-gray-600">{value}</span>
  </div>
);

const EditableDetailItem = ({ label, value, field, onChange, type = 'text' }) => (
  <div className="flex justify-between border-b py-1 last:border-b-0">
    <span className="font-semibold text-gray-700">{label}</span>
    <input
      type={type}
      value={value || ''}
      onChange={(e) => onChange(field, e.target.value)}
      className="border-none bg-transparent text-right text-gray-600 outline-none focus:rounded focus:bg-gray-50 focus:px-2"
    />
  </div>
);
