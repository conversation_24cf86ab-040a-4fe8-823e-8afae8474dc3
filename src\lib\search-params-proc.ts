export const searchParamsProc = (args: {
  values: { key: string; value: string | null }[];
  override?: boolean; // meaning clear the search params first
  refresh?: boolean;
}) => {
  if (args.override) {
    window.history.pushState({}, '', window.location.pathname);
  }
  const searchParams = new URLSearchParams(window.location.search);
  args.values.forEach(({ key, value }) => {
    if (value === null) {
      searchParams.delete(key);
      return;
    }
    searchParams.set(key, value);
  });
  window.history.pushState({}, '', `${window.location.pathname}?${searchParams}`);
  if (args.refresh) {
    window.location.reload();
  }
};
