import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ponent,
  <PERSON><PERSON><PERSON>,
  StatusFilter,
  useDataGridView,
  Variant,
} from '@/components/globals';

import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { useLocationSelector } from '@/components/hooks';
import moment from 'moment';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import { Gateway_PaymentPlansDocument } from '@/graphql/generated/graphql';
import useDebounce from '@/components/hooks/useDebounce';
import PaymentPlanAdd from '../components/payment-plan-add';
import { PaymentPlanUpdate } from '../components';
import { useSearchParams } from 'next/navigation';
import { moneyFormat } from '@/lib/utils';
import { StaticInfoBox } from '@/components/globals/info-box/StaticInfoBox';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { apolloClient } from '@/lib/graphql/ApolloClient';

export enum PaymentPlanStatus {
  active = 'active',
  Success = 'success',
  inactive = 'inactive',
  completed = 'completed',
  pending = 'pending',
  paid = 'paid',
  unpaid = 'unpaid',
}

export const getPaymentPlanStatus = (status: PaymentPlanStatus | string): [Variant, string] => {
  const statusMap: Record<PaymentPlanStatus, Variant> = {
    [PaymentPlanStatus.active]: 'success',
    [PaymentPlanStatus.Success]: 'success',
    [PaymentPlanStatus.inactive]: 'neutral',
    [PaymentPlanStatus.completed]: 'success',
    [PaymentPlanStatus.pending]: 'warning',
    [PaymentPlanStatus.paid]: 'success',
    [PaymentPlanStatus.unpaid]: 'danger',
  };

  const variant = statusMap[status] || 'neutral';
  const label = `${status?.charAt(0).toUpperCase()}${status?.slice(1)}`;

  return [variant, label];
};

export const PaymentPlansTab = () => {
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);

  const queryParams = useSearchParams();
  const paymentPlanId = queryParams?.get('id');
  const [selectedPaymentPlanId, setSelectedPaymentPlanId] = useState(paymentPlanId ?? null);

  const {
    locationFilter,
    locationSelectorElement,
    loading: loadingGroupList,
  } = useLocationSelector({
    onlyActive: true,
  });

  //TODO: API DON"T HAVE TEST DATA YET, CHANGE THIS WHEN WE HAVE TEST DATA

  const {
    data: paymentPlansData,
    loading: paymentPlansLoading,
    refetch: refetchpaymentPlansData,
    error: paymentPlansError,
    pageSize,
    currentPage,
    onPageChange,
    onPageSizeChange,
    setSortField,
    setIsAscendOrder,
    filterValue,
    setFilterValue,
    maxVariables,
  } = useDataGridView({
    query: Gateway_PaymentPlansDocument,
    options: {
      variables: {
        input: {
          groupID: locationFilter?.id ?? '',
          data: {
            page: {
              page: 1,
              pageSize: 10,
            },
          },
        },
      },
      skip: !locationFilter?.id,
    },
    searchParams: debouncedSearchQuery,
    initialFilterField: 'status',
    initialFilterValue: 'All',
  });

  useEffect(() => {
    if (locationFilter?.id) {
      refetchpaymentPlansData();
    }
  }, [locationFilter]);

  useEffect(() => {
    if (paymentPlansError) {
      toast.error(paymentPlansError.message);
    }
  }, [paymentPlansError]);

  const rows = useMemo(() => {
    const data = paymentPlansData?.gateway_paymentPlans?.data;
    if (!data) return [];
    return data.filter((item): item is NonNullable<typeof item> => item !== null);
  }, [paymentPlansData?.gateway_paymentPlans?.data]);

  const fetchExportData = async () => {
    const result = await apolloClient.query({
      query: Gateway_PaymentPlansDocument,
      variables: maxVariables,
    });
    return result?.data?.gateway_paymentPlans?.data ?? [];
  };

  const columns: Column[] = [
    {
      key: 'planID',
      header: 'plan_id',
      width: '120px',
      onClick: (row) => setSelectedPaymentPlanId(row?.planID ?? ''),
    },
    {
      key: 'planName',
      header: 'Plan name',
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'startDate',
      header: 'Start Date',
      width: '80px',
      valueGetter: (row) => moment(row?.startDate).format('MM/DD/YYYY'),
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'customerName',
      header: 'Customer',
    },
    {
      key: 'amount',
      header: 'Amount',
      width: '50px',
      renderCell: (row) => <span>{moneyFormat(row?.amount)}</span>,
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
    {
      key: 'last4',
      header: 'Last 4',
      width: '50px',
    },
    // {
    //   key: 'expires',
    //   header: 'Card Expiry',
    //   width: '80px',
    //   valueGetter: (row) => row?.expires,
    // },
    {
      key: 'duration',
      header: 'Duration',
      width: '80px',
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getPaymentPlanStatus(row.status);
        return <StatusChip variant={status} label={label} />;
      },
      sortable: true,
      onServerSort: (key) => {
        setSortField(key);
        setIsAscendOrder((v) => !v);
      },
    },
  ];

  return (
    <>
      <StaticInfoBox />
      <div className="items-bottom flex justify-between">
        <PageHeader text="Payment Plans" />
        <PaymentPlanAdd refetchListPage={refetchpaymentPlansData} />
      </div>
      <div className="border-b border-gray-300 sm:flex">
        <div className="w-1/4">{locationSelectorElement}</div>
        <div className="ml-auto flex items-center space-x-2 sm:space-x-3">
          <TopComponent value={searchValue} setValue={setSearchValue}>
            <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="paymentPlans" />
          </TopComponent>
        </div>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={rows}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={paymentPlansLoading || loadingGroupList}
          actionComponent={
            <StatusFilter
              value={filterValue}
              setValue={setFilterValue}
              statusList={Object.values(PaymentPlanStatus)}
            />
          }
          mode="server"
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          totalRecords={paymentPlansData?.gateway_paymentPlans?.page?.total ?? 0}
        />
      </div>

      <PaymentPlanUpdate
        isOpen={selectedPaymentPlanId !== null}
        onClose={() => setSelectedPaymentPlanId(null)}
        refetchListPage={refetchpaymentPlansData}
        queryData={{
          planID: selectedPaymentPlanId ?? '',
          groupID: locationFilter?.id ?? '',
        }}
      />
    </>
  );
};
