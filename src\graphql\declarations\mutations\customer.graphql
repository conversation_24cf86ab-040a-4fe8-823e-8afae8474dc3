mutation Gateway_addCustomer($input: Gateway_addCustomerInput!) {
  gateway_addCustomer(input: $input) {
    customerID
  }
}
mutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {
  gateway_updateCustomer(input: $input) {
    customerID
  }
}

mutation Gateway_updateCustomer($input: Gateway_updateCustomerInput!) {
  gateway_updateCustomer(input: $input) {
    customerID
  }
}

mutation Gateway_deleteCustomer($input: Gateway_deleteCustomerInput!) {
  gateway_deleteCustomer(input: $input) {
    customerID
  }
}

mutation Gateway_addPaymentMethod($input: Gateway_addPaymentMethodInput!) {
  gateway_addPaymentMethod(input: $input) {
    customerID
  }
}

mutation Gateway_deletePaymentMethod($input: Gateway_deletePaymentMethodInput!) {
  gateway_deletePaymentMethod(input: $input) {
    customerID
  }
}

mutation Gateway_setDefaultPaymentMethod($input: Gateway_setDefaultPaymentMethodInput!) {
  gateway_setDefaultPaymentMethod(input: $input) {
    customerID
  }
}
