'use client';

import Script from 'next/script';
import { useRef } from 'react';

export function GLPayTokenizerComponent(props: {
  apiKey: string;
  merchantID?: string;
  liveMode: boolean;
  onSubmission: (resp: any) => void;
  onLogs?: (log: any) => void;
  isSubmitting?: boolean;
  setIsSubmitting?: (isSubmitting: boolean) => void;
  isReady?: { [key: string]: boolean };
  setIsReady?: any;
  config: {
    ids: {
      cardNumber: string;
      cardExpiration: string;
      cardCvv: string;
      cardHolderName?: string;
      submitButton: string;
      paymentForm: string;
    };
    text?: {
      submitText?: string;
    };
    styles?: {
      input?: {
        borderRadius?: string;
        borderColor?: string;
        focusBorderColor?: string;
        padding?: string;
        marginBottom?: string;
        backgroundColor?: string;
        color?: string;
      };
      button?: {
        borderRadius?: string;
        borderColor?: string;
        textColor?: string;
        hoverBgColor?: string;
        hoverTextColor?: string;
        padding?: string;
        margin?: string;
        fontSize?: string;
      };
      label?: {
        fontSize?: string;
        fontWeight?: string;
        color?: string;
        marginBottom?: string;
      };
      font?: {
        family?: string;
        style?: string;
        weight?: string;
        display?: string;
        src?: string;
        unicodeRange?: string;
      };
    };
  };
}) {
  const gpayURL = 'https://js.globalpay.com';
  const loaderObject = useRef<any>();
  return (
    // @ts-ignore
    <>
      <Script
        src={`${gpayURL}/4.1.3/globalpayments.js`}
        onLoad={() => {
          // @ts-ignore
          const cardForm = GlobalPayments.ui.form({
            fields: {
              'card-number': {
                placeholder: '•••• •••• •••• ••••',
                target: props.config.ids.cardNumber,
              },
              'card-expiration': {
                placeholder: 'MM / YY',
                target: props.config.ids.cardExpiration,
              },
              'card-cvv': {
                placeholder: '•••',
                target: props.config.ids.cardCvv,
              },
              'card-holder-name': props.config.ids.cardHolderName
                ? {
                    placeholder: 'Jane Smith',
                    target: props.config.ids.cardHolderName,
                  }
                : undefined,
              submit: {
                target: props.config.ids.submitButton,
                label: props.config.text?.submitText || 'Pay Now',
                text: props.config.text?.submitText || 'Pay Now',
              },
            },

            styles: {
              '@font-face': props.config.styles?.font
                ? {
                    'font-family': props.config.styles.font.family,
                    'font-style': props.config.styles.font.style,
                    'font-weight': props.config.styles.font.weight,
                    'font-display': props.config.styles.font.display,
                    src: props.config.styles.font.src,
                    'unicode-range': props.config.styles.font.unicodeRange,
                  }
                : undefined,

              '#secure-payment-field': props.config.styles?.font
                ? {
                    'font-family': `${props.config.styles.font.family}, monospace`,
                  }
                : undefined,

              'input[type=text]': {
                'margin-bottom': props.config.styles?.input?.marginBottom || '20px',
                padding: props.config.styles?.input?.padding || '12px',
                border: `1px solid ${props.config.styles?.input?.borderColor || '#ccc'}`,
                'border-radius': props.config.styles?.input?.borderRadius || '10px',
                'background-color': props.config.styles?.input?.backgroundColor || '#ffffff',
                color: props.config.styles?.input?.color || '#000000',
              },

              'input[type=text]:focus-visible, input[type=tel]:focus-visible': {
                outline: 'none !important',
                border: `1px solid ${props.config.styles?.input?.focusBorderColor || '#71C5E8'}`,
                'box-shadow': `0 0 4px 0 ${props.config.styles?.input?.focusBorderColor || '#71C5E8'} inset`,
              },

              'input[type=tel]': {
                'margin-bottom': props.config.styles?.input?.marginBottom || '20px',
                padding: props.config.styles?.input?.padding || '12px',
                border: `1px solid ${props.config.styles?.input?.borderColor || '#ccc'}`,
                'border-radius': props.config.styles?.input?.borderRadius || '10px',
                'background-color': props.config.styles?.input?.backgroundColor || '#ffffff',
                color: props.config.styles?.input?.color || '#000000',
              },

              'button[type=button]:focus-visible': {
                'background-color': props.config.styles?.button?.hoverBgColor || '#1a56db',
                outline: 'none !important',
                border: `1px solid ${props.config.styles?.button?.borderColor || '#1a56db'}`,
                'box-shadow': '0 -1px 4px 0 gray inset',
              },

              'button[type=button]': {
                border: `1px solid ${props.config.styles?.button?.borderColor || '#1a56db'}`,
                color: props.config.styles?.button?.textColor || '#1a56db',
                padding: props.config.styles?.button?.padding || '12px',
                margin: props.config.styles?.button?.margin || '10px',
                'border-radius': props.config.styles?.button?.borderRadius || '10px',
                cursor: 'pointer',
                'font-size': props.config.styles?.button?.fontSize || '17px',
                width: '100%',
              },

              'button[type=button]:hover': {
                'background-color': props.config.styles?.button?.hoverBgColor || '#1a56db',
                color: props.config.styles?.button?.hoverTextColor || 'white',
              },
            },
          });

          // configuring Hosted Fields
          // @ts-ignore
          GlobalPayments.configure({
            accessToken: props.apiKey,
            merchantId: props.merchantID,
            apiVersion: '2021-03-22',
            enableTwoDigitExpirationYear: true,
            env: props.liveMode ? 'production' : 'sandbox', // or "production"
            enableAutocomplete: true,
            'fieldValidation.enabled': true,
          });

          // method to notify that hosted fields have been initialized
          cardForm.ready(() => {
            // console.log('Registration of all credit card fields occurred');
            props.onLogs?.('Registration of all credit card fields occurred');
            //TODO: Add your successful message
          });

          // appending the token to the form as a hidden field and
          // submitting it to the server-side
          cardForm.on('token-success', (resp) => {
            // add payment token to form as a hidden input
            // console.log('token-success', resp);
            props.onLogs?.(resp);

            // console.log(resp);

            props.onSubmission(resp);
            if (loaderObject.current) {
              clearTimeout(loaderObject.current);
              loaderObject.current = null;
            }
            props.setIsSubmitting?.(false);
          });

          // add error handling if token generation is not successful
          cardForm.on('token-error', () => {
            // TODO: Add your error handling
            props.setIsSubmitting?.(false);
            if (loaderObject.current) {
              clearTimeout(loaderObject.current);
              loaderObject.current = null;
            }
          });

          // field-level event handlers. example:
          cardForm.on('card-number', 'register', () => {
            // console.log('Registration of Card Number occurred');
            props.onLogs?.('Registration of Card Number occurred');
          });

          cardForm.on('card-number-test', (resp) => {
            // console.log('Card Number Test:', resp);
            props.onLogs?.(`Card Number Test: ${resp.valid}`);
            props.setIsReady?.((s: any) => ({ ...s, cardNumber: resp.valid }));
          });

          cardForm.on('card-expiration-test', (resp) => {
            // console.log('Card Expiration Test:', resp);
            props.onLogs?.(`Card Expiration Test: ${resp.valid}`);
            props.setIsReady?.((s: any) => ({ ...s, cardExpiration: resp.valid }));
          });

          cardForm.on('card-cvv-test', (resp) => {
            // console.log('Card CVV Test:', resp);
            props.onLogs?.(`Card CVV Test: ${resp.valid}`);
            props.setIsReady?.((s: any) => ({ ...s, cardCvv: resp.valid }));
          });

          cardForm.on('card-type', (resp) => {
            // console.log('Card Type Test:', resp);
            props.onLogs?.(`Card Type Test: ${resp.cardType}`);
            props.setIsReady?.((s: any) => ({ ...s, cardType: resp.cardType }));
          });

          cardForm.on('submit', 'click', (resp) => {
            // console.log('Submit Click:', resp);
            props.onLogs?.(`Submit Click: ${resp}`);
            props.setIsSubmitting?.(true);
            loaderObject.current = setTimeout(() => {
              props.setIsSubmitting?.(false);
            }, 3000);
          });

          // Prevent default form submission and log form data
          document
            ?.querySelector(props.config.ids.paymentForm)
            ?.addEventListener('submit', (event) => {
              event.preventDefault();
              // console.log('Form submission prevented.');
            });
        }}
        // load after the page has loaded
        strategy="afterInteractive"
      ></Script>
    </>
  );
}
