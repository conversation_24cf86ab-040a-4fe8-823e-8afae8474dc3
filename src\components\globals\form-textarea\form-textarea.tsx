import { ChangeEvent } from 'react';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';
import { Label, TextInput, Tooltip } from 'flowbite-react';
import { HelperText } from 'flowbite-react';
import FormToolTip, { FormToolTipProps } from '../form-tool-tip';
import { useFormFieldHelpers } from '@/components/hooks';
import { Textarea } from '@/components/ui/textarea';

export type FormTextAreaProps = {
  name: string;
  helperText?: string;
  id: string;
  rules?: Exclude<RegisterOptions, 'valueAsNumber' | 'valueAsDate' | 'setValueAs'>;
  visible?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  flex?: number;
  tooltip?: FormToolTipProps['tooltip'];
  onChangeCallback?: (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  variant?: 'primary' | 'secondary';
  label?: string;
  disabled?: boolean;
  defaultValue?: string;
  tabIndex?: number;
  endAdornment?: React.ReactNode;
  type?: string; // Add this line
};

export const FormTextArea = ({
  id,
  name,
  label,
  rules,
  disabled,
  helperText = '',
  defaultValue,
  visible = true,
  readOnly = false,
  maxLength,
  flex,
  tabIndex,
  tooltip,
  endAdornment,
  variant = 'primary',
  onChangeCallback = (e: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => {
    e;
  },
  type = 'text', // Add this line
  ...props
}: FormTextAreaProps): JSX.Element => {
  const { control } = useFormContext();
  const { isRequired, isDisabled } = useFormFieldHelpers({
    disabled,
    rules,
  });

  return (
    <Controller
      name={name}
      key={id}
      control={control}
      rules={rules}
      defaultValue={defaultValue || ''}
      render={({ field: { onChange, onBlur, value, ref }, fieldState: { invalid, error } }) => (
        <div className={`relative w-full ${visible ? '' : 'hidden'} ${flex ? `flex-${flex}` : ''}`}>
          {label && (
            <Label htmlFor={id} className="mb-2 flex">
              {label} {isRequired() && '*'}
              {tooltip && <FormToolTip tooltip={tooltip} mode="input" />}
            </Label>
          )}
          <div className="relative w-full">
            <Textarea
              id={id}
              ref={ref}
              value={value}
              onChange={(e) => {
                if (maxLength && e.target.value.length > maxLength) return;
                onChange(e);
                onChangeCallback(e);
              }}
              onBlur={(e) => {
                onChange(e);
                onBlur();
              }}
              readOnly={readOnly}
              disabled={isDisabled()}
              maxLength={maxLength}
              autoComplete="off"
              placeholder={`Enter ${label}`}
              tabIndex={tabIndex}
              className={`w-full ${endAdornment ? 'pr-12' : ''}`}
              {...props}
            />
            {endAdornment && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                {endAdornment}
              </div>
            )}
            {/*{tooltip && (
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <FormToolTip tooltip={tooltip} mode="input" />
              </div>
            )} */}
          </div>
          <HelperText color={invalid ? 'failure' : 'default'}>
            {invalid ? error?.message : helperText}{' '}
          </HelperText>
        </div>
      )}
    />
  );
};

export default FormTextArea;
