import { Spinner, TextInput } from 'flowbite-react';
import { ChangeEvent, useEffect, useRef, useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { cn } from '@/lib/utils';

export type AutoCompleteOption<TObj = Record<string, any>> = {
  id: string;
  label: string;
  obj?: TObj;
  disabled?: boolean;
  disabledMessage?: string;
};

type AutoCompleteInputProp = {
  options: AutoCompleteOption[];
  onChange?: (option: AutoCompleteOption) => void;
  initialText?: string;
  onTextChange?: (text: string) => void;
  value?: AutoCompleteOption | null;
  inputValue?: string;
  placeholder?: string;
  label?: string;
  className?: string;
  optionsLoading?: boolean;
  disabled?: boolean;
  onQueryText?: (text: string) => void;
};

export const AutoCompleteInput = ({
  options,
  onChange,
  initialText,
  onTextChange,
  value,
  inputValue,
  className,
  disabled,
  placeholder,
  label,
  optionsLoading,
  onQueryText,
}: AutoCompleteInputProp) => {
  const [query, setQuery] = useState(inputValue ?? initialText ?? value?.label ?? ''); // Initialize with value prop

  useEffect(() => {
    if (onQueryText) {
      onQueryText(query);
    }
  }, [query]);

  useEffect(() => {
    if (initialText || initialText === '') {
      setQuery(initialText);
    }
  }, [initialText]);

  useEffect(() => {
    if (inputValue) {
      setQuery(inputValue);
    }
  }, [inputValue]);

  const [showDropdown, setShowDropdown] = useState(false);
  const [optionList, setOptionList] = useState<AutoCompleteOption[]>(options);
  const [selectedOption, setSelectedOption] = useState<AutoCompleteOption | null>(value ?? null);

  const divRef = useRef<HTMLDivElement>(null);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const inputValue = e.target.value;
    setShowDropdown(true);
    setQuery(inputValue);
    onTextChange && onTextChange(inputValue);

    // Filter the options based on the input value
    setOptionList(
      options.filter((option) => option.label.toLowerCase().includes(inputValue.toLowerCase())),
    );
  };

  const handleOptionClick = (option: AutoCompleteOption) => {
    setQuery(option.label);
    setSelectedOption(option);
    setShowDropdown(false);

    if (onChange) {
      onChange(option); // Notify parent of selection change
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (divRef.current && !divRef.current.contains(event.target as Node)) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClickOutside, true);
    return () => {
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, []);

  useEffect(() => {
    if (options.length > 0) {
      setOptionList(options);
    }
  }, [options]);

  return (
    <div
      className={cn('relative', className)}
      tabIndex={0}
      onFocus={() => !disabled && setShowDropdown(true)}
      ref={divRef}
    >
      <TextInput
        type="text"
        value={query}
        onChange={handleChange}
        disabled={disabled}
        placeholder={placeholder ? placeholder : `Select ${label}`}
        className="mb-0 w-full rounded border-none p-2 px-0"
      />
      {!optionsLoading && (
        <div className="absolute right-[10px] top-[20px]">
          {showDropdown ? <FaChevronUp /> : <FaChevronDown />}
        </div>
      )}
      {optionsLoading && (
        <div className="absolute right-[10px] top-[20px]">
          <Spinner size="sm" />
        </div>
      )}

      {showDropdown && (
        <ul className="absolute z-50 -mt-[8px] w-full rounded border border-gray-300 bg-white">
          {optionList.length === 0 ? (
            <li className="p-2">No results found</li>
          ) : (
            optionList.map((option) => (
              <li
                key={option.id}
                className={`relative p-2 ${
                  option.disabled
                    ? 'cursor-not-allowed opacity-50'
                    : 'cursor-pointer hover:bg-gray-200'
                } ${selectedOption?.id === option.id && 'bg-gray-100'}`}
                onClick={() => !option.disabled && handleOptionClick(option)}
                title={option.disabled ? option.disabledMessage : undefined}
              >
                <div className="flex items-center">
                  <span className="ml-2">{option.label}</span>
                </div>
              </li>
            ))
          )}
        </ul>
      )}
    </div>
  );
};
