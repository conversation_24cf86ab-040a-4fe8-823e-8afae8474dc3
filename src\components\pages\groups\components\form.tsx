'use client';

import { Button } from '@/components/ui/button';
import { DialogClose } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const GroupForm = () => {
  return (
    <div className="flex flex-col gap-10">
      <div className="grid grid-cols-6 gap-8">
        <div className="col-span-3">
          <Label>
            Group Name<span className="text-[#F46A6A]">*</span>
          </Label>
          <Select value={'test'} onValueChange={() => {}}>
            <SelectTrigger className="w-full !border !border-[#B3B3B3] text-start">
              <SelectValue placeholder="Select Name" />
            </SelectTrigger>
            <SelectContent>
              {['test1', 'test2'].map((data, index) => (
                <SelectItem key={index} value={data}>
                  {data}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex items-center justify-end gap-8">
        <DialogClose asChild>
          <Button variant="secondary" className="w-full px-8">
            Cancel
          </Button>
        </DialogClose>
        <Button disabled={false} variant="primary" className="w-full px-8" onClick={() => {}}>
          Create
        </Button>
      </div>
    </div>
  );
};

export default GroupForm;
