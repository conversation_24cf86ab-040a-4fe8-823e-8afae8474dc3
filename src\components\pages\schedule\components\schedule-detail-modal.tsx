import React, { useEffect, useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card } from 'flowbite-react';
import { HiX } from 'react-icons/hi';
import { Gateway_PaymentPlanDocument } from '@/graphql/generated/graphql';
import { useQuery } from '@apollo/client';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { toast } from 'react-toastify';
import { getPaymentPlanStatus } from '../../payment-plan/page/payment-plan-tab';
import { StatusChip } from '@/components/globals';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import { GeneralDetailsSection } from '@/components/globals/general-details-section';
import { CustomerDetailSection } from '../../customer/components/customer-detail-section';
import { formatToLocalDateString } from '@/components/shared/utils';
import { PaymentPlanCancel } from '../../payment-plan/components/payment-plan-cancel';
import { int2DecToFloat, moneyFormat, moneyFormatString } from '@/lib/utils';
import { PaymentPlanEdit } from './payment-plan-edit';

type PaymentPlanDetailsProps = {
  isOpen: boolean;
  onClose: () => void;
  queryData: {
    planID: string;
    groupID: string;
  };
};

export const PaymentPlanDetailsModal: React.FC<PaymentPlanDetailsProps> = ({
  isOpen,
  onClose,
  queryData,
}) => {
  const { planID, groupID } = queryData;
  const [isDataUpdating, setIsDataUpdating] = useState(false);
  const {
    data,
    loading: paymentPlanDataIsLoading,
    error,
    refetch,
  } = useQuery(Gateway_PaymentPlanDocument, {
    variables: {
      input: {
        data: {
          planID,
        },
        groupID,
      },
    },
    skip: !planID || !groupID,
  });

  const planData = data?.gateway_paymentPlan;

  useEffect(() => {
    refetch();
  }, [planID, groupID]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const filteredHistory = useMemo(() => {
    const data = planData?.history;
    if (!data) return [];
    return data.filter((item): item is NonNullable<typeof item> => item !== null);
  }, [planData?.history]);

  const columns: Column[] = [
    {
      key: 'date',
      header: 'DATE',
      valueGetter: (row) => formatToLocalDateString(row?.date),
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const [status, label] = getPaymentPlanStatus(row.status);
        return <StatusChip variant={status} label={label} />;
      },
    },

    {
      key: 'amount',
      header: 'Amount',
      renderCell: (row) => <span>{moneyFormat(row.amount)}</span>,
    },

    {
      key: 'transactionID',
      header: 'Transaction #',
      width: '170px',
    },
  ];
  const [status, label] = getPaymentPlanStatus(planData?.status ?? '');

  return (
    <Modal show={isOpen} onClose={onClose} size="7xl">
      <div className="p-6">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button color="gray" size="sm" onClick={onClose}>
              Back
            </Button>
            <h2 className="text-2xl font-semibold text-blue-600">Subscription Details</h2>
          </div>
          <Button color="gray" size="sm" onClick={onClose}>
            <HiX className="h-5 w-5" />
          </Button>
        </div>

        <div className="mb-6 flex items-start justify-between">
          <div className="flex items-center gap-5">
            <div className="flex items-center space-x-2">
              <span className="text-xl font-bold">Plan Name: {planData?.planName}</span>
            </div>
            <StatusChip variant={status} label={label} />
            <div className="text-gray-500">
              Date Created: {formatToLocalDateString(new Date(planData?.createdAt ?? ''))}
            </div>
          </div>
          <div className="flex gap-10 text-right">
            <div className="mb-2 text-3xl font-bold">${planData?.amount}</div>
            <div className="flex gap-3 space-x-2">
              {/* <Button color="light">Edit Plan</Button> */}
              {planData?.status !== 'Inactive' && (
                <>
                  <PaymentPlanEdit
                    refetch={refetch}
                    setIsDataUpdating={setIsDataUpdating}
                    queryData={{ planID, groupID }}
                    planData={planData}
                  />
                  <PaymentPlanCancel
                    refetch={refetch}
                    setIsDataUpdating={setIsDataUpdating}
                    queryData={{ planID, groupID }}
                  />
                </>
              )}
            </div>
          </div>
        </div>
        <SpinnerLoading isLoading={paymentPlanDataIsLoading || isDataUpdating} />
        {!paymentPlanDataIsLoading && (
          <div className="mb-8 grid max-h-[50vh] grid-cols-2 gap-8 overflow-y-auto">
            <Card>
              <div>
                <GeneralDetailsSection
                  details={[
                    { label: 'Plan Id', value: planData?.planID },
                    { label: 'Location', value: planData?.locationName },
                    {
                      label: 'Start Date',
                      value: formatToLocalDateString(new Date(planData?.startDate ?? '')),
                    },
                    {
                      label: 'Until',
                      value: formatToLocalDateString(new Date(planData?.endDate ?? '')),
                    },
                    // { label: 'Last 4', value: planData?.last4 },
                    {
                      label: 'Interval',
                      value: !planData?.mode ? `${planData?.paymentInterval} Days` : '--',
                    },
                    { label: 'Frequency', value: `Every ${planData?.paymentEvery} Instance` },
                    { label: 'Mode', value: `Per ${planData?.mode || 'Day'}` },
                    {
                      label: 'Subscription Credit',
                      value: moneyFormatString(int2DecToFloat(planData?.subscriptionCredit ?? 0)),
                    },
                    {
                      label: 'Next Payment Date',
                      value: planData?.nextPaymentDate
                        ? formatToLocalDateString(new Date(planData?.nextPaymentDate))
                        : '--',
                    },
                    {
                      label: 'Cancel Reason',
                      value: planData?.cancelReason || '--',
                    },
                    {
                      label: 'Recurring Refundable',
                      value: planData?.recurringRefundable ? 'Yes' : 'No',
                    },
                    // { label: 'Next Charge Date', value: `${planData?.paymentEvery}` },
                    // {
                    //   label: 'Actual Charge Frequency',
                    //   value: `${(planData?.paymentEvery || 1) * (planData?.paymentInterval || 1)} Days`,
                    // },
                  ]}
                />

                <div className="mt-8">
                  <h3 className="mb-4 text-lg font-semibold">History</h3>
                  <DataGridView
                    rows={filteredHistory}
                    columns={columns}
                    disablePagination
                    pageSize={100}
                  />
                  <div className="p-1 text-center text-sm text-gray-600">
                    <p>For older records, click here</p>
                  </div>
                </div>
              </div>
            </Card>
            <CustomerDetailSection
              customer={{
                customerID: planData?.customerID,
                name: planData?.customerName,
                email: planData?.customerEmail,
                phone: planData?.customerPhone,
                country: planData?.customerCountry,
                billingAddress: planData?.customerBillingAddress,
              }}
            />
          </div>
        )}
      </div>
    </Modal>
  );
};
