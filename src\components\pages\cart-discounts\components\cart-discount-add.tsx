import { useState } from 'react';
import { <PERSON><PERSON>, Modal } from 'flowbite-react';
import { CartDiscountForm } from './cart-discount-form';
import { useMutation } from '@apollo/client';
import { Gateway_CreateDiscountDocument } from '@/graphql/generated/graphql';
import { message } from '@/components/shared/utils';
import { toast } from 'react-toastify';
import {
  cardDiscountFormToCreateApiMapper,
  CartDiscountFormData,
  cartDiscountFormDataDefault,
} from '../utils';
import { useForm } from 'react-hook-form';
import { SpinnerLoading } from '@/components/globals/spinner-loading';
import { FaPlus } from 'react-icons/fa';

type CartDiscountProps = {
  refetchListPage: () => void;
  groupID: string;
};

export const CartDiscount = ({ refetchListPage, groupID }: CartDiscountProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const methods = useForm<CartDiscountFormData>({
    defaultValues: { ...cartDiscountFormDataDefault },
  });

  const [createCartDiscountMutation, { loading: createCartDiscountLoading }] = useMutation(
    Gateway_CreateDiscountDocument,
    {
      onCompleted: (_) => {
        toast.success(message.api.successCreate('Category'));
        methods.reset({ ...cartDiscountFormDataDefault });
        setIsModalOpen(false);
        refetchListPage();
      },
      onError: (error) => {
        toast.error(message.api.errorCreate('Category', error.message));
      },
    },
  );

  const onSubmitForm = async (data: CartDiscountFormData) => {
    try {
      await createCartDiscountMutation({
        variables: {
          input: cardDiscountFormToCreateApiMapper(data, groupID),
        },
      });
    } catch (e) {
      console.error('Add Product Mutation error: ', e);
    }
  };

  return (
    <>
      <Button className="mt-[20px] h-[38px] p-0" color="blue" onClick={() => setIsModalOpen(true)}>
        <div className="flex items-center gap-x-3">
          <FaPlus className="text-xl" />
          <span>Add Cart Discount</span>
        </div>
      </Button>
      <Modal show={isModalOpen} onClose={() => setIsModalOpen(false)} size="4xl">
        <Modal.Header className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-blue-600">Add Cart Discount</h3>
        </Modal.Header>
        <Modal.Body>
          <SpinnerLoading isLoading={createCartDiscountLoading} />
          <CartDiscountForm methods={methods} onSubmit={onSubmitForm} />
        </Modal.Body>
      </Modal>
    </>
  );
};
