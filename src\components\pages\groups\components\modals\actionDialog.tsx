'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
} from '@/components/ui/dialog';

const CleanUpDialog = ({
  isOpen,
  onClose,
  onSubmit,
  actionType,
  actionData,
  onFormSubmit,
}: any) => {
  const handleSubmit = () => {};

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="opacity-0">
        <DialogContent className="flex max-w-[624px] flex-col p-10 text-[#495057]">
          <DialogDescription>
            <div className="flex flex-col items-center justify-center gap-y-4">
              <span className="text-2xl font-bold text-[#495057]"></span>
              <div className="flex w-full flex-col justify-between gap-y-10">
                <span className="text-center text-[#495057]">test</span>
                <div className="flex w-full justify-between gap-4">
                  <DialogClose asChild>
                    <Button className="w-full border border-[#3C5B80] bg-white py-4 font-bold text-[#3C5B80]">
                      Cancel
                    </Button>
                  </DialogClose>

                  <Button
                    className="w-full bg-[#3C5B80] py-4 font-bold text-white"
                    onClick={handleSubmit}
                  >
                    Create
                  </Button>
                </div>
              </div>
            </div>
          </DialogDescription>
        </DialogContent>
      </DialogOverlay>
    </Dialog>
  );
};

export default CleanUpDialog;
