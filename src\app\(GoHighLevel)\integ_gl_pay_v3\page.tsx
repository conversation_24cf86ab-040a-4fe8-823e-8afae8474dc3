'use client';

import { useForm, FormProvider } from 'react-hook-form';
import { FormInput } from '@/components/globals';
import { message, emailPatternRegex } from '@/components/shared/utils';
import { useEffect, useState } from 'react';
import { NextPage } from 'next';
import { apolloClient } from '@/lib/graphql/ApolloClient';
import { toast } from 'react-toastify';
import styles from './index.module.css';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { GetPaymentPageData, SubmitPaymentUNIPageData } from '@/graphql/declarations/payments';
import { TSEPHostedTokenizerComponent } from '@/components/payments/tsep/tokenizer';

interface PaymentInitiateProps {
  type: 'payment_initiate_props';
  publishableKey: string;
  amount: number;
  currency: string;
  mode: string;
  productDetails: { productId: string; priceId: string };
  contact?: {
    id: string;
    name: string;
    email: string;
    contact: string;
  };
  orderId: string;
  transactionId: string;
  subscriptionId: string;
  locationId: string;
}

const Payment: NextPage = () => {
  const methods = useForm({
    defaultValues: {
      fullName: '',
      cardNumber: '',
      cardToken: '',
      cvc: '',
      expiryDate: '',
      email: '',
      companyName: '',
      street: '',
      city: '',
      zipCode: '',
    },
    // add validator that checks if cardToken, cvc, expiryDate are filled
  });
  const [paymentData, setPaymentData] = useState<PaymentInitiateProps | null>(null);
  const [calculations, setCalculations] = useState({
    subtotal: 0,
    discount: 0,
    total: 0,
  });
  const [lineItems, setLineItems] = useState<
    {
      name: string;
      price: number;
      quantity: number;
      description: string;
      total: number;
    }[]
  >([]);

  async function loadInvoiceData() {
    if (!paymentData) return;

    const d = await apolloClient.query({
      query: GetPaymentPageData,
      variables: {
        input: {
          apiKey: paymentData?.publishableKey,
          transactionID: paymentData?.transactionId,
        },
      },
    });

    let data = d.data.ghl_api_getPaymentPageData?.data;
    if (!data) return;

    setCalculations({
      total: data.amount / 100,
      discount: (data.amountSummary?.discount ?? 0) / 100,
      subtotal: (data.amountSummary?.subtotal ?? 0) / 100,
    });

    setLineItems(
      data.lineItems?.map((item: any, i: number) => ({
        name: item?.name ?? `Item ${i + 1}`,
        price: (item?.price ?? 0) / 100,
        quantity: item?.quantity ?? 1,
        total: (item?.total ?? 0) / 100,
        description: item?.description ?? '',
      })) ?? [],
    );

    methods.setValue('email', data?.customerData?.email ?? '');
    methods.setValue('companyName', data?.customerData?.company ?? '');
    methods.setValue('street', data?.customerData?.address_line_1 ?? '');
    methods.setValue('city', data?.customerData?.city ?? '');
    methods.setValue('zipCode', data?.customerData?.postal_code ?? '');
    methods.setValue('fullName', data?.customerData?.name ?? '');

    // setFieldData('tsep-zipCode', `${data?.customerData?.postal_code ?? 0}`, 5);
    // setFieldData('tsep-cardHolderName', `${data?.customerData?.name ?? ''}`, 5);
  }

  const setFieldData = (field: string, value: string, retries: number) => {
    // search field by id
    let fieldElement = document.getElementById(field) as HTMLInputElement;
    if (fieldElement) {
      fieldElement.value = value;
    } else {
      if (retries <= 0) {
        console.error('Field not found', field);
        return;
      }

      retries--;

      // retry after 1 second
      setTimeout(() => {
        setFieldData(field, value, retries);
      }, 2000);
    }
  };

  useEffect(() => {
    loadInvoiceData();
    setTimeout(() => {}, 5000);
  }, [paymentData]);

  async function postTransactionsFlag(args: { type: string; message?: string }) {
    switch (args.type) {
      case 'success': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_success_response',
            chargeId: args.message || '',
          }),
          '*',
        );
        break;
      }
      case 'error': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_error_response',
            error: {
              description: args.message || '',
            },
          }),
          '*',
        );
        break;
      }
      case 'cancel': {
        window.parent.postMessage(
          JSON.stringify({
            type: 'custom_element_close_response',
          }),
          '*',
        );
        break;
      }
    }
  }

  async function providerReadyFlag() {}

  function processEvents(rawdata: any) {
    try {
      const data = JSON.parse(rawdata);
      const dType = data.type;
      if (!dType) {
        console.log('No type found in data');
        return;
      }

      switch (dType) {
        case 'payment_initiate_props': {
          let d = data as PaymentInitiateProps;
          setPaymentData(d);
          break;
        }
      }
    } catch (e) {
      console.error('Error in parsing data', e);
    }
  }

  useEffect(() => {
    providerReadyFlag();
    window.addEventListener('message', ({ data }) => {
      processEvents(data);
    });
  }, []);

  const getIP = async () => {
    const resp = await fetch('https://api.ipify.org?format=json');
    const data = await resp.json();
    return data.ip;
  };

  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitPayment = async () => {
    if (!paymentData) return;
    setIsSubmitting(true);
    let ip = await getIP();
    try {
      let month = methods.getValues('expiryDate').split('/')[0];
      let year = methods.getValues('expiryDate').split('/')[1];
      const resp = await apolloClient.mutate({
        mutation: SubmitPaymentUNIPageData,
        variables: {
          input: {
            apiKey: paymentData?.publishableKey,
            transactionID: paymentData?.transactionId,
            // paymentToken: payToken,
            paymentCard: {
              // We know this is blank, leave it blank
              // cardNumber: methods.getValues('cardNumber'),
              cardCvv: methods.getValues('cvc'),
              cardExpMonth: month,
              cardExpYear: year,
              cardToken: methods.getValues('cardToken'),
            },
            paymentToken: methods.getValues('cardToken'),
            ipAddress: ip,
          },
        },
      });
      toast.success('Payment Successful');

      let txID = resp.data?.ghl_api_submitPayment_uni?.txID;

      if (!txID) {
        throw new Error('Payment Failed');
      }

      postTransactionsFlag({
        type: 'success',
        message: txID,
      });
    } catch (error) {
      console.error(error);
      // toast.error("Payment Failed");
      // postTransactionsFlag({ type: "error", message: error?.message });
    }
    setIsSubmitting(false);
  };

  // if (!isReady) {
  //   return <div>Loading...</div>;
  // }

  const watchedValues = methods.watch();
  const cardToken = watchedValues.cardToken;
  const cardTokenReady = cardToken !== '' && cardToken !== undefined;

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(submitPayment)}>
        <div className={styles.payment}>
          <div className={styles.container}>
            <div className={cn('px-4 font-bold')}>Payment</div>
            <div className={cn(styles.cards, 'flex-col lg:flex-row')}>
              <div className={cn(styles.cardDetails, 'flex-[2]')}>
                <div className={styles.orderDetails}>
                  <div className={styles.headerSm}>
                    <b className={styles.b}>Items</b>
                    <img className={styles.infoIcon} alt="" src="info.svg" />
                  </div>
                  <div className={styles.table}>
                    <div className={styles.tableColumns}>
                      <div className={styles.column}>
                        {lineItems.map((item, index) => (
                          <div key={index} className={styles.tablescell}>
                            {item.name}
                          </div>
                        ))}
                      </div>
                      <div className={styles.column}>
                        {lineItems.map((item, index) => (
                          <div key={index} className={styles.tablescell}>
                            $ {item.price}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                <div className={cn(styles.inputs)}>
                  <div className={styles.row}>
                    <div className={styles.inputField}>
                      <FormInput
                        id="fullName"
                        name="fullName"
                        label="Full name (as displayed on card)"
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                  </div>
                </div>
                <div className={cn(styles.inputs, '!hidden')}>
                  <div className={styles.row}>
                    <div className={styles.inputField}>
                      {/* <FormFormattedInput
                        id="cardNumber"
                        name="cardNumber"
                        label="Card number"
                        mask="9999 9999 9999 9999"
                        rules={{ required: message.requiredField }}
                      /> */}
                      {/* <FormInput
                        id="cardToken"
                        name="cardToken"
                        label="Card token"
                        rules={{ required: message.requiredField }}
                      /> */}
                    </div>
                  </div>
                  {/* <div className={styles.row}>
                    <div className={styles.inputField}>
                      <FormFormattedInput
                        id="cvc"
                        name="cvc"
                        label="CVC"
                        mask="999"
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                    <div className={styles.inputField}>
                      <FormFormattedInput
                        id="expiryDate"
                        name="expiryDate"
                        label="Expiry date"
                        mask="99/99"
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                  </div> */}
                </div>
                <br />
                <TSEPHostedTokenizerComponent
                  // fieldErrors={fieldErrors}
                  onEvent={(eventType, event) => {
                    console.log(eventType, event);
                  }}
                  labels={{
                    cardNumber: 'Card Number',
                    expiryDate: 'Expiry Date',
                    cvv: 'CVC',
                    cardHolderName: 'Card Holder Name',
                    zipCode: 'Zip Code',
                  }}
                  allowOptionals={{
                    cvv: true,
                    zipCode: true,
                    cardHolderName: true,
                  }}
                  onToken={(token) => {
                    methods.setValue('fullName', token.cardHolderName);
                    methods.setValue('cardToken', token.tsepToken);
                    methods.setValue('cvc', token.cvv2);
                    methods.setValue('expiryDate', token.expirationDate);
                    methods.setValue('zipCode', token.zipCode);
                    // methods.setValue('brand', token.cardType);
                    // console.log('token', token);
                  }}
                  onTokenError={() => {
                    // console.error('error', error);
                    methods.setValue('fullName', '');
                    methods.setValue('cardToken', '');
                    methods.setValue('cvc', '');
                    methods.setValue('expiryDate', '');
                    methods.setValue('zipCode', '');
                    // methods.setValue('brand', '');
                    // setVerifyResult(undefined);
                  }}
                />
                <br />
                <button
                  type="submit"
                  className={styles.button}
                  disabled={isSubmitting || !cardTokenReady}
                >
                  <p className={styles.text1}>
                    {isSubmitting ? 'Processing...' : `Pay $${calculations.total.toFixed(2)}`}{' '}
                    {/* {`${cardTokenReady}`} */}
                  </p>
                </button>
                {/* {JSON.stringify(watchedValues)} */}
              </div>
              <div className={cn(styles.priceLogos, 'flex-1 px-4')}>
                <div className={styles.totalPrice}>
                  <div className={styles.inputs}>
                    <div className={styles.listItems}>
                      <div className={styles.listItem}>
                        <div className={styles.heading}>Original price</div>
                        <div className={styles.heading}>${calculations.subtotal}</div>
                      </div>
                      <div className={styles.listItem}>
                        <div className={styles.heading}>Discount</div>
                        <div className={styles.heading3}>-${calculations.discount}</div>
                      </div>
                    </div>
                    <div className={styles.listItem4}>
                      <div className={styles.listItem5}>Total</div>
                      <div className={styles.listItem5}>${calculations.total}</div>
                    </div>
                  </div>
                </div>
                <div className={cn(styles.brandLogos, 'flex-col sm:flex-row')}>
                  <div className="relative aspect-[95/32] h-12">
                    <Image
                      className={styles.brandLogospaypalIcon}
                      alt=""
                      src="/icons/payments/paypal.png"
                      layout="fill"
                    />
                  </div>
                  <div className="relative aspect-[58/32] h-12">
                    <Image
                      className={styles.brandLogosvisaIcon}
                      alt=""
                      src="/icons/payments/visa.png"
                      layout="fill"
                    />
                  </div>
                  <div className="relative aspect-[40/32] h-12">
                    <Image
                      className={styles.brandLogosmastercardIcon}
                      alt=""
                      src="/icons/payments/mastercard.png"
                      layout="fill"
                    />
                  </div>
                </div>
                <div className={styles.billingDetails}>
                  <div className={styles.headerSm}>
                    <b className={styles.b}>Billing details</b>
                    <img className={styles.infoIcon} alt="" src="info.svg" />
                  </div>
                  <div className={styles.inputs1}>
                    <div className={styles.listItems}>
                      <FormInput
                        id="email"
                        name="email"
                        label="Email"
                        type="email"
                        rules={{
                          required: message.requiredField,
                          pattern: {
                            value: emailPatternRegex,
                            message: message.emailPattern,
                          },
                        }}
                      />
                    </div>
                    <div className={styles.listItems}>
                      <FormInput id="companyName" name="companyName" label="Company name" />
                    </div>
                    <div className={styles.listItems}>
                      <FormInput
                        id="street"
                        name="street"
                        label="Street"
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                    <div className={styles.listItems}>
                      <FormInput
                        id="city"
                        name="city"
                        label="City"
                        rules={{ required: message.requiredField }}
                      />
                    </div>
                    <div className={styles.listItems}>
                      <FormInput
                        id="zipCode"
                        name="zipCode"
                        label="Zip/Postal code"
                        type="number"
                        rules={{
                          required: message.requiredField,
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className={cn(styles.helper, 'px-4')}>
              <div className={styles.heading}>
                <span>{`Payment processed by `}</span>
                <span className={styles.payu}>NGNair Payments</span>
              </div>
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};

export default Payment;
