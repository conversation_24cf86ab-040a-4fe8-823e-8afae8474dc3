'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function PayLinkPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard
    router.replace('/dashboard');
  }, [router]);

  return (
    <div className="flex h-full w-full items-center justify-center p-8">
      <div className="max-w-lg rounded-lg border border-yellow-200 bg-yellow-50 p-6 text-center">
        <h2 className="mb-4 text-xl font-semibold text-yellow-800">
          Pay Links Currently Unavailable
        </h2>
        <p className="text-yellow-700">
          The catalog feature is temporarily disabled. You will be redirected to the dashboard.
        </p>
      </div>
    </div>
  );
}
